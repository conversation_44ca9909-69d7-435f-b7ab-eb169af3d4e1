<?php

return [
    /*
    |--------------------------------------------------------------------------
    | 微信公众号配置 - 手机端微信授权登录
    |--------------------------------------------------------------------------
    |
    | 这里是微信公众号的配置信息，用于手机端微信授权登录
    |
    */
    'app_id' => env('WECHAT_APP_ID', ''),
    'app_secret' => env('WECHAT_APP_SECRET', ''),
    'oauth_callback' => env('WECHAT_OAUTH_CALLBACK', 'https://pay.itapgo.com/app/#/wechat-callback'),

    /*
    |--------------------------------------------------------------------------
    | 微信公众号配置 (兼容旧版本)
    |--------------------------------------------------------------------------
    |
    | 这里是为了兼容旧版本的配置路径
    |
    */
    'official_account' => [
        'default' => [
            'app_id' => env('WECHAT_APP_ID', ''),
            'secret' => env('WECHAT_APP_SECRET', ''),
        ],
        'app_id' => env('WECHAT_APP_ID', ''),
        'secret' => env('WECHAT_APP_SECRET', ''),
    ],

    /*
    |--------------------------------------------------------------------------
    | 微信开放平台配置 - PC管理后台扫码登录
    |--------------------------------------------------------------------------
    |
    | 这里是微信开放平台的配置信息，用于PC管理后台扫码登录
    | 严格区分于手机端微信公众号配置
    |
    */
    'web' => [
        'app_id' => env('WECHAT_WEB_APP_ID', ''),
        'app_secret' => env('WECHAT_WEB_APP_SECRET', ''),
        'redirect_uri' => env('WECHAT_REDIRECT_URI', ''),
    ],
];
