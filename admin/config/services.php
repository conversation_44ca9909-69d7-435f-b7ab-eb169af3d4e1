<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Third Party Services
    |--------------------------------------------------------------------------
    |
    | This file is for storing the credentials for third party services such
    | as Mailgun, Postmark, AWS and more. This file provides the de facto
    | location for this type of information, allowing packages to have
    | a conventional file to locate the various service credentials.
    |
    */

    'mailgun' => [
        'domain' => env('MAILGUN_DOMAIN'),
        'secret' => env('MAILGUN_SECRET'),
        'endpoint' => env('MAILGUN_ENDPOINT', 'api.mailgun.net'),
        'scheme' => 'https',
    ],

    'postmark' => [
        'token' => env('POSTMARK_TOKEN'),
    ],

    'ses' => [
        'key' => env('AWS_ACCESS_KEY_ID'),
        'secret' => env('AWS_SECRET_ACCESS_KEY'),
        'region' => env('AWS_DEFAULT_REGION', 'us-east-1'),
    ],
    
    /*
    |--------------------------------------------------------------------------
    | 微信服务配置
    |--------------------------------------------------------------------------
    |
    | 这里是微信相关服务的配置信息，包括公众号、小程序等
    |
    */
    'wechat' => [
        'app_id' => env('WECHAT_APP_ID', ''),
        'app_secret' => env('WECHAT_APP_SECRET', ''),
        'token' => env('WECHAT_TOKEN', ''),
        'aes_key' => env('WECHAT_AES_KEY', ''),
        
        // 授权回调URL
        'oauth_callback' => env('WECHAT_OAUTH_CALLBACK', 'https://pay.itapgo.com/app/#/wechat-callback'),
        
        // 微信支付配置
        'payment' => [
            'mch_id' => env('WECHAT_PAYMENT_MCH_ID', ''),
            'key' => env('WECHAT_PAYMENT_KEY', ''),
            'cert_path' => env('WECHAT_PAYMENT_CERT_PATH', ''),
            'key_path' => env('WECHAT_PAYMENT_KEY_PATH', ''),
            'notify_url' => env('WECHAT_PAYMENT_NOTIFY_URL', ''),
        ],
    ],

];
