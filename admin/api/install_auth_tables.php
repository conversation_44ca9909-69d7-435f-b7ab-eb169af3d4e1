<?php
/**
 * 安装认证相关表结构
 */

// 设置HTTP响应头
header('Content-Type: application/json');

// 引入配置
require_once __DIR__ . '/config.php';

// 创建数据库连接
$conn = new mysqli(
    $DB_CONFIG['HOST'],
    $DB_CONFIG['USER'],
    $DB_CONFIG['PASSWORD'],
    $DB_CONFIG['DATABASE'],
    $DB_CONFIG['PORT']
);

// 检查连接
if ($conn->connect_error) {
    echo json_encode([
        'success' => false,
        'message' => '数据库连接失败: ' . $conn->connect_error
    ]);
    exit;
}

// 设置字符集
$conn->set_charset($DB_CONFIG['CHARSET']);

// 创建auth_tokens表
$create_auth_tokens = "
CREATE TABLE IF NOT EXISTS auth_tokens (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    token VARCHAR(255) NOT NULL,
    expires_at DATETIME NOT NULL,
    created_at DATETIME NOT NULL,
    last_used_at DATETIME NOT NULL,
    UNIQUE KEY token_unique (token),
    INDEX user_id_idx (user_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
";

// 执行SQL
$result = $conn->query($create_auth_tokens);

if ($result) {
    // 检查表是否已经存在
    $check_exists = $conn->query("SHOW TABLES LIKE 'auth_tokens'");
    $table_exists = $check_exists->num_rows > 0;
    
    echo json_encode([
        'success' => true,
        'message' => $table_exists ? 'auth_tokens表已存在' : 'auth_tokens表创建成功'
    ]);
} else {
    echo json_encode([
        'success' => false,
        'message' => '创建auth_tokens表失败: ' . $conn->error
    ]);
}

// 关闭连接
$conn->close();
?> 