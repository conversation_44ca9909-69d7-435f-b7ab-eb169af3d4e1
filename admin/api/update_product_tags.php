<?php
// 设置响应头
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 引入配置
require_once 'config.php';

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 只允许POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode([
        'code' => 405,
        'message' => '只允许POST请求',
        'data' => null
    ]);
    exit;
}

// 获取请求体JSON数据
$request_body = file_get_contents('php://input');
$data = json_decode($request_body, true);

// 验证必填参数
if (!isset($data['id']) || !is_numeric($data['id'])) {
    echo json_encode([
        'code' => 400,
        'message' => '缺少商品ID或格式不正确',
        'data' => null
    ]);
    exit;
}

// 验证标签参数
$tag_fields = ['is_hot', 'is_new', 'is_recommend', 'is_premium'];
$update_fields = [];
$params = [];
$types = '';

foreach ($tag_fields as $field) {
    if (isset($data[$field]) && is_bool($data[$field])) {
        $update_fields[] = "{$field} = ?";
        $params[] = $data[$field] ? 1 : 0;
        $types .= 'i';
    }
}

// 至少需要一个标签更新
if (empty($update_fields)) {
    echo json_encode([
        'code' => 400,
        'message' => '至少需要一个有效的标签参数',
        'data' => null
    ]);
    exit;
}

// 连接数据库
$conn = new mysqli(
    $DB_CONFIG['HOST'],
    $DB_CONFIG['USER'],
    $DB_CONFIG['PASSWORD'],
    $DB_CONFIG['DATABASE'],
    $DB_CONFIG['PORT']
);

// 检查连接
if ($conn->connect_error) {
    echo json_encode([
        'code' => 500,
        'message' => '数据库连接失败: ' . $conn->connect_error,
        'data' => null
    ]);
    exit;
}

// 设置字符集
$conn->set_charset($DB_CONFIG['CHARSET']);

// 构建更新SQL
$update_sql = "UPDATE products SET " . implode(', ', $update_fields) . " WHERE id = ?";
$params[] = $data['id'];
$types .= 'i';

// 执行更新
$stmt = $conn->prepare($update_sql);
$stmt->bind_param($types, ...$params);
$result = $stmt->execute();

if ($result) {
    // 查询更新后的商品信息
    $query_sql = "SELECT id, name, thumbnail, price, is_hot, is_new, is_recommend, is_premium FROM products WHERE id = ?";
    $query_stmt = $conn->prepare($query_sql);
    $query_stmt->bind_param('i', $data['id']);
    $query_stmt->execute();
    $query_result = $query_stmt->get_result();
    $product = $query_result->fetch_assoc();
    
    // 提取标签
    $tags = [];
    if ($product['is_hot'] == 1) $tags[] = '热销';
    if ($product['is_new'] == 1) $tags[] = '新品';
    if ($product['is_recommend'] == 1) $tags[] = '推荐';
    if ($product['is_premium'] == 1) $tags[] = '精品';
    
    // 添加标签信息
    $product['tags'] = $tags;
    
    echo json_encode([
        'code' => 0,
        'message' => '更新成功',
        'data' => $product
    ]);
    
    $query_stmt->close();
} else {
    echo json_encode([
        'code' => 500,
        'message' => '更新失败: ' . $stmt->error,
        'data' => null
    ]);
}

$stmt->close();
$conn->close(); 