<?php
/**
 * 获取活跃的首页导航项列表
 */

// 加载配置文件
require_once dirname(__DIR__) . '/config.php';

// 设置响应头
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 如果是预检请求，直接返回成功
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 只处理GET请求
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    sendResponse(405, '不支持的请求方法');
}

try {
    // 连接数据库
    $db = new mysqli(
        $DB_CONFIG['HOST'],
        $DB_CONFIG['USER'],
        $DB_CONFIG['PASSWORD'],
        $DB_CONFIG['DATABASE'],
        $DB_CONFIG['PORT']
    );
    
    if ($db->connect_error) {
        throw new Exception('数据库连接失败: ' . $db->connect_error);
    }

    // 检查home_nav_items表是否存在
    $tableExists = false;
    $tablesResult = $db->query("SHOW TABLES LIKE 'home_nav_items'");
    if ($tablesResult && $tablesResult->num_rows > 0) {
        $tableExists = true;
    }

    $navItems = [];

    if ($tableExists) {
        // 从home_nav_items表获取数据
        $sql = "SELECT id, icon, title, link_url as path, status, sort_order FROM home_nav_items WHERE status = 1 ORDER BY sort_order ASC";
        $result = $db->query($sql);
        
        if ($result && $result->num_rows > 0) {
            while ($row = $result->fetch_assoc()) {
                $navItems[] = $row;
            }
        }
    } else {
        // 如果表不存在，使用默认数据
        $navItems = [
            ['id' => 1, 'icon' => 'shop-o', 'title' => '商品分类', 'path' => '/category', 'status' => 1, 'sort_order' => 10],
            ['id' => 2, 'icon' => 'gift-o', 'title' => '每日特惠', 'path' => '/daily-special', 'status' => 1, 'sort_order' => 20],
            ['id' => 3, 'icon' => 'balance-o', 'title' => '净水充值', 'path' => '/water-recharge', 'status' => 1, 'sort_order' => 30],
            ['id' => 4, 'icon' => 'location-o', 'title' => '取水点', 'path' => '/water-point', 'status' => 1, 'sort_order' => 40],
            ['id' => 5, 'icon' => 'service-o', 'title' => '客户服务', 'path' => '/service', 'status' => 1, 'sort_order' => 50]
        ];
    }
    
    sendResponse(0, '获取成功', $navItems);
} catch (Exception $e) {
    sendResponse(500, '获取首页导航项失败: ' . $e->getMessage());
}

/**
 * 发送JSON响应
 * @param int $code 状态码
 * @param string $message 消息
 * @param mixed $data 数据
 */
function sendResponse($code, $message, $data = null) {
    $response = array(
        'code' => $code,
        'message' => $message
    );
    
    if ($data !== null) {
        $response['data'] = $data;
    }
    
    echo json_encode($response);
    exit;
}
?> 