<?php
/**
 * 底部导航配置管理API
 */

// 加载配置文件
require_once __DIR__ . '/config.php';

// 设置响应头
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');

// 如果是预检请求，直接返回成功
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 连接数据库
try {
    $db = new mysqli(
        $DB_CONFIG['HOST'],
        $DB_CONFIG['USER'],
        $DB_CONFIG['PASSWORD'],
        $DB_CONFIG['DATABASE'],
        $DB_CONFIG['PORT']
    );
    
    if ($db->connect_error) {
        throw new Exception('数据库连接失败: ' . $db->connect_error);
    }
    
    // 设置字符集
    $db->set_charset($DB_CONFIG['CHARSET']);
} catch (Exception $e) {
    sendResponse(500, '数据库连接失败: ' . $e->getMessage());
}

// 确保导航表存在
ensureNavConfigTableExists($db);

// 处理请求
$method = $_SERVER['REQUEST_METHOD'];
$action = isset($_GET['action']) ? $_GET['action'] : '';

try {
    switch ($method) {
        case 'GET':
            // 查询操作
            if ($action === 'detail' && isset($_GET['id'])) {
                getNavItemById($_GET['id'], $db);
            } else {
                getNavItems($db);
            }
            break;
            
        case 'POST':
            // 添加操作
            addNavItem($db);
            break;
            
        case 'PUT':
            // 更新操作
            updateNavItem($db);
            break;
            
        case 'DELETE':
            // 删除操作
            if (isset($_GET['id'])) {
                deleteNavItem($_GET['id'], $db);
            } else {
                sendResponse(400, '缺少必要的ID参数');
            }
            break;
            
        default:
            sendResponse(405, '不支持的请求方法');
            break;
    }
} catch (Exception $e) {
    sendResponse(500, '操作失败: ' . $e->getMessage());
} finally {
    // 关闭数据库连接
    if (isset($db) && !$db->connect_error) {
        $db->close();
    }
}

/**
 * 确保导航配置表存在
 */
function ensureNavConfigTableExists($db) {
    $sql = "CREATE TABLE IF NOT EXISTS `nav_config` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `nav_id` varchar(50) NOT NULL COMMENT '导航ID',
        `nav_name` varchar(50) NOT NULL COMMENT '导航名称',
        `icon` varchar(100) NOT NULL COMMENT '图标名称',
        `path` varchar(255) NOT NULL COMMENT '链接路径',
        `highlight` tinyint(1) DEFAULT '0' COMMENT '是否高亮',
        `status` tinyint(1) DEFAULT '1' COMMENT '状态 1-启用 0-禁用',
        `sort_order` int(11) DEFAULT '0' COMMENT '排序',
        `created_at` datetime DEFAULT NULL COMMENT '创建时间',
        `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
        PRIMARY KEY (`id`),
        UNIQUE KEY `nav_id` (`nav_id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='APP底部导航配置';";
    
    $db->query($sql);
    
    // 检查更新时间字段是否存在，如果不存在则添加
    $checkColumnSql = "SHOW COLUMNS FROM `nav_config` LIKE 'updated_at'";
    $columnResult = $db->query($checkColumnSql);
    if ($columnResult && $columnResult->num_rows === 0) {
        $addColumnSql = "ALTER TABLE `nav_config` ADD COLUMN `updated_at` datetime DEFAULT NULL COMMENT '更新时间'";
        $db->query($addColumnSql);
    }
    
    // 检查创建时间字段是否存在，如果不存在则添加
    $checkCreatedColumnSql = "SHOW COLUMNS FROM `nav_config` LIKE 'created_at'";
    $createdColumnResult = $db->query($checkCreatedColumnSql);
    if ($createdColumnResult && $createdColumnResult->num_rows === 0) {
        $addCreatedColumnSql = "ALTER TABLE `nav_config` ADD COLUMN `created_at` datetime DEFAULT NULL COMMENT '创建时间'";
        $db->query($addCreatedColumnSql);
    }
    
    // 检查是否有数据，如果没有则添加默认数据
    $checkSql = "SELECT COUNT(*) as count FROM nav_config";
    $result = $db->query($checkSql);
    $row = $result->fetch_assoc();
    
    if ($row['count'] == 0) {
        addDefaultNavItems($db);
    }
}

/**
 * 添加默认导航项
 */
function addDefaultNavItems($db) {
    $now = date('Y-m-d H:i:s');
    $defaultItems = [
        [
            'nav_id' => 'home',
            'nav_name' => '首页',
            'icon' => 'home-o',
            'path' => '/',
            'highlight' => 1,
            'status' => 1,
            'sort_order' => 10,
            'created_at' => $now,
            'updated_at' => $now
        ],
        [
            'nav_id' => 'device',
            'nav_name' => '设备',
            'icon' => 'cluster-o',
            'path' => '/device',
            'highlight' => 0,
            'status' => 1,
            'sort_order' => 20,
            'created_at' => $now,
            'updated_at' => $now
        ],
        [
            'nav_id' => 'water',
            'nav_name' => '取水点',
            'icon' => 'location-o',
            'path' => '/water-point',
            'highlight' => 0,
            'status' => 1,
            'sort_order' => 30,
            'created_at' => $now,
            'updated_at' => $now
        ],
        [
            'nav_id' => 'user',
            'nav_name' => '我的',
            'icon' => 'user-o',
            'path' => '/user',
            'highlight' => 0,
            'status' => 1,
            'sort_order' => 40,
            'created_at' => $now,
            'updated_at' => $now
        ]
    ];
    
    foreach ($defaultItems as $item) {
        $sql = "INSERT INTO nav_config (nav_id, nav_name, icon, path, highlight, status, sort_order, created_at, updated_at)
                VALUES ('{$item['nav_id']}', '{$item['nav_name']}', '{$item['icon']}', '{$item['path']}', {$item['highlight']}, {$item['status']}, {$item['sort_order']}, '{$item['created_at']}', '{$item['updated_at']}')";
        $db->query($sql);
    }
}

/**
 * 获取所有导航项
 */
function getNavItems($db) {
    $sql = "SELECT * FROM nav_config ORDER BY sort_order ASC";
    $result = $db->query($sql);
    
    $navItems = array();
    if ($result && $result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            $navItems[] = $row;
        }
        sendResponse(0, '获取成功', $navItems);
    } else {
        sendResponse(0, '暂无数据', []);
    }
}

/**
 * 根据ID获取导航项
 */
function getNavItemById($id, $db) {
    $id = $db->real_escape_string($id);
    $sql = "SELECT * FROM nav_config WHERE nav_id = '$id'";
    $result = $db->query($sql);
    
    if ($result && $result->num_rows > 0) {
        $navItem = $result->fetch_assoc();
        sendResponse(0, '获取成功', $navItem);
    } else {
        sendResponse(404, '未找到该导航项');
    }
}

/**
 * 添加导航项
 */
function addNavItem($db) {
    // 获取POST数据
    $data = json_decode(file_get_contents('php://input'), true);
    
    // 验证数据
    $requiredFields = ['nav_name', 'icon', 'path'];
    foreach ($requiredFields as $field) {
        if (!isset($data[$field]) || empty($data[$field])) {
            sendResponse(400, "缺少必要的字段: $field");
        }
    }
    
    // 检查是否已存在相同路径的导航项
    $path = $db->real_escape_string($data['path']);
    $sql = "SELECT COUNT(*) as count FROM nav_config WHERE path = '$path'";
    $result = $db->query($sql);
    $row = $result->fetch_assoc();
    if ($row['count'] > 0) {
        sendResponse(400, '已存在相同路径的导航项');
    }
    
    // 检查导航项数量是否超过5个
    $sql = "SELECT COUNT(*) as count FROM nav_config";
    $result = $db->query($sql);
    $row = $result->fetch_assoc();
    if ($row['count'] >= 5) {
        sendResponse(400, '底部导航最多只能添加5个项目');
    }
    
    // 准备数据
    $navId = isset($data['nav_id']) && !empty($data['nav_id']) ? $db->real_escape_string($data['nav_id']) : 'nav_' . time();
    $navName = $db->real_escape_string($data['nav_name']);
    $icon = $db->real_escape_string($data['icon']);
    $highlight = isset($data['highlight']) ? (int)$data['highlight'] : 0;
    $status = isset($data['status']) ? (int)$data['status'] : 1;
    $sortOrder = isset($data['sort_order']) ? (int)$data['sort_order'] : 999;
    $now = date('Y-m-d H:i:s');
    
    // 插入数据
    $sql = "INSERT INTO nav_config (nav_id, nav_name, icon, path, highlight, status, sort_order, created_at, updated_at)
            VALUES ('$navId', '$navName', '$icon', '$path', $highlight, $status, $sortOrder, '$now', '$now')";
    
    if ($db->query($sql)) {
        // 获取插入的ID
        $id = $db->insert_id;
        // 查询插入的数据
        $sql = "SELECT * FROM nav_config WHERE id = $id";
        $result = $db->query($sql);
        $navItem = $result->fetch_assoc();
        
        sendResponse(0, '添加成功', $navItem);
    } else {
        sendResponse(500, '添加失败: ' . $db->error);
    }
}

/**
 * 更新导航项
 */
function updateNavItem($db) {
    // 获取PUT数据
    $data = json_decode(file_get_contents('php://input'), true);
    
    // 验证数据
    if (!isset($data['id']) && !isset($data['nav_id'])) {
        sendResponse(400, '缺少必要的ID参数');
    }
    
    $requiredFields = ['nav_name', 'icon', 'path'];
    foreach ($requiredFields as $field) {
        if (!isset($data[$field]) || empty($data[$field])) {
            sendResponse(400, "缺少必要的字段: $field");
        }
    }
    
    // 准备数据
    $id = isset($data['id']) ? (int)$data['id'] : null;
    $navId = isset($data['nav_id']) ? $db->real_escape_string($data['nav_id']) : null;
    $navName = $db->real_escape_string($data['nav_name']);
    $icon = $db->real_escape_string($data['icon']);
    $path = $db->real_escape_string($data['path']);
    $highlight = isset($data['highlight']) ? (int)$data['highlight'] : 0;
    $status = isset($data['status']) ? (int)$data['status'] : 1;
    $sortOrder = isset($data['sort_order']) ? (int)$data['sort_order'] : 0;
    $now = date('Y-m-d H:i:s');
    
    // 检查是否存在相同路径的导航项（排除自身）
    if ($id) {
        $sql = "SELECT COUNT(*) as count FROM nav_config WHERE path = '$path' AND id != $id";
    } else {
        $sql = "SELECT COUNT(*) as count FROM nav_config WHERE path = '$path' AND nav_id != '$navId'";
    }
    $result = $db->query($sql);
    $row = $result->fetch_assoc();
    if ($row['count'] > 0) {
        sendResponse(400, '已存在相同路径的导航项');
    }
    
    // 更新数据
    if ($id) {
        $sql = "UPDATE nav_config SET 
                nav_name = '$navName', 
                icon = '$icon', 
                path = '$path', 
                highlight = $highlight, 
                status = $status, 
                sort_order = $sortOrder, 
                updated_at = '$now'
                WHERE id = $id";
    } else {
        $sql = "UPDATE nav_config SET 
                nav_name = '$navName', 
                icon = '$icon', 
                path = '$path', 
                highlight = $highlight, 
                status = $status, 
                sort_order = $sortOrder, 
                updated_at = '$now'
                WHERE nav_id = '$navId'";
    }
    
    if ($db->query($sql)) {
        // 查询更新后的数据
        if ($id) {
            $sql = "SELECT * FROM nav_config WHERE id = $id";
        } else {
            $sql = "SELECT * FROM nav_config WHERE nav_id = '$navId'";
        }
        $result = $db->query($sql);
        $navItem = $result->fetch_assoc();
        
        sendResponse(0, '更新成功', $navItem);
    } else {
        sendResponse(500, '更新失败: ' . $db->error);
    }
}

/**
 * 删除导航项
 */
function deleteNavItem($id, $db) {
    // 处理ID可能是数字ID或导航ID
    if (is_numeric($id)) {
        $sql = "DELETE FROM nav_config WHERE id = " . (int)$id;
    } else {
        $id = $db->real_escape_string($id);
        $sql = "DELETE FROM nav_config WHERE nav_id = '$id'";
    }
    
    if ($db->query($sql)) {
        sendResponse(0, '删除成功');
    } else {
        sendResponse(500, '删除失败: ' . $db->error);
    }
}

/**
 * 发送JSON响应
 * @param int $code 状态码
 * @param string $message 消息
 * @param mixed $data 数据
 */
function sendResponse($code, $message, $data = null) {
    $response = array(
        'code' => $code,
        'message' => $message
    );
    
    if ($data !== null) {
        $response['data'] = $data;
    }
    
    echo json_encode($response, JSON_UNESCAPED_UNICODE);
    exit;
}
?> 