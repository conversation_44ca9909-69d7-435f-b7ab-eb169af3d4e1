<?php
/**
 * 获取底部导航菜单项API
 */

// 允许跨域请求
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');

// 如果是预检请求，直接返回成功
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 只处理GET请求
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    sendResponse(405, '不支持的请求方法');
    exit;
}

// 添加调试日志
error_log("导航菜单API被调用: " . $_SERVER['REQUEST_URI']);

// 提供固定导航数据，避免数据库连接问题
$navItems = [
    ['id' => 1, 'title' => '首页', 'icon' => 'home-o', 'path' => '/', 'highlight' => 0, 'status' => 1, 'sort_order' => 10],
    ['id' => 2, 'title' => '设备', 'icon' => 'apps-o', 'path' => '/device', 'highlight' => 0, 'status' => 1, 'sort_order' => 20],
    ['id' => 3, 'title' => '取水点', 'icon' => 'location-o', 'path' => '/water-point', 'highlight' => 0, 'status' => 1, 'sort_order' => 30],
    ['id' => 4, 'title' => '商家', 'icon' => 'shop-o', 'path' => '/merchant', 'highlight' => 0, 'status' => 1, 'sort_order' => 40],
    ['id' => 5, 'title' => '我的', 'icon' => 'user-o', 'path' => '/user', 'highlight' => 0, 'status' => 1, 'sort_order' => 50]
];

// 加载配置文件（可选）
$configFile = __DIR__ . '/config.php';
if (file_exists($configFile)) {
    require_once $configFile;
    
    try {
        // 连接数据库
        if (isset($DB_CONFIG)) {
            $db = new mysqli(
                $DB_CONFIG['HOST'],
                $DB_CONFIG['USER'],
                $DB_CONFIG['PASSWORD'],
                $DB_CONFIG['DATABASE'],
                $DB_CONFIG['PORT']
            );
            
            if (!$db->connect_error) {
                // 从nav_config表获取数据
                $sql = "SELECT nav_id as id, nav_name as title, icon, path, highlight, status, sort_order FROM nav_config WHERE status = 1 ORDER BY sort_order ASC";
                $result = $db->query($sql);
                
                if ($result && $result->num_rows > 0) {
                    $dbNavItems = [];
                    while ($row = $result->fetch_assoc()) {
                        $dbNavItems[] = $row;
                    }
                    error_log("从数据库获取到 " . $result->num_rows . " 条导航菜单记录");
                    
                    // 只有成功获取数据库数据时，才覆盖默认导航
                    if (count($dbNavItems) > 0) {
                        $navItems = $dbNavItems;
                    }
                }
                
                $db->close();
            } else {
                error_log("数据库连接失败: " . $db->connect_error);
            }
        }
    } catch (Exception $e) {
        error_log("导航菜单API数据库错误: " . $e->getMessage());
        // 发生错误时使用默认导航 - 已在上面定义
    }
}

// 无论如何都返回导航数据
sendResponse(0, '获取成功', $navItems);

/**
 * 发送JSON响应
 * @param int $code 状态码
 * @param string $message 消息
 * @param mixed $data 数据
 */
function sendResponse($code, $message, $data = null) {
    $response = array(
        'code' => $code,
        'message' => $message
    );
    
    if ($data !== null) {
        $response['data'] = $data;
    }
    
    echo json_encode($response, JSON_UNESCAPED_UNICODE);
    exit;
}
?> 