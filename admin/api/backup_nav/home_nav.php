<?php
/**
 * 首页导航配置管理API
 */

// 加载配置文件
require_once __DIR__ . '/config.php';

// 设置响应头
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');

// 如果是预检请求，直接返回成功
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 连接数据库
try {
    $db = new mysqli(
        $DB_CONFIG['HOST'],
        $DB_CONFIG['USER'],
        $DB_CONFIG['PASSWORD'],
        $DB_CONFIG['DATABASE'],
        $DB_CONFIG['PORT']
    );
    
    if ($db->connect_error) {
        throw new Exception('数据库连接失败: ' . $db->connect_error);
    }
    
    // 设置字符集
    $db->set_charset($DB_CONFIG['CHARSET']);
} catch (Exception $e) {
    sendResponse(500, '数据库连接失败: ' . $e->getMessage());
}

// 确保导航表存在
ensureHomeNavTableExists($db);

// 处理请求
$method = $_SERVER['REQUEST_METHOD'];
$action = isset($_GET['action']) ? $_GET['action'] : '';

try {
    switch ($method) {
        case 'GET':
            // 查询操作
            if ($action === 'detail' && isset($_GET['id'])) {
                getHomeNavItemById($_GET['id'], $db);
            } else {
                getHomeNavItems($db);
            }
            break;
            
        case 'POST':
            // 添加操作
            addHomeNavItem($db);
            break;
            
        case 'PUT':
            // 更新操作
            updateHomeNavItem($db);
            break;
            
        case 'DELETE':
            // 删除操作
            if (isset($_GET['id'])) {
                deleteHomeNavItem($_GET['id'], $db);
            } else {
                sendResponse(400, '缺少必要的ID参数');
            }
            break;
            
        default:
            sendResponse(405, '不支持的请求方法');
            break;
    }
} catch (Exception $e) {
    sendResponse(500, '操作失败: ' . $e->getMessage());
} finally {
    // 关闭数据库连接
    if (isset($db) && !$db->connect_error) {
        $db->close();
    }
}

/**
 * 确保首页导航表存在
 */
function ensureHomeNavTableExists($db) {
    $sql = "CREATE TABLE IF NOT EXISTS `home_nav_items` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `icon` varchar(100) NOT NULL COMMENT '图标名称',
        `title` varchar(50) NOT NULL COMMENT '导航标题',
        `link_url` varchar(255) NOT NULL COMMENT '链接地址',
        `link_type` varchar(20) NOT NULL DEFAULT 'path' COMMENT '链接类型',
        `status` tinyint(1) DEFAULT '1' COMMENT '状态 1-启用 0-禁用',
        `sort_order` int(11) DEFAULT '0' COMMENT '排序',
        `created_at` datetime DEFAULT NULL COMMENT '创建时间',
        `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
        PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='APP首页导航配置';";
    
    $db->query($sql);
    
    // 检查更新时间字段是否存在，如果不存在则添加
    $checkColumnSql = "SHOW COLUMNS FROM `home_nav_items` LIKE 'updated_at'";
    $columnResult = $db->query($checkColumnSql);
    if ($columnResult && $columnResult->num_rows === 0) {
        $addColumnSql = "ALTER TABLE `home_nav_items` ADD COLUMN `updated_at` datetime DEFAULT NULL COMMENT '更新时间'";
        $db->query($addColumnSql);
    }
    
    // 检查创建时间字段是否存在，如果不存在则添加
    $checkCreatedColumnSql = "SHOW COLUMNS FROM `home_nav_items` LIKE 'created_at'";
    $createdColumnResult = $db->query($checkCreatedColumnSql);
    if ($createdColumnResult && $createdColumnResult->num_rows === 0) {
        $addCreatedColumnSql = "ALTER TABLE `home_nav_items` ADD COLUMN `created_at` datetime DEFAULT NULL COMMENT '创建时间'";
        $db->query($addCreatedColumnSql);
    }
    
    // 检查是否有数据，如果没有则添加默认数据
    $checkSql = "SELECT COUNT(*) as count FROM home_nav_items";
    $result = $db->query($checkSql);
    $row = $result->fetch_assoc();
    
    if ($row['count'] == 0) {
        addDefaultHomeNavItems($db);
    }
}

/**
 * 添加默认导航项
 */
function addDefaultHomeNavItems($db) {
    $now = date('Y-m-d H:i:s');
    $defaultItems = [
        [
            'icon' => 'shop-o',
            'title' => '商品分类',
            'link_url' => '/category',
            'link_type' => 'path',
            'status' => 1,
            'sort_order' => 10,
            'created_at' => $now,
            'updated_at' => $now
        ],
        [
            'icon' => 'gift-o',
            'title' => '每日特惠',
            'link_url' => '/daily-special',
            'link_type' => 'path',
            'status' => 1,
            'sort_order' => 20,
            'created_at' => $now,
            'updated_at' => $now
        ],
        [
            'icon' => 'balance-o',
            'title' => '净水充值',
            'link_url' => '/water-recharge',
            'link_type' => 'path',
            'status' => 1,
            'sort_order' => 30,
            'created_at' => $now,
            'updated_at' => $now
        ],
        [
            'icon' => 'location-o',
            'title' => '取水点',
            'link_url' => '/water-point',
            'link_type' => 'path',
            'status' => 1,
            'sort_order' => 40,
            'created_at' => $now,
            'updated_at' => $now
        ],
        [
            'icon' => 'service-o',
            'title' => '客户服务',
            'link_url' => '/service',
            'link_type' => 'path',
            'status' => 1,
            'sort_order' => 50,
            'created_at' => $now,
            'updated_at' => $now
        ]
    ];
    
    foreach ($defaultItems as $item) {
        $sql = "INSERT INTO home_nav_items (icon, title, link_url, link_type, status, sort_order, created_at, updated_at)
                VALUES ('{$item['icon']}', '{$item['title']}', '{$item['link_url']}', '{$item['link_type']}', {$item['status']}, {$item['sort_order']}, '{$item['created_at']}', '{$item['updated_at']}')";
        $db->query($sql);
    }
}

/**
 * 获取所有导航项
 */
function getHomeNavItems($db) {
    $sql = "SELECT * FROM home_nav_items ORDER BY sort_order ASC";
    $result = $db->query($sql);
    
    $navItems = array();
    if ($result && $result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            $navItems[] = $row;
        }
        sendResponse(0, '获取成功', $navItems);
    } else {
        sendResponse(0, '暂无数据', []);
    }
}

/**
 * 根据ID获取导航项
 */
function getHomeNavItemById($id, $db) {
    $id = (int)$id;
    $sql = "SELECT * FROM home_nav_items WHERE id = $id";
    $result = $db->query($sql);
    
    if ($result && $result->num_rows > 0) {
        $navItem = $result->fetch_assoc();
        sendResponse(0, '获取成功', $navItem);
    } else {
        sendResponse(404, '未找到该导航项');
    }
}

/**
 * 添加导航项
 */
function addHomeNavItem($db) {
    // 获取POST数据
    $data = json_decode(file_get_contents('php://input'), true);
    
    // 验证数据
    $requiredFields = ['title', 'icon', 'link_url'];
    foreach ($requiredFields as $field) {
        if (!isset($data[$field]) || empty($data[$field])) {
            sendResponse(400, "缺少必要的字段: $field");
        }
    }
    
    // 准备数据
    $icon = $db->real_escape_string($data['icon']);
    $title = $db->real_escape_string($data['title']);
    $linkUrl = $db->real_escape_string($data['link_url']);
    $linkType = isset($data['link_type']) ? $db->real_escape_string($data['link_type']) : 'path';
    $status = isset($data['status']) ? (int)$data['status'] : 1;
    $sortOrder = isset($data['sort_order']) ? (int)$data['sort_order'] : 999;
    $now = date('Y-m-d H:i:s');
    
    // 插入数据
    $sql = "INSERT INTO home_nav_items (icon, title, link_url, link_type, status, sort_order, created_at, updated_at)
            VALUES ('$icon', '$title', '$linkUrl', '$linkType', $status, $sortOrder, '$now', '$now')";
    
    if ($db->query($sql)) {
        // 获取插入的ID
        $id = $db->insert_id;
        // 查询插入的数据
        $sql = "SELECT * FROM home_nav_items WHERE id = $id";
        $result = $db->query($sql);
        $navItem = $result->fetch_assoc();
        
        sendResponse(0, '添加成功', $navItem);
    } else {
        sendResponse(500, '添加失败: ' . $db->error);
    }
}

/**
 * 更新导航项
 */
function updateHomeNavItem($db) {
    // 获取PUT数据
    $data = json_decode(file_get_contents('php://input'), true);
    
    // 验证数据
    if (!isset($data['id'])) {
        sendResponse(400, '缺少必要的ID参数');
    }
    
    $requiredFields = ['title', 'icon', 'link_url'];
    foreach ($requiredFields as $field) {
        if (!isset($data[$field]) || empty($data[$field])) {
            sendResponse(400, "缺少必要的字段: $field");
        }
    }
    
    // 准备数据
    $id = (int)$data['id'];
    $icon = $db->real_escape_string($data['icon']);
    $title = $db->real_escape_string($data['title']);
    $linkUrl = $db->real_escape_string($data['link_url']);
    $linkType = isset($data['link_type']) ? $db->real_escape_string($data['link_type']) : 'path';
    $status = isset($data['status']) ? (int)$data['status'] : 1;
    $sortOrder = isset($data['sort_order']) ? (int)$data['sort_order'] : 0;
    $now = date('Y-m-d H:i:s');
    
    // 检查该ID的导航项是否存在
    $checkSql = "SELECT COUNT(*) as count FROM home_nav_items WHERE id = $id";
    $result = $db->query($checkSql);
    $row = $result->fetch_assoc();
    
    if ($row['count'] == 0) {
        sendResponse(404, '未找到该导航项');
    }
    
    // 更新数据
    $sql = "UPDATE home_nav_items SET 
            icon = '$icon', 
            title = '$title', 
            link_url = '$linkUrl', 
            link_type = '$linkType', 
            status = $status, 
            sort_order = $sortOrder, 
            updated_at = '$now'
            WHERE id = $id";
    
    if ($db->query($sql)) {
        // 查询更新后的数据
        $sql = "SELECT * FROM home_nav_items WHERE id = $id";
        $result = $db->query($sql);
        $navItem = $result->fetch_assoc();
        
        sendResponse(0, '更新成功', $navItem);
    } else {
        sendResponse(500, '更新失败: ' . $db->error);
    }
}

/**
 * 删除导航项
 */
function deleteHomeNavItem($id, $db) {
    $id = (int)$id;
    
    // 检查该ID的导航项是否存在
    $checkSql = "SELECT COUNT(*) as count FROM home_nav_items WHERE id = $id";
    $result = $db->query($checkSql);
    $row = $result->fetch_assoc();
    
    if ($row['count'] == 0) {
        sendResponse(404, '未找到该导航项');
    }
    
    // 执行删除
    $sql = "DELETE FROM home_nav_items WHERE id = $id";
    
    if ($db->query($sql)) {
        sendResponse(0, '删除成功');
    } else {
        sendResponse(500, '删除失败: ' . $db->error);
    }
}

/**
 * 发送JSON响应
 * @param int $code 状态码
 * @param string $message 消息
 * @param mixed $data 数据
 */
function sendResponse($code, $message, $data = null) {
    $response = array(
        'code' => $code,
        'message' => $message
    );
    
    if ($data !== null) {
        $response['data'] = $data;
    }
    
    echo json_encode($response, JSON_UNESCAPED_UNICODE);
    exit;
}
?> 