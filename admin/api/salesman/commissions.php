<?php
/**
 * 业务员提成明细API
 * 提供业务员的提成明细数据
 */

// 设置HTTP响应头
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
header('Access-Control-Max-Age: 86400'); // 24小时

// 错误处理设置
ini_set('display_errors', 0);
ini_set('log_errors', 1);
ini_set('error_log', dirname(__DIR__) . '/debug_error.log');
error_reporting(E_ALL);

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 检查请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    echo json_encode([
        'code' => 1,
        'message' => '请求方法不允许',
        'data' => null
    ]);
    exit;
}

// 生成唯一请求ID
$request_id = uniqid();
error_log("[$request_id] 业务员提成明细请求开始");

// 引入Laravel的引导文件
require_once dirname(dirname(dirname(__DIR__))) . '/admin/vendor/autoload.php';
$app = require_once dirname(dirname(dirname(__DIR__))) . '/admin/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Http\Kernel::class);
$response = $kernel->handle(
    $request = Illuminate\Http\Request::capture()
);

// 获取请求中的认证Token
$authHeader = isset($_SERVER['HTTP_AUTHORIZATION']) ? $_SERVER['HTTP_AUTHORIZATION'] : '';
$token = '';

if (preg_match('/Bearer\s(\S+)/', $authHeader, $matches)) {
    $token = $matches[1];
}

// 如果请求头中没有token，尝试从cookie获取
if (empty($token) && isset($_COOKIE['token'])) {
    $token = $_COOKIE['token'];
}

// 如果还是没有token，尝试从查询参数获取
if (empty($token) && isset($_GET['token'])) {
    $token = $_GET['token'];
}

if (empty($token)) {
    http_response_code(401);
    echo json_encode([
        'code' => 1,
        'message' => '未授权访问',
        'data' => null
    ]);
    exit;
}

// 获取用户
try {
    // 尝试使用Laravel Sanctum验证令牌
    $tokenObj = \Laravel\Sanctum\PersonalAccessToken::findToken($token);
    
    if ($tokenObj) {
        $user = $tokenObj->tokenable;
    } else {
        // Sanctum验证失败，尝试JWT验证
        $tokenParts = explode('.', $token);
        if (count($tokenParts) === 3) {
            $payload = json_decode(base64_decode($tokenParts[1]), true);
            
            if (isset($payload['user_id'])) {
                $user = \App\Models\AppUser::find($payload['user_id']);
            }
        }
    }
    
    if (!isset($user) || !$user) {
        http_response_code(401);
        echo json_encode([
            'code' => 1,
            'message' => '无效的访问令牌',
            'data' => null
        ]);
        exit;
    }
} catch (\Exception $e) {
    http_response_code(500);
    echo json_encode([
        'code' => 1,
        'message' => '验证令牌时出错',
        'data' => null
    ]);
    exit;
}

// 检查用户是否为业务员
if (!$user->hasRole('sales')) {
    http_response_code(403);
    echo json_encode([
        'code' => 1,
        'message' => '您不是业务员，无权访问此资源',
        'data' => null
    ]);
    exit;
}

try {
    // 获取请求参数
    $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
    $pageSize = isset($_GET['pageSize']) ? (int)$_GET['pageSize'] : 10;
    $timeRange = isset($_GET['timeRange']) ? $_GET['timeRange'] : 'all';
    
    // 默认排序
    $orderBy = 'create_date';
    $orderDirection = 'desc';
    
    // 构建时间范围查询
    $query = [];
    switch ($timeRange) {
        case 'today':
            $query['start_date'] = date('Y-m-d');
            $query['end_date'] = date('Y-m-d');
            break;
        case 'week':
            $query['start_date'] = date('Y-m-d', strtotime('monday this week'));
            $query['end_date'] = date('Y-m-d', strtotime('sunday this week'));
            break;
        case 'month':
            $query['start_date'] = date('Y-m-01');
            $query['end_date'] = date('Y-m-t');
            break;
        case 'quarter':
            $month = date('n');
            $quarter = ceil($month / 3);
            $query['start_date'] = date('Y-' . (($quarter - 1) * 3 + 1) . '-01');
            $query['end_date'] = date('Y-m-t', strtotime($query['start_date'] . '+2 months'));
            break;
        case 'year':
            $query['start_date'] = date('Y-01-01');
            $query['end_date'] = date('Y-12-31');
            break;
    }
    
    // 获取业务员ID
    $salesman = $user->salesman;
    if (!$salesman) {
        // 如果没有业务员记录，创建一个
        $salesman = new \App\Models\Salesman();
        $salesman->user_id = $user->id;
        $salesman->employee_id = 'S' . str_pad($user->id, 6, '0', STR_PAD_LEFT);
        $salesman->title = '业务员';
        $salesman->status = 'active';
        $salesman->save();
    }
    
    // 获取所有设备
    $devicesQuery = \App\Models\TappDevice::where('app_user_id', $user->id)
        ->where('is_self_use', 0); // 排除自用设备
        
    // 如果有时间范围，添加条件
    if (isset($query['start_date'])) {
        $devicesQuery->whereDate('activate_date', '>=', $query['start_date']);
    }
    if (isset($query['end_date'])) {
        $devicesQuery->whereDate('activate_date', '<=', $query['end_date']);
    }
    
    // 获取设备数据
    $devices = $devicesQuery->orderBy('activate_date', 'desc')
        ->skip(($page - 1) * $pageSize)
        ->take($pageSize)
        ->get();
        
    // 设备总数
    $totalDevices = $devicesQuery->count();
    
    // 处理返回数据
    $list = [];
    foreach ($devices as $device) {
        // 获取设备最近一次充值信息
        $lastRechargeInfo = $device->getLastRechargeInfo();
        
        if ($lastRechargeInfo) {
            $commissionAmount = $device->calculateCommissionAmount();
            
            $list[] = [
                'id' => $device->id,
                'title' => '设备销售提成: ' . $device->device_number,
                'type' => 'income',
                'orderNo' => $lastRechargeInfo->order_number ?? '',
                'amount' => number_format($commissionAmount, 2, '.', ''),
                'customerName' => $device->client_name ?? '',
                'customerPhone' => $device->client_phone ?? '',
                'deviceNumber' => $device->device_number,
                'createTime' => $lastRechargeInfo->create_date,
                'status' => $lastRechargeInfo->order_status == '103' ? 'settled' : 'pending',
                'billMode' => $lastRechargeInfo->billing_mode,
                'billModeText' => $lastRechargeInfo->billing_mode == '1' ? '流量计费' : '包年计费',
                'orderAmount' => $lastRechargeInfo->money ?? 0,
                'surrogate' => $lastRechargeInfo->surrogate_type ?? '0',
                'surrogateText' => $lastRechargeInfo->surrogate_type == '1' ? '代充' : '自充',
            ];
        }
    }
    
    // 计算佣金汇总数据
    $summary = [
        'monthAmount' => '0.00',
        'pendingAmount' => '0.00',
        'totalAmount' => '0.00'
    ];
    
    // 查询本月提成总额
    $monthStart = date('Y-m-01');
    $monthEnd = date('Y-m-t');
    $monthDevices = \App\Models\TappDevice::where('app_user_id', $user->id)
        ->where('is_self_use', 0)
        ->whereDate('activate_date', '>=', $monthStart)
        ->whereDate('activate_date', '<=', $monthEnd)
        ->get();
        
    $monthAmount = 0;
    foreach ($monthDevices as $device) {
        $rechargeInfo = $device->getLastRechargeInfo();
        if ($rechargeInfo && $rechargeInfo->order_status == '103') {
            $monthAmount += $device->calculateCommissionAmount();
        }
    }
    $summary['monthAmount'] = number_format($monthAmount, 2, '.', '');
    
    // 查询所有提成总额
    $allDevices = \App\Models\TappDevice::where('app_user_id', $user->id)
        ->where('is_self_use', 0)
        ->get();
        
    $totalAmount = 0;
    foreach ($allDevices as $device) {
        $rechargeInfo = $device->getLastRechargeInfo();
        if ($rechargeInfo && $rechargeInfo->order_status == '103') {
            $totalAmount += $device->calculateCommissionAmount();
        }
    }
    $summary['totalAmount'] = number_format($totalAmount, 2, '.', '');
    
    // 待结算金额与本月相同，因为每月结算一次
    $summary['pendingAmount'] = $summary['monthAmount'];
    
    // 返回成功响应
    echo json_encode([
        'code' => 0,
        'message' => '获取提成明细成功',
        'data' => [
            'list' => $list,
            'total' => $totalDevices,
            'page' => $page,
            'pageSize' => $pageSize,
            'summary' => $summary
        ]
    ]);
} catch (\Exception $e) {
    error_log("[$request_id] 业务员提成明细数据获取失败: " . $e->getMessage());
    
    http_response_code(500);
    echo json_encode([
        'code' => 1,
        'message' => '获取提成明细失败: ' . $e->getMessage(),
        'data' => null
    ]);
} 