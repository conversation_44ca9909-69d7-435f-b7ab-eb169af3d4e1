<?php
/**
 * 获取业务员的客户列表
 * API: /api/salesman/get_clients.php
 * 
 * 请求参数:
 * - token: JWT token (header或query参数)
 * 
 * 返回数据:
 * - clients: 客户列表，包含客户基本信息和设备信息
 */

// 使用绝对路径引入配置文件
require_once __DIR__ . '/../config.php';

// 设置允许跨域
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization");
header("Content-Type: application/json");

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// 获取token（直接从请求头或参数中获取）
$token = null;
if (isset($_SERVER['HTTP_AUTHORIZATION'])) {
    $auth = $_SERVER['HTTP_AUTHORIZATION'];
    if (strpos($auth, 'Bearer ') === 0) {
        $token = substr($auth, 7);
    }
} elseif (isset($_GET['token'])) {
    $token = $_GET['token'];
}

if (!$token) {
    echo json_encode([
        'code' => 401,
        'message' => "未授权访问",
        'data' => null
    ]);
    exit;
}

// 使用全局配置的数据库连接
$conn = new mysqli(
    $DB_CONFIG['HOST'],
    $DB_CONFIG['USER'],
    $DB_CONFIG['PASSWORD'],
    $DB_CONFIG['DATABASE'],
    $DB_CONFIG['PORT']
);

if ($conn->connect_error) {
    echo json_encode([
        'code' => 500,
        'message' => "数据库连接失败: " . $conn->connect_error,
        'data' => null
    ]);
    exit;
}

// 设置字符集
$conn->set_charset($DB_CONFIG['CHARSET']);

try {
    // 计算滤芯寿命百分比的辅助函数
    function calculateFluxPercent($flux, $fluxMax) {
        // 确保转换为数值类型
        $flux = (float)$flux;
        $fluxMax = (float)$fluxMax;
        
        // 防止除以零
        if ($fluxMax <= 0) {
            return 0;
        }
        
        // 计算百分比并限制在0-100之间
        $percent = ($flux / $fluxMax) * 100;
        return min(max(round($percent, 1), 0), 100);
    }
    
    // 从token中获取用户ID - 使用auth_tokens表
    $stmt = $conn->prepare("SELECT user_id FROM auth_tokens WHERE token = ? LIMIT 1");
    if (!$stmt) {
        throw new Exception("查询token失败: " . $conn->error);
    }
    
    $stmt->bind_param("s", $token);
    $stmt->execute();
    $token_result = $stmt->get_result();
    
    if ($token_result->num_rows === 0) {
        // 旧方案：直接使用token作为用户ID
        $userId = $token;
        error_log("找不到对应token记录，直接使用token作为用户ID: " . $userId);
    } else {
        $token_row = $token_result->fetch_assoc();
        $userId = $token_row['user_id'];
        error_log("从auth_tokens表找到用户ID: " . $userId);
    }
    $stmt->close();
    
    // 为测试目的，记录当前处理的用户ID
    error_log("处理业务员客户查询请求，用户ID: " . $userId);
    
    // 业务员角色验证 - 使用app_users表中的is_salesman字段
    $stmt = $conn->prepare("SELECT id, is_salesman FROM app_users WHERE id = ?");
    $stmt->bind_param("s", $userId);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows === 0) {
        echo json_encode([
            'code' => 0,
            'message' => "获取成功",
            'data' => [
                'clients' => []
            ]
        ]);
        $stmt->close();
        $conn->close();
        exit;
    }
    
    $user = $result->fetch_assoc();
    if ($user['is_salesman'] != 1) {
        echo json_encode([
            'code' => 0,
            'message' => "获取成功",
            'data' => [
                'clients' => []
            ]
        ]);
        $stmt->close();
        $conn->close();
        exit;
    }
    $stmt->close();

    // 直接查询当前用户可以查看的设备记录
    // 查找所有以当前用户ID作为app_user_id关联的设备
    $query = "
        SELECT 
            td.*,
            td.activate_date,
            td.billing_mode,
            td.raw_water_value,
            td.purification_water_value, 
            td.surplus_flow,
            td.remaining_days,
            td.cumulative_filtration_flow,
            td.network_status
        FROM 
            tapp_devices td
        WHERE 
            td.app_user_id = ?
        ORDER BY 
            td.created_at DESC
    ";
    
    $stmt = $conn->prepare($query);
    if (!$stmt) {
        throw new Exception("准备查询设备语句失败: " . $conn->error);
    }
    
    $stmt->bind_param("s", $userId);
    $stmt->execute();
    $result = $stmt->get_result();
    
    $devices = [];
    while ($row = $result->fetch_assoc()) {
        $devices[] = $row;
    }
    $stmt->close();
    
    // 如果没有设备，返回空数组
    if (empty($devices)) {
        echo json_encode([
            'code' => 0,
            'message' => "获取成功",
            'data' => [
                'clients' => []
            ]
        ]);
        $conn->close();
        exit;
    }
    
    // 按客户信息（client_phone）组织设备数据
    $clients = [];
    $clientDevices = [];
    
    foreach ($devices as $device) {
        $clientPhone = isset($device['client_phone']) && !empty($device['client_phone']) ? $device['client_phone'] : '未知';
        $clientName = isset($device['client_name']) && !empty($device['client_name']) ? $device['client_name'] : '未知客户';
        $clientKey = $clientPhone . '_' . $clientName;
        
        // 添加到设备列表
        if (!isset($clientDevices[$clientKey])) {
            $clientDevices[$clientKey] = [];
        }
        
        // 确保所有字段都有默认值，避免NULL值导致的错误
        $clientDevices[$clientKey][] = [
            'id' => $device['id'] ?? '',
            'number' => $device['device_number'] ?? '',
            'name' => !empty($device['device_name']) ? $device['device_name'] : ($device['device_number'] ?? ''),
            'address' => !empty($device['address']) ? $device['address'] : ($device['client_address'] ?? ''),
            'activated_at' => $device['activate_date'] ?? '',
            'status' => $device['status'] ?? 'unknown',
            'f1_flux' => (float)($device['f1_flux'] ?? 0),
            'f1_flux_max' => (float)($device['f1_flux_max'] ?? 100),
            'f1_percent' => calculateFluxPercent($device['f1_flux'] ?? 0, $device['f1_flux_max'] ?? 100),
            'f2_flux' => (float)($device['f2_flux'] ?? 0),
            'f2_flux_max' => (float)($device['f2_flux_max'] ?? 100),
            'f2_percent' => calculateFluxPercent($device['f2_flux'] ?? 0, $device['f2_flux_max'] ?? 100),
            'f3_flux' => (float)($device['f3_flux'] ?? 0),
            'f3_flux_max' => (float)($device['f3_flux_max'] ?? 100),
            'f3_percent' => calculateFluxPercent($device['f3_flux'] ?? 0, $device['f3_flux_max'] ?? 100),
            'billing_mode' => (int)($device['billing_mode'] ?? 0),
            'raw_water_value' => (int)($device['raw_water_value'] ?? 0),
            'purification_water_value' => (int)($device['purification_water_value'] ?? 0),
            'surplus_flow' => (float)($device['surplus_flow'] ?? 0),
            'remaining_days' => (int)($device['remaining_days'] ?? 0),
            'cumulative_filtration_flow' => (float)($device['cumulative_filtration_flow'] ?? 0),
            'network_status' => (int)($device['network_status'] ?? 0),
            'client_name' => $clientName,
            'client_phone' => $clientPhone
        ];
        
        // 如果是新客户，添加到客户列表
        if (!isset($clients[$clientKey])) {
            $clients[$clientKey] = [
                'id' => $clientKey, // 使用组合key作为客户ID
                'name' => $clientName,
                'phone' => $clientPhone,
                'created_at' => $device['activate_date'] ?? $device['created_at'] ?? date('Y-m-d H:i:s')
            ];
        }
    }
    
    // 构建最终返回的客户数据（包含设备）
    $result = [];
    foreach ($clients as $clientKey => $client) {
        $clientData = [
            'id' => $client['id'],
            'name' => $client['name'],
            'phone' => $client['phone'],
            'created_at' => $client['created_at']
        ];
        
        // 确保devices字段始终是数组
        $clientData['devices'] = isset($clientDevices[$clientKey]) ? $clientDevices[$clientKey] : [];
        
        $result[] = $clientData;
    }

    // 返回数据 - 确保所有字段都有值
    echo json_encode([
        'code' => 0,
        'message' => "获取成功",
        'data' => [
            'clients' => $result
        ]
    ], JSON_NUMERIC_CHECK);

} catch (Exception $e) {
    error_log("业务员客户查询错误: " . $e->getMessage());
    echo json_encode([
        'code' => 500,
        'message' => "服务器内部错误: " . $e->getMessage(),
        'data' => null
    ]);
} finally {
    if (isset($conn) && $conn) {
        $conn->close();
    }
} 