<?php
/**
 * 向数据库中插入安装预约的种子数据（简化版）
 */

// 引入数据库连接文件
// // 使用统一的数据库连接文件
require_once __DIR__ . '/../../db.php';

require_once __DIR__ . '/../db_connect.php'; /* 已注释 */

// 获取数据库连接
$conn = get_db_connection();
if (!$conn) {
    die("数据库连接失败");
}

// 创建日志目录
$logDir = __DIR__ . '/../logs';
if (!is_dir($logDir)) {
    mkdir($logDir, 0755, true);
}

// 写入日志文件
$logFile = $logDir . '/seed_data_' . date('Y-m-d') . '.log';
file_put_contents($logFile, date('Y-m-d H:i:s') . " - 开始插入种子数据\n", FILE_APPEND);

// 检查是否已有数据
$checkDataSql = "SELECT COUNT(*) as count FROM install_bookings";
$result = mysqli_query($conn, $checkDataSql);
$row = mysqli_fetch_assoc($result);

if ($row['count'] > 0) {
    file_put_contents($logFile, date('Y-m-d H:i:s') . " - 表中已有数据，跳过插入\n", FILE_APPEND);
    echo "表中已有数据，跳过插入";
    exit;
}

// 准备种子数据 SQL
$seedDataSql = "
INSERT INTO `install_bookings` (
    `booking_no`, `user_id`, `contact_name`, `contact_phone`, `install_address`,
    `install_time`, `package_type`, `package_price`, `installation_fee`, `total_amount`,
    `status`, `payment_status`, `remarks`, `created_at`, `updated_at`
) VALUES
('BK20250401001', 20, '张三', '***********', '北京市朝阳区建国路88号',
 '2025-04-10 09:00:00', 'personal', 199.00, 120.00, 319.00,
 'pending', 'unpaid', '客户希望尽快安装', '2025-04-01 10:30:00', '2025-04-01 10:30:00'),

('BK20250401002', 21, '李四', '***********', '上海市浦东新区陆家嘴金融中心',
 '2025-04-11 14:00:00', 'business_year', 599.00, 120.00, 719.00,
 'confirmed', 'paid', '客户要求安装人员提前联系', '2025-04-01 11:20:00', '2025-04-01 14:30:00'),

('BK20250402001', 32, '王五', '***********', '广州市天河区珠江新城',
 '2025-04-12 09:00:00', 'personal', 199.00, 120.00, 319.00,
 'assigned', 'paid', '客户家里有宠物，请工程师注意', '2025-04-02 09:15:00', '2025-04-02 10:30:00'),

('BK20250402002', 34, '赵六', '***********', '深圳市南山区科技园',
 '2025-04-13 14:00:00', 'business_flow', 799.00, 120.00, 919.00,
 'completed', 'paid', '安装顺利完成，客户很满意', '2025-04-02 13:45:00', '2025-04-03 15:30:00'),

('BK20250403001', 35, '孙七', '***********', '成都市锦江区春熙路',
 '2025-04-14 09:00:00', 'personal', 199.00, 120.00, 319.00,
 'cancelled', 'unpaid', '客户表示稍后会重新预约', '2025-04-03 10:20:00', '2025-04-03 14:15:00'),

('BK20250403002', 36, '周八', '***********', '重庆市渝中区解放碑',
 '2025-04-15 14:00:00', 'unlimited', 999.00, 120.00, 1119.00,
 'in_progress', 'paid', '工程师已到达现场，正在安装中', '2025-04-03 16:30:00', '2025-04-04 09:45:00')
";

// 执行插入
if (mysqli_query($conn, $seedDataSql)) {
    file_put_contents($logFile, date('Y-m-d H:i:s') . " - 成功插入种子数据\n", FILE_APPEND);
    echo "成功插入种子数据";
} else {
    $error = mysqli_error($conn);
    file_put_contents($logFile, date('Y-m-d H:i:s') . " - 插入数据失败: $error\n", FILE_APPEND);
    die("插入数据失败: $error");
}

// 关闭数据库连接
mysqli_close($conn);
