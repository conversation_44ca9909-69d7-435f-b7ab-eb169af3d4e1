<?php
/**
 * 管理员后台 - 获取安装预约详情
 * API路径: /admin/api/installation/admin_get_booking_details.php
 * 请求方式: GET
 *
 * 参数:
 * - id: 预约ID
 *
 * 返回:
 * - code: 0表示成功，非0表示失败
 * - message: 提示信息
 * - data: 预约详情数据
 */

// 设置允许跨域请求
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization");

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    header("HTTP/1.1 200 OK");
    exit;
}

// 引入数据库连接文件
// // 使用统一的数据库连接文件
require_once __DIR__ . '/../../db.php';

require_once __DIR__ . '/../db_connect.php'; /* 已注释 */

// 记录访问日志
error_log("访问安装预约详情API: " . json_encode($_GET));

// 获取预约ID
$id = isset($_GET['id']) ? intval($_GET['id']) : 0;
if ($id <= 0) {
    send_json_response(400, '无效的预约ID');
}

// 获取数据库连接
$conn = get_db_connection();
if (!$conn) {
    send_json_response(500, '数据库连接失败');
}

try {
    // 查询预约详情
    $sql = "SELECT b.*, e.name as engineer_name
            FROM install_bookings b
            LEFT JOIN installation_engineers e ON b.engineer_id = e.id
            WHERE b.id = ?";

    $stmt = mysqli_prepare($conn, $sql);
    mysqli_stmt_bind_param($stmt, 'i', $id);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);

    if ($row = mysqli_fetch_assoc($result)) {
        // 添加状态文本
        $statusMap = [
            'pending' => '待处理',
            'confirmed' => '已确认',
            'assigned' => '已分配',
            'in_progress' => '进行中',
            'completed' => '已完成',
            'cancelled' => '已取消'
        ];

        $row['status_text'] = $statusMap[$row['status']] ?? $row['status'];

        // 返回数据
        send_json_response(0, '获取预约详情成功', $row);
    } else {
        send_json_response(404, '预约不存在');
    }

} catch (Exception $e) {
    error_log("获取预约详情失败: " . $e->getMessage());
    send_json_response(500, '获取预约详情失败: ' . $e->getMessage());
} finally {
    mysqli_close($conn);
}


