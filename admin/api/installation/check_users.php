<?php
/**
 * 检查用户表
 */

// 引入数据库连接文件
// // 使用统一的数据库连接文件
require_once __DIR__ . '/../../db.php';

require_once __DIR__ . '/../db_connect.php'; /* 已注释 */

// 获取数据库连接
$conn = get_db_connection();
if (!$conn) {
    die("数据库连接失败");
}

// 查询用户表
$sql = "SELECT id, name, phone FROM app_users LIMIT 10";
$result = mysqli_query($conn, $sql);

if (!$result) {
    die("查询失败: " . mysqli_error($conn));
}

echo "app_users 表数据:\n";
echo "-----------------------------\n";
echo "ID\t名称\t电话\n";
echo "-----------------------------\n";

while ($row = mysqli_fetch_assoc($result)) {
    echo $row['id'] . "\t" . $row['name'] . "\t" . $row['phone'] . "\n";
}

// 关闭数据库连接
mysqli_close($conn);
