<?php
/**
 * 创建净水器安装预约支付订单
 * API路径: /admin/api/installation/create_payment.php
 * 请求方式: POST
 *
 * 参数:
 * - booking_id: 预约ID
 * - payment_method: 支付方式，默认 wechat
 * - openid: 用户微信openid
 * - include_package: 是否包含套餐费用 (1-包含，0-不包含)
 * - calculated_amount: 前端计算的总金额 (可选，用于校验)
 * - token: 用户登录令牌
 *
 * 返回:
 * - code: 0表示成功，非0表示失败
 * - message: 提示信息
 * - data: 包含订单号和支付参数
 */

// 引入必要的文件
require_once __DIR__ . '/../db.php';
require_once __DIR__ . '/../functions/auth.php';
require_once __DIR__ . '/../functions/logger.php';
require_once __DIR__ . '/../functions.php';

// 定义日志函数
function payment_debug_log($message, $data = [], $type = 'info') {
    $logFile = __DIR__ . '/../logs/installation/payment_' . date('Y-m-d') . '.log';
    $timeStr = date('Y-m-d H:i:s');
    $dataStr = is_array($data) ? json_encode($data, JSON_UNESCAPED_UNICODE) : $data;
    $logMessage = "[{$timeStr}] [{$type}] {$message}: {$dataStr}\n";
    file_put_contents($logFile, $logMessage, FILE_APPEND);
    }

    // 验证用户登录状态
$user = validateToken();
        if (!$user) {
    responseError(401, '未登录或登录已过期');
    }

    // 验证必填参数
if (!isset($_POST['booking_id']) || empty($_POST['booking_id'])) {
    responseError(400, '缺少必要参数: booking_id');
}

if (!isset($_POST['openid']) || empty($_POST['openid'])) {
    responseError(400, '缺少必要参数: openid');
}

// 获取参数
$bookingId = $_POST['booking_id'];
$openid = $_POST['openid'];
$paymentMethod = $_POST['payment_method'] ?? 'wechat';
$includePackage = isset($_POST['include_package']) ? (int)$_POST['include_package'] : 1;
$calculatedAmount = isset($_POST['calculated_amount']) ? (float)$_POST['calculated_amount'] : 0;

// 记录日志
payment_debug_log('创建支付订单请求', [
    'user_id' => $user['id'],
        'booking_id' => $bookingId,
        'payment_method' => $paymentMethod,
    'openid' => $openid,
        'include_package' => $includePackage,
        'calculated_amount' => $calculatedAmount
    ]);

try {
    // 连接数据库
    $conn = get_db_connection();
    
    if (!$conn) {
        payment_debug_log('数据库连接失败', [], 'error');
        responseError(500, '数据库连接失败，请稍后重试');
    }

            // 查询预约信息
    $stmt = $conn->prepare("SELECT * FROM install_bookings WHERE id = ? AND user_id = ?");
    $stmt->bind_param("ii", $bookingId, $user['id']);
            $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows === 0) {
                // 检查该预约是否存在但不属于该用户
        $checkStmt = $conn->prepare("SELECT * FROM install_bookings WHERE id = ?");
        $checkStmt->bind_param("i", $bookingId);
                $checkStmt->execute();
        $checkResult = $checkStmt->get_result();
        $checkBooking = $checkResult->fetch_assoc();

                if (!$checkBooking) {
            payment_debug_log('预约不存在', ['booking_id' => $bookingId], 'error');
            responseError(404, '预约不存在');
                } else if ($checkBooking['payment_status'] === 'paid') {
            payment_debug_log('预约已支付', ['booking_id' => $bookingId], 'warning');
            responseError(400, '该预约已支付，无需重复支付');
                } else {
            payment_debug_log('预约不属于当前用户', [
                'booking_id' => $bookingId,
                'booking_user_id' => $checkBooking['user_id'],
                'current_user_id' => $user['id']
            ], 'error');
            responseError(403, '您无权访问该预约');
            }
    }

    $booking = $result->fetch_assoc();
    payment_debug_log('获取到预约信息', $booking);

    // 检查支付状态
    if ($booking['payment_status'] === 'paid') {
        payment_debug_log('预约已支付', ['booking_id' => $bookingId], 'warning');
        responseError(400, '该预约已支付，无需重复支付');
                }

    // 计算支付金额
    $installationFee = (float)$booking['installation_fee'];
    $packagePrice = (float)$booking['package_price'];
    $totalAmount = $installationFee;

    if ($includePackage) {
        $totalAmount += $packagePrice;
                        }

    // 校验前端计算的金额
    if ($calculatedAmount > 0 && abs($calculatedAmount - $totalAmount) > 0.01) {
        payment_debug_log('金额校验失败', [
            'frontend_amount' => $calculatedAmount,
            'backend_amount' => $totalAmount
        ], 'warning');
        // 使用后端计算的金额，但记录差异
    }

    // 生成订单号
    $orderNo = 'INS' . date('YmdHis') . rand(1000, 9999);

    // 更新预约订单号和金额
    $stmt = $conn->prepare("UPDATE install_bookings SET booking_no = ?, total_amount = ? WHERE id = ?");
    $stmt->bind_param("sdi", $orderNo, $totalAmount, $bookingId);
    $stmt->execute();

    payment_debug_log('更新预约订单号和金额', [
        'booking_id' => $bookingId,
        'order_no' => $orderNo,
        'total_amount' => $totalAmount
    ]);

    // 创建微信支付参数
    $paymentParams = createWechatPayment($orderNo, $booking, $openid, $totalAmount);

    // 返回成功信息
    responseSuccess('创建支付订单成功', [
                'order_no' => $orderNo,
        'total_amount' => $totalAmount,
                'payment_params' => $paymentParams
            ]);
} catch (Exception $e) {
    payment_debug_log('创建支付订单失败', ['error' => $e->getMessage()], 'error');
    responseError(500, '创建支付订单失败: ' . $e->getMessage());
}

/**
 * 创建微信支付参数
 *
 * @param string $orderNo 订单号
 * @param array $booking 预约信息
 * @param string $openid 用户微信openid
 * @param float $amount 支付金额
 * @return array 支付参数
 */
function createWechatPayment($orderNo, $booking, $openid, $amount) {
    global $WECHAT_CONFIG;
    
    // 微信支付参数
    $appid = $WECHAT_CONFIG['APP_ID'];
    $mchId = $WECHAT_CONFIG['PAYMENT']['MCH_ID'];
    $key = $WECHAT_CONFIG['PAYMENT']['KEY'];
    $notifyUrl = $WECHAT_CONFIG['PAYMENT']['NOTIFY_URL'];

    // 订单金额（单位：分）
    $totalFee = intval($amount * 100);

    // 订单描述
    $body = '净水器安装预约-' . $booking['package_type'];

    // 附加数据
    $attach = json_encode([
        'booking_id' => $booking['id'],
        'type' => 'installation'
    ]);

    // 构建请求参数
    $params = [
        'appid' => $appid,
        'mch_id' => $mchId,
        'nonce_str' => generateNonceStr(),
        'body' => $body,
        'out_trade_no' => $orderNo,
        'total_fee' => $totalFee,
        'spbill_create_ip' => $_SERVER['REMOTE_ADDR'],
        'notify_url' => $notifyUrl,
        'trade_type' => 'JSAPI',
        'openid' => $openid,
        'attach' => $attach
    ];

    // 记录签名前的参数和key
    payment_debug_log('签名前的参数', $params);
    payment_debug_log('使用的key', $key);
    
    // 生成签名
    $params['sign'] = generateWxPaySign($params, $key);
    
    // 记录生成的签名
    payment_debug_log('生成的签名', $params['sign']);

    // 将参数转为XML
    $xml = arrayToXml($params);

    // 记录请求参数
    payment_debug_log('微信支付统一下单请求参数', $params);
    payment_debug_log('微信支付统一下单请求XML', $xml);

    // 发送请求到微信支付接口
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'https://api.mch.weixin.qq.com/pay/unifiedorder');
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $xml);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);

    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

    if ($error = curl_error($ch)) {
        curl_close($ch);
        payment_debug_log('微信支付请求失败', ['error' => $error]);
        throw new Exception('微信支付请求失败: ' . $error);
    }

    curl_close($ch);

    // 记录响应
    payment_debug_log('微信支付统一下单响应', ['http_code' => $httpCode, 'response' => $response]);

    // 解析返回的XML
    $result = xmlToArray($response);
    payment_debug_log('微信支付统一下单解析结果', $result);

    if ($result['return_code'] === 'SUCCESS' && $result['result_code'] === 'SUCCESS') {
        // 构建JSAPI支付参数
        $timeStamp = (string)time();
        $nonceStr = generateNonceStr();
        $package = 'prepay_id=' . $result['prepay_id'];
        $signType = 'MD5';

        $payParams = [
            'appId' => $appid,
            'timeStamp' => $timeStamp,
            'nonceStr' => $nonceStr,
            'package' => $package,
            'signType' => $signType
        ];

        $payParams['paySign'] = generateWxPaySign($payParams, $key);

        return $payParams;
    } else {
        throw new Exception($result['return_msg'] ?? '微信支付统一下单失败');
    }
}

/**
 * 生成随机字符串
 *
 * @param int $length 长度
 * @return string 随机字符串
 */
function getRandomString($length = 32) {
    $chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    $str = '';
    for ($i = 0; $i < $length; $i++) {
        $str .= $chars[mt_rand(0, strlen($chars) - 1)];
    }
    return $str;
}

/**
 * 生成随机字符串
 *
 * @param int $length 字符串长度
 * @return string 随机字符串
 */
function generateNonceStr($length = 32) {
    $chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    $str = '';
    for ($i = 0; $i < $length; $i++) {
        $str .= substr($chars, mt_rand(0, strlen($chars) - 1), 1);
    }
    return $str;
}

/**
 * 生成微信支付签名
 *
 * @param array $params 参数数组
 * @param string $key 商户密钥
 * @return string 签名
 */
function generateWxPaySign($params, $key) {
    // 按照键名对参数进行排序
    ksort($params);

    // 构建签名字符串
    $stringA = '';
    foreach ($params as $k => $v) {
        if ($k != 'sign' && $v !== '' && !is_null($v)) {
            $stringA .= "{$k}={$v}&";
        }
    }
    $stringA .= "key={$key}";
    
    // 记录签名字符串
    payment_debug_log('签名字符串', $stringA);

    // MD5加密并转为大写
    $sign = strtoupper(md5($stringA));
    payment_debug_log('MD5签名结果', $sign);
    
    return $sign;
}

/**
 * 将数组转为XML
 *
 * @param array $arr 数组
 * @return string XML字符串
 */
function arrayToXml($arr) {
    $xml = "<xml>";
    foreach ($arr as $key => $val) {
        if (is_numeric($val)) {
            $xml .= "<{$key}>{$val}</{$key}>";
        } else {
            $xml .= "<{$key}><![CDATA[{$val}]]></{$key}>";
        }
    }
    $xml .= "</xml>";
    return $xml;
}

/**
 * 将XML转为数组
 *
 * @param string $xml XML字符串
 * @return array 数组
 */
function xmlToArray($xml) {
    // 禁用libxml错误显示
    libxml_use_internal_errors(true);

    // 解析XML
    $obj = simplexml_load_string($xml, 'SimpleXMLElement', LIBXML_NOCDATA);

    // 转为JSON再转为数组
    $json = json_encode($obj);
    return json_decode($json, true);
}

/**
 * 返回成功响应
 */
function responseSuccess($message, $data = []) {
    header('Content-Type: application/json');
    echo json_encode([
        'code' => 0,
        'message' => $message,
        'data' => $data
    ]);
    exit;
}

/**
 * 返回错误响应
 */
function responseError($code, $message) {
    header('Content-Type: application/json');
    echo json_encode([
        'code' => $code,
        'message' => $message
    ]);
    exit;
}
