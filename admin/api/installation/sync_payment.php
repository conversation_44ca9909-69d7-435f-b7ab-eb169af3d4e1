<?php
/**
 * 同步净水器安装预约支付状态
 * API路径: /admin/api/installation/sync_payment.php
 * 请求方式: POST
 *
 * 参数:
 * - booking_id: 预约ID
 * - order_no: 订单号
 * - token: 用户登录令牌
 *
 * 返回:
 * - code: 0表示成功，非0表示失败
 * - message: 提示信息
 * - data: 预约支付状态
 */

// 引入必要的文件
require_once __DIR__ . '/../../db.php';
require_once __DIR__ . '/../functions/auth.php';
require_once __DIR__ . '/../functions/logger.php';
require_once __DIR__ . '/../../api/functions.php';

// 验证用户登录状态
$user = validateToken();
if (!$user) {
    responseError(401, '未登录或登录已过期');
}

// 验证必填参数
if (!isset($_POST['booking_id']) || empty($_POST['booking_id'])) {
    responseError(400, '缺少必要参数: booking_id');
}

if (!isset($_POST['order_no']) || empty($_POST['order_no'])) {
    responseError(400, '缺少必要参数: order_no');
}

// 获取参数
$bookingId = $_POST['booking_id'];
$orderNo = $_POST['order_no'];

// 记录API调用日志
log_installation_api('sync_payment', [
    'user_id' => $user['id'],
    'booking_id' => $bookingId,
    'order_no' => $orderNo
]);

try {
    // 连接数据库
    $conn = getConnection();

    // 查询预约信息
    $stmt = $conn->prepare("SELECT * FROM install_bookings WHERE id = ? AND user_id = ?");
    $stmt->bind_param("ii", $bookingId, $user['id']);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows === 0) {
        responseError(404, '预约不存在或无权访问');
    }

    $booking = $result->fetch_assoc();

    // 检查订单号是否匹配
    if ($booking['booking_no'] !== $orderNo) {
        responseError(400, '订单号不匹配');
    }

    // 查询支付状态 (在实际生产环境中，应该调用微信支付API查询订单状态)
    // 这里模拟查询，返回支付成功
    $isPaid = true;

    if ($isPaid) {
        // 更新支付状态为已支付
        $stmt = $conn->prepare("UPDATE install_bookings SET payment_status = 'paid', payment_time = NOW(), status = 'confirmed', updated_at = NOW() WHERE id = ?");
        $stmt->bind_param("i", $bookingId);
        $stmt->execute();

        log_installation_api('sync_payment_success', [
            'user_id' => $user['id'],
            'booking_id' => $bookingId,
            'order_no' => $orderNo
        ]);

        responseSuccess('支付状态同步成功', [
            'payment_status' => 'paid',
            'status' => 'confirmed'
        ]);
    } else {
        // 返回当前支付状态
        responseSuccess('支付未完成', [
            'payment_status' => $booking['payment_status'],
            'status' => $booking['status']
        ]);
    }
} catch (Exception $e) {
    log_installation_api('sync_payment_error', [
        'user_id' => $user['id'],
        'booking_id' => $bookingId,
        'error' => $e->getMessage()
    ], LOG_ERROR);

    responseError(500, '同步支付状态失败: ' . $e->getMessage());
}

/**
 * 返回成功响应
 */
function responseSuccess($message, $data = []) {
    header('Content-Type: application/json');
    echo json_encode([
        'code' => 0,
        'message' => $message,
        'data' => $data
    ]);
    exit;
}

/**
 * 返回错误响应
 */
function responseError($code, $message) {
    header('Content-Type: application/json');
    echo json_encode([
        'code' => $code,
        'message' => $message
    ]);
    exit;
} 