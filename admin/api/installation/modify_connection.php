<?php
// 修改create_booking.php文件，通过直接读写方式
$file = __DIR__ . '/create_booking.php';
$content = file_get_contents($file);

// 查找需要替换的代码块
$searchPattern = '$bookingNo = \'INS\' . date(\'YmdHis\') . rand(1000, 9999);';
$connectionCode = <<<EOD
// 直接使用config.php中的配置进行数据库连接
file_put_contents(__DIR__ . '/../logs/debug.log', "\\n[调试] 直接使用config.php而不是functions.php中的get_db_connection\\n", FILE_APPEND);
require_once __DIR__ . '/../../api/config.php';

// 直接使用\$DB_CONFIG连接数据库
if (!isset(\$DB_CONFIG) || !is_array(\$DB_CONFIG)) {
    file_put_contents(__DIR__ . '/../logs/debug.log', "\\n[异常] config.php中没有定义\$DB_CONFIG或不是数组\\n", FILE_APPEND);
    responseError(500, '数据库配置错误，请联系管理员');
}

\$db = new mysqli(
    \$DB_CONFIG['HOST'],
    \$DB_CONFIG['USER'],
    \$DB_CONFIG['PASSWORD'],
    \$DB_CONFIG['DATABASE'],
    \$DB_CONFIG['PORT']
);

file_put_contents(__DIR__ . '/../logs/debug.log', "\\n[调试] \$db 类型: " . gettype(\$db) . ", is_object: " . (is_object(\$db) ? 'yes' : 'no') . ", connect_errno: " . (is_object(\$db) ? \$db->connect_errno : 'N/A') . ", db内容: " . var_export(\$db, true) . "\\n", FILE_APPEND);

if (\$db->connect_error) {
    file_put_contents(__DIR__ . '/../logs/debug.log', "\\n[异常] 数据库连接失败(直接使用config): " . \$db->connect_error . "\\n", FILE_APPEND);
    responseError(500, '数据库连接失败，请联系管理员');
}

\$db->set_charset(\$DB_CONFIG['CHARSET']);
file_put_contents(__DIR__ . '/../logs/debug.log', "\\n[调试] 数据库连接成功(直接使用config)\\n", FILE_APPEND);
EOD;

// 查找原有的get_db_connection调用代码
$oldConnectionPattern = 'require_once __DIR__ . \'/../../api/functions/db.php\';
$db = get_db_connection();';

// 如果找到旧的连接代码，则替换
if (strpos($content, $oldConnectionPattern) !== false) {
    $content = str_replace($oldConnectionPattern, '', $content);
    echo "已移除旧的数据库连接代码\n";
}

// 在预约单号后面添加数据库连接代码
$replaceWith = "$searchPattern\n\n$connectionCode";
$content = str_replace($searchPattern, $replaceWith, $content);

// 保存修改后的文件
file_put_contents($file, $content);
echo "文件已成功修改\n";

// 查看文件内容确认修改
$updatedContent = file_get_contents($file);
if (strpos($updatedContent, 'config.php') !== false) {
    echo "确认修改成功：文件中包含config.php\n";
} else {
    echo "修改可能未成功：文件中不包含config.php\n";
} 