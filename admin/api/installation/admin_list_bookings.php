<?php
/**
 * 管理员后台 - 获取安装预约列表
 * API路径: /admin/api/installation/admin_list_bookings.php
 * 请求方式: GET
 *
 * 参数:
 * - page: 页码，默认1
 * - limit: 每页记录数，默认20
 * - keyword: 搜索关键词(可选)，搜索联系人、电话、地址
 * - status: 预约状态筛选(可选)
 * - start_date: 开始日期(可选)
 * - end_date: 结束日期(可选)
 *
 * 返回:
 * - code: 0表示成功，非0表示失败
 * - message: 提示信息
 * - data: 包含预约列表和分页信息的对象
 */

// 设置允许跨域请求
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization");

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    header("HTTP/1.1 200 OK");
    exit;
}

// 引入数据库连接文件
// // 使用统一的数据库连接文件
require_once __DIR__ . '/../../db.php';

require_once __DIR__ . '/../db_connect.php'; /* 已注释 */

// 记录访问日志
error_log("访问安装预约列表API: " . json_encode($_GET));

// 获取查询参数
$page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
$limit = isset($_GET['limit']) ? min(100, max(1, intval($_GET['limit']))) : 20;
$keyword = isset($_GET['keyword']) ? trim($_GET['keyword']) : '';
$status = isset($_GET['status']) ? trim($_GET['status']) : '';
$start_date = isset($_GET['start_date']) ? trim($_GET['start_date']) : '';
$end_date = isset($_GET['end_date']) ? trim($_GET['end_date']) : '';
$offset = ($page - 1) * $limit;

// 获取数据库连接
$conn = get_db_connection();
if (!$conn) {
    send_json_response(500, '数据库连接失败');
}

try {
    // 构建查询条件
    $where = "WHERE 1=1";
    $params = [];

    if (!empty($keyword)) {
        $where .= " AND (contact_name LIKE ? OR contact_phone LIKE ? OR address LIKE ?)";
        $keyword = "%$keyword%";
        $params[] = $keyword;
        $params[] = $keyword;
        $params[] = $keyword;
    }

    if (!empty($status)) {
        $where .= " AND status = ?";
        $params[] = $status;
    }

    if (!empty($start_date)) {
        $where .= " AND booking_date >= ?";
        $params[] = $start_date;
    }

    if (!empty($end_date)) {
        $where .= " AND booking_date <= ?";
        $params[] = $end_date;
    }

    // 查询总记录数
    $countSql = "SELECT COUNT(*) as total FROM install_bookings $where";
    $countStmt = mysqli_prepare($conn, $countSql);

    if (!empty($params)) {
        $types = str_repeat('s', count($params));
        mysqli_stmt_bind_param($countStmt, $types, ...$params);
    }

    mysqli_stmt_execute($countStmt);
    $countResult = mysqli_stmt_get_result($countStmt);
    $totalRow = mysqli_fetch_assoc($countResult);
    $total = $totalRow['total'];

    // 查询数据列表
    $sql = "SELECT b.*, e.name as engineer_name
            FROM install_bookings b
            LEFT JOIN installation_engineers e ON b.engineer_id = e.id
            $where
            ORDER BY b.id DESC
            LIMIT ?, ?";

    $stmt = mysqli_prepare($conn, $sql);

    // 添加分页参数
    $params[] = $offset;
    $params[] = $limit;

    $types = str_repeat('s', count($params) - 2) . 'ii';
    mysqli_stmt_bind_param($stmt, $types, ...$params);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);

    $bookings = [];
    while ($row = mysqli_fetch_assoc($result)) {
        // 添加状态文本
        $statusMap = [
            'pending' => '待处理',
            'confirmed' => '已确认',
            'assigned' => '已分配',
            'in_progress' => '进行中',
            'completed' => '已完成',
            'cancelled' => '已取消'
        ];

        $row['status_text'] = $statusMap[$row['status']] ?? $row['status'];
        $bookings[] = $row;
    }

    // 返回数据
    $data = [
        'list' => $bookings,
        'pagination' => [
            'total' => (int)$total,
            'page' => $page,
            'limit' => $limit,
            'pages' => ceil($total / $limit)
        ]
    ];

    send_json_response(0, '获取预约列表成功', $data);

} catch (Exception $e) {
    error_log("获取预约列表失败: " . $e->getMessage());
    send_json_response(500, '获取预约列表失败: ' . $e->getMessage());
} finally {
    mysqli_close($conn);
}


