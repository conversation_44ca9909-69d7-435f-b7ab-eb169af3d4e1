<?php
/**
 * 创建净水器安装预约
 * API路径: /admin/api/installation/create_booking.php
 * 请求方式: POST
 *
 * 参数:
 * - token: 用户登录令牌
 * - name: 联系人姓名
 * - phone: 联系电话
 * - address: 安装地址
 * - install_time: 安装时间 (格式: YYYY-MM-DD HH:MM:SS)
 * - package_type: 套餐类型 (personal-个人, unlimited-无限, business_year-商务年, business_flow-商务流量)
 * - remarks: 备注信息 (可选)
 * - referrer_id: 推荐人ID (可选)
 *
 * 返回:
 * - code: 0表示成功，非0表示失败
 * - message: 提示信息
 * - data: 预约信息，包含booking_id
 */

// 引入必要的文件
require_once __DIR__ . '/../functions/db.php';
require_once __DIR__ . '/../functions/auth.php';
require_once __DIR__ . '/../functions/logger.php';
require_once __DIR__ . '/../../api/functions.php';

// 处理JSON输入
$input = file_get_contents('php://input');
if (!empty($input)) {
    $jsonData = json_decode($input, true);
    if ($jsonData) {
        $_POST = array_merge($_POST, $jsonData);
    }
}

// 调试信息
error_log('POST数据: ' . print_r($_POST, true));

// 验证用户登录状态
$user = validateToken();
if (!$user) {
    responseError(401, '未登录或登录已过期');
}

// 验证必填参数
$requiredParams = ['install_time', 'package_type'];
foreach ($requiredParams as $param) {
    if (!isset($_POST[$param]) || empty($_POST[$param])) {
        responseError(400, '缺少必要参数: ' . $param);
    }
}

// 检查联系人信息 - 支持新旧参数名
if ((!isset($_POST['contact_name']) || empty($_POST['contact_name'])) && 
    (!isset($_POST['name']) || empty($_POST['name']))) {
    responseError(400, '缺少必要参数: 联系人姓名');
}

if ((!isset($_POST['contact_phone']) || empty($_POST['contact_phone'])) && 
    (!isset($_POST['phone']) || empty($_POST['phone']))) {
    responseError(400, '缺少必要参数: 联系电话');
}

if ((!isset($_POST['install_address']) || empty($_POST['install_address'])) && 
    (!isset($_POST['address']) || empty($_POST['address']))) {
    responseError(400, '缺少必要参数: 安装地址');
}

// 获取参数 - 优先使用新参数名
$name = isset($_POST['contact_name']) && !empty($_POST['contact_name']) ? 
    $_POST['contact_name'] : $_POST['name'];
$phone = isset($_POST['contact_phone']) && !empty($_POST['contact_phone']) ? 
    $_POST['contact_phone'] : $_POST['phone'];
$address = isset($_POST['install_address']) && !empty($_POST['install_address']) ? 
    $_POST['install_address'] : $_POST['address'];
$installTime = $_POST['install_time'];
$packageType = $_POST['package_type'];
$remarks = $_POST['remarks'] ?? '';
$referrerId = isset($_POST['referrer_id']) && !empty($_POST['referrer_id']) ? $_POST['referrer_id'] : null;

// 记录API调用日志
log_installation_api('create_booking', [
    'user_id' => $user['id'],
    'package_type' => $packageType,
    'install_time' => $installTime
]);

try {
    // 连接数据库
    $conn = get_db_connection();

    // 生成预约单号
    $bookingNo = 'BK' . date('YmdHis') . rand(1000, 9999);

    // 设置套餐价格
    $packagePrice = 0;
    $installationFee = 120.00; // 默认安装费

    switch ($packageType) {
        case 'personal':
            $packagePrice = 980.00;
            break;
        case 'unlimited':
            $packagePrice = 1200.00;
            break;
        case 'business_year':
            $packagePrice = 2400.00;
            break;
        case 'business_flow':
            $packagePrice = 3700.00;
            break;
        default:
            $packagePrice = 980.00;
    }

    // 计算总金额
    $totalAmount = $packagePrice + $installationFee;

    // 保存预约信息
    $stmt = $conn->prepare("
        INSERT INTO install_bookings (
            booking_no, user_id, referrer_id, package_type, package_price,
            installation_fee, total_amount, contact_name, contact_phone,
            install_address, install_time, remarks, status, payment_status,
            created_at, updated_at
        ) VALUES (
            ?, ?, ?, ?, ?,
            ?, ?, ?, ?,
            ?, ?, ?, 'pending', 'unpaid',
            NOW(), NOW()
        )
    ");

    $stmt->bind_param(
        "sissddssssss",
        $bookingNo, $user['id'], $referrerId, $packageType, $packagePrice,
        $installationFee, $totalAmount, $name, $phone,
        $address, $installTime, $remarks
    );

    $stmt->execute();

    if ($stmt->affected_rows <= 0) {
        throw new Exception("保存预约信息失败");
    }

    $bookingId = $stmt->insert_id;

    // 记录日志
    log_installation_api('create_booking_success', [
        'user_id' => $user['id'],
        'booking_id' => $bookingId,
        'booking_no' => $bookingNo
    ]);

    // 关闭数据库连接
    $conn->close();

    // 返回成功信息
    header('Content-Type: application/json');
    echo json_encode([
        'code' => 0,
        'message' => '创建预约成功',
        'data' => [
            'booking_id' => $bookingId,
            'booking_no' => $bookingNo,
            'total_amount' => $totalAmount,
            'package_price' => $packagePrice,
            'installation_fee' => $installationFee
        ]
    ], JSON_UNESCAPED_UNICODE);
    exit;
} catch (Exception $e) {
    // 记录错误日志
    log_installation_api('create_booking_error', [
        'user_id' => $user['id'],
        'error' => $e->getMessage()
    ], LOG_ERROR);

    header('Content-Type: application/json');
    echo json_encode([
        'code' => 500,
        'message' => '创建预约失败: ' . $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
    exit;
}