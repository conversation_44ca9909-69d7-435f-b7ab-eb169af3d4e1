<?php
/**
 * 支付宝支付同步返回处理
 * 
 * 处理支付宝支付完成后的同步跳转
 * 验证返回参数的合法性
 * 将用户引导回应用页面
 */

// 设置时区
date_default_timezone_set('Asia/Shanghai');

// 开启输出缓冲
ob_start();

// 设置错误处理
ini_set('display_errors', 0);
error_reporting(E_ALL);

// 日志函数
function return_log($message, $data = [], $type = 'info') {
    $logDir = __DIR__ . '/../logs/installation/';
    
    if (!is_dir($logDir)) {
        mkdir($logDir, 0755, true);
    }
    
    $logFile = $logDir . 'alipay_return_' . date('Y-m-d') . '.log';
    $log = [
        'time' => date('Y-m-d H:i:s'),
        'type' => $type,
        'message' => $message,
        'data' => $data
    ];
    
    file_put_contents($logFile, json_encode($log, JSON_UNESCAPED_UNICODE) . "\n", FILE_APPEND);
}

// 设置响应头
header('Content-Type: text/html; charset=utf-8');

// 记录返回参数
return_log('收到支付宝同步返回', $_GET);

// 加载必要的函数库
$functionsFile = __DIR__ . '/../functions.php';
if (file_exists($functionsFile)) {
    require_once($functionsFile);
} else {
    return_log('核心函数库文件不存在', [], 'error');
    echo "加载核心文件失败，请联系管理员";
    exit;
}

// 支付宝配置
$alipayConfig = [
    // 应用ID
    'app_id' => '2021000000000000', // 需要替换为真实的应用ID
    // 商户私钥
    'merchant_private_key' => '请替换为真实的商户私钥',
    // 支付宝公钥
    'alipay_public_key' => '请替换为真实的支付宝公钥',
    // 编码
    'charset' => 'UTF-8',
    // 签名方式
    'sign_type' => 'RSA2',
    // 支付宝网关
    'gateway_url' => 'https://openapi.alipaydev.com/gateway.do',
];

// 验证签名函数
function verifyAlipaySign($params, $alipayPublicKey) {
    // 移除sign和sign_type参数
    $sign = $params['sign'] ?? '';
    unset($params['sign']);
    unset($params['sign_type']);
    
    // 排序并构建签名字符串
    ksort($params);
    $signStr = '';
    foreach ($params as $key => $value) {
        if ($value !== '' && $value !== null) {
            $signStr .= $key . '=' . $value . '&';
        }
    }
    $signStr = rtrim($signStr, '&');
    
    // 此处应使用支付宝公钥验证签名
    // $result = openssl_verify($signStr, base64_decode($sign), $alipayPublicKey, OPENSSL_ALGO_SHA256);
    // return $result === 1;
    
    // 简化示例，始终返回true
    return true;
}

// 检查返回数据
if (empty($_GET)) {
    return_log('返回数据为空', [], 'error');
    $redirectUrl = 'https://pay.itapgo.com/Tapp/app-vue/dist/#/installation/booking/error?message=' . urlencode('支付返回数据为空');
    header('Location: ' . $redirectUrl);
    exit;
}

// 获取返回数据
$outTradeNo = $_GET['out_trade_no'] ?? ''; // 商户订单号
$tradeNo = $_GET['trade_no'] ?? ''; // 支付宝交易号
$tradeStatus = $_GET['trade_status'] ?? ''; // 交易状态

return_log('解析返回参数', [
    'out_trade_no' => $outTradeNo,
    'trade_no' => $tradeNo,
    'trade_status' => $tradeStatus
]);

// 验证签名
if (!empty($_GET['sign']) && !verifyAlipaySign($_GET, $alipayConfig['alipay_public_key'])) {
    return_log('签名验证失败', $_GET, 'error');
    $redirectUrl = 'https://pay.itapgo.com/Tapp/app-vue/dist/#/installation/booking/error?message=' . urlencode('支付签名验证失败');
    header('Location: ' . $redirectUrl);
    exit;
}

// 验证交易状态
if ($tradeStatus !== 'TRADE_SUCCESS' && $tradeStatus !== 'TRADE_FINISHED') {
    return_log('支付未成功', ['trade_status' => $tradeStatus], 'warning');
    $redirectUrl = 'https://pay.itapgo.com/Tapp/app-vue/dist/#/installation/booking/error?message=' . urlencode('支付未完成');
    header('Location: ' . $redirectUrl);
    exit;
}

// 查询订单信息
try {
    $db = getDbConnection('app');
    
    $query = "SELECT b.* FROM installation_payment_orders o 
              JOIN installation_bookings b ON o.booking_id = b.id
              WHERE o.order_no = :order_no";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':order_no', $outTradeNo);
    $stmt->execute();
    $booking = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$booking) {
        return_log('未找到相关预约', ['order_no' => $outTradeNo], 'error');
        $redirectUrl = 'https://pay.itapgo.com/Tapp/app-vue/dist/#/installation/booking/error?message=' . urlencode('未找到相关预约');
        header('Location: ' . $redirectUrl);
        exit;
    }
    
    return_log('查询预约信息成功', $booking);
    
    // 构建成功跳转URL
    $redirectUrl = 'https://pay.itapgo.com/Tapp/app-vue/dist/#/installation/booking/success?id=' . $booking['id'];
    
    // 重定向到成功页面
    return_log('重定向到成功页面', ['url' => $redirectUrl]);
    header('Location: ' . $redirectUrl);
    exit;
    
} catch (Exception $e) {
    return_log('查询订单信息失败', ['error' => $e->getMessage()], 'error');
    $redirectUrl = 'https://pay.itapgo.com/Tapp/app-vue/dist/#/installation/booking/error?message=' . urlencode('系统错误，请联系客服');
    header('Location: ' . $redirectUrl);
    exit;
} 