<?php
/**
 * 获取安装预约列表或可用时间段
 * API路径: /api/installation/get_bookings.php
 * 请求方式: GET
 * 
 * 参数:
 * - status: 可选，预约状态 (pending, confirmed, completed, cancelled)
 * - page: 可选，页码，默认1
 * - limit: 可选，每页数量，默认10
 * - type: 可选，查询类型，当值为available_slots时返回可用时间段
 * 
 * 返回:
 * - code: 0表示成功，非0表示失败
 * - message: 提示信息
 * - data: 预约列表或可用时间段
 */

// 使用统一的数据库连接文件
require_once __DIR__ . '/../db.php';
require_once __DIR__ . '/../functions/auth.php';
require_once __DIR__ . '/../functions/logger.php';
require_once __DIR__ . '/../../api/functions.php';

// 验证用户登录状态
$user = validateToken();
if (!$user) {
    responseError(401, '未登录或登录已过期');
}

// 获取查询类型
$queryType = isset($_GET['type']) ? $_GET['type'] : 'list';

// 如果是获取可用时间段
if ($queryType === 'available_slots') {
    // 获取未来7天的可用时间段，每天上午和下午各一个时间段
    $availableSlots = [];
    $currentDate = date('Y-m-d');
    
    // 生成未来7天的可用时间
    for ($i = 1; $i <= 7; $i++) {
        $date = date('Y-m-d', strtotime("$currentDate +$i days"));
        $dayOfWeek = date('N', strtotime($date)); // 1-7，表示周一到周日
        
        // 周末只提供上午时间段
        if ($dayOfWeek >= 6) { // 周六和周日
            $availableSlots[] = [
                'date' => $date,
                'time' => '10:00-12:00',
                'label' => date('m月d日', strtotime($date)) . ' (上午)',
                'value' => $date . ' 10:00:00',
                'is_weekend' => true
            ];
        } else {
            // 工作日提供上午和下午时间段
            $availableSlots[] = [
                'date' => $date,
                'time' => '10:00-12:00',
                'label' => date('m月d日', strtotime($date)) . ' (上午)',
                'value' => $date . ' 10:00:00',
                'is_weekend' => false
            ];
            $availableSlots[] = [
                'date' => $date,
                'time' => '14:00-17:00',
                'label' => date('m月d日', strtotime($date)) . ' (下午)',
                'value' => $date . ' 14:00:00',
                'is_weekend' => false
            ];
        }
    }
    
    // 返回可用时间段
    responseSuccess($availableSlots);
    exit;
}

// 获取输入参数
$status = isset($_GET['status']) ? $_GET['status'] : null;
$page = isset($_GET['page']) ? intval($_GET['page']) : 1;
$limit = isset($_GET['limit']) ? intval($_GET['limit']) : 10;

// 验证参数
if ($page < 1) {
    $page = 1;
}
if ($limit < 1 || $limit > 50) {
    $limit = 10;
}

// 计算偏移量
$offset = ($page - 1) * $limit;

// 获取数据库连接
$db = get_db_connection();

try {
    // 构建查询条件
    $conditions = ["b.user_id = ?"];
    $params = [$user['id']];
    $types = "i";
    
    if ($status) {
        $conditions[] = "b.status = ?";
        $params[] = $status;
        $types .= "s";
    }
    
    $whereClause = implode(" AND ", $conditions);
    
    // 查询总数
    $countSql = "SELECT COUNT(*) as total FROM install_bookings b WHERE $whereClause";
    $stmt = $db->prepare($countSql);
    
    if (!empty($params)) {
        $types = str_repeat('s', count($params));
        $stmt->bind_param($types, ...$params);
    }
    
    $stmt->execute();
    $result = $stmt->get_result();
    $row = $result->fetch_assoc();
    $total = intval($row['total']);
    
    // 查询预约列表
    $sql = "SELECT b.*, 
                  e.name as engineer_name, 
                  e.phone as engineer_phone
           FROM install_bookings b
           LEFT JOIN installation_engineers e ON b.engineer_id = e.id
           WHERE $whereClause
           ORDER BY b.created_at DESC
           LIMIT $offset, $limit";
    
    $stmt = $db->prepare($sql);
    
    if (!empty($params)) {
        $types = str_repeat('s', count($params));
        $stmt->bind_param($types, ...$params);
    }
    
    $stmt->execute();
    $result = $stmt->get_result();
    $bookings = $result->fetch_all(MYSQLI_ASSOC);
    
    // 格式化预约列表
    foreach ($bookings as &$booking) {
        $booking['id'] = intval($booking['id']);
        $booking['user_id'] = intval($booking['user_id']);
        if (isset($booking['total_amount'])) {
        $booking['total_amount'] = floatval($booking['total_amount']);
        }
        
        // 添加状态文本
        $booking['status_text'] = getStatusText($booking['status'] ?? 'pending');
        
        // 添加套餐类型文本
        if (isset($booking['package_type'])) {
        $booking['package_type_text'] = getPackageTypeName($booking['package_type']);
        }
    }
    
    // 返回成功响应
    responseSuccess($bookings, [
        'total' => $total,
        'page' => $page,
        'limit' => $limit
    ]);
} catch (Exception $e) {
    // 记录错误日志
    error_log("获取预约列表失败: " . $e->getMessage());
    
    // 返回错误响应
    responseError(500, '获取预约列表失败: ' . $e->getMessage());
}

/**
 * 获取状态文本
 * @param string $status 状态代码
 * @return string 状态文本
 */
function getStatusText($status) {
    $statusMap = [
        'pending' => '待处理',
        'confirmed' => '已确认',
        'completed' => '已完成',
        'cancelled' => '已取消'
    ];
    
    return $statusMap[$status] ?? '未知状态';
}

/**
 * 获取套餐类型名称
 * @param string $packageType 套餐类型代码
 * @return string 套餐类型名称
 */
function getPackageTypeName($packageType) {
    $packageNames = [
        'personal' => '个人/企业套餐',
        'unlimited' => '无限畅饮套餐',
        'business_year' => '商务机包年套餐',
        'business_flow' => '商务机流量套餐'
    ];
    
    return $packageNames[$packageType] ?? '未知套餐';
}
