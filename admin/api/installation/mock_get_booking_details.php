<?php
/**
 * 管理员后台 - 获取安装预约详情（模拟数据）
 * API路径: /admin/api/installation/mock_get_booking_details.php
 * 请求方式: GET
 */

// 设置允许跨域请求
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization");
header('Content-Type: application/json');

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    header("HTTP/1.1 200 OK");
    exit;
}

// 获取预约ID
$id = isset($_GET['id']) ? intval($_GET['id']) : 0;

// 模拟数据
$mockData = [
    1 => [
        'id' => 1,
        'booking_no' => 'BK20250401001',
        'contact_name' => '张三',
        'contact_phone' => '13800138000',
        'address' => '北京市朝阳区建国路88号',
        'booking_date' => '2025-04-10',
        'time_slot' => '上午 9:00-12:00',
        'device_type' => '家用净水器',
        'status' => 'pending',
        'status_text' => '待处理',
        'created_at' => '2025-04-01 10:30:00',
        'updated_at' => '2025-04-01 10:30:00',
        'remark' => '客户希望尽快安装'
    ],
    2 => [
        'id' => 2,
        'booking_no' => 'BK20250401002',
        'contact_name' => '李四',
        'contact_phone' => '13900139000',
        'address' => '上海市浦东新区陆家嘴金融中心',
        'booking_date' => '2025-04-11',
        'time_slot' => '下午 14:00-17:00',
        'device_type' => '商用净水系统',
        'status' => 'confirmed',
        'status_text' => '已确认',
        'created_at' => '2025-04-01 11:20:00',
        'updated_at' => '2025-04-01 14:30:00',
        'remark' => '客户要求安装人员提前联系'
    ],
    3 => [
        'id' => 3,
        'booking_no' => 'BK20250402001',
        'contact_name' => '王五',
        'contact_phone' => '13700137000',
        'address' => '广州市天河区珠江新城',
        'booking_date' => '2025-04-12',
        'time_slot' => '上午 9:00-12:00',
        'device_type' => '家用净水器',
        'status' => 'assigned',
        'status_text' => '已分配',
        'engineer_id' => 1,
        'engineer_name' => '张工程师',
        'created_at' => '2025-04-02 09:15:00',
        'updated_at' => '2025-04-02 10:30:00',
        'remark' => '客户家里有宠物，请工程师注意'
    ],
    4 => [
        'id' => 4,
        'booking_no' => 'BK20250402002',
        'contact_name' => '赵六',
        'contact_phone' => '13600136000',
        'address' => '深圳市南山区科技园',
        'booking_date' => '2025-04-13',
        'time_slot' => '下午 14:00-17:00',
        'device_type' => '商用净水系统',
        'status' => 'completed',
        'status_text' => '已完成',
        'engineer_id' => 2,
        'engineer_name' => '李工程师',
        'created_at' => '2025-04-02 13:45:00',
        'updated_at' => '2025-04-03 15:30:00',
        'remark' => '安装顺利完成，客户很满意'
    ],
    5 => [
        'id' => 5,
        'booking_no' => 'BK20250403001',
        'contact_name' => '孙七',
        'contact_phone' => '13500135000',
        'address' => '成都市锦江区春熙路',
        'booking_date' => '2025-04-14',
        'time_slot' => '上午 9:00-12:00',
        'device_type' => '家用净水器',
        'status' => 'cancelled',
        'status_text' => '已取消',
        'cancel_reason' => '客户临时有事，无法安排安装',
        'created_at' => '2025-04-03 10:20:00',
        'updated_at' => '2025-04-03 14:15:00',
        'remark' => '客户表示稍后会重新预约'
    ],
    6 => [
        'id' => 6,
        'booking_no' => 'BK20250403002',
        'contact_name' => '周八',
        'contact_phone' => '13400134000',
        'address' => '重庆市渝中区解放碑',
        'booking_date' => '2025-04-15',
        'time_slot' => '下午 14:00-17:00',
        'device_type' => '商用净水系统',
        'status' => 'in_progress',
        'status_text' => '进行中',
        'engineer_id' => 3,
        'engineer_name' => '王工程师',
        'created_at' => '2025-04-03 16:30:00',
        'updated_at' => '2025-04-04 09:45:00',
        'remark' => '工程师已到达现场，正在安装中'
    ]
];

// 检查ID是否存在
if (isset($mockData[$id])) {
    $response = [
        'code' => 0,
        'message' => '获取预约详情成功',
        'data' => $mockData[$id]
    ];
} else {
    $response = [
        'code' => 404,
        'message' => '预约不存在',
        'data' => null
    ];
}

echo json_encode($response, JSON_UNESCAPED_UNICODE);
exit;
