<?php
/**
 * 更新安装预约支付状态
 * API路径: /api/installation/update_payment_status.php
 * 请求方式: POST
 * 
 * 参数:
 * - booking_id: 预约ID
 * - status: 支付状态 (paid, unpaid)
 * - payment_method: 支付方式 (wechat, alipay)
 * - transaction_id: 交易ID（可选）
 * 
 * 返回:
 * - code: 0表示成功，非0表示失败
 * - message: 提示信息
 * - data: 更新后的预约信息
 */

// 添加错误日志记录
function debug_log($message, $data = null) {
    $logDir = __DIR__ . '/../logs';
    if (!is_dir($logDir)) {
        mkdir($logDir, 0755, true);
    }
    
    $logFile = $logDir . '/payment_status_' . date('Y-m-d') . '.log';
    $logMessage = '[' . date('Y-m-d H:i:s') . '] ' . $message;
    
    if ($data !== null) {
        if (is_array($data) || is_object($data)) {
            $logMessage .= ' - ' . json_encode($data, JSON_UNESCAPED_UNICODE);
        } else {
            $logMessage .= ' - ' . $data;
        }
    }
    
    file_put_contents($logFile, $logMessage . "\n", FILE_APPEND);
}

// 开启错误报告
ini_set('display_errors', 1);
error_reporting(E_ALL);

// 设置CORS头，允许跨域请求
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Origin, X-Requested-With, Content-Type, Accept, Authorization');
header('Access-Control-Allow-Credentials: true');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 记录API开始执行
debug_log('更新支付状态API开始执行');
debug_log('请求方法: ' . $_SERVER['REQUEST_METHOD']);
debug_log('请求头部: ' . json_encode(getallheaders()));
debug_log('POST数据: ' . json_encode($_POST));

// 引入必要文件
// // 使用统一的数据库连接文件
require_once __DIR__ . '/../../db.php';

require_once __DIR__ . '/../../api/functions.php'; /* 已注释 */

// 添加错误处理函数
function exception_error_handler($severity, $message, $file, $line) {
    debug_log("PHP错误: [$severity] $message in $file:$line");
    return true;
}
set_error_handler("exception_error_handler");

try {
    // 验证用户登录状态
    $user = validateToken();
    if (!$user) {
        debug_log('验证Token失败，用户未登录');
        responseError(401, '未登录或登录已过期');
    }

    debug_log('用户Token验证成功', $user);

    // 验证必填参数
    $requiredParams = ['booking_id', 'status'];
    $missingParams = [];
    foreach ($requiredParams as $param) {
        if (!isset($_POST[$param]) || empty($_POST[$param])) {
            $missingParams[] = $param;
        }
    }

    if (!empty($missingParams)) {
        debug_log('缺少必要参数', $missingParams);
        responseError(400, "缺少必要参数: " . implode(', ', $missingParams));
    }

    // 获取输入参数
    $bookingId = intval($_POST['booking_id']);
    $status = $_POST['status'];
    $paymentMethod = $_POST['payment_method'] ?? null;
    $transactionId = $_POST['transaction_id'] ?? null;

    debug_log('更新支付状态请求参数', [
        'booking_id' => $bookingId,
        'status' => $status,
        'payment_method' => $paymentMethod,
        'transaction_id' => $transactionId
    ]);

    // 验证支付状态
    $validStatuses = ['paid', 'unpaid'];
    if (!in_array($status, $validStatuses)) {
        debug_log('无效的支付状态', $status);
        responseError(400, '无效的支付状态');
    }

    // 获取数据库连接
    $db = getDbConnection();
    if (!$db) {
        debug_log('数据库连接失败');
        responseError(500, '数据库连接失败，请稍后重试');
    }

    // 开始数据库事务
    $db->beginTransaction();

    try {
        // 查询预约信息
        $sql = "SELECT * FROM install_bookings WHERE id = ? AND user_id = ?";
        $stmt = $db->prepare($sql);
        $stmt->bindParam(1, $bookingId, PDO::PARAM_INT);
        $stmt->bindParam(2, $user['id'], PDO::PARAM_INT);
        $stmt->execute();
        
        $booking = $stmt->fetch(PDO::FETCH_ASSOC);
        if (!$booking) {
            debug_log('未找到匹配的预约', ['booking_id' => $bookingId, 'user_id' => $user['id']]);
            $db->rollBack();
            responseError(404, '预约不存在或无权访问');
        }
        
        debug_log('成功获取预约信息', $booking);
        
        // 检查当前支付状态
        if ($booking['payment_status'] === $status) {
            debug_log('预约支付状态已经是' . $status, $booking);
            $db->commit();
            responseSuccess(['booking_id' => $bookingId, 'payment_status' => $status]);
            return;
        }
        
        // 更新支付状态
        $updateFields = [
            'payment_status' => $status,
            'updated_at' => date('Y-m-d H:i:s')
        ];
        
        if ($status === 'paid') {
            $updateFields['payment_time'] = date('Y-m-d H:i:s');
            
            if ($paymentMethod) {
                $updateFields['payment_method'] = $paymentMethod;
            }
            
            if ($transactionId) {
                $updateFields['transaction_id'] = $transactionId;
            }
        }
        
        // 构建SQL更新语句
        $updateSql = "UPDATE install_bookings SET ";
        $updateParams = [];
        
        foreach ($updateFields as $field => $value) {
            $updateSql .= "{$field} = ?, ";
            $updateParams[] = $value;
        }
        
        $updateSql = rtrim($updateSql, ', ');
        $updateSql .= " WHERE id = ?";
        $updateParams[] = $bookingId;
        
        $stmt = $db->prepare($updateSql);
        $result = $stmt->execute($updateParams);
        
        if (!$result) {
            debug_log('更新支付状态失败', ['error' => $stmt->errorInfo()]);
            $db->rollBack();
            responseError(500, '更新支付状态失败');
        }
        
        debug_log('成功更新预约支付状态', [
            'booking_id' => $bookingId,
            'old_status' => $booking['payment_status'],
            'new_status' => $status
        ]);
        
        // 如果有关联订单，也更新订单状态
        if (!empty($booking['order_id'])) {
            $orderId = $booking['order_id'];
            
            // 查询订单信息
            $orderSql = "SELECT * FROM orders WHERE id = ?";
            $orderStmt = $db->prepare($orderSql);
            $orderStmt->bindParam(1, $orderId, PDO::PARAM_INT);
            $orderStmt->execute();
            
            $order = $orderStmt->fetch(PDO::FETCH_ASSOC);
            if ($order) {
                debug_log('找到关联订单', $order);
                
                // 更新订单状态
                $updateOrderSql = "UPDATE orders SET 
                    status = ?, 
                    payment_status = ?, 
                    payment_time = ?, 
                    updated_at = ?";
                
                $updateOrderParams = [
                    $status === 'paid' ? 'completed' : 'pending',
                    $status === 'paid' ? 'paid' : 'unpaid',
                    $status === 'paid' ? date('Y-m-d H:i:s') : null,
                    date('Y-m-d H:i:s')
                ];
                
                if ($paymentMethod) {
                    $updateOrderSql .= ", payment_method = ?";
                    $updateOrderParams[] = $paymentMethod;
                }
                
                if ($transactionId) {
                    $updateOrderSql .= ", transaction_id = ?";
                    $updateOrderParams[] = $transactionId;
                }
                
                $updateOrderSql .= " WHERE id = ?";
                $updateOrderParams[] = $orderId;
                
                $orderUpdateStmt = $db->prepare($updateOrderSql);
                $orderUpdateResult = $orderUpdateStmt->execute($updateOrderParams);
                
                if (!$orderUpdateResult) {
                    debug_log('更新订单状态失败', ['error' => $orderUpdateStmt->errorInfo()]);
                    // 不回滚事务，继续处理
                }
                
                debug_log('成功更新订单状态', [
                    'order_id' => $orderId,
                    'new_status' => $status === 'paid' ? 'completed' : 'pending'
                ]);
            }
        }
        
        // 提交事务
        $db->commit();
        
        // 查询更新后的预约信息
        $sql = "SELECT * FROM install_bookings WHERE id = ?";
        $stmt = $db->prepare($sql);
        $stmt->bindParam(1, $bookingId, PDO::PARAM_INT);
        $stmt->execute();
        
        $updatedBooking = $stmt->fetch(PDO::FETCH_ASSOC);
        
        // 返回成功响应
        responseSuccess([
            'booking_id' => $bookingId,
            'payment_status' => $updatedBooking['payment_status'],
            'payment_time' => $updatedBooking['payment_time'],
            'payment_method' => $updatedBooking['payment_method']
        ]);
    } catch (Exception $e) {
        // 回滚事务
        $db->rollBack();
        
        // 记录错误日志
        debug_log("更新支付状态失败: " . $e->getMessage(), ['trace' => $e->getTraceAsString()]);
        error_log("更新支付状态失败: " . $e->getMessage());
        
        // 返回错误响应
        responseError(500, '更新支付状态失败: ' . $e->getMessage());
    }
} catch (Exception $e) {
    // 记录错误日志
    debug_log("更新支付状态异常: " . $e->getMessage(), ['trace' => $e->getTraceAsString()]);
    error_log("更新支付状态异常: " . $e->getMessage());
    
    // 返回错误响应
    responseError(500, '更新支付状态异常: ' . $e->getMessage());
} 