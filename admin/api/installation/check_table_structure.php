<?php
/**
 * 检查表结构
 */

// 引入数据库连接文件
// // 使用统一的数据库连接文件
require_once __DIR__ . '/../../db.php';

require_once __DIR__ . '/../db_connect.php'; /* 已注释 */

// 获取数据库连接
$conn = get_db_connection();
if (!$conn) {
    die("数据库连接失败");
}

// 检查表结构
$sql = "DESCRIBE install_bookings";
$result = mysqli_query($conn, $sql);

if (!$result) {
    die("查询失败: " . mysqli_error($conn));
}

echo "install_bookings 表结构:\n";
echo "-----------------------------\n";
echo "字段名\t\t类型\t\t可为空\t键\t默认值\t额外\n";
echo "-----------------------------\n";

while ($row = mysqli_fetch_assoc($result)) {
    echo $row['Field'] . "\t\t" . $row['Type'] . "\t\t" . $row['Null'] . "\t" . $row['Key'] . "\t" . $row['Default'] . "\t" . $row['Extra'] . "\n";
}

// 关闭数据库连接
mysqli_close($conn);
