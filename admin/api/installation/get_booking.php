<?php
/**
 * 获取安装预约详情
 * API路径: /api/installation/get_booking.php
 * 请求方式: GET
 * 
 * 参数:
 * - id: 预约ID
 * - public: 是否公开访问(1=是，0=否)，公开访问不需要验证登录
 * 
 * 返回:
 * - code: 0表示成功，非0表示失败
 * - message: 提示信息
 * - data: 预约详情
 */

require_once __DIR__ . '/../functions/db.php';
require_once __DIR__ . '/../functions/auth.php';
require_once __DIR__ . '/../functions/response.php';

// 如果响应函数不存在，定义它们
if (!function_exists('responseSuccess')) {
    function responseSuccess($data = null, $message = 'success') {
        header('Content-Type: application/json; charset=utf-8');
        echo json_encode([
            'code' => 0,
            'message' => $message,
            'data' => $data
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
}

if (!function_exists('responseError')) {
    function responseError($code, $message = 'error', $data = null) {
        header('Content-Type: application/json; charset=utf-8');
        echo json_encode([
            'code' => $code,
            'message' => $message,
            'data' => $data
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
}

// 检查是否公开访问
$isPublic = isset($_GET['public']) && $_GET['public'] == '1';

// 如果不是公开访问，则验证用户身份
$user = null;
if (!$isPublic) {
    // 获取header中的token
    $headers = getallheaders();
    $token = '';
    if (isset($headers['Authorization'])) {
        $authHeader = $headers['Authorization'];
        if (stripos($authHeader, 'Bearer ') === 0) {
            $token = substr($authHeader, 7);
        }
    }

    // 验证用户身份
    $user = verify_auth($token);
    if (!$user) {
        responseError(401, '未登录或登录已过期');
    }
}

// 验证必填参数
if (!isset($_GET['id']) || empty($_GET['id'])) {
    responseError(400, '缺少必要参数: id');
}

// 获取输入参数
$bookingId = intval($_GET['id']);

// 获取数据库连接
$db = get_db_connection();
if (!$db) {
    responseError(500, '数据库连接失败');
}

try {
    // 构建SQL查询，根据是否公开访问使用不同的条件
    if ($isPublic) {
        $sql = "SELECT b.*, 
                    e.name as engineer_name, 
                    e.phone as engineer_phone
                FROM install_bookings b
                LEFT JOIN installation_engineers e ON b.engineer_id = e.id
                WHERE b.id = ?";
        
        $stmt = $db->prepare($sql);
        if (!$stmt) {
            responseError(500, '准备SQL语句失败: ' . $db->error);
        }
        
        $stmt->bind_param("i", $bookingId);
    } else {
        $sql = "SELECT b.*, 
                    e.name as engineer_name, 
                    e.phone as engineer_phone
                FROM install_bookings b
                LEFT JOIN installation_engineers e ON b.engineer_id = e.id
                WHERE b.id = ? AND b.user_id = ?";
        
        $stmt = $db->prepare($sql);
        if (!$stmt) {
            responseError(500, '准备SQL语句失败: ' . $db->error);
        }
        
        $stmt->bind_param("ii", $bookingId, $user['id']);
    }
    
    if (!$stmt->execute()) {
        responseError(500, '执行SQL失败: ' . $stmt->error);
    }
    
    $result = $stmt->get_result();
    $booking = $result->fetch_assoc();
    
    if (!$booking) {
        responseError(404, '预约不存在或无权查看');
    }
    
    // 格式化预约信息
    $booking['id'] = intval($booking['id']);
    $booking['user_id'] = intval($booking['user_id']);
    $booking['total_amount'] = floatval($booking['total_amount']);
    $booking['package_price'] = floatval($booking['package_price']);
    $booking['installation_fee'] = floatval($booking['installation_fee']);
    
    // 确保payment_status字段存在
    if (!isset($booking['payment_status']) || $booking['payment_status'] === null) {
        $booking['payment_status'] = 'unpaid';
    }
    
    // 如果有推荐人，获取推荐人信息
    if (!empty($booking['referrer_id'])) {
        $booking['referrer_id'] = intval($booking['referrer_id']);
        
        $sql = "SELECT id, name, avatar FROM app_users WHERE id = ?";
        $stmt = $db->prepare($sql);
        if ($stmt) {
            $stmt->bind_param("i", $booking['referrer_id']);
            $stmt->execute();
            $result = $stmt->get_result();
            $referrer = $result->fetch_assoc();
            if ($referrer) {
                $booking['referrer'] = [
                    'id' => intval($referrer['id']),
                    'name' => $referrer['name'],
                    'avatar' => $referrer['avatar']
                ];
            }
        }
    }
    
    // 返回成功响应
    responseSuccess($booking);
} catch (Exception $e) {
    // 记录错误日志
    error_log("获取预约详情失败: " . $e->getMessage());
    
    // 返回错误响应
    responseError(500, '获取预约详情失败: ' . $e->getMessage());
}
