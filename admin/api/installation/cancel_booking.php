<?php
/**
 * 取消净水器安装预约
 * API路径: /api/installation/cancel_booking.php
 * 请求方式: POST
 * 
 * 参数:
 * - booking_id: 预约ID
 * - cancel_reason: 取消原因（可选）
 * 
 * 返回:
 * - code: 0表示成功，非0表示失败
 * - message: 提示信息
 */

// // 使用统一的数据库连接文件
require_once __DIR__ . '/../../db.php';

require_once __DIR__ . '/../../api/functions.php'; /* 已注释 */

// 验证用户登录状态
$user = validateToken();
if (!$user) {
    responseError(401, '未登录或登录已过期');
}

// 验证必填参数
$bookingId = 0;
if (isset($_POST['id']) && !empty($_POST['id'])) {
    $bookingId = intval($_POST['id']);
} elseif (isset($_POST['booking_id']) && !empty($_POST['booking_id'])) {
$bookingId = intval($_POST['booking_id']);
}

if ($bookingId <= 0) {
    responseError(400, '缺少必要参数: id或booking_id');
}

$cancelReason = isset($_POST['cancel_reason']) ? $_POST['cancel_reason'] : '用户取消';

// 开始数据库事务
$db = getDbConnection();
$db->beginTransaction();

try {
    // 检查预约记录是否存在且属于当前用户
    $checkSql = "SELECT * FROM install_bookings WHERE id = :booking_id AND user_id = :user_id LIMIT 1";
    $stmt = $db->prepare($checkSql);
    $stmt->bindParam(':booking_id', $bookingId);
    $stmt->bindParam(':user_id', $user['id']);
    $stmt->execute();
    
    $booking = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$booking) {
        responseError(404, '预约记录不存在或无权操作');
    }
    
    // 检查预约状态是否可以取消
    if ($booking['status'] !== 'pending' && $booking['status'] !== 'confirmed') {
        responseError(400, '当前状态无法取消预约');
    }
    
    // 更新预约状态
    $updateSql = "UPDATE install_bookings 
                 SET status = 'cancelled', 
                     cancel_reason = :cancel_reason,
                     updated_at = NOW() 
                 WHERE id = :booking_id AND user_id = :user_id";
    
    $stmt = $db->prepare($updateSql);
    $stmt->bindParam(':booking_id', $bookingId);
    $stmt->bindParam(':user_id', $user['id']);
    $stmt->bindParam(':cancel_reason', $cancelReason);
    $stmt->execute();
    
    if ($stmt->rowCount() === 0) {
        throw new Exception('更新预约状态失败');
    }
    
    // 提交事务
    $db->commit();
    
    // 返回成功响应
    responseSuccess(null, '预约已成功取消');
} catch (Exception $e) {
    // 回滚事务
    $db->rollBack();
    
    // 记录错误日志
    error_log("取消安装预约失败: " . $e->getMessage());
    
    // 返回错误响应
    responseError(500, '取消预约失败，请稍后重试');
}