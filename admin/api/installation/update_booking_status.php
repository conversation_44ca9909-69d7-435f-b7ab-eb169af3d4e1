<?php
/**
 * 管理员后台 - 更新安装预约状态
 * API路径: /admin/api/installation/update_booking_status.php
 * 请求方式: POST
 *
 * 参数:
 * - id: 预约ID
 * - status: 新状态
 * - engineer_id: 工程师ID (当状态为assigned时必填)
 * - cancel_reason: 取消原因 (当状态为cancelled时必填)
 * - remark: 备注信息 (可选)
 *
 * 返回:
 * - code: 0表示成功，非0表示失败
 * - message: 提示信息
 * - data: 更新后的预约数据
 */

// 设置允许跨域请求
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization");

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    header("HTTP/1.1 200 OK");
    exit;
}

// 引入数据库连接文件
// // 使用统一的数据库连接文件
require_once __DIR__ . '/../../db.php';

require_once __DIR__ . '/../db_connect.php'; /* 已注释 */

// 记录访问日志
error_log("访问更新预约状态API: " . json_encode($_POST));

// 获取POST参数
$id = isset($_POST['id']) ? intval($_POST['id']) : 0;
$status = isset($_POST['status']) ? trim($_POST['status']) : '';
$engineer_id = isset($_POST['engineer_id']) ? intval($_POST['engineer_id']) : 0;
$cancel_reason = isset($_POST['cancel_reason']) ? trim($_POST['cancel_reason']) : '';
$remark = isset($_POST['remark']) ? trim($_POST['remark']) : '';

// 验证参数
if ($id <= 0) {
    send_json_response(400, '无效的预约ID');
}

if (empty($status)) {
    send_json_response(400, '状态不能为空');
}

// 验证状态值
$validStatuses = ['pending', 'confirmed', 'assigned', 'in_progress', 'completed', 'cancelled'];
if (!in_array($status, $validStatuses)) {
    send_json_response(400, '无效的状态值');
}

// 验证特定状态的必填参数
if ($status === 'assigned' && $engineer_id <= 0) {
    send_json_response(400, '分配状态必须指定工程师');
}

if ($status === 'cancelled' && empty($cancel_reason)) {
    send_json_response(400, '取消状态必须提供取消原因');
}

// 获取数据库连接
$conn = get_db_connection();
if (!$conn) {
    send_json_response(500, '数据库连接失败');
}

try {
    // 开始事务
    mysqli_begin_transaction($conn);

    // 查询当前预约信息
    $checkSql = "SELECT * FROM install_bookings WHERE id = ?";
    $checkStmt = mysqli_prepare($conn, $checkSql);
    mysqli_stmt_bind_param($checkStmt, 'i', $id);
    mysqli_stmt_execute($checkStmt);
    $result = mysqli_stmt_get_result($checkStmt);

    if (!$booking = mysqli_fetch_assoc($result)) {
        mysqli_rollback($conn);
        send_json_response(404, '预约不存在');
    }

    // 更新预约状态
    $updateSql = "UPDATE install_bookings SET
                    status = ?,
                    engineer_id = ?,
                    cancel_reason = ?,
                    remark = ?,
                    updated_at = NOW()
                  WHERE id = ?";

    $updateStmt = mysqli_prepare($conn, $updateSql);
    mysqli_stmt_bind_param($updateStmt, 'sissi', $status, $engineer_id, $cancel_reason, $remark, $id);

    if (!mysqli_stmt_execute($updateStmt)) {
        mysqli_rollback($conn);
        send_json_response(500, '更新预约状态失败: ' . mysqli_error($conn));
    }

    // 提交事务
    mysqli_commit($conn);

    // 查询更新后的预约信息
    $sql = "SELECT b.*, e.name as engineer_name
            FROM install_bookings b
            LEFT JOIN installation_engineers e ON b.engineer_id = e.id
            WHERE b.id = ?";

    $stmt = mysqli_prepare($conn, $sql);
    mysqli_stmt_bind_param($stmt, 'i', $id);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);
    $updatedBooking = mysqli_fetch_assoc($result);

    // 添加状态文本
    $statusMap = [
        'pending' => '待处理',
        'confirmed' => '已确认',
        'assigned' => '已分配',
        'in_progress' => '进行中',
        'completed' => '已完成',
        'cancelled' => '已取消'
    ];

    $updatedBooking['status_text'] = $statusMap[$updatedBooking['status']] ?? $updatedBooking['status'];

    // 返回成功响应
    send_json_response(0, '更新预约状态成功', $updatedBooking);

} catch (Exception $e) {
    // 回滚事务
    mysqli_rollback($conn);
    error_log("更新预约状态失败: " . $e->getMessage());
    send_json_response(500, '更新预约状态失败: ' . $e->getMessage());
} finally {
    mysqli_close($conn);
}


