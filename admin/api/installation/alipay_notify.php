<?php
/**
 * 支付宝支付异步通知处理
 * 
 * 处理支付宝支付成功后的异步通知
 * 验证通知的合法性
 * 更新订单状态
 * 更新预约支付状态
 */

// 设置时区
date_default_timezone_set('Asia/Shanghai');

// 开启输出缓冲
ob_start();

// 设置错误报告
ini_set('display_errors', 0);
error_reporting(E_ALL);

/**
 * 记录日志
 */
function notify_log($message, $data = null, $level = 'info') {
    $logDir = '../logs/installation/';
    if (!is_dir($logDir)) {
        mkdir($logDir, 0777, true);
    }
    
    $logFile = $logDir . 'alipay_notify_' . date('Y-m-d') . '.log';
    $time = date('Y-m-d H:i:s');
    $dataString = is_array($data) || is_object($data) ? json_encode($data, JSON_UNESCAPED_UNICODE) : $data;
    $logContent = "[$time] [$level] $message" . ($dataString ? " - $dataString" : "") . PHP_EOL;
    
    file_put_contents($logFile, $logContent, FILE_APPEND);
}

// 设置响应头
header('Content-Type: text/plain; charset=utf-8');

// 记录通知开始
notify_log('接收到支付宝异步通知');

// 记录原始通知数据
$postData = file_get_contents('php://input');
if (empty($postData)) {
    $postData = $_POST;
}
notify_log('原始通知数据', $postData);

// 包含函数文件
$functionsFile = '../functions.php';
if (!file_exists($functionsFile)) {
    notify_log('函数文件不存在', $functionsFile, 'error');
    echo "fail";
    exit;
}
require_once $functionsFile;

// 验证签名
function verifyAlipaySign($params, $alipayPublicKey) {
    // 移除sign和sign_type参数
    $sign = $params['sign'] ?? '';
    $signType = $params['sign_type'] ?? 'RSA2';
    unset($params['sign']);
    unset($params['sign_type']);
    
    // 按字典序排序参数
    ksort($params);
    
    // 组装签名字符串
    $signStr = '';
    foreach ($params as $key => $value) {
        if ($value !== '' && !is_null($value)) {
            $signStr .= $key . '=' . $value . '&';
        }
    }
    $signStr = rtrim($signStr, '&');
    
    // 验证签名
    $alipayPubKey = "-----BEGIN PUBLIC KEY-----\n" . 
                    chunk_split($alipayPublicKey, 64, "\n") . 
                    "-----END PUBLIC KEY-----";
    
    $result = false;
    switch ($signType) {
        case 'RSA2':
            $result = openssl_verify($signStr, base64_decode($sign), $alipayPubKey, OPENSSL_ALGO_SHA256);
            break;
        case 'RSA':
            $result = openssl_verify($signStr, base64_decode($sign), $alipayPubKey, OPENSSL_ALGO_SHA1);
            break;
    }
    
    return $result === 1;
}

// 验证通知数据
if (empty($_POST)) {
    notify_log('通知数据为空', null, 'error');
    echo "fail";
    exit;
}

// 提取必要参数
$outTradeNo = $_POST['out_trade_no'] ?? '';
$tradeNo = $_POST['trade_no'] ?? '';
$tradeStatus = $_POST['trade_status'] ?? '';
$totalAmount = $_POST['total_amount'] ?? 0;
$receiptAmount = $_POST['receipt_amount'] ?? 0;

notify_log('解析通知参数', [
    'out_trade_no' => $outTradeNo,
    'trade_no' => $tradeNo,
    'trade_status' => $tradeStatus,
    'total_amount' => $totalAmount,
    'receipt_amount' => $receiptAmount
]);

// 支付宝配置
// 交易状态判断
if ($tradeStatus !== 'TRADE_SUCCESS' && $tradeStatus !== 'TRADE_FINISHED') {
    notify_log('交易未成功', ['trade_status' => $tradeStatus], 'warning');
    echo "success"; // 仍然返回success，避免支付宝重复通知
    exit;
}

// 获取数据库连接
try {
    $db = getDbConnection('app');
    notify_log('数据库连接成功');
} catch (Exception $e) {
    notify_log('数据库连接失败', ['error' => $e->getMessage()], 'error');
    echo "fail";
    exit;
}

// 查询订单信息
try {
    $query = "SELECT * FROM installation_payment_orders WHERE order_no = :order_no";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':order_no', $outTradeNo);
    $stmt->execute();
    $order = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$order) {
        notify_log('订单不存在', ['order_no' => $outTradeNo], 'error');
        echo "fail";
        exit;
    }

    notify_log('订单信息', $order);
    
    // 检查订单状态
    if ($order['status'] === 'paid') {
        notify_log('订单已支付，无需重复处理', ['order_no' => $outTradeNo]);
        echo "success";
        exit;
    }
    
    // 检查金额是否匹配
    if (number_format($order['amount'], 2, '.', '') !== number_format($totalAmount, 2, '.', '')) {
        notify_log('订单金额不匹配', [
            'order_amount' => $order['amount'],
            'notify_amount' => $totalAmount
        ], 'error');
        // 仍然处理，但记录警告
        notify_log('订单金额不匹配，但仍继续处理', [], 'warning');
    }
    
} catch (Exception $e) {
    notify_log('查询订单信息失败', ['error' => $e->getMessage()], 'error');
    echo "fail";
    exit;
}

// 更新订单和预约状态
try {
    $db->beginTransaction();
    
    // 更新订单状态
    $query = "UPDATE installation_payment_orders 
              SET status = 'paid', 
                  payment_time = NOW(), 
                  transaction_id = :transaction_id,
                  updated_at = NOW() 
              WHERE order_no = :order_no";
              
    $stmt = $db->prepare($query);
    $stmt->bindParam(':transaction_id', $tradeNo);
    $stmt->bindParam(':order_no', $outTradeNo);
    $stmt->execute();
    
    // 更新预约状态
    $bookingId = $order['booking_id'];
    $query = "UPDATE installation_bookings 
              SET status = 'paid', 
                  paid = 1,
                  payment_time = NOW(),
                  payment_method = 'alipay',
                  updated_at = NOW() 
              WHERE id = :booking_id";
              
    $stmt = $db->prepare($query);
    $stmt->bindParam(':booking_id', $bookingId);
    $stmt->execute();
    
    $db->commit();
    
    notify_log('支付成功，订单和预约状态已更新', [
        'order_no' => $outTradeNo,
        'booking_id' => $bookingId
    ]);
    
} catch (Exception $e) {
    $db->rollBack();
    notify_log('更新订单状态失败', ['error' => $e->getMessage()], 'error');
    echo "fail";
    exit;
}

// 返回成功
echo "success";
exit; 