<?php
// 修复create_booking.php文件，移除重复的数据库连接代码
$file = __DIR__ . '/create_booking.php';
$content = file_get_contents($file);

// 保存原始内容备份
file_put_contents($file . '.bak', $content);
echo "已创建备份文件: " . $file . ".bak\n";

// 定义两个数据库连接代码模式，用于查找和移除重复
$pattern1Start = "// 直接使用config.php中的配置进行数据库连接";
$pattern1End = "file_put_contents(__DIR__ . '/../logs/debug.log', \"\\n[调试] 数据库连接成功(直接使用config)\\n\", FILE_APPEND);";

// 寻找第一个模式的开始位置
$pos1 = strpos($content, $pattern1Start);
if ($pos1 === false) {
    echo "没有找到数据库连接代码，退出\n";
    exit;
}

// 寻找第一个模式的结束位置
$pos1End = strpos($content, $pattern1End, $pos1);
if ($pos1End === false) {
    echo "没有找到第一个模式的结束位置，退出\n";
    exit;
}
$pos1End += strlen($pattern1End);

// 从第一个模式结束处寻找第二个模式的开始位置
$pos2 = strpos($content, $pattern1Start, $pos1End);
if ($pos2 === false) {
    echo "没有找到重复的数据库连接代码，退出\n";
    exit;
}

// 寻找第二个模式的结束位置
$pos2End = strpos($content, $pattern1End, $pos2);
if ($pos2End === false) {
    echo "没有找到第二个模式的结束位置，退出\n";
    exit;
}
$pos2End += strlen($pattern1End);

// 输出找到的位置信息
echo "第一个模式: $pos1 - $pos1End\n";
echo "第二个模式: $pos2 - $pos2End\n";

// 提取第二个模式的代码
$duplicateCode = substr($content, $pos2, $pos2End - $pos2);
echo "重复代码长度: " . strlen($duplicateCode) . " 字节\n";

// 从内容中移除第二个模式的代码
$newContent = substr($content, 0, $pos2) . substr($content, $pos2End);
echo "新内容长度: " . strlen($newContent) . " 字节 (原始: " . strlen($content) . " 字节)\n";

// 保存修复后的文件
file_put_contents($file, $newContent);
echo "文件已修复\n";

// 验证修复
$fixedContent = file_get_contents($file);
$count = substr_count($fixedContent, $pattern1Start);
echo "验证: 连接代码模式在文件中出现 $count 次\n"; 