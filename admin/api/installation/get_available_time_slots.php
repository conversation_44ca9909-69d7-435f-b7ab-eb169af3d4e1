<?php
/**
 * 获取净水器安装可用时间段
 * API路径: /api/installation/get_available_time_slots.php
 * 请求方式: GET
 *
 * 返回:
 * - code: 0表示成功，非0表示失败
 * - message: 提示信息
 * - data: 可用时间段列表
 */

// 使用统一的数据库连接文件
require_once __DIR__ . '/../../db.php';

require_once __DIR__ . '/../../api/functions.php';

// 验证用户登录状态
$user = validateToken();
if (!$user) {
    responseError(401, '未登录或登录已过期');
}

// 获取未来7天的可用时间段
$availableSlots = [];
$currentDate = date('Y-m-d');

// 生成未来7天的可用时间
for ($i = 1; $i <= 7; $i++) {
    $date = date('Y-m-d', strtotime("$currentDate +$i days"));
    $dayOfWeek = date('N', strtotime($date)); // 1-7，表示周一到周日
    
    // 周末只提供上午时间段
    if ($dayOfWeek >= 6) { // 周六和周日
        $availableSlots[] = [
            'date' => $date,
            'time' => '10:00-12:00',
            'label' => date('m月d日', strtotime($date)) . ' (上午)',
            'value' => $date . ' 10:00:00',
            'is_weekend' => true
        ];
    } else {
        // 工作日提供上午和下午时间段
        $availableSlots[] = [
            'date' => $date,
            'time' => '10:00-12:00',
            'label' => date('m月d日', strtotime($date)) . ' (上午)',
            'value' => $date . ' 10:00:00',
            'is_weekend' => false
        ];
        $availableSlots[] = [
            'date' => $date,
            'time' => '14:00-17:00',
            'label' => date('m月d日', strtotime($date)) . ' (下午)',
            'value' => $date . ' 14:00:00',
            'is_weekend' => false
        ];
    }
}

// 返回可用时间段列表
responseSuccess($availableSlots); 