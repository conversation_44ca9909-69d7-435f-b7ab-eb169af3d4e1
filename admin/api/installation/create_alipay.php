<?php
/**
 * 支付宝支付创建接口
 * 
 * 参数：
 * - booking_id：预约ID
 * - payment_method：支付方式（alipay_wap）
 * - return_url：支付完成后的跳转地址（可选）
 * - notify_url：支付完成后的异步通知地址（可选）
 * 
 * 返回值：
 * - 成功：{"code": 0, "message": "success", "data": {"form": "表单"}}
 * - 失败：{"code": 错误码, "message": "错误信息", "data": null}
 */

// 设置时区
date_default_timezone_set('Asia/Shanghai');

// 开启输出缓冲
ob_start();

// 设置错误处理
ini_set('display_errors', 0);
error_reporting(E_ALL);

// 设置响应头
header('Content-Type: application/json; charset=utf-8');

// 允许跨域
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');

// 如果是 OPTIONS 请求，直接返回200
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 记录调试日志函数
function payment_debug_log($message, $data = [], $type = 'info') {
    $logDir = __DIR__ . '/../logs/installation/';
    
    if (!is_dir($logDir)) {
        mkdir($logDir, 0755, true);
    }
    
    $logFile = $logDir . 'alipay_payment_' . date('Y-m-d') . '.log';
    $log = [
        'time' => date('Y-m-d H:i:s'),
        'type' => $type,
        'message' => $message,
        'data' => $data
    ];
    
    file_put_contents($logFile, json_encode($log, JSON_UNESCAPED_UNICODE) . "\n", FILE_APPEND);
}

// 加载必要的函数库
$functionsFile = __DIR__ . '/../functions.php';
if (file_exists($functionsFile)) {
    require_once($functionsFile);
} else {
    payment_debug_log('核心函数库文件不存在', [], 'error');
    echo json_encode(['code' => 500, 'message' => '系统错误：核心函数库不存在', 'data' => null]);
    exit;
}

// 获取请求TOKEN
$token = getTokenFromRequest();
payment_debug_log('接收到支付宝支付请求', ['token' => $token]);

// 验证TOKEN
$tokenValidationResult = validateToken($token);
if (!$tokenValidationResult) {
    payment_debug_log('TOKEN验证失败', ['token' => $token], 'error');
    responseError(401, '身份验证失败，请重新登录');
    exit;
}

// 验证请求参数
if (!isset($_POST['booking_id']) || empty($_POST['booking_id'])) {
    payment_debug_log('缺少必要参数：booking_id', $_POST, 'error');
    responseError(400, '缺少必要参数：booking_id');
    exit;
}

if (!isset($_POST['payment_method']) || empty($_POST['payment_method'])) {
    payment_debug_log('缺少必要参数：payment_method', $_POST, 'error');
    responseError(400, '缺少必要参数：payment_method');
    exit;
}

// 获取参数
$bookingId = $_POST['booking_id'];
$paymentMethod = $_POST['payment_method'];
$returnUrl = isset($_POST['return_url']) ? $_POST['return_url'] : "https://pay.itapgo.com/Tapp/app-vue/dist/#/installation/booking-result?booking_id={$bookingId}&status=success";
$notifyUrl = isset($_POST['notify_url']) ? $_POST['notify_url'] : "https://pay.itapgo.com/Tapp/admin/api/installation/alipay_notify.php";

payment_debug_log('支付参数', [
    'booking_id' => $bookingId,
    'payment_method' => $paymentMethod,
    'return_url' => $returnUrl,
    'notify_url' => $notifyUrl
]);

// 校验支付方式
if ($paymentMethod != 'alipay_wap') {
    payment_debug_log('不支持的支付方式', ['payment_method' => $paymentMethod], 'error');
    responseError(400, '不支持的支付方式');
    exit;
}

// 获取数据库连接
try {
    $db = getDbConnection('app');
    payment_debug_log('数据库连接成功');
} catch (Exception $e) {
    payment_debug_log('数据库连接失败', ['error' => $e->getMessage()], 'error');
    responseError(500, '系统错误：数据库连接失败');
    exit;
}

// 查询预约信息
try {
    $query = "SELECT * FROM installation_bookings WHERE id = :id";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':id', $bookingId);
    $stmt->execute();
    $booking = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$booking) {
        payment_debug_log('预约不存在', ['booking_id' => $bookingId], 'error');
        responseError(404, '预约不存在');
        exit;
    }

    payment_debug_log('预约信息', $booking);
} catch (Exception $e) {
    payment_debug_log('查询预约信息失败', ['error' => $e->getMessage()], 'error');
    responseError(500, '系统错误：查询预约信息失败');
    exit;
}

// 检查用户是否匹配
if ($booking['user_id'] != $tokenValidationResult['sub']) {
    payment_debug_log('用户与预约不匹配', [
        'booking_user_id' => $booking['user_id'],
        'token_user_id' => $tokenValidationResult['sub']
    ], 'error');
    responseError(403, '无权操作此预约');
    exit;
}

// 检查预约状态
if ($booking['status'] != 'pending' && $booking['status'] != 'unpaid') {
    payment_debug_log('预约状态不正确', ['status' => $booking['status']], 'error');
    responseError(400, '此预约状态不允许支付');
    exit;
}

// 检查是否已支付
if ($booking['paid'] == 1) {
    payment_debug_log('预约已支付', ['booking_id' => $bookingId], 'error');
    responseError(400, '此预约已支付');
    exit;
}

// 查询预约项目和价格信息
try {
    $query = "SELECT * FROM installation_booking_items WHERE booking_id = :booking_id";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':booking_id', $bookingId);
    $stmt->execute();
    $bookingItems = $stmt->fetchAll(PDO::FETCH_ASSOC);

    if (empty($bookingItems)) {
        payment_debug_log('预约项目不存在', ['booking_id' => $bookingId], 'error');
        responseError(404, '预约项目不存在');
        exit;
    }

    payment_debug_log('预约项目信息', $bookingItems);
} catch (Exception $e) {
    payment_debug_log('查询预约项目失败', ['error' => $e->getMessage()], 'error');
    responseError(500, '系统错误：查询预约项目失败');
    exit;
}

// 计算总价
$totalAmount = 0;
foreach ($bookingItems as $item) {
    $totalAmount += floatval($item['price']);
}

payment_debug_log('计算总价', ['total_amount' => $totalAmount]);

// 生成订单号
$orderNo = 'ALP' . date('YmdHis') . rand(1000, 9999);

// 支付宝配置信息
$alipayConfig = [
    // 应用ID
    'app_id' => '2021000000000000', // 需要替换为真实的应用ID
    // 商户私钥
    'merchant_private_key' => '请替换为真实的商户私钥',
    // 支付宝公钥
    'alipay_public_key' => '请替换为真实的支付宝公钥',
    // 编码
    'charset' => 'UTF-8',
    // 签名方式
    'sign_type' => 'RSA2',
    // 支付宝网关
    'gateway_url' => 'https://openapi.alipaydev.com/gateway.do',
];

// 保存订单信息
try {
    $db->beginTransaction();

    $query = "INSERT INTO installation_payment_orders (booking_id, order_no, payment_method, amount, status, created_at) 
              VALUES (:booking_id, :order_no, :payment_method, :amount, 'pending', NOW())";
    
    $stmt = $db->prepare($query);
    $stmt->bindParam(':booking_id', $bookingId);
    $stmt->bindParam(':order_no', $orderNo);
    $stmt->bindParam(':payment_method', $paymentMethod);
    $stmt->bindParam(':amount', $totalAmount);
    $stmt->execute();
    
    $orderId = $db->lastInsertId();
    
    // 更新预约状态
    $query = "UPDATE installation_bookings SET status = 'unpaid' WHERE id = :id";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':id', $bookingId);
    $stmt->execute();
    
    $db->commit();
    
    payment_debug_log('订单创建成功', [
        'order_id' => $orderId,
        'order_no' => $orderNo
    ]);
} catch (Exception $e) {
    $db->rollBack();
    payment_debug_log('创建订单失败', ['error' => $e->getMessage()], 'error');
    responseError(500, '系统错误：创建订单失败');
    exit;
}

// 构建支付请求参数
$requestParams = [
    'app_id' => $alipayConfig['app_id'],
    'method' => 'alipay.trade.wap.pay',
    'format' => 'JSON',
    'return_url' => $returnUrl,
    'notify_url' => $notifyUrl,
    'charset' => $alipayConfig['charset'],
    'sign_type' => $alipayConfig['sign_type'],
    'timestamp' => date('Y-m-d H:i:s'),
    'version' => '1.0',
    'biz_content' => json_encode([
        'out_trade_no' => $orderNo,
        'product_code' => 'QUICK_WAP_WAY',
        'total_amount' => number_format($totalAmount, 2, '.', ''),
        'subject' => '安装服务订单-' . $orderNo,
        'body' => '安装服务费用'
    ], JSON_UNESCAPED_UNICODE)
];

// 构建签名
ksort($requestParams);
$signStr = '';
foreach ($requestParams as $key => $value) {
    $signStr .= $key . '=' . $value . '&';
}
$signStr = rtrim($signStr, '&');

// 实际应用中使用以下代码进行签名
// $sign = '';
// openssl_sign($signStr, $sign, $privateKey, OPENSSL_ALGO_SHA256);
// $sign = base64_encode($sign);
// 为了示例，使用一个假的签名
$sign = 'mock_signature';

$requestParams['sign'] = $sign;

// 构建表单
$form = '<form id="alipaySubmit" name="alipaySubmit" action="' . $alipayConfig['gateway_url'] . '" method="POST">';
foreach ($requestParams as $key => $value) {
    $form .= '<input type="hidden" name="' . $key . '" value="' . htmlspecialchars($value) . '"/>';
}
$form .= '<input type="submit" value="确认" style="display:none;"></form>';
$form .= '<script>document.forms["alipaySubmit"].submit();</script>';

payment_debug_log('生成表单成功', ['form_length' => strlen($form)]);

// 返回支付表单
responseSuccess(['form' => $form]); 