<?php
/**
 * 管理员后台 - 获取安装统计数据
 * API路径: /admin/api/installation/get_statistics.php
 * 请求方式: GET
 *
 * 参数:
 * - type: 统计类型 (daily, weekly, monthly)，默认daily
 * - start_date: 开始日期 (可选)
 * - end_date: 结束日期 (可选)
 * - engineer_id: 工程师ID (可选)
 *
 * 返回:
 * - code: 0表示成功，非0表示失败
 * - message: 提示信息
 * - data: 统计数据
 */

// 设置允许跨域请求
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization");

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    header("HTTP/1.1 200 OK");
    exit;
}

// 引入数据库连接文件
// // 使用统一的数据库连接文件
require_once __DIR__ . '/../../db.php';

require_once __DIR__ . '/../db_connect.php'; /* 已注释 */

// 记录访问日志
error_log("访问安装统计API: " . json_encode($_GET));

// 获取查询参数
$type = isset($_GET['type']) ? trim($_GET['type']) : 'daily';
$start_date = isset($_GET['start_date']) ? trim($_GET['start_date']) : date('Y-m-d', strtotime('-30 days'));
$end_date = isset($_GET['end_date']) ? trim($_GET['end_date']) : date('Y-m-d');
$engineer_id = isset($_GET['engineer_id']) ? intval($_GET['engineer_id']) : 0;

// 验证统计类型
$validTypes = ['daily', 'weekly', 'monthly'];
if (!in_array($type, $validTypes)) {
    send_json_response(400, '无效的统计类型');
}

// 获取数据库连接
$conn = get_db_connection();
if (!$conn) {
    send_json_response(500, '数据库连接失败');
}

try {
    // 构建查询条件
    $where = "WHERE booking_date BETWEEN ? AND ?";
    $params = [$start_date, $end_date];
    $types = 'ss';

    if ($engineer_id > 0) {
        $where .= " AND engineer_id = ?";
        $params[] = $engineer_id;
        $types .= 'i';
    }

    // 1. 获取概览数据
    $overviewSql = "SELECT
                        COUNT(*) as total_bookings,
                        SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed_bookings,
                        SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending_bookings,
                        SUM(CASE WHEN status = 'cancelled' THEN 1 ELSE 0 END) as cancelled_bookings,
                        SUM(CASE WHEN payment_status = 'paid' THEN 1 ELSE 0 END) as paid_bookings,
                        SUM(CASE WHEN payment_status = 'unpaid' THEN 1 ELSE 0 END) as unpaid_bookings,
                        SUM(CASE WHEN payment_status = 'paid' THEN amount ELSE 0 END) as total_revenue
                    FROM install_bookings
                    $where";

    $overviewStmt = mysqli_prepare($conn, $overviewSql);
    mysqli_stmt_bind_param($overviewStmt, $types, ...$params);
    mysqli_stmt_execute($overviewStmt);
    $overviewResult = mysqli_stmt_get_result($overviewStmt);
    $overview = mysqli_fetch_assoc($overviewResult);

    // 2. 获取状态分布
    $statusSql = "SELECT
                    status,
                    COUNT(*) as count
                  FROM install_bookings
                  $where
                  GROUP BY status";

    $statusStmt = mysqli_prepare($conn, $statusSql);
    mysqli_stmt_bind_param($statusStmt, $types, ...$params);
    mysqli_stmt_execute($statusStmt);
    $statusResult = mysqli_stmt_get_result($statusStmt);

    $status_distribution = [];
    while ($row = mysqli_fetch_assoc($statusResult)) {
        $status_distribution[] = [
            'status' => $row['status'],
            'count' => (int)$row['count']
        ];
    }

    // 3. 获取工程师排名
    $engineerSql = "SELECT
                        e.id,
                        e.name,
                        COUNT(b.id) as total_bookings,
                        SUM(CASE WHEN b.status = 'completed' THEN 1 ELSE 0 END) as completed_bookings
                    FROM installation_engineers e
                    LEFT JOIN install_bookings b ON e.id = b.engineer_id AND b.booking_date BETWEEN ? AND ?
                    GROUP BY e.id, e.name
                    ORDER BY completed_bookings DESC, total_bookings DESC
                    LIMIT 10";

    $engineerStmt = mysqli_prepare($conn, $engineerSql);
    mysqli_stmt_bind_param($engineerStmt, 'ss', $start_date, $end_date);
    mysqli_stmt_execute($engineerStmt);
    $engineerResult = mysqli_stmt_get_result($engineerStmt);

    $engineer_ranking = [];
    while ($row = mysqli_fetch_assoc($engineerResult)) {
        $engineer_ranking[] = [
            'id' => (int)$row['id'],
            'name' => $row['name'],
            'total_bookings' => (int)$row['total_bookings'],
            'completed_bookings' => (int)$row['completed_bookings'],
            'completion_rate' => $row['total_bookings'] > 0 ? round($row['completed_bookings'] / $row['total_bookings'] * 100, 2) : 0
        ];
    }

    // 4. 获取趋势数据
    $groupBy = '';
    $dateFormat = '';

    switch ($type) {
        case 'daily':
            $dateFormat = '%Y-%m-%d';
            $groupBy = 'DATE(booking_date)';
            break;
        case 'weekly':
            $dateFormat = '%Y-%u';
            $groupBy = 'YEARWEEK(booking_date)';
            break;
        case 'monthly':
            $dateFormat = '%Y-%m';
            $groupBy = 'DATE_FORMAT(booking_date, "%Y-%m")';
            break;
    }

    $trendSql = "SELECT
                    DATE_FORMAT(booking_date, '$dateFormat') as time_period,
                    COUNT(*) as total,
                    SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed,
                    SUM(CASE WHEN payment_status = 'paid' THEN 1 ELSE 0 END) as paid
                 FROM install_bookings
                 $where
                 GROUP BY $groupBy
                 ORDER BY booking_date";

    $trendStmt = mysqli_prepare($conn, $trendSql);
    mysqli_stmt_bind_param($trendStmt, $types, ...$params);
    mysqli_stmt_execute($trendStmt);
    $trendResult = mysqli_stmt_get_result($trendStmt);

    $trends = [];
    while ($row = mysqli_fetch_assoc($trendResult)) {
        $trends[] = [
            'time_period' => $row['time_period'],
            'total' => (int)$row['total'],
            'completed' => (int)$row['completed'],
            'paid' => (int)$row['paid']
        ];
    }

    // 5. 获取最近的预约记录
    $recentSql = "SELECT
                    DATE(booking_date) as date,
                    COUNT(*) as total_bookings,
                    SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending_bookings,
                    SUM(CASE WHEN status = 'confirmed' THEN 1 ELSE 0 END) as confirmed_bookings,
                    SUM(CASE WHEN status = 'assigned' THEN 1 ELSE 0 END) as assigned_bookings,
                    SUM(CASE WHEN status = 'in_progress' THEN 1 ELSE 0 END) as in_progress_bookings,
                    SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed_bookings,
                    SUM(CASE WHEN status = 'cancelled' THEN 1 ELSE 0 END) as cancelled_bookings,
                    ROUND(SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) / COUNT(*), 2) as completion_rate,
                    AVG(CASE WHEN status = 'completed' THEN TIMESTAMPDIFF(HOUR, created_at, updated_at) / 24 ELSE NULL END) as avg_duration
                  FROM install_bookings
                  $where
                  GROUP BY DATE(booking_date)
                  ORDER BY date DESC
                  LIMIT 10";

    $recentStmt = mysqli_prepare($conn, $recentSql);
    mysqli_stmt_bind_param($recentStmt, $types, ...$params);
    mysqli_stmt_execute($recentStmt);
    $recentResult = mysqli_stmt_get_result($recentStmt);

    $recent_bookings = [];
    while ($row = mysqli_fetch_assoc($recentResult)) {
        $recent_bookings[] = [
            'date' => $row['date'],
            'total_bookings' => (int)$row['total_bookings'],
            'pending_bookings' => (int)$row['pending_bookings'],
            'confirmed_bookings' => (int)$row['confirmed_bookings'],
            'assigned_bookings' => (int)$row['assigned_bookings'],
            'in_progress_bookings' => (int)$row['in_progress_bookings'],
            'completed_bookings' => (int)$row['completed_bookings'],
            'cancelled_bookings' => (int)$row['cancelled_bookings'],
            'completion_rate' => (float)$row['completion_rate'],
            'avg_duration' => $row['avg_duration'] ? round((float)$row['avg_duration'], 1) : 0
        ];
    }

    // 组合所有数据
    $data = [
        'overview' => $overview,
        'status_distribution' => $status_distribution,
        'engineer_ranking' => $engineer_ranking,
        'trends' => $trends,
        'recent_bookings' => $recent_bookings
    ];

    // 返回成功响应
    send_json_response(0, '获取统计数据成功', $data);

} catch (Exception $e) {
    error_log("获取统计数据失败: " . $e->getMessage());
    send_json_response(500, '获取统计数据失败: ' . $e->getMessage());
} finally {
    mysqli_close($conn);
}


