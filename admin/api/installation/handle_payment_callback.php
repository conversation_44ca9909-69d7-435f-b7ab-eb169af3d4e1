<?php
/**
 * 处理安装预约支付回调
 * 此文件由微信支付回调通知接口调用
 */

// 引入日志记录函数
require_once __DIR__ . '/../functions/logger_installation.php';

/**
 * 处理安装预约支付成功
 * @param int $bookingId 预约ID
 * @param string $transactionId 微信支付交易号
 * @param mysqli $conn 数据库连接
 * @return bool 处理结果
 */
function handleInstallationPayment($bookingId, $transactionId, $conn) {
    // 记录日志
    notify_debug_log('处理安装预约支付', ['booking_id' => $bookingId, 'transaction_id' => $transactionId]);
    log_installation_payment('处理安装预约支付开始', LOG_INFO, ['booking_id' => $bookingId, 'transaction_id' => $transactionId]);

    try {
        // 查询预约信息
        $stmt = $conn->prepare("SELECT * FROM install_bookings WHERE id = ? AND payment_status = 'unpaid' FOR UPDATE");
        if (!$stmt) {
            $error = "Prepare 预约查询失败: " . $conn->error;
            log_installation_payment($error, LOG_ERROR);
            throw new Exception($error);
        }
        $stmt->bind_param("i", $bookingId);
        if (!$stmt->execute()) {
            $error = "Execute 预约查询失败: " . $stmt->error;
            log_installation_payment($error, LOG_ERROR);
            throw new Exception($error);
        }
        $result = $stmt->get_result();
        $stmt->close();

        if ($result->num_rows === 0) {
            notify_debug_log("找不到未支付的预约", ['booking_id' => $bookingId]);
            log_installation_payment("找不到未支付的预约", LOG_WARNING, ['booking_id' => $bookingId]);
            return false;
        }

        $booking = $result->fetch_assoc();
        notify_debug_log('找到预约信息', $booking);
        log_installation_payment('找到预约信息', LOG_INFO, ['booking_id' => $bookingId, 'booking_no' => $booking['booking_no']]);

        // 更新预约状态为已支付
        $stmt = $conn->prepare("UPDATE install_bookings SET
            payment_status = 'paid',
            payment_time = NOW(),
            payment_method = 'wechat',
            transaction_id = ?,
            status = 'confirmed',
            updated_at = NOW()
            WHERE id = ? AND payment_status = 'unpaid'");

        if (!$stmt) {
            $error = "Prepare 预约更新失败: " . $conn->error;
            log_installation_payment($error, LOG_ERROR);
            throw new Exception($error);
        }

        $stmt->bind_param("si", $transactionId, $bookingId);
        if (!$stmt->execute()) {
            $error = "Execute 预约更新失败: " . $stmt->error;
            log_installation_payment($error, LOG_ERROR);
            throw new Exception($error);
        }

        $updatedRows = $stmt->affected_rows;
        $stmt->close();

        if ($updatedRows === 0) {
            notify_debug_log("预约状态更新失败，可能已被其他进程处理", ['booking_id' => $bookingId]);
            log_installation_payment("预约状态更新失败，可能已被其他进程处理", LOG_WARNING, ['booking_id' => $bookingId]);
            return false;
        }

        notify_debug_log("预约状态已更新为已支付", ['booking_id' => $bookingId]);
        log_installation_payment("预约状态已更新为已支付", LOG_INFO, ['booking_id' => $bookingId, 'booking_no' => $booking['booking_no']]);

        // 如果有推荐人，更新用户的推荐人信息
        if (!empty($booking['referrer_id']) && $booking['referrer_id'] > 0) {
            $userId = $booking['user_id'];
            log_installation_payment("检测到推荐人信息，准备更新用户推荐人", LOG_INFO, [
                'booking_id' => $bookingId,
                'user_id' => $userId,
                'referrer_id' => $booking['referrer_id']
            ]);

            // 查询用户信息
            $stmt = $conn->prepare("SELECT referrer_id FROM app_users WHERE id = ?");
            if (!$stmt) {
                $error = "Prepare 用户查询失败: " . $conn->error;
                log_installation_payment($error, LOG_ERROR);
                throw new Exception($error);
            }
            $stmt->bind_param("i", $userId);
            if (!$stmt->execute()) {
                $error = "Execute 用户查询失败: " . $stmt->error;
                log_installation_payment($error, LOG_ERROR);
                throw new Exception($error);
            }
            $userResult = $stmt->get_result();
            $stmt->close();

            if ($userResult->num_rows > 0) {
                $user = $userResult->fetch_assoc();

                // 如果用户没有推荐人，则设置推荐人
                if (empty($user['referrer_id'])) {
                    $stmt = $conn->prepare("UPDATE app_users SET referrer_id = ?, updated_at = NOW() WHERE id = ?");
                    if (!$stmt) {
                        $error = "Prepare 用户推荐人更新失败: " . $conn->error;
                        log_installation_payment($error, LOG_ERROR);
                        throw new Exception($error);
                    }
                    $stmt->bind_param("ii", $booking['referrer_id'], $userId);
                    if (!$stmt->execute()) {
                        $error = "Execute 用户推荐人更新失败: " . $stmt->error;
                        log_installation_payment($error, LOG_ERROR);
                        throw new Exception($error);
                    }
                    $stmt->close();

                    notify_debug_log("已更新用户推荐人", ['user_id' => $userId, 'referrer_id' => $booking['referrer_id']]);
                    log_installation_payment("已更新用户推荐人", LOG_INFO, ['user_id' => $userId, 'referrer_id' => $booking['referrer_id']]);
                } else {
                    log_installation_payment("用户已有推荐人，不更新", LOG_INFO, ['user_id' => $userId, 'existing_referrer_id' => $user['referrer_id']]);
                }
            }
        }

        // 记录支付成功日志
        log_installation_payment("安装预约支付处理成功", LOG_INFO, [
            'booking_id' => $bookingId,
            'booking_no' => $booking['booking_no'],
            'transaction_id' => $transactionId,
            'amount' => $booking['total_amount']
        ]);

        return true;
    } catch (Exception $e) {
        notify_debug_log("处理安装预约支付异常", ['error' => $e->getMessage(), 'trace' => $e->getTraceAsString()]);
        log_installation_payment("处理安装预约支付异常", LOG_ERROR, ['error' => $e->getMessage(), 'trace' => $e->getTraceAsString()]);
        return false;
    }
}
