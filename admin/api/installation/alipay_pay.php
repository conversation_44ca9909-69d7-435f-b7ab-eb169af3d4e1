<?php
/**
 * 支付宝支付处理文件
 * 接收订单信息并生成支付宝支付链接
 * 
 * @param string order_no 订单号
 * @param float total_amount 支付金额
 * @param string subject 支付标题
 * 
 * @return 跳转到支付宝支付页面
 */

// 设置时区
date_default_timezone_set('Asia/Shanghai');

// 开启输出缓冲
ob_start();

// 设置错误处理
ini_set('display_errors', 0);
error_reporting(E_ALL);

// 自定义错误处理函数
function errorHandler($errno, $errstr, $errfile, $errline) {
    $error = [
        'code' => $errno,
        'message' => $errstr,
        'file' => $errfile,
        'line' => $errline
    ];
    
    // 记录错误日志
    error_log(json_encode($error), 3, __DIR__ . '/../logs/installation/alipay_errors.log');
    
    // 清空输出缓冲
    ob_clean();
    
    // 返回JSON格式的错误信息
    header('Content-Type: application/json; charset=utf-8');
    echo json_encode(['status' => 'error', 'message' => 'PHP错误: ' . $errstr, 'details' => $error]);
    exit;
}

// 注册错误处理函数
set_error_handler('errorHandler');

// 异常处理函数
function exceptionHandler($exception) {
    $error = [
        'message' => $exception->getMessage(),
        'code' => $exception->getCode(),
        'file' => $exception->getFile(),
        'line' => $exception->getLine(),
        'trace' => $exception->getTraceAsString()
    ];
    
    // 记录错误日志
    error_log(json_encode($error), 3, __DIR__ . '/../logs/installation/alipay_errors.log');
    
    // 清空输出缓冲
    ob_clean();
    
    // 返回JSON格式的错误信息
    header('Content-Type: application/json; charset=utf-8');
    echo json_encode(['status' => 'error', 'message' => '系统异常: ' . $exception->getMessage(), 'details' => $error]);
    exit;
}

// 注册异常处理函数
set_exception_handler('exceptionHandler');

// 加载必要的函数库
$functionsFile = __DIR__ . '/../functions.php';
if (file_exists($functionsFile)) {
    require_once($functionsFile);
} else {
    throw new Exception('核心函数库文件不存在');
}

// 支付宝支付日志函数
function alipay_debug_log($message, $data = []) {
    $logDir = __DIR__ . '/../logs/installation/';
    if (!is_dir($logDir)) {
        mkdir($logDir, 0755, true);
    }
    
    $logFile = $logDir . 'alipay_payment_' . date('Y-m-d') . '.log';
    $log = [
        'time' => date('Y-m-d H:i:s'),
        'message' => $message,
        'data' => $data
    ];
    
    file_put_contents($logFile, json_encode($log, JSON_UNESCAPED_UNICODE) . "\n", FILE_APPEND);
}

// 记录请求日志
alipay_debug_log('收到支付宝支付请求', $_GET);

// 检查请求参数
if (!isset($_GET['order_no']) || !isset($_GET['total_amount']) || !isset($_GET['subject'])) {
    alipay_debug_log('请求参数不完整', $_GET);
    responseError(400, '请求参数不完整');
}

$orderNo = $_GET['order_no'];
$totalAmount = $_GET['total_amount'];
$subject = $_GET['subject'];

alipay_debug_log('支付参数', [
    'order_no' => $orderNo,
    'total_amount' => $totalAmount,
    'subject' => $subject
]);

// 获取数据库连接
try {
    $db = getDbConnection('app');
    alipay_debug_log('数据库连接成功');
} catch (Exception $e) {
    alipay_debug_log('数据库连接失败', ['error' => $e->getMessage()]);
    responseError(500, '数据库连接失败: ' . $e->getMessage());
}

// 查询订单信息
try {
    $sql = "SELECT * FROM installation_payment_orders WHERE order_no = :order_no";
    $stmt = $db->prepare($sql);
    $stmt->bindParam(':order_no', $orderNo);
    $stmt->execute();
    $order = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$order) {
        alipay_debug_log('订单不存在', ['order_no' => $orderNo]);
        responseError(404, '订单不存在');
    }
    
    alipay_debug_log('查询订单信息成功', $order);
} catch (Exception $e) {
    alipay_debug_log('查询订单信息失败', ['error' => $e->getMessage()]);
    responseError(500, '查询订单信息失败: ' . $e->getMessage());
}

// 支付宝支付配置
$alipayConfig = [
    // 应用ID
    'app_id' => '2021000000000000', // 需要替换为真实的应用ID
    // 商户私钥，使用RSA签名验证
    'merchant_private_key' => '请替换为真实的商户私钥',
    // 支付宝公钥
    'alipay_public_key' => '请替换为真实的支付宝公钥',
    // 支付宝网关
    'gateway_url' => 'https://openapi.alipay.com/gateway.do',
    // 编码
    'charset' => 'UTF-8',
    // 签名方式
    'sign_type' => 'RSA2',
    // 支付宝异步通知地址
    'notify_url' => 'https://pay.itapgo.com/Tapp/admin/api/installation/alipay_notify.php',
    // 支付宝同步跳转地址
    'return_url' => 'https://pay.itapgo.com/Tapp/app-vue/dist/#/installation/booking-success/' . $order['booking_id'],
];

alipay_debug_log('支付宝配置', $alipayConfig);

// 组装请求参数
$requestParams = [
    'app_id' => $alipayConfig['app_id'],
    'method' => 'alipay.trade.wap.pay', // 手机网站支付接口
    'format' => 'JSON',
    'return_url' => $alipayConfig['return_url'],
    'charset' => $alipayConfig['charset'],
    'sign_type' => $alipayConfig['sign_type'],
    'timestamp' => date('Y-m-d H:i:s'),
    'version' => '1.0',
    'notify_url' => $alipayConfig['notify_url'],
    'biz_content' => json_encode([
        'out_trade_no' => $orderNo,
        'total_amount' => $totalAmount,
        'subject' => $subject,
        'product_code' => 'QUICK_WAP_WAY'
    ], JSON_UNESCAPED_UNICODE)
];

alipay_debug_log('请求参数', $requestParams);

// 签名函数
function generateAlipaySign($params, $privateKey, $signType = 'RSA2') {
    ksort($params);
    
    $stringToSign = '';
    foreach ($params as $k => $v) {
        if ($v !== '' && $v !== null && $k != 'sign') {
            $stringToSign .= $k . '=' . $v . '&';
        }
    }
    $stringToSign = rtrim($stringToSign, '&');
    
    // 在实际应用中，这里需要使用OpenSSL进行RSA签名
    // 简化处理，仅用于示例
    $sign = '模拟的支付宝签名'; // 实际应用请使用正确的签名算法
    
    return $sign;
}

// 生成签名
$sign = generateAlipaySign($requestParams, $alipayConfig['merchant_private_key'], $alipayConfig['sign_type']);
$requestParams['sign'] = $sign;

alipay_debug_log('生成签名', ['sign' => $sign]);

// 构建请求URL
$requestUrl = $alipayConfig['gateway_url'] . '?' . http_build_query($requestParams);

alipay_debug_log('跳转支付宝支付链接', ['url' => $requestUrl]);

// 更新订单支付数据
try {
    $paymentData = json_encode([
        'alipay_url' => $requestUrl,
        'order_no' => $orderNo,
        'total_amount' => $totalAmount,
        'subject' => $subject
    ]);
    
    $sql = "UPDATE installation_payment_orders SET payment_data = :payment_data WHERE order_no = :order_no";
    $stmt = $db->prepare($sql);
    $stmt->bindParam(':payment_data', $paymentData);
    $stmt->bindParam(':order_no', $orderNo);
    $stmt->execute();
    
    alipay_debug_log('更新订单支付数据成功');
} catch (Exception $e) {
    alipay_debug_log('更新订单支付数据失败', ['error' => $e->getMessage()]);
    // 不阻止继续处理
}

// 跳转到支付宝支付页面
header('Location: ' . $requestUrl);
exit; 