<?php
/**
 * 获取净水器安装预约详情
 * API路径: /api/installation/get_booking_details.php
 * 请求方式: GET
 * 
 * 参数:
 * - id: 预约ID
 * 
 * 返回:
 * - code: 0表示成功，非0表示失败
 * - message: 提示信息
 * - data: 预约详情对象
 */

// // 使用统一的数据库连接文件
require_once __DIR__ . '/../../db.php';

require_once __DIR__ . '/../../api/functions.php'; /* 已注释 */

// 验证用户登录状态
$user = validateToken();
if (!$user) {
    responseError(401, '未登录或登录已过期');
}

// 验证必填参数
if (!isset($_GET['id']) || empty($_GET['id'])) {
    responseError(400, '缺少必要参数: id');
}

$bookingId = intval($_GET['id']);

// 查询预约详情
$db = getDbConnection();
$sql = "SELECT 
            b.*,
            u.username as referrer_username,
            u.wechat_nickname as referrer_nickname,
            u.wechat_avatar as referrer_avatar
        FROM 
            install_bookings b
        LEFT JOIN 
            app_users u ON b.referrer_id = u.id
        WHERE 
            b.id = :booking_id AND b.user_id = :user_id
        LIMIT 1";

$stmt = $db->prepare($sql);
$stmt->bindParam(':booking_id', $bookingId);
$stmt->bindParam(':user_id', $user['id']);
$stmt->execute();

$booking = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$booking) {
    responseError(404, '预约记录不存在或无权查看');
}

// 格式化套餐类型
$packageTypeMap = [
    'personal' => '个人套餐',
    'unlimited' => '无限续用套餐',
    'business_year' => '商业年费套餐',
    'business_flow' => '商业流量套餐'
];
$booking['package_type_text'] = $packageTypeMap[$booking['package_type']] ?? $booking['package_type'];

// 格式化状态
$statusMap = [
    'pending' => '待处理',
    'confirmed' => '已确认',
    'completed' => '已完成',
    'cancelled' => '已取消'
];
$booking['status_text'] = $statusMap[$booking['status']] ?? $booking['status'];

// 返回成功响应
responseSuccess($booking); 