<?php
/**
 * 向数据库中插入安装预约的种子数据
 */

// 引入数据库连接文件
// // 使用统一的数据库连接文件
require_once __DIR__ . '/../../db.php';

require_once __DIR__ . '/../db_connect.php'; /* 已注释 */

// 获取数据库连接
$conn = get_db_connection();
if (!$conn) {
    die("数据库连接失败");
}

// 创建日志目录
$logDir = __DIR__ . '/../logs';
if (!is_dir($logDir)) {
    mkdir($logDir, 0755, true);
}

// 写入日志文件
$logFile = $logDir . '/seed_data_' . date('Y-m-d') . '.log';
file_put_contents($logFile, date('Y-m-d H:i:s') . " - 开始插入种子数据\n", FILE_APPEND);

// 检查表是否存在
$checkTableSql = "SHOW TABLES LIKE 'install_bookings'";
$result = mysqli_query($conn, $checkTableSql);

if (mysqli_num_rows($result) == 0) {
    // 创建表
    $createTableSql = "CREATE TABLE `install_bookings` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `booking_no` varchar(20) NOT NULL COMMENT '预约编号',
        `user_id` int(11) DEFAULT NULL COMMENT '用户ID',
        `contact_name` varchar(50) NOT NULL COMMENT '联系人姓名',
        `contact_phone` varchar(20) NOT NULL COMMENT '联系电话',
        `address` varchar(255) NOT NULL COMMENT '安装地址',
        `booking_date` date NOT NULL COMMENT '预约日期',
        `time_slot` varchar(50) NOT NULL COMMENT '时间段',
        `device_type` varchar(50) NOT NULL COMMENT '设备类型',
        `status` enum('pending','confirmed','assigned','in_progress','completed','cancelled') NOT NULL DEFAULT 'pending' COMMENT '状态',
        `engineer_id` int(11) DEFAULT NULL COMMENT '工程师ID',
        `payment_status` enum('paid','unpaid') NOT NULL DEFAULT 'unpaid' COMMENT '支付状态',
        `amount` decimal(10,2) DEFAULT '0.00' COMMENT '金额',
        `cancel_reason` varchar(255) DEFAULT NULL COMMENT '取消原因',
        `remark` text COMMENT '备注',
        `referrer_id` int(11) DEFAULT NULL COMMENT '推荐人ID',
        `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
        `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
        PRIMARY KEY (`id`),
        UNIQUE KEY `booking_no` (`booking_no`),
        KEY `user_id` (`user_id`),
        KEY `engineer_id` (`engineer_id`),
        KEY `status` (`status`),
        KEY `booking_date` (`booking_date`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='安装预约表'";

    if (!mysqli_query($conn, $createTableSql)) {
        file_put_contents($logFile, date('Y-m-d H:i:s') . " - 创建表失败: " . mysqli_error($conn) . "\n", FILE_APPEND);
        die("创建表失败: " . mysqli_error($conn));
    }

    file_put_contents($logFile, date('Y-m-d H:i:s') . " - 创建表成功\n", FILE_APPEND);
}

// 检查工程师表是否存在
$checkEngineersTableSql = "SHOW TABLES LIKE 'installation_engineers'";
$result = mysqli_query($conn, $checkEngineersTableSql);

if (mysqli_num_rows($result) == 0) {
    // 创建工程师表
    $createEngineersTableSql = "CREATE TABLE `installation_engineers` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `name` varchar(50) NOT NULL COMMENT '工程师姓名',
        `phone` varchar(20) NOT NULL COMMENT '联系电话',
        `status` enum('active','inactive') NOT NULL DEFAULT 'active' COMMENT '状态',
        `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
        `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
        PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='工程师表'";

    if (!mysqli_query($conn, $createEngineersTableSql)) {
        file_put_contents($logFile, date('Y-m-d H:i:s') . " - 创建工程师表失败: " . mysqli_error($conn) . "\n", FILE_APPEND);
        die("创建工程师表失败: " . mysqli_error($conn));
    }

    file_put_contents($logFile, date('Y-m-d H:i:s') . " - 创建工程师表成功\n", FILE_APPEND);

    // 插入工程师数据
    $engineersData = [
        ['name' => '张工程师', 'phone' => '13800138001'],
        ['name' => '李工程师', 'phone' => '13800138002'],
        ['name' => '王工程师', 'phone' => '13800138003']
    ];

    foreach ($engineersData as $engineer) {
        $insertEngineerSql = "INSERT INTO `installation_engineers` (`name`, `phone`) VALUES (?, ?)";
        $stmt = mysqli_prepare($conn, $insertEngineerSql);
        mysqli_stmt_bind_param($stmt, 'ss', $engineer['name'], $engineer['phone']);

        if (!mysqli_stmt_execute($stmt)) {
            file_put_contents($logFile, date('Y-m-d H:i:s') . " - 插入工程师数据失败: " . mysqli_error($conn) . "\n", FILE_APPEND);
            die("插入工程师数据失败: " . mysqli_error($conn));
        }
    }

    file_put_contents($logFile, date('Y-m-d H:i:s') . " - 插入工程师数据成功\n", FILE_APPEND);
}

// 检查是否已有数据
$checkDataSql = "SELECT COUNT(*) as count FROM install_bookings";
$result = mysqli_query($conn, $checkDataSql);
$row = mysqli_fetch_assoc($result);

if ($row['count'] > 0) {
    file_put_contents($logFile, date('Y-m-d H:i:s') . " - 表中已有数据，跳过插入\n", FILE_APPEND);
    echo "表中已有数据，跳过插入";
    exit;
}

// 准备种子数据
$seedData = [
    [
        'booking_no' => 'BK20250401001',
        'user_id' => 1,
        'contact_name' => '张三',
        'contact_phone' => '13800138000',
        'install_address' => '北京市朝阳区建国路88号',
        'install_time' => '2025-04-10 09:00:00',
        'package_type' => 'personal',
        'package_price' => 199.00,
        'installation_fee' => 120.00,
        'total_amount' => 319.00,
        'status' => 'pending',
        'engineer_id' => null,
        'payment_status' => 'unpaid',
        'payment_time' => null,
        'payment_method' => null,
        'transaction_id' => null,
        'cancellation_reason' => null,
        'remarks' => '客户希望尽快安装',
        'referrer_id' => null,
        'device_model' => 'RO-100',
        'created_at' => '2025-04-01 10:30:00',
        'updated_at' => '2025-04-01 10:30:00'
    ],
    [
        'booking_no' => 'BK20250401002',
        'user_id' => 2,
        'contact_name' => '李四',
        'contact_phone' => '***********',
        'install_address' => '上海市浦东新区陆家嘴金融中心',
        'install_time' => '2025-04-11 14:00:00',
        'package_type' => 'business_year',
        'package_price' => 599.00,
        'installation_fee' => 120.00,
        'total_amount' => 719.00,
        'status' => 'confirmed',
        'engineer_id' => null,
        'payment_status' => 'paid',
        'payment_time' => '2025-04-01 12:30:00',
        'payment_method' => 'wechat',
        'transaction_id' => 'wx20250401123000',
        'cancellation_reason' => null,
        'remarks' => '客户要求安装人员提前联系',
        'referrer_id' => null,
        'device_model' => 'RO-200',
        'created_at' => '2025-04-01 11:20:00',
        'updated_at' => '2025-04-01 14:30:00'
    ],
    [
        'booking_no' => 'BK20250402001',
        'user_id' => 3,
        'contact_name' => '王五',
        'contact_phone' => '***********',
        'install_address' => '广州市天河区珠江新城',
        'install_time' => '2025-04-12 09:00:00',
        'package_type' => 'personal',
        'package_price' => 199.00,
        'installation_fee' => 120.00,
        'total_amount' => 319.00,
        'status' => 'assigned',
        'engineer_id' => 1,
        'payment_status' => 'paid',
        'payment_time' => '2025-04-02 10:15:00',
        'payment_method' => 'alipay',
        'transaction_id' => 'ali20250402101500',
        'cancellation_reason' => null,
        'remarks' => '客户家里有宠物，请工程师注意',
        'referrer_id' => null,
        'device_model' => 'RO-100',
        'created_at' => '2025-04-02 09:15:00',
        'updated_at' => '2025-04-02 10:30:00'
    ],
    [
        'booking_no' => 'BK20250402002',
        'user_id' => 4,
        'contact_name' => '赵六',
        'contact_phone' => '***********',
        'install_address' => '深圳市南山区科技园',
        'install_time' => '2025-04-13 14:00:00',
        'package_type' => 'business_flow',
        'package_price' => 799.00,
        'installation_fee' => 120.00,
        'total_amount' => 919.00,
        'status' => 'completed',
        'engineer_id' => 2,
        'payment_status' => 'paid',
        'payment_time' => '2025-04-02 14:30:00',
        'payment_method' => 'wechat',
        'transaction_id' => 'wx20250402143000',
        'completion_time' => '2025-04-03 15:30:00',
        'cancellation_reason' => null,
        'remarks' => '安装顺利完成，客户很满意',
        'referrer_id' => null,
        'device_model' => 'RO-300',
        'rating' => 5,
        'review' => '安装师傅非常专业，服务态度好，安装快速干净',
        'review_time' => '2025-04-03 16:00:00',
        'created_at' => '2025-04-02 13:45:00',
        'updated_at' => '2025-04-03 15:30:00'
    ],
    [
        'booking_no' => 'BK20250403001',
        'user_id' => 5,
        'contact_name' => '孙七',
        'contact_phone' => '***********',
        'install_address' => '成都市锦江区春熙路',
        'install_time' => '2025-04-14 09:00:00',
        'package_type' => 'personal',
        'package_price' => 199.00,
        'installation_fee' => 120.00,
        'total_amount' => 319.00,
        'status' => 'cancelled',
        'engineer_id' => null,
        'payment_status' => 'unpaid',
        'payment_time' => null,
        'payment_method' => null,
        'transaction_id' => null,
        'cancellation_reason' => '客户临时有事，无法安排安装',
        'remarks' => '客户表示稍后会重新预约',
        'referrer_id' => null,
        'device_model' => 'RO-100',
        'created_at' => '2025-04-03 10:20:00',
        'updated_at' => '2025-04-03 14:15:00'
    ],
    [
        'booking_no' => 'BK20250403002',
        'user_id' => 6,
        'contact_name' => '周八',
        'contact_phone' => '13400134000',
        'install_address' => '重庆市渝中区解放碑',
        'install_time' => '2025-04-15 14:00:00',
        'package_type' => 'unlimited',
        'package_price' => 999.00,
        'installation_fee' => 120.00,
        'total_amount' => 1119.00,
        'status' => 'in_progress',
        'engineer_id' => 3,
        'payment_status' => 'paid',
        'payment_time' => '2025-04-03 17:00:00',
        'payment_method' => 'wechat',
        'transaction_id' => 'wx20250403170000',
        'cancellation_reason' => null,
        'remarks' => '工程师已到达现场，正在安装中',
        'referrer_id' => null,
        'device_model' => 'RO-400',
        'created_at' => '2025-04-03 16:30:00',
        'updated_at' => '2025-04-04 09:45:00'
    ]
];

// 插入种子数据
$insertSql = "INSERT INTO `install_bookings` (
    `booking_no`, `user_id`, `contact_name`, `contact_phone`, `install_address`,
    `install_time`, `package_type`, `package_price`, `installation_fee`, `total_amount`,
    `status`, `engineer_id`, `payment_status`, `payment_time`, `payment_method`,
    `transaction_id`, `cancellation_reason`, `remarks`, `referrer_id`,
    `device_model`, `rating`, `review`, `review_time`, `completion_time`,
    `created_at`, `updated_at`
) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

$stmt = mysqli_prepare($conn, $insertSql);

foreach ($seedData as $booking) {
    // 处理可能为空的字段
    $payment_time = $booking['payment_time'] ?? null;
    $payment_method = $booking['payment_method'] ?? null;
    $transaction_id = $booking['transaction_id'] ?? null;
    $cancellation_reason = $booking['cancellation_reason'] ?? null;
    $remarks = $booking['remarks'] ?? null;
    $device_model = $booking['device_model'] ?? null;
    $rating = $booking['rating'] ?? null;
    $review = $booking['review'] ?? null;
    $review_time = $booking['review_time'] ?? null;
    $completion_time = $booking['completion_time'] ?? null;

    mysqli_stmt_bind_param($stmt, 'sissssddddsississssisssss',
        $booking['booking_no'],
        $booking['user_id'],
        $booking['contact_name'],
        $booking['contact_phone'],
        $booking['install_address'],
        $booking['install_time'],
        $booking['package_type'],
        $booking['package_price'],
        $booking['installation_fee'],
        $booking['total_amount'],
        $booking['status'],
        $booking['engineer_id'],
        $booking['payment_status'],
        $payment_time,
        $payment_method,
        $transaction_id,
        $cancellation_reason,
        $remarks,
        $booking['referrer_id'],
        $device_model,
        $rating,
        $review,
        $review_time,
        $completion_time,
        $booking['created_at'],
        $booking['updated_at']
    );

    if (!mysqli_stmt_execute($stmt)) {
        file_put_contents($logFile, date('Y-m-d H:i:s') . " - 插入数据失败: " . mysqli_error($conn) . "\n", FILE_APPEND);
        die("插入数据失败: " . mysqli_error($conn));
    }
}

file_put_contents($logFile, date('Y-m-d H:i:s') . " - 成功插入 " . count($seedData) . " 条数据\n", FILE_APPEND);
echo "成功插入 " . count($seedData) . " 条数据";

// 关闭数据库连接
mysqli_close($conn);
