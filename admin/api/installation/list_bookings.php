<?php
/**
 * 获取用户净水器安装预约列表
 * API路径: /api/installation/list_bookings.php
 * 请求方式: GET
 * 
 * 参数:
 * - page: 页码，默认1
 * - limit: 每页记录数，默认10
 * - status: 预约状态筛选(可选): pending, confirmed, completed, cancelled
 * 
 * 返回:
 * - code: 0表示成功，非0表示失败
 * - message: 提示信息
 * - data: 包含预约列表和分页信息的对象
 */

// // 使用统一的数据库连接文件
require_once __DIR__ . '/../../db.php';

require_once __DIR__ . '/../../api/functions.php'; /* 已注释 */

// 验证用户登录状态
$user = validateToken();
if (!$user) {
    responseError(401, '未登录或登录已过期');
}

// 获取查询参数
$page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
$limit = isset($_GET['limit']) ? min(50, max(1, intval($_GET['limit']))) : 10;
$status = isset($_GET['status']) ? $_GET['status'] : null;
$offset = ($page - 1) * $limit;

// 构建查询
$db = getDbConnection();
$params = [':user_id' => $user['id']];
$whereClauses = ['b.user_id = :user_id'];

// 添加状态筛选
if ($status) {
    $whereClauses[] = "b.status = :status";
    $params[':status'] = $status;
}

// 构建WHERE子句
$whereClause = implode(' AND ', $whereClauses);

// 查询总记录数
$countSql = "SELECT COUNT(*) FROM install_bookings b WHERE $whereClause";
$stmt = $db->prepare($countSql);
foreach ($params as $key => $value) {
    $stmt->bindValue($key, $value);
}
$stmt->execute();
$total = $stmt->fetchColumn();

// 查询预约列表
$sql = "SELECT 
            b.*,
            u.username as referrer_username,
            u.wechat_nickname as referrer_nickname
        FROM 
            install_bookings b
        LEFT JOIN 
            app_users u ON b.referrer_id = u.id
        WHERE 
            $whereClause
        ORDER BY 
            b.created_at DESC
        LIMIT :offset, :limit";

$stmt = $db->prepare($sql);
foreach ($params as $key => $value) {
    $stmt->bindValue($key, $value);
}
$stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
$stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
$stmt->execute();
$bookings = $stmt->fetchAll(PDO::FETCH_ASSOC);

// 处理结果数据
$packageTypeMap = [
    'personal' => '个人套餐',
    'unlimited' => '无限续用套餐',
    'business_year' => '商业年费套餐',
    'business_flow' => '商业流量套餐'
];

$statusMap = [
    'pending' => '待处理',
    'confirmed' => '已确认',
    'completed' => '已完成',
    'cancelled' => '已取消'
];

foreach ($bookings as &$booking) {
    $booking['package_type_text'] = $packageTypeMap[$booking['package_type']] ?? $booking['package_type'];
    $booking['status_text'] = $statusMap[$booking['status']] ?? $booking['status'];
}

// 构建分页信息
$pagination = [
    'total' => (int)$total,
    'page' => $page,
    'limit' => $limit,
    'pages' => ceil($total / $limit)
];

// 返回成功响应
responseSuccess([
    'list' => $bookings,
    'pagination' => $pagination
]);