<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS, PUT, DELETE');
header('Access-Control-Allow-Headers: X-Requested-With, Content-Type, Authorization, X-CSRF-TOKEN');

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 引入配置文件
require_once __DIR__ . '/../config.php';

try {
    // 获取数据库连接
    $pdo = new PDO(
        "mysql:host={$DB_CONFIG['HOST']};dbname={$DB_CONFIG['DATABASE']};charset=utf8mb4",
        $DB_CONFIG['USER'],
        $DB_CONFIG['PASSWORD'],
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4"
        ]
    );
    
    $method = $_SERVER['REQUEST_METHOD'];
    $requestUri = $_SERVER['REQUEST_URI'];
    
    // 解析查询参数
    $queryParams = [];
    if (isset($_SERVER['QUERY_STRING'])) {
        parse_str($_SERVER['QUERY_STRING'], $queryParams);
    }
    
    // 获取POST数据
    $postData = [];
    if ($method === 'POST' || $method === 'PUT') {
        $input = file_get_contents('php://input');
        if ($input) {
            $postData = json_decode($input, true) ?: [];
        }
    }
    
    switch ($method) {
        case 'GET':
            if (strpos($requestUri, '/statistics') !== false || isset($queryParams['statistics'])) {
                // 获取统计信息
                $stmt = $pdo->query("SELECT COUNT(*) as total FROM login_logs");
                $total = $stmt->fetch()['total'];
                
                $stmt = $pdo->query("SELECT COUNT(*) as count FROM login_logs WHERE status = 'success'");
                $successCount = $stmt->fetch()['count'];
                
                $stmt = $pdo->query("SELECT COUNT(*) as count FROM login_logs WHERE status = 'failed'");
                $failedCount = $stmt->fetch()['count'];
                
                $successRate = $total > 0 ? round(($successCount / $total) * 100, 2) : 0;
                
                // 按登录方式统计
                $stmt = $pdo->query("SELECT login_method, COUNT(*) as count FROM login_logs GROUP BY login_method");
                $methodStats = [];
                while ($row = $stmt->fetch()) {
                    $methodStats[$row['login_method']] = $row['count'];
                }
                
                // 按用户类型统计
                $stmt = $pdo->query("SELECT user_type, COUNT(*) as count FROM login_logs GROUP BY user_type");
                $userTypeStats = [];
                while ($row = $stmt->fetch()) {
                    $userTypeStats[$row['user_type']] = $row['count'];
                }
                
                // 今日登录统计
                $stmt = $pdo->query("SELECT COUNT(*) as count FROM login_logs WHERE DATE(created_at) = CURDATE()");
                $todayCount = $stmt->fetch()['count'];
                
                echo json_encode([
                    'code' => 0,
                    'message' => '获取统计信息成功',
                    'data' => [
                        'total' => $total,
                        'success_count' => $successCount,
                        'failed_count' => $failedCount,
                        'success_rate' => $successRate,
                        'today_count' => $todayCount,
                        'method_stats' => $methodStats,
                        'user_type_stats' => $userTypeStats
                    ]
                ]);
            } else {
                // 获取登录日志列表
                $page = intval($queryParams['page'] ?? 1);
                $limit = intval($queryParams['limit'] ?? 15);
                $keyword = $queryParams['keyword'] ?? '';
                $loginMethod = $queryParams['loginMethod'] ?? $queryParams['login_method'] ?? '';
                $status = $queryParams['status'] ?? '';
                $userType = $queryParams['userType'] ?? $queryParams['user_type'] ?? '';
                $dateStart = $queryParams['startDate'] ?? $queryParams['date_start'] ?? '';
                $dateEnd = $queryParams['endDate'] ?? $queryParams['date_end'] ?? '';
                
                $offset = ($page - 1) * $limit;
                
                // 构建查询
                $sql = "SELECT 
                    l.*,
                    COALESCE(au.name, au.wechat_nickname, adu.name) as user_name,
                    COALESCE(au.phone, adu.phone) as user_phone
                FROM login_logs l
                LEFT JOIN app_users au ON l.user_id = au.id AND l.user_type = 'app_user'
                LEFT JOIN admin_users adu ON l.user_id = adu.id AND l.user_type = 'admin_user'
                WHERE 1=1";
                
                $params = [];
                
                // 添加搜索条件
                if ($keyword) {
                    $sql .= " AND (l.ip_address LIKE ? OR au.name LIKE ? OR au.wechat_nickname LIKE ? OR au.phone LIKE ? OR adu.name LIKE ? OR adu.phone LIKE ?)";
                    $keywordParam = "%{$keyword}%";
                    $params = array_merge($params, [$keywordParam, $keywordParam, $keywordParam, $keywordParam, $keywordParam, $keywordParam]);
                }
                
                if ($loginMethod) {
                    $sql .= " AND l.login_method = ?";
                    $params[] = $loginMethod;
                }
                
                if ($status) {
                    $sql .= " AND l.status = ?";
                    $params[] = $status;
                }
                
                if ($userType) {
                    $sql .= " AND l.user_type = ?";
                    $params[] = $userType;
                }
                
                if ($dateStart) {
                    $sql .= " AND l.created_at >= ?";
                    $params[] = $dateStart . ' 00:00:00';
                }
                
                if ($dateEnd) {
                    $sql .= " AND l.created_at <= ?";
                    $params[] = $dateEnd . ' 23:59:59';
                }
                
                // 获取总数
                $countSql = "SELECT COUNT(*) as total FROM (" . $sql . ") as count_query";
                $stmt = $pdo->prepare($countSql);
                $stmt->execute($params);
                $total = $stmt->fetch()['total'];
                
                // 获取分页数据
                $sql .= " ORDER BY l.created_at DESC LIMIT $limit OFFSET $offset";
                
                $stmt = $pdo->prepare($sql);
                $stmt->execute($params);
                $logs = $stmt->fetchAll();
                
                // 格式化数据
                $formattedLogs = array_map(function($log) {
                    return [
                        'id' => $log['id'],
                        'user_id' => $log['user_id'],
                        'user_type' => $log['user_type'],
                        'user_name' => $log['user_name'] ?: '未知用户',
                        'user_phone' => $log['user_phone'],
                        'login_method' => $log['login_method'],
                        'ip_address' => $log['ip_address'],
                        'user_agent' => $log['user_agent'],
                        'status' => $log['status'],
                        'message' => $log['message'],
                        'created_at' => $log['created_at'],
                        'updated_at' => $log['updated_at']
                    ];
                }, $logs);
                
                echo json_encode([
                    'code' => 0,
                    'message' => '获取登录日志成功',
                    'data' => [
                        'list' => $formattedLogs,
                        'total' => $total,
                        'page' => $page,
                        'limit' => $limit,
                        'pages' => ceil($total / $limit)
                    ]
                ]);
            }
            break;
            
        case 'DELETE':
            // 批量删除日志
            $ids = $postData['ids'] ?? [];
            if (empty($ids)) {
                echo json_encode([
                    'code' => 400,
                    'message' => '请选择要删除的日志',
                    'data' => null
                ]);
                break;
            }
            
            $placeholders = str_repeat('?,', count($ids) - 1) . '?';
            $sql = "DELETE FROM login_logs WHERE id IN ($placeholders)";
            $stmt = $pdo->prepare($sql);
            $stmt->execute($ids);
            $deleted = $stmt->rowCount();
            
            echo json_encode([
                'code' => 0,
                'message' => "成功删除 {$deleted} 条日志",
                'data' => ['deleted_count' => $deleted]
            ]);
            break;
            
        case 'POST':
            if (strpos($requestUri, '/cleanup') !== false) {
                // 清理过期日志
                $days = intval($postData['days'] ?? 30);
                $cutoffDate = date('Y-m-d H:i:s', strtotime("-{$days} days"));
                
                $sql = "DELETE FROM login_logs WHERE created_at < ?";
                $stmt = $pdo->prepare($sql);
                $stmt->execute([$cutoffDate]);
                $deleted = $stmt->rowCount();
                
                echo json_encode([
                    'code' => 0,
                    'message' => "成功清理 {$deleted} 条过期日志",
                    'data' => ['deleted_count' => $deleted, 'cutoff_date' => $cutoffDate]
                ]);
            } else {
                echo json_encode([
                    'code' => 405,
                    'message' => '不支持的操作',
                    'data' => null
                ]);
            }
            break;
            
        default:
            echo json_encode([
                'code' => 405,
                'message' => '不支持的请求方法',
                'data' => null
            ]);
            break;
    }
    
} catch (Exception $e) {
    echo json_encode([
        'code' => 500,
        'message' => '服务器错误: ' . $e->getMessage(),
        'data' => null
    ]);
}
?> 