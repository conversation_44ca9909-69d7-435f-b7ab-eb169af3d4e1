<?php
/**
 * 微信支付日志API
 * 用于记录微信支付过程中的日志
 */

// 引入必要的文件
require_once __DIR__ . '/logger.php';
require_once __DIR__ . '/../config.php';

// 设置响应头
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 只允许POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['code' => 1, 'message' => 'Method Not Allowed']);
    exit;
}

// 获取请求体
$requestBody = file_get_contents('php://input');
$data = json_decode($requestBody, true);

// 验证请求数据
if (!$data || !isset($data['event']) || !isset($data['message'])) {
    http_response_code(400);
    echo json_encode(['code' => 1, 'message' => 'Invalid request data']);
    exit;
}

// 提取日志数据
$event = $data['event'];
$message = $data['message'];
$context = isset($data['context']) ? $data['context'] : [];
$level = isset($data['level']) ? strtoupper($data['level']) : 'INFO';

// 验证日志级别
$validLevels = ['DEBUG', 'INFO', 'WARNING', 'ERROR'];
if (!in_array($level, $validLevels)) {
    $level = 'INFO';
}

// 记录日志
$logMethod = strtolower($level);
$result = Logger::$logMethod(Logger::WECHAT_PAYMENT, "[{$event}] {$message}", $context);

// 返回结果
if ($result) {
    echo json_encode(['code' => 0, 'message' => 'Log recorded successfully']);
} else {
    http_response_code(500);
    echo json_encode(['code' => 1, 'message' => 'Failed to record log']);
}
