<?php
/**
 * 日志工具类
 * 用于记录系统运行日志，特别是微信相关操作的日志
 */
class Logger {
    // 日志级别
    const DEBUG = 'DEBUG';
    const INFO = 'INFO';
    const WARNING = 'WARNING';
    const ERROR = 'ERROR';
    
    // 日志类型
    const WECHAT_ADDRESS = 'wechat_address';
    const WECHAT_PAYMENT = 'wechat_payment';
    const SYSTEM = 'system';
    
    /**
     * 写入日志
     * 
     * @param string $type 日志类型
     * @param string $level 日志级别
     * @param string $message 日志消息
     * @param array $context 上下文数据
     * @return bool 是否写入成功
     */
    public static function log($type, $level, $message, $context = []) {
        // 获取日志目录
        $logDir = __DIR__;
        
        // 确保日志目录存在且可写
        if (!is_dir($logDir) || !is_writable($logDir)) {
            error_log("Logger: Log directory does not exist or is not writable: $logDir");
            return false;
        }
        
        // 根据类型确定日志文件名
        $filename = self::getLogFilename($type);
        $logFile = $logDir . '/' . $filename;
        
        // 格式化日志内容
        $logEntry = self::formatLogEntry($level, $message, $context);
        
        // 写入日志
        $result = file_put_contents($logFile, $logEntry, FILE_APPEND);
        
        return ($result !== false);
    }
    
    /**
     * 记录调试日志
     * 
     * @param string $type 日志类型
     * @param string $message 日志消息
     * @param array $context 上下文数据
     * @return bool 是否写入成功
     */
    public static function debug($type, $message, $context = []) {
        return self::log($type, self::DEBUG, $message, $context);
    }
    
    /**
     * 记录信息日志
     * 
     * @param string $type 日志类型
     * @param string $message 日志消息
     * @param array $context 上下文数据
     * @return bool 是否写入成功
     */
    public static function info($type, $message, $context = []) {
        return self::log($type, self::INFO, $message, $context);
    }
    
    /**
     * 记录警告日志
     * 
     * @param string $type 日志类型
     * @param string $message 日志消息
     * @param array $context 上下文数据
     * @return bool 是否写入成功
     */
    public static function warning($type, $message, $context = []) {
        return self::log($type, self::WARNING, $message, $context);
    }
    
    /**
     * 记录错误日志
     * 
     * @param string $type 日志类型
     * @param string $message 日志消息
     * @param array $context 上下文数据
     * @return bool 是否写入成功
     */
    public static function error($type, $message, $context = []) {
        return self::log($type, self::ERROR, $message, $context);
    }
    
    /**
     * 获取日志文件名
     * 
     * @param string $type 日志类型
     * @return string 日志文件名
     */
    private static function getLogFilename($type) {
        $date = date('Y-m-d');
        return "{$type}_{$date}.log";
    }
    
    /**
     * 格式化日志条目
     * 
     * @param string $level 日志级别
     * @param string $message 日志消息
     * @param array $context 上下文数据
     * @return string 格式化后的日志条目
     */
    private static function formatLogEntry($level, $message, $context) {
        $timestamp = date('Y-m-d H:i:s');
        $ip = self::getClientIP();
        $userAgent = isset($_SERVER['HTTP_USER_AGENT']) ? $_SERVER['HTTP_USER_AGENT'] : 'Unknown';
        
        // 格式化上下文数据
        $contextStr = '';
        if (!empty($context)) {
            // 移除敏感信息
            $sanitizedContext = self::sanitizeContext($context);
            $contextStr = json_encode($sanitizedContext, JSON_UNESCAPED_UNICODE);
        }
        
        // 构建日志条目
        $entry = "[$timestamp] [$ip] [$level] $message";
        if (!empty($contextStr)) {
            $entry .= " Context: $contextStr";
        }
        $entry .= " UserAgent: $userAgent" . PHP_EOL;
        
        return $entry;
    }
    
    /**
     * 获取客户端IP地址
     * 
     * @return string 客户端IP地址
     */
    private static function getClientIP() {
        $ip = 'Unknown';
        
        if (!empty($_SERVER['HTTP_CLIENT_IP'])) {
            $ip = $_SERVER['HTTP_CLIENT_IP'];
        } elseif (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
            $ip = $_SERVER['HTTP_X_FORWARDED_FOR'];
        } elseif (!empty($_SERVER['REMOTE_ADDR'])) {
            $ip = $_SERVER['REMOTE_ADDR'];
        }
        
        return $ip;
    }
    
    /**
     * 清理上下文数据，移除敏感信息
     * 
     * @param array $context 上下文数据
     * @return array 清理后的上下文数据
     */
    private static function sanitizeContext($context) {
        $sensitiveKeys = ['password', 'token', 'secret', 'key', 'auth', 'credential', 'paySign'];
        $sanitized = [];
        
        foreach ($context as $key => $value) {
            // 检查是否是敏感字段
            $isSensitive = false;
            foreach ($sensitiveKeys as $sensitiveKey) {
                if (stripos($key, $sensitiveKey) !== false) {
                    $isSensitive = true;
                    break;
                }
            }
            
            if ($isSensitive) {
                // 对敏感字段进行脱敏处理
                if (is_string($value) && strlen($value) > 4) {
                    $sanitized[$key] = substr($value, 0, 2) . '***' . substr($value, -2);
                } else {
                    $sanitized[$key] = '***';
                }
            } elseif (is_array($value)) {
                // 递归处理嵌套数组
                $sanitized[$key] = self::sanitizeContext($value);
            } else {
                $sanitized[$key] = $value;
            }
        }
        
        return $sanitized;
    }
}
