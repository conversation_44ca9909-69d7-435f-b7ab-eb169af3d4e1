<?php
require_once __DIR__ . '/../functions/init.php';
require_once __DIR__ . '/../functions/auth.php';

use App\Models\Merchant;
use App\Models\MerchantTrade;

// 验证请求
$auth = checkAuth();
if (!$auth['success']) {
    echoJsonError($auth['message'], $auth['code']);
    exit;
}

// 获取用户
$user = $auth['user'];

// 获取查询参数
$merchantId = isset($_GET['merchant_id']) ? $_GET['merchant_id'] : null;
$page = isset($_GET['page']) ? intval($_GET['page']) : 1;
$limit = isset($_GET['limit']) ? intval($_GET['limit']) : 10;
$status = isset($_GET['status']) ? $_GET['status'] : null;
$tradeType = isset($_GET['trade_type']) ? $_GET['trade_type'] : null;
$paymentMethod = isset($_GET['payment_method']) ? $_GET['payment_method'] : null;
$startDate = isset($_GET['start_date']) ? $_GET['start_date'] : null;
$endDate = isset($_GET['end_date']) ? $_GET['end_date'] : null;
$keyword = isset($_GET['keyword']) ? $_GET['keyword'] : null;
$sortField = isset($_GET['sort_field']) ? $_GET['sort_field'] : 'pay_time';
$sortOrder = isset($_GET['sort_order']) ? $_GET['sort_order'] : 'desc';

// 验证参数
if ($page < 1) $page = 1;
if ($limit < 1 || $limit > 100) $limit = 10;

// 允许的排序字段
$allowedSortFields = ['id', 'pay_time', 'amount', 'status'];
if (!in_array($sortField, $allowedSortFields)) {
    $sortField = 'pay_time';
}

// 允许的排序方向
$allowedSortOrders = ['asc', 'desc'];
if (!in_array($sortOrder, $allowedSortOrders)) {
    $sortOrder = 'desc';
}

try {
    // 构建查询
    $query = MerchantTrade::query();
    
    // 应用商户ID过滤
    if ($merchantId) {
        $query->where('merchant_id', $merchantId);
    }
    
    // 应用过滤条件
    if ($status) {
        $query->where('status', $status);
    }
    
    if ($tradeType) {
        $query->where('trade_type', $tradeType);
    }
    
    if ($paymentMethod) {
        $query->where('payment_method', $paymentMethod);
    }
    
    if ($startDate) {
        $query->where('pay_time', '>=', $startDate . ' 00:00:00');
    }
    
    if ($endDate) {
        $query->where('pay_time', '<=', $endDate . ' 23:59:59');
    }
    
    if ($keyword) {
        $query->where(function($q) use ($keyword) {
            $q->where('trade_no', 'like', "%{$keyword}%")
              ->orWhere('merchant_name', 'like', "%{$keyword}%")
              ->orWhere('payer_name', 'like', "%{$keyword}%")
              ->orWhere('payer_account', 'like', "%{$keyword}%")
              ->orWhere('transaction_id', 'like', "%{$keyword}%");
        });
    }
    
    // 获取总记录数
    $total = $query->count();
    
    // 计算汇总数据
    $summary = [
        'total_count' => $total,
        'total_amount' => floatval($query->sum('amount')),
        'total_fee' => floatval($query->sum('fee')),
        'total_actual_amount' => floatval($query->sum('actual_amount')),
    ];
    
    // 应用排序和分页
    $trades = $query->orderBy($sortField, $sortOrder)
                   ->skip(($page - 1) * $limit)
                   ->take($limit)
                   ->get();
    
    // 格式化数据
    $items = [];
    foreach ($trades as $trade) {
        $items[] = [
            'id' => $trade->id,
            'trade_no' => $trade->trade_no,
            'merchant_id' => $trade->merchant_id,
            'merchant_name' => $trade->merchant_name,
            'payment_method' => $trade->payment_method,
            'payment_method_text' => $trade->payment_method_text,
            'amount' => floatval($trade->amount),
            'fee' => floatval($trade->fee),
            'actual_amount' => floatval($trade->actual_amount),
            'trade_type' => $trade->trade_type,
            'trade_type_text' => $trade->trade_type_text,
            'status' => $trade->status,
            'status_text' => $trade->status_text,
            'pay_time' => $trade->pay_time ? $trade->pay_time->format('Y-m-d H:i:s') : null,
            'payer_account' => $trade->payer_account,
            'payer_name' => $trade->payer_name,
            'transaction_id' => $trade->transaction_id,
            'remark' => $trade->remark,
            'created_at' => $trade->created_at ? $trade->created_at->format('Y-m-d H:i:s') : null,
        ];
    }
    
    // 返回数据
    echoJsonSuccess([
        'total' => $total,
        'page' => $page,
        'limit' => $limit,
        'summary' => $summary,
        'items' => $items
    ]);
} catch (Exception $e) {
    echoJsonError('获取交易记录失败: ' . $e->getMessage());
} 