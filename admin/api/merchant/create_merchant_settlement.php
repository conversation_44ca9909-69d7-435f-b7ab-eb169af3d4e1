<?php
require_once __DIR__ . '/../functions/init.php';
require_once __DIR__ . '/../functions/auth.php';

use App\Models\Merchant;
use App\Models\MerchantSettlement;
use App\Models\MerchantLog;

// 验证请求
$auth = checkAuth();
if (!$auth['success']) {
    echoJsonError($auth['message'], $auth['code']);
    exit;
}

// 获取用户
$user = $auth['user'];

// 验证请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echoJsonError('Invalid request method', 405);
    exit;
}

// 获取请求数据
$data = json_decode(file_get_contents('php://input'), true);

// 验证必填字段
$requiredFields = ['merchant_id', 'amount'];
foreach ($requiredFields as $field) {
    if (empty($data[$field])) {
        echoJsonError("Field {$field} is required");
        exit;
    }
}

try {
    // 获取商户信息
    $merchant = Merchant::find($data['merchant_id']);
    if (!$merchant) {
        echoJsonError('Merchant not found');
        exit;
    }

    // 验证金额
    $amount = floatval($data['amount']);
    if ($amount <= 0) {
        echoJsonError('Settlement amount must be greater than 0');
        exit;
    }

    // 生成结算单号
    $settlementNo = 'S' . date('YmdHis') . rand(1000, 9999);

    // 创建结算记录
    $settlement = new MerchantSettlement();
    $settlement->settlement_no = $settlementNo;
    $settlement->merchant_id = $merchant->id;
    $settlement->amount = $amount;
    $settlement->status = 'pending';
    $settlement->bank_name = $data['bank_name'] ?? $merchant->bank_name;
    $settlement->bank_branch = $data['bank_branch'] ?? $merchant->bank_branch;
    $settlement->bank_account_name = $data['bank_account_name'] ?? $merchant->bank_account_name;
    $settlement->bank_account_no = $data['bank_account_no'] ?? $merchant->bank_account_no;
    $settlement->remarks = $data['remarks'] ?? '';
    $settlement->save();

    // 记录操作日志
    MerchantLog::create([
        'merchant_id' => $merchant->id,
        'user_id' => $user->id,
        'user_type' => 'admin',
        'action' => 'create_settlement',
        'description' => "创建结算记录：{$settlementNo}，金额：{$amount}元",
        'ip' => $_SERVER['REMOTE_ADDR'],
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? ''
    ]);

    // 返回成功
    echoJsonSuccess([
        'id' => $settlement->id,
        'settlement_no' => $settlement->settlement_no
    ]);
} catch (Exception $e) {
    echoJsonError('创建结算记录失败: ' . $e->getMessage());
} 