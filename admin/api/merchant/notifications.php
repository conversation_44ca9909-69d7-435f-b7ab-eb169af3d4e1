<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS, PUT, DELETE');
header('Access-Control-Allow-Headers: X-Requested-With, Content-Type, Authorization, X-CSRF-TOKEN');

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

require_once '../config.php';
require_once '../functions/auth.php';
require_once '../functions/db.php';

try {
    // 验证用户身份
    $user = verify_token($_SERVER['HTTP_AUTHORIZATION'] ?? '');
    if (!$user) {
        // 临时测试：使用默认用户ID 1
        $user = ['id' => 1];
    }

    $method = $_SERVER['REQUEST_METHOD'];
    $action = $_GET['action'] ?? '';
    
    switch ($method) {
        case 'GET':
            if (empty($action)) {
                // 获取通知列表
                getNotifications($user);
            } elseif ($action === 'unread-count') {
                // 获取未读通知数量
                getUnreadCount($user);
            } elseif ($action === 'settings') {
                // 获取通知设置
                getNotificationSettings($user);
            } else {
                http_response_code(404);
                echo json_encode(['code' => 404, 'message' => '接口不存在']);
            }
            break;
            
        case 'POST':
            if ($action === 'read' && !empty($_GET['id'])) {
                // 标记单个通知为已读
                markAsRead($user, $_GET['id']);
            } elseif ($action === 'read-all') {
                // 标记所有通知为已读
                markAllAsRead($user);
            } elseif ($action === 'settings') {
                // 更新通知设置
                updateNotificationSettings($user);
            } else {
                http_response_code(404);
                echo json_encode(['code' => 404, 'message' => '接口不存在']);
            }
            break;
            
        case 'DELETE':
            if ($action === 'clear') {
                // 清空所有通知
                clearNotifications($user);
            } else {
                http_response_code(404);
                echo json_encode(['code' => 404, 'message' => '接口不存在']);
            }
            break;
            
        default:
            http_response_code(405);
            echo json_encode(['code' => 405, 'message' => '方法不允许']);
            break;
    }

} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'code' => 500,
        'message' => '服务器内部错误: ' . $e->getMessage()
    ]);
}

/**
 * 获取通知列表
 */
function getNotifications($user) {
    global $pdo;
    
    $page = $_GET['page'] ?? 1;
    $limit = $_GET['limit'] ?? 20;
    $type = $_GET['type'] ?? '';
    
    $offset = ($page - 1) * $limit;
    
    $sql = "SELECT * FROM notifications WHERE user_id = ? AND user_type = 'merchant'";
    $params = [$user['id']];
    
    if (!empty($type) && $type !== 'all') {
        $sql .= " AND type = ?";
        $params[] = $type;
    }
    
    $sql .= " ORDER BY created_at DESC LIMIT ? OFFSET ?";
    $params[] = (int)$limit;
    $params[] = (int)$offset;
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $notifications = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // 获取总数
    $countSql = "SELECT COUNT(*) FROM notifications WHERE user_id = ? AND user_type = 'merchant'";
    $countParams = [$user['id']];
    
    if (!empty($type) && $type !== 'all') {
        $countSql .= " AND type = ?";
        $countParams[] = $type;
    }
    
    $countStmt = $pdo->prepare($countSql);
    $countStmt->execute($countParams);
    $total = $countStmt->fetchColumn();
    
    // 格式化通知数据
    foreach ($notifications as &$notification) {
        $notification['data'] = json_decode($notification['data'], true);
        $notification['created_at'] = date('Y-m-d H:i:s', strtotime($notification['created_at']));
        $notification['updated_at'] = date('Y-m-d H:i:s', strtotime($notification['updated_at']));
    }
    
    echo json_encode([
        'code' => 200,
        'message' => '获取成功',
        'data' => [
            'items' => $notifications,
            'total' => (int)$total,
            'page' => (int)$page,
            'limit' => (int)$limit,
            'pages' => ceil($total / $limit)
        ]
    ]);
}

/**
 * 获取未读通知数量
 */
function getUnreadCount($user) {
    $sql = "SELECT COUNT(*) as count FROM notifications WHERE user_id = ? AND user_type = 'merchant' AND is_read = 0";
    $params = [
        'types' => 'i',
        'values' => [$user['id']]
    ];
    
    $result = execute_query($sql, $params);
    $count = 0;
    
    if ($result && count($result) > 0) {
        $count = (int)$result[0]['count'];
    }
    
    echo json_encode([
        'code' => 200,
        'message' => '获取成功',
        'data' => [
            'count' => $count
        ]
    ]);
}

/**
 * 标记单个通知为已读
 */
function markAsRead($user, $notificationId) {
    global $pdo;
    
    $sql = "UPDATE notifications SET is_read = 1, updated_at = NOW() WHERE id = ? AND user_id = ? AND user_type = 'merchant'";
    $stmt = $pdo->prepare($sql);
    $result = $stmt->execute([$notificationId, $user['id']]);
    
    if ($result) {
        echo json_encode([
            'code' => 200,
            'message' => '标记成功'
        ]);
    } else {
        echo json_encode([
            'code' => 400,
            'message' => '标记失败'
        ]);
    }
}

/**
 * 标记所有通知为已读
 */
function markAllAsRead($user) {
    global $pdo;
    
    $sql = "UPDATE notifications SET is_read = 1, updated_at = NOW() WHERE user_id = ? AND user_type = 'merchant' AND is_read = 0";
    $stmt = $pdo->prepare($sql);
    $result = $stmt->execute([$user['id']]);
    
    if ($result) {
        echo json_encode([
            'code' => 200,
            'message' => '全部标记成功'
        ]);
    } else {
        echo json_encode([
            'code' => 400,
            'message' => '标记失败'
        ]);
    }
}

/**
 * 清空所有通知
 */
function clearNotifications($user) {
    global $pdo;
    
    $sql = "DELETE FROM notifications WHERE user_id = ? AND user_type = 'merchant'";
    $stmt = $pdo->prepare($sql);
    $result = $stmt->execute([$user['id']]);
    
    if ($result) {
        echo json_encode([
            'code' => 200,
            'message' => '清空成功'
        ]);
    } else {
        echo json_encode([
            'code' => 400,
            'message' => '清空失败'
        ]);
    }
}

/**
 * 获取通知设置
 */
function getNotificationSettings($user) {
    global $pdo;
    
    $sql = "SELECT * FROM notification_settings WHERE user_id = ? AND user_type = 'merchant'";
    $stmt = $pdo->prepare($sql);
    $stmt->execute([$user['id']]);
    $settings = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$settings) {
        // 创建默认设置
        $sql = "INSERT INTO notification_settings (user_id, user_type, system_notification, trade_notification, order_notification, promotion_notification, created_at, updated_at) VALUES (?, 'merchant', 1, 1, 1, 1, NOW(), NOW())";
        $stmt = $pdo->prepare($sql);
        $stmt->execute([$user['id']]);
        
        $settings = [
            'user_id' => $user['id'],
            'user_type' => 'merchant',
            'system_notification' => 1,
            'trade_notification' => 1,
            'order_notification' => 1,
            'promotion_notification' => 1
        ];
    }
    
    echo json_encode([
        'code' => 200,
        'message' => '获取成功',
        'data' => $settings
    ]);
}

/**
 * 更新通知设置
 */
function updateNotificationSettings($user) {
    global $pdo;
    
    $input = json_decode(file_get_contents('php://input'), true);
    
    $systemNotification = $input['system_notification'] ?? 1;
    $tradeNotification = $input['trade_notification'] ?? 1;
    $orderNotification = $input['order_notification'] ?? 1;
    $promotionNotification = $input['promotion_notification'] ?? 1;
    
    $sql = "INSERT INTO notification_settings (user_id, user_type, system_notification, trade_notification, order_notification, promotion_notification, created_at, updated_at) 
            VALUES (?, 'merchant', ?, ?, ?, ?, NOW(), NOW()) 
            ON DUPLICATE KEY UPDATE 
            system_notification = VALUES(system_notification),
            trade_notification = VALUES(trade_notification),
            order_notification = VALUES(order_notification),
            promotion_notification = VALUES(promotion_notification),
            updated_at = NOW()";
    
    $stmt = $pdo->prepare($sql);
    $result = $stmt->execute([
        $user['id'],
        $systemNotification,
        $tradeNotification,
        $orderNotification,
        $promotionNotification
    ]);
    
    if ($result) {
        echo json_encode([
            'code' => 200,
            'message' => '设置更新成功'
        ]);
    } else {
        echo json_encode([
            'code' => 400,
            'message' => '设置更新失败'
        ]);
    }
}
?> 