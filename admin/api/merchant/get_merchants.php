<?php
require_once __DIR__ . '/../functions/init.php';
require_once __DIR__ . '/../functions/auth.php';

use App\Models\Merchant;

// 验证请求
$auth = checkAuth();
if (!$auth['success']) {
    echoJsonError($auth['message'], $auth['code']);
    exit;
}

// 获取用户
$user = $auth['user'];

// 获取查询参数
$page = isset($_GET['page']) ? intval($_GET['page']) : 1;
$limit = isset($_GET['limit']) ? intval($_GET['limit']) : 10;
$status = isset($_GET['status']) ? $_GET['status'] : null;
$keyword = isset($_GET['keyword']) ? $_GET['keyword'] : null;
$sortField = isset($_GET['sort_field']) ? $_GET['sort_field'] : 'created_at';
$sortOrder = isset($_GET['sort_order']) ? $_GET['sort_order'] : 'desc';

// 验证参数
if ($page < 1) $page = 1;
if ($limit < 1 || $limit > 100) $limit = 10;

// 允许的排序字段
$allowedSortFields = ['id', 'name', 'status', 'balance', 'created_at', 'updated_at'];
if (!in_array($sortField, $allowedSortFields)) {
    $sortField = 'created_at';
}

// 允许的排序方向
$allowedSortOrders = ['asc', 'desc'];
if (!in_array($sortOrder, $allowedSortOrders)) {
    $sortOrder = 'desc';
}

try {
    // 构建查询
    $query = Merchant::query();
    
    // 应用过滤条件
    if ($status) {
        $query->where('status', $status);
    }
    
    if ($keyword) {
        $query->where(function($q) use ($keyword) {
            $q->where('name', 'like', "%{$keyword}%")
              ->orWhere('merchant_id', 'like', "%{$keyword}%")
              ->orWhere('principal_name', 'like', "%{$keyword}%")
              ->orWhere('principal_mobile', 'like', "%{$keyword}%");
        });
    }
    
    // 获取总记录数
    $total = $query->count();
    
    // 应用排序和分页
    $merchants = $query->orderBy($sortField, $sortOrder)
                      ->skip(($page - 1) * $limit)
                      ->take($limit)
                      ->get();
    
    // 格式化数据
    $items = [];
    foreach ($merchants as $merchant) {
        // 获取交易统计
        $tradeStats = [
            'total_count' => $merchant->trades()->where('status', 'success')->count(),
            'total_amount' => floatval($merchant->trades()->where('status', 'success')->sum('amount')),
            'today_amount' => floatval($merchant->trades()->where('status', 'success')->whereDate('pay_time', date('Y-m-d'))->sum('amount')),
            'month_amount' => floatval($merchant->trades()->where('status', 'success')->whereMonth('pay_time', date('m'))->whereYear('pay_time', date('Y'))->sum('amount')),
        ];

        $items[] = [
            'id' => $merchant->id,
            'merchant_id' => $merchant->merchant_id,
            'name' => $merchant->name,
            'principal_name' => $merchant->principal_name,
            'principal_mobile' => $merchant->principal_mobile,
            'logo' => $merchant->logo,
            'status' => $merchant->status,
            'status_text' => $merchant->status_text,
            'fee_rate' => floatval($merchant->fee_rate),
            'trade_stats' => $tradeStats,
            'has_app_user' => !empty($merchant->app_user_id),
            'created_at' => $merchant->created_at ? $merchant->created_at->format('Y-m-d H:i:s') : null,
            'updated_at' => $merchant->updated_at ? $merchant->updated_at->format('Y-m-d H:i:s') : null,
        ];
    }
    
    // 返回数据
    echoJsonSuccess([
        'total' => $total,
        'page' => $page,
        'limit' => $limit,
        'items' => $items
    ]);
} catch (Exception $e) {
    echoJsonError('获取商户列表失败: ' . $e->getMessage());
} 