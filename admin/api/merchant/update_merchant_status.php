<?php
require_once __DIR__ . '/../functions/init.php';
require_once __DIR__ . '/../functions/auth.php';

use App\Models\Merchant;
use App\Models\MerchantLog;

// 验证请求
$auth = checkAuth();
if (!$auth['success']) {
    echoJsonError($auth['message'], $auth['code']);
    exit;
}

// 获取用户
$user = $auth['user'];

// 验证请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echoJsonError('Invalid request method', 405);
    exit;
}

// 获取请求数据
$data = json_decode(file_get_contents('php://input'), true);

// 验证必填字段
if (empty($data['id'])) {
    echoJsonError('Merchant ID is required');
    exit;
}

if (empty($data['status'])) {
    echoJsonError('Status is required');
    exit;
}

try {
    // 获取商户信息
    $merchant = Merchant::find($data['id']);
    if (!$merchant) {
        echoJsonError('Merchant not found');
        exit;
    }

    // 验证状态值
    $allowedStatus = ['pending', 'active', 'suspended', 'terminated'];
    if (!in_array($data['status'], $allowedStatus)) {
        echoJsonError('Invalid status value');
        exit;
    }

    // 如果状态没有变化，直接返回成功
    if ($merchant->status === $data['status']) {
        echoJsonSuccess(['id' => $merchant->id]);
        exit;
    }

    // 获取状态中文描述
    $statusText = [
        'pending' => '待审核',
        'active' => '正常',
        'suspended' => '已暂停',
        'terminated' => '已终止'
    ];

    // 更新状态
    $oldStatus = $merchant->status;
    $merchant->status = $data['status'];
    $merchant->save();

    // 记录操作日志
    MerchantLog::create([
        'merchant_id' => $merchant->id,
        'user_id' => $user->id,
        'user_type' => 'admin',
        'action' => 'update_status',
        'description' => "更新商户状态：{$statusText[$oldStatus]} -> {$statusText[$data['status']]}",
        'ip' => $_SERVER['REMOTE_ADDR'],
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? ''
    ]);

    // 返回成功
    echoJsonSuccess([
        'id' => $merchant->id,
        'old_status' => $oldStatus,
        'new_status' => $data['status']
    ]);
} catch (Exception $e) {
    echoJsonError('更新商户状态失败: ' . $e->getMessage());
} 