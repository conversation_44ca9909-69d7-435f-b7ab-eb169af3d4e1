<?php
require_once __DIR__ . '/../functions/init.php';
require_once __DIR__ . '/../functions/auth.php';

use App\Models\Merchant;
use App\Models\AppUser;

// 验证请求
$auth = checkAuth();
if (!$auth['success']) {
    echoJsonError($auth['message'], $auth['code']);
    exit;
}

// 获取用户
$user = $auth['user'];

// 获取商户ID参数
if (!isset($_GET['id']) || empty($_GET['id'])) {
    echoJsonError('缺少商户ID参数');
    exit;
}

$id = $_GET['id'];

try {
    // 根据ID查询商户
    $merchant = Merchant::find($id);
    
    if (!$merchant) {
        echoJsonError('商户不存在');
        exit;
    }
    
    // 获取关联的APP用户信息
    $appUser = null;
    if ($merchant->app_user_id) {
        $appUser = AppUser::find($merchant->app_user_id);
    }
    
    // 获取月交易统计
    $currentMonth = date('Y-m-01');
    $monthlyStats = [
        'trade_count' => $merchant->trades()
            ->where('status', 'success')
            ->where('pay_time', '>=', $currentMonth)
            ->count(),
        'trade_amount' => floatval($merchant->trades()
            ->where('status', 'success')
            ->where('pay_time', '>=', $currentMonth)
            ->sum('amount')),
        'income' => floatval($merchant->trades()
            ->where('status', 'success')
            ->where('pay_time', '>=', $currentMonth)
            ->sum('actual_amount')),
    ];
    
    // 获取今日交易统计
    $today = date('Y-m-d');
    $dailyStats = [
        'trade_count' => $merchant->trades()
            ->where('status', 'success')
            ->whereDate('pay_time', $today)
            ->count(),
        'trade_amount' => floatval($merchant->trades()
            ->where('status', 'success')
            ->whereDate('pay_time', $today)
            ->sum('amount')),
        'income' => floatval($merchant->trades()
            ->where('status', 'success')
            ->whereDate('pay_time', $today)
            ->sum('actual_amount')),
    ];
    
    // 格式化数据
    $data = [
        'id' => $merchant->id,
        'merchant_id' => $merchant->merchant_id,
        'name' => $merchant->name,
        'principal_name' => $merchant->principal_name,
        'principal_mobile' => $merchant->principal_mobile,
        'business_license' => $merchant->business_license,
        'id_card_number' => $merchant->id_card_number,
        'contact_address' => $merchant->contact_address,
        'business_category' => $merchant->business_category,
        'logo' => $merchant->logo,
        'qrcode' => $merchant->qrcode,
        'status' => $merchant->status,
        'status_text' => $merchant->status_text,
        'reject_reason' => $merchant->reject_reason,
        'fee_rate' => floatval($merchant->fee_rate),
        'bank_info' => $merchant->bank_info,
        'qualification_info' => $merchant->qualification_info,
        'additional_info' => $merchant->additional_info,
        'app_user' => $appUser ? [
            'id' => $appUser->id,
            'name' => $appUser->name,
            'mobile' => $appUser->mobile,
            'avatar' => $appUser->avatar,
            'status' => $appUser->status,
        ] : null,
        'monthly_stats' => $monthlyStats,
        'daily_stats' => $dailyStats,
        'yearly_stats' => [
            'trade_count' => $merchant->trades()
                ->where('status', 'success')
                ->whereYear('pay_time', date('Y'))
                ->count(),
            'trade_amount' => floatval($merchant->trades()
                ->where('status', 'success')
                ->whereYear('pay_time', date('Y'))
                ->sum('amount')),
            'income' => floatval($merchant->trades()
                ->where('status', 'success')
                ->whereYear('pay_time', date('Y'))
                ->sum('actual_amount')),
        ],
        'total_stats' => [
            'trade_count' => $merchant->trades()
                ->where('status', 'success')
                ->count(),
            'trade_amount' => floatval($merchant->trades()
                ->where('status', 'success')
                ->sum('amount')),
            'income' => floatval($merchant->trades()
                ->where('status', 'success')
                ->sum('actual_amount')),
        ],
        'payment_methods' => [
            'alipay' => floatval($merchant->trades()
                ->where('status', 'success')
                ->where('payment_method', 'alipay')
                ->sum('amount')),
            'wechat' => floatval($merchant->trades()
                ->where('status', 'success')
                ->where('payment_method', 'wechat')
                ->sum('amount')),
            'other' => floatval($merchant->trades()
                ->where('status', 'success')
                ->whereNotIn('payment_method', ['alipay', 'wechat'])
                ->sum('amount')),
        ],
        'created_at' => $merchant->created_at ? $merchant->created_at->format('Y-m-d H:i:s') : null,
        'updated_at' => $merchant->updated_at ? $merchant->updated_at->format('Y-m-d H:i:s') : null,
    ];
    
    // 返回数据
    echoJsonSuccess($data);
} catch (Exception $e) {
    echoJsonError('获取商户详情失败: ' . $e->getMessage());
} 