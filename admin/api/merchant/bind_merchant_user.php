<?php
require_once __DIR__ . '/../functions/init.php';
require_once __DIR__ . '/../functions/auth.php';

use App\Models\Merchant;
use App\Models\AppUser;

// 验证请求
$auth = checkAuth();
if (!$auth['success']) {
    echoJsonError($auth['message'], $auth['code']);
    exit;
}

// 获取管理员用户
$admin = $auth['user'];

// 获取请求参数
$data = json_decode(file_get_contents('php://input'), true);

if (!isset($data['merchant_id']) || empty($data['merchant_id'])) {
    echoJsonError('缺少商户ID参数');
    exit;
}

if (!isset($data['app_user_id']) || empty($data['app_user_id'])) {
    echoJsonError('缺少APP用户ID参数');
    exit;
}

$merchantId = $data['merchant_id'];
$appUserId = $data['app_user_id'];

try {
    // 查询商户
    $merchant = Merchant::find($merchantId);
    
    if (!$merchant) {
        echoJsonError('商户不存在');
        exit;
    }
    
    // 查询用户
    $appUser = AppUser::find($appUserId);
    
    if (!$appUser) {
        echoJsonError('用户不存在');
        exit;
    }
    
    // 检查手机号是否匹配
    if ($merchant->principal_mobile != $appUser->mobile) {
        echoJsonError('用户手机号与商户负责人手机号不匹配');
        exit;
    }
    
    // 更新商户关联的用户ID
    $merchant->app_user_id = $appUserId;
    $merchant->save();
    
    // 更新用户角色为商户
    if (!$appUser->roles || !in_array('merchant', explode(',', $appUser->roles))) {
        $roles = $appUser->roles ? explode(',', $appUser->roles) : [];
        $roles[] = 'merchant';
        $appUser->roles = implode(',', array_unique($roles));
        $appUser->save();
    }
    
    // 返回数据
    echoJsonSuccess([
        'merchant' => [
            'id' => $merchant->id,
            'merchant_id' => $merchant->merchant_id,
            'name' => $merchant->name,
            'app_user_id' => $merchant->app_user_id
        ],
        'app_user' => [
            'id' => $appUser->id,
            'name' => $appUser->name,
            'mobile' => $appUser->mobile,
            'roles' => $appUser->roles
        ],
        'message' => '绑定成功'
    ]);
} catch (Exception $e) {
    echoJsonError('绑定商户和用户失败: ' . $e->getMessage());
} 