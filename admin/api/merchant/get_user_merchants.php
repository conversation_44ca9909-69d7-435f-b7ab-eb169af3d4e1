<?php
require_once __DIR__ . '/../functions/init.php';
require_once __DIR__ . '/../functions/auth.php';

use App\Models\Merchant;
use App\Models\AppUser;

// 验证请求
$auth = checkAuth();
if (!$auth['success']) {
    echoJsonError($auth['message'], $auth['code']);
    exit;
}

// 获取手机号参数
$mobile = isset($_GET['mobile']) ? $_GET['mobile'] : null;
$appUserId = isset($_GET['app_user_id']) ? intval($_GET['app_user_id']) : null;

if (!$mobile && !$appUserId) {
    echoJsonError('缺少手机号或用户ID参数');
    exit;
}

try {
    // 查询用户
    $appUser = null;
    
    if ($appUserId) {
        $appUser = AppUser::find($appUserId);
    } elseif ($mobile) {
        $appUser = AppUser::where('mobile', $mobile)->first();
    }
    
    if (!$appUser) {
        echoJsonError('用户不存在');
        exit;
    }
    
    // 查询关联的商户
    $query = Merchant::where('principal_mobile', $appUser->mobile)
                 ->orWhere('app_user_id', $appUser->id);
    
    $merchants = $query->get();
    
    // 格式化数据
    $items = [];
    foreach ($merchants as $merchant) {
        $items[] = [
            'id' => $merchant->id,
            'merchant_id' => $merchant->merchant_id,
            'name' => $merchant->name,
            'principal_name' => $merchant->principal_name,
            'principal_mobile' => $merchant->principal_mobile,
            'logo' => $merchant->logo,
            'status' => $merchant->status,
            'status_text' => $merchant->status_text,
            'balance' => floatval($merchant->balance),
            'frozen_balance' => floatval($merchant->frozen_balance),
            'withdrawable_balance' => floatval($merchant->withdrawable_balance),
            'is_bound' => $merchant->app_user_id == $appUser->id,
            'created_at' => $merchant->created_at ? $merchant->created_at->format('Y-m-d H:i:s') : null,
        ];
    }
    
    // 返回数据
    echoJsonSuccess([
        'app_user' => [
            'id' => $appUser->id,
            'name' => $appUser->name,
            'mobile' => $appUser->mobile,
            'avatar' => $appUser->avatar,
        ],
        'merchants' => $items,
        'total' => count($items)
    ]);
} catch (Exception $e) {
    echoJsonError('获取用户商户失败: ' . $e->getMessage());
}