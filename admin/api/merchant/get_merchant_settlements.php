<?php
require_once __DIR__ . '/../functions/init.php';
require_once __DIR__ . '/../functions/auth.php';

use App\Models\MerchantSettlement;

// 验证请求
$auth = checkAuth();
if (!$auth['success']) {
    echoJsonError($auth['message'], $auth['code']);
    exit;
}

// 获取用户
$user = $auth['user'];

// 获取查询参数
$page = isset($_GET['page']) ? intval($_GET['page']) : 1;
$limit = isset($_GET['limit']) ? intval($_GET['limit']) : 10;
$merchantId = isset($_GET['merchant_id']) ? intval($_GET['merchant_id']) : null;
$status = isset($_GET['status']) ? $_GET['status'] : null;
$startDate = isset($_GET['start_date']) ? $_GET['start_date'] : null;
$endDate = isset($_GET['end_date']) ? $_GET['end_date'] : null;
$sortField = isset($_GET['sort_field']) ? $_GET['sort_field'] : 'created_at';
$sortOrder = isset($_GET['sort_order']) ? $_GET['sort_order'] : 'desc';

// 验证参数
if ($page < 1) $page = 1;
if ($limit < 1 || $limit > 100) $limit = 10;

// 允许的排序字段
$allowedSortFields = ['id', 'settlement_no', 'amount', 'status', 'settlement_time', 'created_at'];
if (!in_array($sortField, $allowedSortFields)) {
    $sortField = 'created_at';
}

// 允许的排序方向
$allowedSortOrders = ['asc', 'desc'];
if (!in_array($sortOrder, $allowedSortOrders)) {
    $sortOrder = 'desc';
}

try {
    // 构建查询
    $query = MerchantSettlement::query();
    
    // 应用过滤条件
    if ($merchantId) {
        $query->where('merchant_id', $merchantId);
    }
    
    if ($status) {
        $query->where('status', $status);
    }
    
    if ($startDate) {
        $query->where('created_at', '>=', $startDate . ' 00:00:00');
    }
    
    if ($endDate) {
        $query->where('created_at', '<=', $endDate . ' 23:59:59');
    }
    
    // 获取总记录数
    $total = $query->count();
    
    // 应用排序和分页
    $settlements = $query->with('merchant')
                        ->orderBy($sortField, $sortOrder)
                        ->skip(($page - 1) * $limit)
                        ->take($limit)
                        ->get();
    
    // 格式化数据
    $items = [];
    foreach ($settlements as $settlement) {
        $items[] = [
            'id' => $settlement->id,
            'settlement_no' => $settlement->settlement_no,
            'merchant_id' => $settlement->merchant_id,
            'merchant_name' => $settlement->merchant->name ?? '',
            'amount' => floatval($settlement->amount),
            'status' => $settlement->status,
            'status_text' => $settlement->status_text,
            'bank_name' => $settlement->bank_name,
            'bank_branch' => $settlement->bank_branch,
            'bank_account_name' => $settlement->bank_account_name,
            'bank_account_no' => $settlement->bank_account_no,
            'settlement_time' => $settlement->settlement_time ? date('Y-m-d H:i:s', strtotime($settlement->settlement_time)) : null,
            'remarks' => $settlement->remarks,
            'created_at' => $settlement->created_at ? $settlement->created_at->format('Y-m-d H:i:s') : null,
            'updated_at' => $settlement->updated_at ? $settlement->updated_at->format('Y-m-d H:i:s') : null,
        ];
    }
    
    // 返回数据
    echoJsonSuccess([
        'total' => $total,
        'page' => $page,
        'limit' => $limit,
        'items' => $items
    ]);
} catch (Exception $e) {
    echoJsonError('获取结算记录失败: ' . $e->getMessage());
} 