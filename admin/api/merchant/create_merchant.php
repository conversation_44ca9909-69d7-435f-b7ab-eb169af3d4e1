<?php
require_once __DIR__ . '/../functions/init.php';
require_once __DIR__ . '/../functions/auth.php';
require_once __DIR__ . '/../functions/upload.php';

use App\Models\Merchant;
use App\Models\MerchantLog;

// 验证请求
$auth = checkAuth();
if (!$auth['success']) {
    echoJsonError($auth['message'], $auth['code']);
    exit;
}

// 获取用户
$user = $auth['user'];

// 验证请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echoJsonError('Invalid request method', 405);
    exit;
}

// 获取请求数据
$data = json_decode(file_get_contents('php://input'), true);

// 验证必填字段
$requiredFields = ['name', 'principal_name', 'principal_mobile', 'fee_rate'];
foreach ($requiredFields as $field) {
    if (empty($data[$field])) {
        echoJsonError("Field {$field} is required");
        exit;
    }
}

try {
    // 验证手机号格式
    if (!preg_match('/^1[3-9]\d{9}$/', $data['principal_mobile'])) {
        echoJsonError('Invalid mobile number format');
        exit;
    }

    // 验证费率范围
    $feeRate = floatval($data['fee_rate']);
    if ($feeRate < 0 || $feeRate > 100) {
        echoJsonError('Fee rate must be between 0 and 100');
        exit;
    }

    // 检查商户名称是否已存在
    if (Merchant::where('name', $data['name'])->exists()) {
        echoJsonError('Merchant name already exists');
        exit;
    }

    // 生成商户ID
    $merchantId = 'M' . date('YmdHis') . rand(1000, 9999);

    // 创建商户记录
    $merchant = new Merchant();
    $merchant->merchant_id = $merchantId;
    $merchant->name = $data['name'];
    $merchant->logo = $data['logo'] ?? '';
    $merchant->principal_name = $data['principal_name'];
    $merchant->principal_mobile = $data['principal_mobile'];
    $merchant->business_license = $data['business_license'] ?? '';
    $merchant->bank_name = $data['bank_name'] ?? '';
    $merchant->bank_branch = $data['bank_branch'] ?? '';
    $merchant->bank_account_name = $data['bank_account_name'] ?? '';
    $merchant->bank_account_no = $data['bank_account_no'] ?? '';
    $merchant->fee_rate = $feeRate;
    $merchant->status = 'pending';
    $merchant->province = $data['province'] ?? '';
    $merchant->city = $data['city'] ?? '';
    $merchant->district = $data['district'] ?? '';
    $merchant->address = $data['address'] ?? '';
    $merchant->save();

    // 记录操作日志
    MerchantLog::create([
        'merchant_id' => $merchant->id,
        'user_id' => $user->id,
        'user_type' => 'admin',
        'action' => 'create',
        'description' => "创建商户：{$merchant->name}",
        'ip' => $_SERVER['REMOTE_ADDR'],
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? ''
    ]);

    // 返回成功
    echoJsonSuccess([
        'id' => $merchant->id,
        'merchant_id' => $merchant->merchant_id
    ]);
} catch (Exception $e) {
    echoJsonError('创建商户失败: ' . $e->getMessage());
} 