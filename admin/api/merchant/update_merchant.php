<?php
require_once __DIR__ . '/../functions/init.php';
require_once __DIR__ . '/../functions/auth.php';
require_once __DIR__ . '/../functions/upload.php';

use App\Models\Merchant;
use App\Models\MerchantLog;

// 验证请求
$auth = checkAuth();
if (!$auth['success']) {
    echoJsonError($auth['message'], $auth['code']);
    exit;
}

// 获取用户
$user = $auth['user'];

// 验证请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echoJsonError('Invalid request method', 405);
    exit;
}

// 获取请求数据
$data = json_decode(file_get_contents('php://input'), true);

// 验证商户ID
if (empty($data['id'])) {
    echoJsonError('Merchant ID is required');
    exit;
}

try {
    // 获取商户信息
    $merchant = Merchant::find($data['id']);
    if (!$merchant) {
        echoJsonError('Merchant not found');
        exit;
    }

    // 记录修改前的信息
    $changes = [];
    
    // 更新基本信息
    if (isset($data['name']) && $data['name'] !== $merchant->name) {
        // 检查商户名称是否已存在
        if (Merchant::where('name', $data['name'])->where('id', '!=', $merchant->id)->exists()) {
            echoJsonError('Merchant name already exists');
            exit;
        }
        $changes[] = "名称: {$merchant->name} -> {$data['name']}";
        $merchant->name = $data['name'];
    }

    // 更新负责人信息
    if (isset($data['principal_name']) && $data['principal_name'] !== $merchant->principal_name) {
        $changes[] = "负责人: {$merchant->principal_name} -> {$data['principal_name']}";
        $merchant->principal_name = $data['principal_name'];
    }

    if (isset($data['principal_mobile'])) {
        if (!preg_match('/^1[3-9]\d{9}$/', $data['principal_mobile'])) {
            echoJsonError('Invalid mobile number format');
            exit;
        }
        if ($data['principal_mobile'] !== $merchant->principal_mobile) {
            $changes[] = "联系电话: {$merchant->principal_mobile} -> {$data['principal_mobile']}";
            $merchant->principal_mobile = $data['principal_mobile'];
        }
    }

    // 更新费率
    if (isset($data['fee_rate'])) {
        $feeRate = floatval($data['fee_rate']);
        if ($feeRate < 0 || $feeRate > 100) {
            echoJsonError('Fee rate must be between 0 and 100');
            exit;
        }
        if ($feeRate !== floatval($merchant->fee_rate)) {
            $changes[] = "费率: {$merchant->fee_rate}% -> {$feeRate}%";
            $merchant->fee_rate = $feeRate;
        }
    }

    // 更新其他可选信息
    $optionalFields = [
        'logo' => '商户LOGO',
        'business_license' => '营业执照',
        'bank_name' => '开户银行',
        'bank_branch' => '支行名称',
        'bank_account_name' => '开户名',
        'bank_account_no' => '银行账号',
        'province' => '省份',
        'city' => '城市',
        'district' => '区县',
        'address' => '详细地址'
    ];

    foreach ($optionalFields as $field => $label) {
        if (isset($data[$field]) && $data[$field] !== $merchant->$field) {
            $changes[] = "{$label}: {$merchant->$field} -> {$data[$field]}";
            $merchant->$field = $data[$field];
        }
    }

    // 保存更新
    if (!empty($changes)) {
        $merchant->save();

        // 记录操作日志
        MerchantLog::create([
            'merchant_id' => $merchant->id,
            'user_id' => $user->id,
            'user_type' => 'admin',
            'action' => 'update',
            'description' => "更新商户信息：\n" . implode("\n", $changes),
            'ip' => $_SERVER['REMOTE_ADDR'],
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? ''
        ]);
    }

    // 返回成功
    echoJsonSuccess([
        'id' => $merchant->id,
        'changes' => $changes
    ]);
} catch (Exception $e) {
    echoJsonError('更新商户失败: ' . $e->getMessage());
} 