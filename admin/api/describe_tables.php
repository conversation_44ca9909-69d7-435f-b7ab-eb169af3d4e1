<?php require_once "config.php"; $db = new mysqli($DB_CONFIG["HOST"], $DB_CONFIG["USER"], $DB_CONFIG["PASSWORD"], $DB_CONFIG["DATABASE"], $DB_CONFIG["PORT"]); if (!$db->connect_error) { echo "=== nav_config表结构 ===\n"; $result = $db->query("DESCRIBE nav_config"); if ($result) { while($row = $result->fetch_assoc()) { print_r($row); } } echo "\n\n=== home_nav_items表结构 ===\n"; $result = $db->query("DESCRIBE home_nav_items"); if ($result) { while($row = $result->fetch_assoc()) { print_r($row); } } $db->close(); } ?>
