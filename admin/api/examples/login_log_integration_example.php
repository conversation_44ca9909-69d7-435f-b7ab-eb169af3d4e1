<?php

/**
 * 登录日志集成示例
 * 
 * 这个文件展示了如何在现有的登录接口中集成登录日志记录功能
 */

require_once __DIR__ . '/../functions/login_log_helper.php';

// ========================================
// 示例1: APP用户微信登录接口集成
// ========================================

function appWechatLogin($code) {
    try {
        // 原有的微信登录逻辑
        $userInfo = getWechatUserInfo($code);
        
        if ($userInfo) {
            $userId = $userInfo['id'];
            
            // 登录成功 - 记录日志
            recordWechatLoginLog($userId, 'success', '微信登录成功');
            
            return [
                'code' => 0,
                'message' => '登录成功',
                'data' => $userInfo
            ];
        } else {
            // 登录失败 - 记录日志（用户ID为null）
            recordWechatLoginLog(null, 'failed', '微信授权失败');
            
            return [
                'code' => 1,
                'message' => '微信授权失败',
                'data' => null
            ];
        }
    } catch (Exception $e) {
        // 异常情况 - 记录日志
        recordWechatLoginLog(null, 'failed', '微信登录异常: ' . $e->getMessage());
        
        return [
            'code' => 500,
            'message' => '登录异常',
            'data' => null
        ];
    }
}

// ========================================
// 示例2: APP用户密码登录接口集成
// ========================================

function appPasswordLogin($phone, $password) {
    try {
        // 原有的密码登录逻辑
        $user = getUserByPhone($phone);
        
        if (!$user) {
            // 用户不存在 - 记录日志
            recordPasswordLoginLog(null, 'app_user', 'failed', '用户不存在');
            
            return [
                'code' => 1,
                'message' => '用户不存在',
                'data' => null
            ];
        }
        
        if (!verifyPassword($password, $user['password'])) {
            // 密码错误 - 记录日志
            recordPasswordLoginLog($user['id'], 'app_user', 'failed', '密码错误');
            
            return [
                'code' => 1,
                'message' => '密码错误',
                'data' => null
            ];
        }
        
        // 登录成功 - 记录日志
        recordPasswordLoginLog($user['id'], 'app_user', 'success', '密码登录成功');
        
        return [
            'code' => 0,
            'message' => '登录成功',
            'data' => $user
        ];
        
    } catch (Exception $e) {
        // 异常情况 - 记录日志
        recordPasswordLoginLog(null, 'app_user', 'failed', '密码登录异常: ' . $e->getMessage());
        
        return [
            'code' => 500,
            'message' => '登录异常',
            'data' => null
        ];
    }
}

// ========================================
// 示例3: 管理员登录接口集成
// ========================================

function adminLogin($username, $password) {
    try {
        // 原有的管理员登录逻辑
        $admin = getAdminByUsername($username);
        
        if (!$admin) {
            // 管理员不存在 - 记录日志
            recordAdminLoginLog(null, 'password', 'failed', '管理员不存在');
            
            return [
                'code' => 1,
                'message' => '管理员不存在',
                'data' => null
            ];
        }
        
        if (!verifyPassword($password, $admin['password'])) {
            // 密码错误 - 记录日志
            recordAdminLoginLog($admin['id'], 'password', 'failed', '密码错误');
            
            return [
                'code' => 1,
                'message' => '密码错误',
                'data' => null
            ];
        }
        
        // 登录成功 - 记录日志
        recordAdminLoginLog($admin['id'], 'password', 'success', '管理员登录成功');
        
        return [
            'code' => 0,
            'message' => '登录成功',
            'data' => $admin
        ];
        
    } catch (Exception $e) {
        // 异常情况 - 记录日志
        recordAdminLoginLog(null, 'password', 'failed', '管理员登录异常: ' . $e->getMessage());
        
        return [
            'code' => 500,
            'message' => '登录异常',
            'data' => null
        ];
    }
}

// ========================================
// 示例4: 验证码登录接口集成
// ========================================

function appCodeLogin($phone, $code) {
    try {
        // 验证验证码
        if (!verifyCode($phone, $code)) {
            // 验证码错误 - 记录日志
            recordCodeLoginLog(null, 'failed', '验证码错误');
            
            return [
                'code' => 1,
                'message' => '验证码错误',
                'data' => null
            ];
        }
        
        // 获取或创建用户
        $user = getUserByPhone($phone);
        if (!$user) {
            $user = createUserByPhone($phone);
        }
        
        // 登录成功 - 记录日志
        recordCodeLoginLog($user['id'], 'success', '验证码登录成功');
        
        return [
            'code' => 0,
            'message' => '登录成功',
            'data' => $user
        ];
        
    } catch (Exception $e) {
        // 异常情况 - 记录日志
        recordCodeLoginLog(null, 'failed', '验证码登录异常: ' . $e->getMessage());
        
        return [
            'code' => 500,
            'message' => '登录异常',
            'data' => null
        ];
    }
}

// ========================================
// 示例5: 手机号绑定接口集成
// ========================================

function bindPhone($userId, $phone, $code) {
    try {
        // 验证验证码
        if (!verifyCode($phone, $code)) {
            // 验证码错误 - 记录日志
            recordBindPhoneLog($userId, 'failed', '验证码错误');
            
            return [
                'code' => 1,
                'message' => '验证码错误',
                'data' => null
            ];
        }
        
        // 检查手机号是否已被绑定
        if (isPhoneBound($phone)) {
            // 手机号已被绑定 - 记录日志
            recordBindPhoneLog($userId, 'failed', '手机号已被绑定');
            
            return [
                'code' => 1,
                'message' => '手机号已被绑定',
                'data' => null
            ];
        }
        
        // 绑定手机号
        $result = updateUserPhone($userId, $phone);
        
        if ($result) {
            // 绑定成功 - 记录日志
            recordBindPhoneLog($userId, 'success', '手机号绑定成功');
            
            return [
                'code' => 0,
                'message' => '绑定成功',
                'data' => null
            ];
        } else {
            // 绑定失败 - 记录日志
            recordBindPhoneLog($userId, 'failed', '绑定失败');
            
            return [
                'code' => 1,
                'message' => '绑定失败',
                'data' => null
            ];
        }
        
    } catch (Exception $e) {
        // 异常情况 - 记录日志
        recordBindPhoneLog($userId, 'failed', '绑定异常: ' . $e->getMessage());
        
        return [
            'code' => 500,
            'message' => '绑定异常',
            'data' => null
        ];
    }
}

// ========================================
// 辅助函数（示例用，实际项目中应该使用真实的函数）
// ========================================

function getWechatUserInfo($code) {
    // 模拟微信用户信息获取
    return ['id' => 1, 'name' => '微信用户', 'openid' => 'wx123456'];
}

function getUserByPhone($phone) {
    // 模拟根据手机号获取用户
    return ['id' => 1, 'phone' => $phone, 'name' => '用户'];
}

function getAdminByUsername($username) {
    // 模拟根据用户名获取管理员
    return ['id' => 1, 'username' => $username, 'name' => '管理员'];
}

function verifyPassword($password, $hashedPassword) {
    // 模拟密码验证
    return password_verify($password, $hashedPassword);
}

function verifyCode($phone, $code) {
    // 模拟验证码验证
    return true; // 示例中总是返回true
}

function createUserByPhone($phone) {
    // 模拟创建用户
    return ['id' => 2, 'phone' => $phone, 'name' => '新用户'];
}

function isPhoneBound($phone) {
    // 模拟检查手机号是否已绑定
    return false; // 示例中总是返回false
}

function updateUserPhone($userId, $phone) {
    // 模拟更新用户手机号
    return true; // 示例中总是返回true
}

// ========================================
// 使用说明
// ========================================

/*
在现有的登录接口中集成登录日志记录功能的步骤：

1. 引入助手函数文件：
   require_once __DIR__ . '/functions/login_log_helper.php';

2. 在登录成功时调用相应的记录函数：
   recordWechatLoginLog($userId, 'success', '微信登录成功');
   recordPasswordLoginLog($userId, 'app_user', 'success', '密码登录成功');
   recordAdminLoginLog($adminId, 'password', 'success', '管理员登录成功');

3. 在登录失败时也要记录日志：
   recordWechatLoginLog(null, 'failed', '微信授权失败');
   recordPasswordLoginLog(null, 'app_user', 'failed', '用户不存在');

4. 在异常情况下记录详细的错误信息：
   recordWechatLoginLog(null, 'failed', '微信登录异常: ' . $e->getMessage());

注意事项：
- 登录日志记录不应该影响主要的登录业务流程
- 即使日志记录失败，也不应该导致登录失败
- 记录的消息应该简洁明了，便于后续分析
- 对于失败的登录尝试，用户ID可以传null
- IP地址和用户代理会自动获取，无需手动传入
*/ 