<?php
/**
 * 获取商品列表（精选/推荐）
 * 
 * @param int $limit 限制返回的商品数量，默认为10个
 * @return array 商品列表数据
 */
function get_products($limit = 10) {
    global $DB_CONFIG;
    
    try {
        // 连接数据库
        $conn = new mysqli(
            $DB_CONFIG['HOST'],
            $DB_CONFIG['USER'],
            $DB_CONFIG['PASSWORD'],
            $DB_CONFIG['DATABASE'],
            $DB_CONFIG['PORT']
        );
        
        // 检查连接
        if ($conn->connect_error) {
            return [
                'code' => 500,
                'message' => '数据库连接失败',
                'data' => null
            ];
        }
        
        // 设置字符集
        $conn->set_charset($DB_CONFIG['CHARSET']);
        
        // 查询精选商品
        $sql = "SELECT p.*, c.name as category_name 
                FROM products p 
                LEFT JOIN product_categories c ON p.category_id = c.id 
                WHERE p.is_recommend = 1 AND p.status = 1 
                ORDER BY p.sort ASC 
                LIMIT ?";
        
        $stmt = $conn->prepare($sql);
        $stmt->bind_param('i', $limit);
        $stmt->execute();
        $result = $stmt->get_result();
        
        $products = [];
        
        if ($result && $result->num_rows > 0) {
            while ($row = $result->fetch_assoc()) {
                // 提取标签（如果有）
                $tags = [];
                if (isset($row['is_hot']) && $row['is_hot'] == 1) {
                    $tags[] = '热销';
                }
                if (isset($row['is_new']) && $row['is_new'] == 1) {
                    $tags[] = '新品';
                }
                if (isset($row['is_recommend']) && $row['is_recommend'] == 1) {
                    $tags[] = '推荐';
                }
                if (isset($row['is_premium']) && $row['is_premium'] == 1) {
                    $tags[] = '精品';
                }
                
                $products[] = [
                    'id' => $row['id'],
                    'name' => $row['name'],
                    'image_url' => $row['thumbnail'],
                    'price' => $row['price'],
                    'original_price' => $row['original_price'],
                    'sales' => $row['sales'],
                    'stock' => $row['stock'],
                    'rating' => isset($row['rating']) ? $row['rating'] : 5.0,
                    'category_id' => $row['category_id'],
                    'category_name' => $row['category_name'],
                    'tags' => $tags,
                    'is_hot' => isset($row['is_hot']) ? (bool)$row['is_hot'] : false,
                    'is_new' => isset($row['is_new']) ? (bool)$row['is_new'] : false,
                    'is_recommend' => isset($row['is_recommend']) ? (bool)$row['is_recommend'] : false,
                    'is_premium' => isset($row['is_premium']) ? (bool)$row['is_premium'] : false
                ];
            }
            
            $stmt->close();
            $conn->close();
            
            return [
                'code' => 0,
                'message' => '获取成功',
                'data' => $products
            ];
        } else {
            // 若数据库中没有数据，返回默认值
            $stmt->close();
            $conn->close();
            
            $default_products = [
                [
                    'id' => 1,
                    'name' => '智能净水器 T1000',
                    'image_url' => '/uploads/products/water_purifier_1.jpg',
                    'price' => 1999.00,
                    'original_price' => 2499.00,
                    'sales' => 356,
                    'stock' => 100,
                    'rating' => 4.8,
                    'category_id' => 1,
                    'category_name' => '净水器',
                    'tags' => ['热销', '智能']
                ],
                [
                    'id' => 2,
                    'name' => '家用反渗透纯水机',
                    'image_url' => '/uploads/products/water_purifier_2.jpg',
                    'price' => 1299.00,
                    'original_price' => 1599.00,
                    'sales' => 249,
                    'stock' => 200,
                    'rating' => 4.5,
                    'category_id' => 1,
                    'category_name' => '净水器',
                    'tags' => ['经济型']
                ],
                [
                    'id' => 3,
                    'name' => '净水器滤芯套装',
                    'image_url' => '/uploads/products/filter_1.jpg',
                    'price' => 399.00,
                    'original_price' => 499.00,
                    'sales' => 1024,
                    'stock' => 500,
                    'rating' => 4.9,
                    'category_id' => 2,
                    'category_name' => '滤芯配件',
                    'tags' => ['热销', '配件']
                ],
                [
                    'id' => 4,
                    'name' => '智能水质检测仪',
                    'image_url' => '/uploads/products/tester_1.jpg',
                    'price' => 89.00,
                    'original_price' => 129.00,
                    'sales' => 789,
                    'stock' => 300,
                    'rating' => 4.6,
                    'category_id' => 3,
                    'category_name' => '检测设备',
                    'tags' => ['实用']
                ]
            ];
            
            return [
                'code' => 0,
                'message' => '获取成功(默认值)',
                'data' => $default_products
            ];
        }
    } catch (Exception $e) {
        return [
            'code' => 500,
            'message' => '服务器错误：' . $e->getMessage(),
            'data' => null
        ];
    }
}

/**
 * 获取商品列表，支持分页、排序和分类过滤
 * 
 * @param int $page 页码，默认1
 * @param int $page_size 每页数量，默认10
 * @param string $sort 排序字段，如price、sales等
 * @param string $sort_direction 排序方向，asc或desc
 * @param int $category_id 分类ID
 * @param string $keyword 搜索关键词
 * @return array 商品列表数据
 */
function get_product_list($page = 1, $page_size = 10, $sort = 'id', $sort_direction = 'desc', $category_id = 0, $keyword = '') {
    global $DB_CONFIG;
    
    try {
        // 连接数据库
        $conn = new mysqli(
            $DB_CONFIG['HOST'],
            $DB_CONFIG['USER'],
            $DB_CONFIG['PASSWORD'],
            $DB_CONFIG['DATABASE'],
            $DB_CONFIG['PORT']
        );
        
        // 检查连接
        if ($conn->connect_error) {
            return [
                'code' => 500,
                'message' => '数据库连接失败',
                'data' => null
            ];
        }
        
        // 设置字符集
        $conn->set_charset($DB_CONFIG['CHARSET']);
        
        // 构建查询条件
        $conditions = ['p.status = 1'];
        $params = [];
        $types = "";
        
        // 分类筛选
        if ($category_id > 0) {
            $conditions[] = 'p.category_id = ?';
            $params[] = $category_id;
            $types .= 'i';
        }
        
        // 关键词搜索
        if (!empty($keyword)) {
            $conditions[] = '(p.name LIKE ? OR p.description LIKE ? OR p.tags LIKE ?)';
            $keyword_param = "%{$keyword}%";
            $params[] = $keyword_param;
            $params[] = $keyword_param;
            $params[] = $keyword_param;
            $types .= 'sss';
        }
        
        // 组合WHERE条件
        $where_clause = implode(' AND ', $conditions);
        
        // 验证排序字段
        $allowed_sort_fields = ['id', 'price', 'sales', 'rating', 'create_time'];
        if (!in_array($sort, $allowed_sort_fields)) {
            $sort = 'id';
        }
        
        // 验证排序方向
        $sort_direction = strtoupper($sort_direction) === 'ASC' ? 'ASC' : 'DESC';
        
        // 计算偏移量
        $offset = ($page - 1) * $page_size;
        
        // 查询商品总数
        $count_sql = "SELECT COUNT(*) as total FROM products p WHERE {$where_clause}";
        $stmt = $conn->prepare($count_sql);
        
        if (!empty($params)) {
            $stmt->bind_param($types, ...$params);
        }
        
        $stmt->execute();
        $count_result = $stmt->get_result();
        $total = $count_result->fetch_assoc()['total'];
        $stmt->close();
        
        // 查询商品列表
        $sql = "SELECT p.*, c.name as category_name 
                FROM products p 
                LEFT JOIN product_categories c ON p.category_id = c.id 
                WHERE {$where_clause} 
                ORDER BY p.{$sort} {$sort_direction} 
                LIMIT ?, ?";
        
        $stmt = $conn->prepare($sql);
        
        // 添加分页参数
        $params[] = $offset;
        $params[] = $page_size;
        $types .= 'ii';
        
        $stmt->bind_param($types, ...$params);
        $stmt->execute();
        $result = $stmt->get_result();
        
        $products = [];
        
        if ($result && $result->num_rows > 0) {
            while ($row = $result->fetch_assoc()) {
                // 提取标签（如果有）
                $tags = [];
                if (isset($row['is_hot']) && $row['is_hot'] == 1) {
                    $tags[] = '热销';
                }
                if (isset($row['is_new']) && $row['is_new'] == 1) {
                    $tags[] = '新品';
                }
                if (isset($row['is_recommend']) && $row['is_recommend'] == 1) {
                    $tags[] = '推荐';
                }
                if (isset($row['is_premium']) && $row['is_premium'] == 1) {
                    $tags[] = '精品';
                }
                
                $products[] = [
                    'id' => $row['id'],
                    'name' => $row['name'],
                    'image_url' => $row['thumbnail'],
                    'price' => $row['price'],
                    'original_price' => $row['original_price'],
                    'sales' => $row['sales'],
                    'stock' => $row['stock'],
                    'rating' => isset($row['rating']) ? $row['rating'] : 5.0,
                    'category_id' => $row['category_id'],
                    'category_name' => $row['category_name'],
                    'tags' => $tags,
                    'is_hot' => isset($row['is_hot']) ? (bool)$row['is_hot'] : false,
                    'is_new' => isset($row['is_new']) ? (bool)$row['is_new'] : false,
                    'is_recommend' => isset($row['is_recommend']) ? (bool)$row['is_recommend'] : false,
                    'is_premium' => isset($row['is_premium']) ? (bool)$row['is_premium'] : false,
                    'description' => $row['description']
                ];
            }
            
            $stmt->close();
            $conn->close();
            
            return [
                'code' => 0,
                'message' => '获取成功',
                'data' => [
                    'total' => $total,
                    'current_page' => $page,
                    'page_size' => $page_size,
                    'total_pages' => ceil($total / $page_size),
                    'list' => $products
                ]
            ];
        } else {
            // 若数据库中没有数据，返回空列表
            $stmt->close();
            $conn->close();
            
            return [
                'code' => 0,
                'message' => '暂无数据',
                'data' => [
                    'total' => 0,
                    'current_page' => $page,
                    'page_size' => $page_size,
                    'total_pages' => 0,
                    'list' => []
                ]
            ];
        }
    } catch (Exception $e) {
        return [
            'code' => 500,
            'message' => '服务器错误：' . $e->getMessage(),
            'data' => null
        ];
    }
} 