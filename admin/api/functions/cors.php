<?php
/**
 * CORS处理函数
 * 允许跨域请求
 */

/**
 * 处理跨域请求
 */
function handle_cors() {
    // 允许的域名列表
    $allowed_origins = [
        'https://pay.itapgo.com',
        'http://localhost:3000',
        'http://localhost:5173'
    ];
    
    // 如果是开发环境，允许所有域名
    $is_dev = isset($_SERVER['HTTP_HOST']) && (
        strpos($_SERVER['HTTP_HOST'], 'localhost') !== false || 
        strpos($_SERVER['HTTP_HOST'], '127.0.0.1') !== false
    );
    
    // 获取请求的Origin
    $origin = isset($_SERVER['HTTP_ORIGIN']) ? $_SERVER['HTTP_ORIGIN'] : '';
    
    // 判断Origin是否在允许列表中，或者是开发环境
    if ($is_dev || in_array($origin, $allowed_origins)) {
        header("Access-Control-Allow-Origin: {$origin}");
        header("Access-Control-Allow-Methods: GET, POST, OPTIONS");
        header("Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With");
        header("Access-Control-Allow-Credentials: true");
        header("Access-Control-Max-Age: 86400"); // 24小时内不再预检
    }
    
    // 处理OPTIONS请求
    if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
        header("HTTP/1.1 200 OK");
        exit;
    }
} 