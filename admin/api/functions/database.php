<?php
/**
 * 数据库连接工具函数
 */

/**
 * 连接数据库
 * @return mysqli|null 数据库连接
 */
function connectDatabase() {
    static $db = null;
    
    if ($db === null) {
        $db_host = '127.0.0.1';
        $db_user = 'ddg.app';
        $db_pass = '8GmWPjwbwY4waXcT';
        $db_name = 'ddg.app';
        $db_port = 3306;
        
        // 记录连接尝试
        error_log("尝试连接数据库: host={$db_host}, user={$db_user}, database={$db_name}, port={$db_port}");
        
        // 创建连接
        $db = new mysqli($db_host, $db_user, $db_pass, $db_name, $db_port);
        
        // 检查连接
        if ($db->connect_error) {
            error_log("数据库连接失败: " . $db->connect_error);
            return null;
        }
        
        // 设置字符集
        $db->set_charset('utf8mb4');
        
        error_log("数据库连接成功");
    }
    
    return $db;
}

/**
 * 关闭数据库连接
 * @param mysqli $db 数据库连接
 */
function closeDatabase($db) {
    if ($db instanceof mysqli) {
        $db->close();
    }
}

/**
 * 执行SQL查询
 * @param string $sql SQL语句
 * @param array $params 参数
 * @return array|null 查询结果
 */
function query($sql, $params = []) {
    $db = connectDatabase();
    
    if (!$db) {
        return null;
    }
    
    try {
        $stmt = $db->prepare($sql);
        
        if (!$stmt) {
            error_log("SQL准备失败: " . $db->error);
            return null;
        }
        
        // 绑定参数
        if (!empty($params)) {
            $types = '';
            $bindParams = [];
            
            foreach ($params as $param) {
                if (is_int($param)) {
                    $types .= 'i';
                } elseif (is_float($param)) {
                    $types .= 'd';
                } elseif (is_string($param)) {
                    $types .= 's';
                } else {
                    $types .= 'b';
                }
                
                $bindParams[] = $param;
            }
            
            $stmt->bind_param($types, ...$bindParams);
        }
        
        // 执行查询
        $stmt->execute();
        
        // 获取结果
        $result = $stmt->get_result();
        
        if ($result) {
            $rows = [];
            
            while ($row = $result->fetch_assoc()) {
                $rows[] = $row;
            }
            
            $stmt->close();
            return $rows;
        } else {
            $stmt->close();
            return null;
        }
    } catch (Exception $e) {
        error_log("数据库查询异常: " . $e->getMessage());
        return null;
    }
}
?> 