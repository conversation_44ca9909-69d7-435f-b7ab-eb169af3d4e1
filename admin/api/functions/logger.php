<?php
/**
 * 日志记录工具
 * 记录系统日志并保存到文件
 */

// 定义日志级别
if (!defined('LOG_DEBUG')) define('LOG_DEBUG', 1);   // 调试信息
if (!defined('LOG_INFO')) define('LOG_INFO', 2);    // 普通信息
if (!defined('LOG_WARNING')) define('LOG_WARNING', 3); // 警告信息
if (!defined('LOG_ERROR')) define('LOG_ERROR', 4);   // 错误信息

/**
 * 记录日志到文件
 * @param string $message 日志消息
 * @param int $level 日志级别 (LOG_DEBUG, LOG_INFO, LOG_WARNING, LOG_ERROR)
 * @param string $category 日志分类
 * @return bool 是否记录成功
 */
function log_message($message, $level = LOG_INFO, $category = 'app') {
    // 获取日志文件路径
    $logDir = __DIR__ . '/../logs';
    
    // 确保日志目录存在
    if (!file_exists($logDir)) {
        mkdir($logDir, 0755, true);
    }
    
    // 根据日志级别获取对应的名称
    $levelName = 'INFO';
    switch ($level) {
        case LOG_DEBUG:
            $levelName = 'DEBUG';
            break;
        case LOG_INFO:
            $levelName = 'INFO';
            break;
        case LOG_WARNING:
            $levelName = 'WARNING';
            break;
        case LOG_ERROR:
            $levelName = 'ERROR';
            break;
    }
    
    // 构建日志文件名
    $date = date('Y-m-d');
    $logFile = "{$logDir}/{$category}_{$date}.log";
    
    // 构建日志内容
    $logTime = date('Y-m-d H:i:s');
    $logContent = "[{$logTime}] [{$levelName}] {$message}" . PHP_EOL;
    
    // 写入日志文件
    return file_put_contents($logFile, $logContent, FILE_APPEND | LOCK_EX) !== false;
}

/**
 * 记录微信支付相关日志
 * @param string $message 日志消息
 * @param array $data 附加数据（可选）
 * @param int $level 日志级别
 * @return bool 是否记录成功
 */
function log_wechat_pay($message, $data = null, $level = LOG_INFO) {
    $logMessage = $message;
    
    // 如果提供了数据，转换为JSON添加到日志中
    if ($data !== null) {
        $jsonData = json_encode($data, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
        $logMessage .= " - 数据: {$jsonData}";
    }
    
    // 使用专门的微信支付日志分类
    return log_message($logMessage, $level, 'wechat_pay');
}

/**
 * 记录支付错误日志
 * @param string $message 错误消息
 * @param array $data 错误数据（可选）
 * @return bool 是否记录成功
 */
function log_payment_error($message, $data = null) {
    return log_wechat_pay($message, $data, LOG_ERROR);
}

/**
 * 记录安装预约API相关日志
 * @param string $message 日志消息
 * @param array $data 附加数据（可选）
 * @param int $level 日志级别
 * @return bool 是否记录成功
 */
function log_installation_api($message, $data = null, $level = LOG_INFO) {
    $logMessage = $message;
    
    // 如果提供了数据，转换为JSON添加到日志中
    if ($data !== null) {
        $jsonData = json_encode($data, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
        $logMessage .= " - 数据: {$jsonData}";
    }
    
    // 使用专门的安装预约日志分类
    return log_message($logMessage, $level, 'installation');
}