<?php
/**
 * 数据库连接相关函数
 */

// 引入配置
if (!defined('DEBUG_MODE')) {
    require_once dirname(__DIR__) . '/config.php';
}

/**
 * 获取数据库连接
 * @param string $db_type 数据库类型: default(默认), payment(支付系统), water(水系统)
 * @return mysqli|null 数据库连接对象
 */
if (!function_exists('get_db_connection')) {
    function get_db_connection($db_type = 'default') {
        global $DB_CONFIG, $PAYMENT_DB_CONFIG, $WATER_DB_CONFIG;
        
        try {
            $config = null;
            
            switch ($db_type) {
                case 'payment':
                    $config = $PAYMENT_DB_CONFIG;
                    break;
                case 'water':
                    $config = $WATER_DB_CONFIG;
                    break;
                case 'default':
                default:
                    $config = $DB_CONFIG;
                    break;
            }
            
            if (!$config) {
                error_log("[DB ERROR] 无法获取 $db_type 数据库配置");
                return null;
            }
            
            // 设置连接超时
            $conn = mysqli_init();
            if (!$conn) {
                error_log("[DB ERROR] mysqli_init 失败");
                return null;
            }
            
            // 设置连接超时为5秒
            mysqli_options($conn, MYSQLI_OPT_CONNECT_TIMEOUT, 5);
            
            // 尝试连接
            $connected = mysqli_real_connect(
                $conn,
                $config['HOST'],
                $config['USER'],
                $config['PASSWORD'],
                $config['DATABASE'],
                $config['PORT']
            );
            
            if (!$connected) {
                error_log("[DB ERROR] 连接失败 ($db_type): " . mysqli_connect_error());
                return null;
            }
            
            // 设置字符集
            mysqli_set_charset($conn, $config['CHARSET']);
            
            return $conn;
        } catch (Exception $e) {
            error_log("[DB ERROR] 连接异常 ($db_type): " . $e->getMessage());
            return null;
        }
    }
}

/**
 * 执行SQL查询并返回结果数组
 * @param string $sql SQL查询语句
 * @param array $params 参数数组 [type => 类型字符串, values => 参数值数组]
 * @param string $db_type 数据库类型
 * @return array|null 查询结果数组或null
 */
function execute_query($sql, $params = null, $db_type = 'default') {
    $conn = get_db_connection($db_type);
    if (!$conn) {
        return null;
    }
    
    try {
        $stmt = $conn->prepare($sql);
        if (!$stmt) {
            error_log("[DB ERROR] 预处理SQL失败: " . $conn->error);
            $conn->close();
            return null;
        }
        
        if ($params && is_array($params) && isset($params['types']) && isset($params['values'])) {
            $stmt->bind_param($params['types'], ...$params['values']);
        }
        
        if (!$stmt->execute()) {
            error_log("[DB ERROR] 执行SQL失败: " . $stmt->error);
            $stmt->close();
            $conn->close();
            return null;
        }
        
        $result = $stmt->get_result();
        if (!$result) {
            error_log("[DB ERROR] 获取结果集失败: " . $stmt->error);
            $stmt->close();
            $conn->close();
            return null;
        }
        
        $data = [];
        while ($row = $result->fetch_assoc()) {
            $data[] = $row;
        }
        
        $stmt->close();
        $conn->close();
        
        return $data;
    } catch (Exception $e) {
        error_log("[DB ERROR] 查询异常: " . $e->getMessage());
        if ($conn) {
            $conn->close();
        }
        return null;
    }
}

/**
 * 执行SQL写入/更新/删除操作
 * @param string $sql SQL语句
 * @param array $params 参数数组 [type => 类型字符串, values => 参数值数组]
 * @param string $db_type 数据库类型
 * @return int|null 受影响的行数或null
 */
function execute_update($sql, $params = null, $db_type = 'default') {
    $conn = get_db_connection($db_type);
    if (!$conn) {
        return null;
    }
    
    try {
        $stmt = $conn->prepare($sql);
        if (!$stmt) {
            error_log("[DB ERROR] 预处理SQL失败: " . $conn->error);
            $conn->close();
            return null;
        }
        
        if ($params && is_array($params) && isset($params['types']) && isset($params['values'])) {
            $stmt->bind_param($params['types'], ...$params['values']);
        }
        
        if (!$stmt->execute()) {
            error_log("[DB ERROR] 执行SQL失败: " . $stmt->error);
            $stmt->close();
            $conn->close();
            return null;
        }
        
        $affected = $stmt->affected_rows;
        $insert_id = $stmt->insert_id;
        
        $stmt->close();
        $conn->close();
        
        // 如果是INSERT操作且有自增ID，返回该ID
        if ($insert_id > 0) {
            return $insert_id;
        }
        
        return $affected;
    } catch (Exception $e) {
        error_log("[DB ERROR] 更新异常: " . $e->getMessage());
        if ($conn) {
            $conn->close();
        }
        return null;
    }
}

/**
 * 简单数据库连接函数
 * @param string $db_type 数据库类型: default(默认), payment(支付系统), water(水系统)
 * @return mysqli|null 数据库连接对象
 */
function db_connect($db_type = 'default')
{
    return get_db_connection($db_type);
}