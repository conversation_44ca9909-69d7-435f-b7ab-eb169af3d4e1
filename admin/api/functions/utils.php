<?php
/**
 * 工具函数库
 * 提供各种辅助函数
 */

/**
 * 美化输出变量，用于调试
 * @param mixed $var 要输出的变量
 * @param boolean $exit 是否在输出后退出
 * @return void
 */
function dd($var, $exit = true) {
    echo '<pre>';
    var_dump($var);
    echo '</pre>';
    if ($exit) exit;
}

/**
 * 获取当前时间戳（毫秒级）
 * @return float 当前毫秒时间戳
 */
function getMicrotime() {
    return microtime(true);
}

/**
 * 生成唯一ID
 * @return string 唯一ID
 */
function generateUniqueId() {
    return md5(uniqid(rand(), true));
}

/**
 * 检查字符串是否为JSON
 * @param string $string 要检查的字符串
 * @return boolean 是否为有效的JSON字符串
 */
function isJson($string) {
    json_decode($string);
    return (json_last_error() == JSON_ERROR_NONE);
}

/**
 * 格式化金额为2位小数
 * @param float $amount 金额
 * @return string 格式化后的金额
 */
function formatAmount($amount) {
    return number_format((float)$amount, 2, '.', '');
}

/**
 * 获取本地的绝对路径
 * 支持windows和linux
 * @param string $relativePath 相对路径
 * @return string 绝对路径
 */
function getAbsolutePath($relativePath) {
    $basePath = dirname(dirname(dirname(__FILE__)));
    return $basePath . '/' . ltrim($relativePath, '/');
}

/**
 * 检查文件是否存在
 * @param string $path 文件路径
 * @return boolean 文件是否存在
 */
function fileExists($path) {
    return file_exists($path);
}

/**
 * 安全读取文件内容
 * @param string $path 文件路径
 * @return string|false 文件内容或false（失败时）
 */
function safeReadFile($path) {
    if (!file_exists($path)) return false;
    return file_get_contents($path);
}

/**
 * 安全写入文件
 * @param string $path 文件路径
 * @param string $content 要写入的内容
 * @return boolean 是否写入成功
 */
function safeWriteFile($path, $content) {
    try {
        return (file_put_contents($path, $content) !== false);
    } catch (Exception $e) {
        return false;
    }
}

/**
 * 获取客户端IP地址
 * @return string IP地址
 */
function getClientIp() {
    if (!empty($_SERVER['HTTP_CLIENT_IP'])) {
        return $_SERVER['HTTP_CLIENT_IP'];
    } elseif (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
        return $_SERVER['HTTP_X_FORWARDED_FOR'];
    } else {
        return $_SERVER['REMOTE_ADDR'];
    }
}

/**
 * 日志记录函数
 * @param string $message 日志消息
 * @param string $level 日志级别
 * @param string $file 日志文件名
 * @return boolean 是否记录成功
 */
function writeLog($message, $level = 'info', $file = 'system.log') {
    $logDir = dirname(dirname(__FILE__)) . '/logs';
    if (!is_dir($logDir)) {
        mkdir($logDir, 0755, true);
    }
    
    $logFile = $logDir . '/' . $file;
    $timestamp = date('Y-m-d H:i:s');
    $logMessage = "[$timestamp] [$level] $message\n";
    
    return (file_put_contents($logFile, $logMessage, FILE_APPEND) !== false);
}

/**
 * 过滤用户输入
 * @param string $input 用户输入
 * @return string 过滤后的输入
 */
function filterInput($input) {
    return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
}

/**
 * 生成随机字符串
 * @param int $length 字符串长度
 * @return string 随机字符串
 */
function generateRandomString($length = 10) {
    $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
    $charactersLength = strlen($characters);
    $randomString = '';
    for ($i = 0; $i < $length; $i++) {
        $randomString .= $characters[rand(0, $charactersLength - 1)];
    }
    return $randomString;
}

/**
 * 递归创建目录
 * @param string $path 目录路径
 * @param int $permissions 权限（默认为0755）
 * @return boolean 是否创建成功
 */
function createDirectory($path, $permissions = 0755) {
    if (is_dir($path)) return true;
    $prev_path = dirname($path);
    if (!is_dir($prev_path) && !createDirectory($prev_path, $permissions)) return false;
    return mkdir($path, $permissions);
}
