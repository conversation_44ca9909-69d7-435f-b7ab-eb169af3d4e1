<?php
/**
 * 获取商城基本信息
 * 
 * @return array 商城信息
 */
function get_mall_info() {
    global $DB_CONFIG, $APP_CONFIG;
    
    try {
        // 连接数据库
        $conn = new mysqli(
            $DB_CONFIG['HOST'],
            $DB_CONFIG['USER'],
            $DB_CONFIG['PASSWORD'],
            $DB_CONFIG['DATABASE'],
            $DB_CONFIG['PORT']
        );
        
        // 检查连接
        if ($conn->connect_error) {
            return [
                'code' => 500,
                'message' => '数据库连接失败',
                'data' => null
            ];
        }
        
        // 设置字符集
        $conn->set_charset($DB_CONFIG['CHARSET']);
        
        // 查询商城信息
        $sql = "SELECT * FROM shop_config LIMIT 1";
        $result = $conn->query($sql);
        
        if ($result && $result->num_rows > 0) {
            $mall_info = $result->fetch_assoc();
            
            // 处理返回数据
            $data = [
                'name' => $mall_info['shop_name'] ?? $APP_CONFIG['NAME'],
                'logo' => $mall_info['logo_url'] ?? '',
                'slogan' => $mall_info['slogan'] ?? '点点够，买好货',
                'notice' => $mall_info['notice'] ?? '',
                'contact_phone' => $mall_info['contact_phone'] ?? '',
                'contact_email' => $mall_info['contact_email'] ?? '',
                'contact_address' => $mall_info['contact_address'] ?? '',
                'business_hours' => $mall_info['business_hours'] ?? '9:00-21:00'
            ];
            
            $conn->close();
            
            return [
                'code' => 0,
                'message' => '获取成功',
                'data' => $data
            ];
        } else {
            // 若数据库中没有数据，返回默认值
            $data = [
                'name' => $APP_CONFIG['NAME'],
                'logo' => '/uploads/logo.png',
                'slogan' => '点点够，买好货',
                'notice' => '欢迎使用点点够商城！',
                'contact_phone' => '************',
                'contact_email' => '<EMAIL>',
                'contact_address' => '北京市朝阳区',
                'business_hours' => '9:00-21:00'
            ];
            
            $conn->close();
            
            return [
                'code' => 0,
                'message' => '获取成功(默认值)',
                'data' => $data
            ];
        }
    } catch (Exception $e) {
        return [
            'code' => 500,
            'message' => '服务器错误：' . $e->getMessage(),
            'data' => null
        ];
    }
} 