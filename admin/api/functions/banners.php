<?php
/**
 * 获取首页轮播图
 * 
 * @return array 轮播图数据
 */
function get_banners() {
    global $DB_CONFIG;
    
    try {
        // 连接数据库
        $conn = new mysqli(
            $DB_CONFIG['HOST'],
            $DB_CONFIG['USER'],
            $DB_CONFIG['PASSWORD'],
            $DB_CONFIG['DATABASE'],
            $DB_CONFIG['PORT']
        );
        
        // 检查连接
        if ($conn->connect_error) {
            return [
                'code' => 500,
                'message' => '数据库连接失败',
                'data' => null
            ];
        }
        
        // 设置字符集
        $conn->set_charset($DB_CONFIG['CHARSET']);
        
        // 查询轮播图 - 修正了SQL查询以匹配数据库结构
        $sql = "SELECT * FROM banners WHERE status = 'active' AND position = 'home_top' 
                AND (start_time IS NULL OR start_time <= NOW()) 
                AND (end_time IS NULL OR end_time >= NOW()) 
                ORDER BY sort DESC LIMIT 5";
        $result = $conn->query($sql);
        
        $banners = [];
        
        if ($result && $result->num_rows > 0) {
            while ($row = $result->fetch_assoc()) {
                $banners[] = [
                    'id' => $row['id'],
                    'title' => $row['title'],
                    'image_url' => $row['image_url'],
                    'link_url' => $row['link_url'],
                    'link_type' => $row['link_type']
                ];
            }
            
            $conn->close();
            
            return [
                'code' => 0,
                'message' => '获取成功',
                'data' => $banners
            ];
        } else {
            // 若数据库中没有数据，返回默认值
            $default_banners = [
                [
                    'id' => 1,
                    'title' => '纯净矿泉水',
                    'image_url' => '/storage/banners/banner_bX6jOyrCJv.png',
                    'link_url' => 'product:1',
                    'link_type' => 'product'
                ],
                [
                    'id' => 2,
                    'title' => '净水器设备',
                    'image_url' => '/storage/banners/banner_water_purifier.svg',
                    'link_url' => 'category:2',
                    'link_type' => 'category'
                ],
                [
                    'id' => 3,
                    'title' => '社区取水点',
                    'image_url' => '/storage/banners/ibqmzucDwZtz8k3GSeGp1nubcweKchJlTDQTAdhx.png',
                    'link_url' => 'waterpoint:1',
                    'link_type' => 'waterpoint'
                ]
            ];
            
            $conn->close();
            
            return [
                'code' => 0,
                'message' => '获取成功(默认值)',
                'data' => $default_banners
            ];
        }
    } catch (Exception $e) {
        return [
            'code' => 500,
            'message' => '服务器错误：' . $e->getMessage(),
            'data' => null
        ];
    }
} 