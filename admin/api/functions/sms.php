<?php
/**
 * 短信验证码相关函数
 */

require_once __DIR__ . '/db.php';

/**
 * 验证短信验证码
 * @param string $phone 手机号
 * @param string $code 验证码
 * @param string $type 验证码类型，默认为'bind'
 * @return bool 验证是否成功
 */
function validate_sms_code($phone, $code, $type = 'bind') {
    $conn = get_db_connection();
    if (!$conn) {
        error_log("短信验证 - 数据库连接失败");
        return false;
    }
    
    try {
        // 查询有效的验证码（5分钟内且未使用）
        $stmt = $conn->prepare("
            SELECT id FROM sms_codes 
            WHERE phone = ? AND code = ? AND type = ? AND is_used = 0 
            AND created_at > DATE_SUB(NOW(), INTERVAL 5 MINUTE)
            ORDER BY created_at DESC 
            LIMIT 1
        ");
        
        $stmt->bind_param("sss", $phone, $code, $type);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result->num_rows > 0) {
            $row = $result->fetch_assoc();
            $code_id = $row['id'];
            
            // 标记验证码为已使用
            $update_stmt = $conn->prepare("UPDATE sms_codes SET is_used = 1, used_at = NOW() WHERE id = ?");
            $update_stmt->bind_param("i", $code_id);
            $update_stmt->execute();
            $update_stmt->close();
            
            $stmt->close();
            $conn->close();
            
            error_log("短信验证成功 - 手机号: $phone, 验证码: $code, 类型: $type");
            return true;
        } else {
            $stmt->close();
            $conn->close();
            
            error_log("短信验证失败 - 手机号: $phone, 验证码: $code, 类型: $type (验证码不存在、已过期或已使用)");
            return false;
        }
    } catch (Exception $e) {
        error_log("短信验证异常 - " . $e->getMessage());
        if ($conn) {
            $conn->close();
        }
        return false;
    }
}

/**
 * 清除指定手机号的验证码
 * @param string $phone 手机号
 * @param string $type 验证码类型，默认为'bind'
 * @return bool 清除是否成功
 */
function clear_sms_code($phone, $type = 'bind') {
    $conn = get_db_connection();
    if (!$conn) {
        error_log("清除短信验证码 - 数据库连接失败");
        return false;
    }
    
    try {
        // 标记所有相关验证码为已使用
        $stmt = $conn->prepare("UPDATE sms_codes SET is_used = 1, used_at = NOW() WHERE phone = ? AND type = ? AND is_used = 0");
        $stmt->bind_param("ss", $phone, $type);
        $stmt->execute();
        
        $affected_rows = $stmt->affected_rows;
        $stmt->close();
        $conn->close();
        
        error_log("清除短信验证码成功 - 手机号: $phone, 类型: $type, 影响行数: $affected_rows");
        return true;
    } catch (Exception $e) {
        error_log("清除短信验证码异常 - " . $e->getMessage());
        if ($conn) {
            $conn->close();
        }
        return false;
    }
}

/**
 * 检查短信发送频率限制
 * @param string $phone 手机号
 * @param string $type 验证码类型
 * @param int $interval 间隔时间（秒），默认60秒
 * @return bool 是否可以发送
 */
function check_sms_rate_limit($phone, $type, $interval = 60) {
    $conn = get_db_connection();
    if (!$conn) {
        error_log("检查短信频率限制 - 数据库连接失败");
        return false;
    }
    
    try {
        $stmt = $conn->prepare("
            SELECT id FROM sms_codes 
            WHERE phone = ? AND type = ? 
            AND created_at > DATE_SUB(NOW(), INTERVAL ? SECOND)
            LIMIT 1
        ");
        
        $stmt->bind_param("ssi", $phone, $type, $interval);
        $stmt->execute();
        $result = $stmt->get_result();
        
        $can_send = $result->num_rows == 0;
        
        $stmt->close();
        $conn->close();
        
        return $can_send;
    } catch (Exception $e) {
        error_log("检查短信频率限制异常 - " . $e->getMessage());
        if ($conn) {
            $conn->close();
        }
        return false;
    }
}

/**
 * 生成短信验证码
 * @param int $length 验证码长度，默认6位
 * @return string 验证码
 */
function generate_sms_code($length = 6) {
    return sprintf("%0{$length}d", mt_rand(0, pow(10, $length) - 1));
}

/**
 * 保存短信验证码到数据库
 * @param string $phone 手机号
 * @param string $code 验证码
 * @param string $type 验证码类型
 * @return int|false 返回插入的ID或false
 */
function save_sms_code($phone, $code, $type) {
    $conn = get_db_connection();
    if (!$conn) {
        error_log("保存短信验证码 - 数据库连接失败");
        return false;
    }
    
    try {
        $stmt = $conn->prepare("INSERT INTO sms_codes (phone, code, type, is_used, created_at) VALUES (?, ?, ?, 0, NOW())");
        $stmt->bind_param("sss", $phone, $code, $type);
        
        if ($stmt->execute()) {
            $insert_id = $conn->insert_id;
            $stmt->close();
            $conn->close();
            
            error_log("保存短信验证码成功 - 手机号: $phone, 验证码: $code, 类型: $type, ID: $insert_id");
            return $insert_id;
        } else {
            $error = $stmt->error;
            $stmt->close();
            $conn->close();
            
            error_log("保存短信验证码失败 - " . $error);
            return false;
        }
    } catch (Exception $e) {
        error_log("保存短信验证码异常 - " . $e->getMessage());
        if ($conn) {
            $conn->close();
        }
        return false;
    }
}
?> 