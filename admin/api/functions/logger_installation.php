<?php
/**
 * 安装预约模块日志记录函数
 */

// 定义日志级别
define('LOG_DEBUG', 'DEBUG');
define('LOG_INFO', 'INFO');
define('LOG_WARNING', 'WARNING');
define('LOG_ERROR', 'ERROR');

/**
 * 记录安装预约模块日志
 * 
 * @param string $message 日志消息
 * @param string $level 日志级别 (DEBUG, INFO, WARNING, ERROR)
 * @param array $context 上下文数据
 * @return void
 */
function log_installation($message, $level = LOG_INFO, $context = []) {
    // 获取日期
    $date = date('Y-m-d');
    
    // 日志文件路径
    $logFile = __DIR__ . '/../logs/installation/installation_' . $date . '.log';
    
    // 确保日志目录存在
    $logDir = dirname($logFile);
    if (!is_dir($logDir)) {
        mkdir($logDir, 0777, true);
    }
    
    // 格式化日志消息
    $timestamp = date('Y-m-d H:i:s');
    $contextStr = !empty($context) ? json_encode($context, JSON_UNESCAPED_UNICODE) : '{}';
    $logEntry = "[$timestamp] [$level] $message | $contextStr" . PHP_EOL;
    
    // 写入日志文件
    file_put_contents($logFile, $logEntry, FILE_APPEND);
}

/**
 * 记录安装预约支付日志
 * 
 * @param string $message 日志消息
 * @param string $level 日志级别 (DEBUG, INFO, WARNING, ERROR)
 * @param array $context 上下文数据
 * @return void
 */
function log_installation_payment($message, $level = LOG_INFO, $context = []) {
    // 获取日期
    $date = date('Y-m-d');
    
    // 日志文件路径
    $logFile = __DIR__ . '/../logs/installation/payment_' . $date . '.log';
    
    // 确保日志目录存在
    $logDir = dirname($logFile);
    if (!is_dir($logDir)) {
        mkdir($logDir, 0777, true);
    }
    
    // 格式化日志消息
    $timestamp = date('Y-m-d H:i:s');
    $contextStr = !empty($context) ? json_encode($context, JSON_UNESCAPED_UNICODE) : '{}';
    $logEntry = "[$timestamp] [$level] $message | $contextStr" . PHP_EOL;
    
    // 写入日志文件
    file_put_contents($logFile, $logEntry, FILE_APPEND);
}

/**
 * 记录安装预约API调用日志
 * 
 * @param string $api API名称
 * @param array $params 请求参数
 * @param array $response 响应数据
 * @param string $level 日志级别 (DEBUG, INFO, WARNING, ERROR)
 * @return void
 */
function log_installation_api($api, $params = [], $response = [], $level = LOG_INFO) {
    // 获取日期
    $date = date('Y-m-d');
    
    // 日志文件路径
    $logFile = __DIR__ . '/../logs/installation/api_' . $date . '.log';
    
    // 确保日志目录存在
    $logDir = dirname($logFile);
    if (!is_dir($logDir)) {
        mkdir($logDir, 0777, true);
    }
    
    // 格式化日志消息
    $timestamp = date('Y-m-d H:i:s');
    $paramsStr = !empty($params) ? json_encode($params, JSON_UNESCAPED_UNICODE) : '{}';
    $responseStr = !empty($response) ? json_encode($response, JSON_UNESCAPED_UNICODE) : '{}';
    $logEntry = "[$timestamp] [$level] API: $api | Params: $paramsStr | Response: $responseStr" . PHP_EOL;
    
    // 写入日志文件
    file_put_contents($logFile, $logEntry, FILE_APPEND);
}
