<?php
/**
 * 微信工具函数库
 */

// 微信应用配置
define('WECHAT_APP_ID', 'wx501332efbaae387c');
define('WECHAT_APP_SECRET', 'f70ad4faefb54e68e3a5e7b5885a7c28');

/**
 * 获取微信用户信息
 * @param string $code 微信授权码
 * @return array 用户信息
 */
function getWechatUserInfo($code) {
    // 获取访问令牌
    $tokenUrl = "https://api.weixin.qq.com/sns/oauth2/access_token?appid=" . WECHAT_APP_ID . "&secret=" . WECHAT_APP_SECRET . "&code=" . $code . "&grant_type=authorization_code";
    $tokenResponse = httpGet($tokenUrl);
    $tokenData = json_decode($tokenResponse, true);
    
    if (empty($tokenData['access_token']) || empty($tokenData['openid'])) {
        error_log("获取微信访问令牌失败: " . json_encode($tokenData));
        throw new Exception(isset($tokenData['errmsg']) ? $tokenData['errmsg'] : '获取访问令牌失败');
    }
    
    // 获取用户信息
    $userInfoUrl = "https://api.weixin.qq.com/sns/userinfo?access_token=" . $tokenData['access_token'] . "&openid=" . $tokenData['openid'] . "&lang=zh_CN";
    $userInfoResponse = httpGet($userInfoUrl);
    $userInfo = json_decode($userInfoResponse, true);
    
    if (empty($userInfo['nickname'])) {
        error_log("获取微信用户信息失败: " . json_encode($userInfo));
        throw new Exception(isset($userInfo['errmsg']) ? $userInfo['errmsg'] : '获取用户信息失败');
    }
    
    return $userInfo;
}

/**
 * 发送HTTP GET请求
 * @param string $url 请求地址
 * @return string 响应内容
 */
function httpGet($url) {
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    $response = curl_exec($ch);
    
    if ($response === false) {
        $error = curl_error($ch);
        curl_close($ch);
        throw new Exception("HTTP请求失败: " . $error);
    }
    
    curl_close($ch);
    return $response;
}

/**
 * 生成模拟微信用户信息（仅用于测试）
 * @param string $code 模拟code
 * @return array 模拟用户信息
 */
function mockWechatUserInfo($code) {
    return [
        'openid' => 'test_openid_' . substr(md5($code), 0, 8),
        'nickname' => '测试用户',
        'sex' => 1,
        'language' => 'zh_CN',
        'city' => '深圳',
        'province' => '广东',
        'country' => '中国',
        'headimgurl' => 'https://thirdwx.qlogo.cn/mmopen/vi_32/default_avatar/132',
        'privilege' => [],
        'unionid' => 'test_unionid_' . substr(md5($code), 0, 8)
    ];
}
?> 