<?php
// 通用API响应函数
function responseError($code, $msg) {
    header('Content-Type: application/json');
    echo json_encode([
        'code' => $code,
        'message' => $msg,
        'data' => null
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

function responseSuccess($data) {
    header('Content-Type: application/json');
    echo json_encode([
        'code' => 0,
        'message' => 'success',
        'data' => $data
    ], JSON_UNESCAPED_UNICODE);
    exit;
} 