<?php
/**
 * 商品分类相关函数
 */

/**
 * 获取商品分类列表
 * 
 * @return array 分类列表数据
 */
function get_categories() {
    global $DB_CONFIG;
    
    try {
        // 连接数据库
        $conn = new mysqli(
            $DB_CONFIG['HOST'],
            $DB_CONFIG['USER'],
            $DB_CONFIG['PASSWORD'],
            $DB_CONFIG['DATABASE'],
            $DB_CONFIG['PORT']
        );
        
        // 检查连接
        if ($conn->connect_error) {
            return [
                'code' => 500,
                'message' => '数据库连接失败',
                'data' => null
            ];
        }
        
        // 设置字符集
        $conn->set_charset($DB_CONFIG['CHARSET']);
        
        // 查询分类数据
        $sql = "SELECT id, name, icon, sort, status, created_at, updated_at 
                FROM product_categories 
                WHERE status = 1 
                ORDER BY sort ASC, id ASC";
        
        $result = $conn->query($sql);
        
        $categories = [];
        
        if ($result && $result->num_rows > 0) {
            while ($row = $result->fetch_assoc()) {
                $categories[] = [
                    'id' => (int)$row['id'],
                    'name' => $row['name'],
                    'icon' => $row['icon'],
                    'sort' => (int)$row['sort'],
                    'status' => (int)$row['status'],
                    'created_at' => $row['created_at'],
                    'updated_at' => $row['updated_at']
                ];
            }
            
            $conn->close();
            
            return [
                'code' => 0,
                'message' => '获取成功',
                'data' => $categories
            ];
        } else {
            // 若数据库中没有数据，返回默认分类
            $conn->close();
            
            $default_categories = [
                [
                    'id' => 1,
                    'name' => '净水器',
                    'icon' => '/images/categories/water-purifier.png',
                    'sort' => 1,
                    'status' => 1,
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ],
                [
                    'id' => 2,
                    'name' => '滤芯配件',
                    'icon' => '/images/categories/filter.png',
                    'sort' => 2,
                    'status' => 1,
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ],
                [
                    'id' => 3,
                    'name' => '检测设备',
                    'icon' => '/images/categories/detector.png',
                    'sort' => 3,
                    'status' => 1,
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ]
            ];
            
            return [
                'code' => 0,
                'message' => '获取成功',
                'data' => $default_categories
            ];
        }
        
    } catch (Exception $e) {
        return [
            'code' => 500,
            'message' => '获取分类失败: ' . $e->getMessage(),
            'data' => null
        ];
    }
}

/**
 * 获取单个分类详情
 * 
 * @param int $id 分类ID
 * @return array 分类详情数据
 */
function get_category($id) {
    global $DB_CONFIG;
    
    try {
        // 连接数据库
        $conn = new mysqli(
            $DB_CONFIG['HOST'],
            $DB_CONFIG['USER'],
            $DB_CONFIG['PASSWORD'],
            $DB_CONFIG['DATABASE'],
            $DB_CONFIG['PORT']
        );
        
        // 检查连接
        if ($conn->connect_error) {
            return [
                'code' => 500,
                'message' => '数据库连接失败',
                'data' => null
            ];
        }
        
        // 设置字符集
        $conn->set_charset($DB_CONFIG['CHARSET']);
        
        // 查询分类数据
        $sql = "SELECT id, name, icon, sort, status, created_at, updated_at 
                FROM product_categories 
                WHERE id = ? AND status = 1";
        
        $stmt = $conn->prepare($sql);
        $stmt->bind_param('i', $id);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result && $result->num_rows > 0) {
            $row = $result->fetch_assoc();
            
            $category = [
                'id' => (int)$row['id'],
                'name' => $row['name'],
                'icon' => $row['icon'],
                'sort' => (int)$row['sort'],
                'status' => (int)$row['status'],
                'created_at' => $row['created_at'],
                'updated_at' => $row['updated_at']
            ];
            
            $stmt->close();
            $conn->close();
            
            return [
                'code' => 0,
                'message' => '获取成功',
                'data' => $category
            ];
        } else {
            $stmt->close();
            $conn->close();
            
            return [
                'code' => 404,
                'message' => '分类不存在',
                'data' => null
            ];
        }
        
    } catch (Exception $e) {
        return [
            'code' => 500,
            'message' => '获取分类失败: ' . $e->getMessage(),
            'data' => null
        ];
    }
}