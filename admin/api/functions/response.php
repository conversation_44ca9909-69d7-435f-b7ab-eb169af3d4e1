<?php
/**
 * API响应处理函数
 */

// 成功响应
if (!function_exists('responseSuccess')) {
    function responseSuccess($data = null, $message = 'success') {
        header('Content-Type: application/json; charset=utf-8');
        echo json_encode([
            'code' => 0,
            'message' => $message,
            'data' => $data
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
}

// 错误响应
if (!function_exists('responseError')) {
    function responseError($code, $message = 'error', $data = null) {
        header('Content-Type: application/json; charset=utf-8');
        echo json_encode([
            'code' => $code,
            'message' => $message,
            'data' => $data
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
}

// 调试日志
if (!function_exists('debug_log')) {
    function debug_log($data, $title = '') {
        $logDir = __DIR__ . '/../logs';
        
        // 确保日志目录存在
        if (!file_exists($logDir)) {
            mkdir($logDir, 0777, true);
        }
        
        $logFile = $logDir . '/debug.log';
        $logData = date('Y-m-d H:i:s') . " - ";
        
        if (!empty($title)) {
            $logData .= "[{$title}] ";
        }
        
        if (is_array($data) || is_object($data)) {
            $logData .= print_r($data, true);
        } else {
            $logData .= $data;
        }
        
        $logData .= "\n";
        
        file_put_contents($logFile, $logData, FILE_APPEND);
    }
} 