<?php

/**
 * 登录日志记录助手函数
 */

require_once __DIR__ . '/../config.php';

/**
 * 记录登录日志
 * 
 * @param int|null $userId 用户ID
 * @param string $userType 用户类型 (app_user/admin_user)
 * @param string $loginMethod 登录方式 (password/wechat/code/bind_phone/auto_bind)
 * @param string $status 状态 (success/failed)
 * @param string $message 消息
 * @param string|null $ipAddress IP地址
 * @param string|null $userAgent 用户代理
 * @return bool
 */
function recordLoginLog($userId, $userType, $loginMethod, $status, $message = '', $ipAddress = null, $userAgent = null) {
    global $DB_CONFIG;
    
    try {
        // 获取数据库连接
        $pdo = new PDO(
            "mysql:host={$DB_CONFIG['HOST']};dbname={$DB_CONFIG['DATABASE']};charset=utf8mb4",
            $DB_CONFIG['USER'],
            $DB_CONFIG['PASSWORD'],
            [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4"
            ]
        );
        
        // 获取IP地址和用户代理
        if (!$ipAddress) {
            $ipAddress = $_SERVER['REMOTE_ADDR'] ?? '';
            // 检查是否有代理IP
            if (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
                $ipAddress = $_SERVER['HTTP_X_FORWARDED_FOR'];
            } elseif (!empty($_SERVER['HTTP_X_REAL_IP'])) {
                $ipAddress = $_SERVER['HTTP_X_REAL_IP'];
            }
        }
        
        if (!$userAgent) {
            $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
        }
        
        // 插入登录日志
        $sql = "INSERT INTO login_logs (user_id, user_type, login_method, ip_address, user_agent, status, message, created_at, updated_at) 
                VALUES (?, ?, ?, ?, ?, ?, ?, NOW(), NOW())";
        
        $stmt = $pdo->prepare($sql);
        $result = $stmt->execute([
            $userId,
            $userType,
            $loginMethod,
            $ipAddress,
            $userAgent,
            $status,
            $message
        ]);
        
        return $result;
        
    } catch (Exception $e) {
        // 记录错误日志，但不影响主要业务流程
        error_log("记录登录日志失败: " . $e->getMessage());
        return false;
    }
}

/**
 * 记录APP用户登录日志
 */
function recordAppUserLoginLog($userId, $loginMethod, $status, $message = '') {
    return recordLoginLog($userId, 'app_user', $loginMethod, $status, $message);
}

/**
 * 记录管理员登录日志
 */
function recordAdminLoginLog($userId, $loginMethod, $status, $message = '') {
    return recordLoginLog($userId, 'admin_user', $loginMethod, $status, $message);
}

/**
 * 记录微信登录日志
 */
function recordWechatLoginLog($userId, $status, $message = '') {
    return recordAppUserLoginLog($userId, 'wechat', $status, $message);
}

/**
 * 记录密码登录日志
 */
function recordPasswordLoginLog($userId, $userType, $status, $message = '') {
    return recordLoginLog($userId, $userType, 'password', $status, $message);
}

/**
 * 记录验证码登录日志
 */
function recordCodeLoginLog($userId, $status, $message = '') {
    return recordAppUserLoginLog($userId, 'code', $status, $message);
}

/**
 * 记录手机号绑定日志
 */
function recordBindPhoneLog($userId, $status, $message = '') {
    return recordAppUserLoginLog($userId, 'bind_phone', $status, $message);
}

/**
 * 记录自动绑定日志
 */
function recordAutoBindLog($userId, $status, $message = '') {
    return recordAppUserLoginLog($userId, 'auto_bind', $status, $message);
} 