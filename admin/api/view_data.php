<?php require_once "config.php"; $db = new mysqli($DB_CONFIG["HOST"], $DB_CONFIG["USER"], $DB_CONFIG["PASSWORD"], $DB_CONFIG["DATABASE"], $DB_CONFIG["PORT"]); if (!$db->connect_error) { echo "=== nav_config表数据 ===\n"; $result = $db->query("SELECT * FROM nav_config ORDER BY sort_order ASC"); if ($result) { while($row = $result->fetch_assoc()) { echo json_encode($row, JSON_UNESCAPED_UNICODE)."\n"; } } echo "\n\n=== home_nav_items表数据 ===\n"; $result = $db->query("SELECT * FROM home_nav_items ORDER BY sort_order ASC"); if ($result) { while($row = $result->fetch_assoc()) { echo json_encode($row, JSON_UNESCAPED_UNICODE)."\n"; } } $db->close(); } ?>
