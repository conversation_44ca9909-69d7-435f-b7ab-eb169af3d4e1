<?php
/**
 * 商品详情API
 */

// 设置响应头
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 引入配置
require_once 'config.php';

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 获取商品ID
$id = isset($_GET['id']) ? intval($_GET['id']) : 0;

if ($id <= 0) {
    echo json_encode([
        'code' => 400,
        'message' => '缺少有效的商品ID',
        'data' => null
    ]);
    exit;
}

// 连接数据库
$conn = new mysqli(
    $DB_CONFIG['HOST'],
    $DB_CONFIG['USER'],
    $DB_CONFIG['PASSWORD'],
    $DB_CONFIG['DATABASE'],
    $DB_CONFIG['PORT']
);

// 检查连接
if ($conn->connect_error) {
    echo json_encode([
        'code' => 500,
        'message' => '数据库连接失败: ' . $conn->connect_error,
        'data' => null
    ]);
    exit;
}

// 设置字符集
$conn->set_charset($DB_CONFIG['CHARSET']);

// 查询商品信息
$sql = "SELECT p.*, c.name as category_name 
        FROM products p 
        LEFT JOIN product_categories c ON p.category_id = c.id 
        WHERE p.id = ? AND p.status = 1";

$stmt = $conn->prepare($sql);
$stmt->bind_param('i', $id);
$stmt->execute();
$result = $stmt->get_result();

if ($result && $result->num_rows > 0) {
    $product = $result->fetch_assoc();
    
    // 提取标签
    $tags = [];
    if (isset($product['is_hot']) && $product['is_hot'] == 1) {
        $tags[] = '热销';
    }
    if (isset($product['is_new']) && $product['is_new'] == 1) {
        $tags[] = '新品';
    }
    if (isset($product['is_recommend']) && $product['is_recommend'] == 1) {
        $tags[] = '推荐';
    }
    if (isset($product['is_premium']) && $product['is_premium'] == 1) {
        $tags[] = '精品';
    }
    
    // 处理图片URL
    $product['image_url'] = $product['thumbnail'];
    
    // 格式化返回数据
    $response_data = [
        'id' => $product['id'],
        'name' => $product['name'],
        'sub_title' => $product['sub_title'],
        'image_url' => $product['image_url'],
        'price' => $product['price'],
        'original_price' => $product['original_price'],
        'sales' => $product['sales'],
        'stock' => $product['stock'],
        'description' => $product['description'],
        'content' => $product['content'],
        'category_id' => $product['category_id'],
        'category_name' => $product['category_name'],
        'tags' => $tags,
        'is_hot' => (bool)$product['is_hot'],
        'is_new' => (bool)$product['is_new'],
        'is_recommend' => (bool)$product['is_recommend'],
        'is_premium' => (bool)$product['is_premium'],
        'is_on_sale' => (bool)$product['is_on_sale'],
        'created_at' => $product['created_at'],
        'updated_at' => $product['updated_at']
    ];
    
    echo json_encode([
        'code' => 0,
        'message' => '获取成功',
        'data' => $response_data
    ]);
} else {
    echo json_encode([
        'code' => 404,
        'message' => '商品不存在或已下架',
        'data' => null
    ]);
}

$stmt->close();
$conn->close(); 