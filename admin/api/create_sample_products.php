<?php
// 设置响应头
header('Content-Type: application/json');

// 引入配置
require_once 'config.php';

// 连接数据库
$conn = new mysqli(
    $DB_CONFIG['HOST'],
    $DB_CONFIG['USER'],
    $DB_CONFIG['PASSWORD'],
    $DB_CONFIG['DATABASE'],
    $DB_CONFIG['PORT']
);

// 检查连接
if ($conn->connect_error) {
    echo json_encode([
        'code' => 500,
        'message' => '数据库连接失败: ' . $conn->connect_error,
        'data' => null
    ]);
    exit;
}

// 设置字符集
$conn->set_charset($DB_CONFIG['CHARSET']);

// 检查商品分类表是否存在并创建示例分类
$categoryExists = $conn->query("SELECT 1 FROM product_categories LIMIT 1");
if (!$categoryExists) {
    // 创建分类表
    $createCategoryTable = "CREATE TABLE IF NOT EXISTS `product_categories` (
        `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
        `name` varchar(50) NOT NULL COMMENT '分类名称',
        `description` varchar(200) DEFAULT NULL COMMENT '分类描述',
        `thumbnail` varchar(255) DEFAULT NULL COMMENT '分类缩略图',
        `parent_id` bigint(20) UNSIGNED DEFAULT 0 COMMENT '父级分类ID',
        `sort` int(11) DEFAULT 0 COMMENT '排序',
        `status` tinyint(4) DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
        `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
        `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;";
    
    $conn->query($createCategoryTable);
    
    // 插入默认分类
    $insertCategories = "INSERT INTO `product_categories` (`name`, `description`, `sort`) VALUES 
        ('净水器', '各种智能净水器产品', 1),
        ('滤芯配件', '净水器滤芯及配件', 2),
        ('净水服务', '净水相关服务', 3),
        ('检测设备', '水质检测设备', 4);";
    
    $conn->query($insertCategories);
    
    echo "创建商品分类表并添加示例分类.<br>";
}

// 检查products表是否有数据
$productCount = $conn->query("SELECT COUNT(*) as count FROM products")->fetch_assoc()['count'];

if ($productCount > 0) {
    echo json_encode([
        'code' => 0,
        'message' => '商品表已有 ' . $productCount . ' 条数据，无需添加示例数据',
        'data' => null
    ]);
    exit;
}

// 插入示例商品数据
$sampleProducts = [
    [
        'category_id' => 1,
        'name' => '智能净水器 T1000',
        'sub_title' => '五级过滤，智能控制，健康饮水',
        'thumbnail' => '/uploads/products/water_purifier_1.jpg',
        'price' => 1999.00,
        'original_price' => 2499.00,
        'cost_price' => 1500.00,
        'stock' => 100,
        'sales' => 356,
        'description' => '采用五级过滤技术，智能显示水质，自动冲洗，让您饮水更健康',
        'is_on_sale' => 1,
        'is_hot' => 1,
        'is_recommend' => 1,
        'is_new' => 0,
        'is_premium' => 1,
        'sort' => 1
    ],
    [
        'category_id' => 1,
        'name' => '家用反渗透纯水机',
        'sub_title' => '家用经济型纯水机，高效过滤',
        'thumbnail' => '/uploads/products/water_purifier_2.jpg',
        'price' => 1299.00,
        'original_price' => 1599.00,
        'cost_price' => 900.00,
        'stock' => 200,
        'sales' => 249,
        'description' => '采用反渗透技术，有效过滤水中杂质，经济实用的家庭饮水选择',
        'is_on_sale' => 1,
        'is_hot' => 0,
        'is_recommend' => 1,
        'is_new' => 0,
        'is_premium' => 0,
        'sort' => 2
    ],
    [
        'category_id' => 2,
        'name' => '净水器滤芯套装',
        'sub_title' => '原装进口，长效过滤',
        'thumbnail' => '/uploads/products/filter_1.jpg',
        'price' => 399.00,
        'original_price' => 499.00,
        'cost_price' => 250.00,
        'stock' => 500,
        'sales' => 1024,
        'description' => '原装进口滤芯，高效过滤，长效使用，适配多种净水器型号',
        'is_on_sale' => 1,
        'is_hot' => 1,
        'is_recommend' => 1,
        'is_new' => 0,
        'is_premium' => 1,
        'sort' => 3
    ],
    [
        'category_id' => 4,
        'name' => '智能水质检测仪',
        'sub_title' => '随时检测，安心饮水',
        'thumbnail' => '/uploads/products/tester_1.jpg',
        'price' => 89.00,
        'original_price' => 129.00,
        'cost_price' => 50.00,
        'stock' => 300,
        'sales' => 789,
        'description' => '便携式水质检测仪，智能检测水质指标，让您随时了解饮水安全',
        'is_on_sale' => 1,
        'is_hot' => 0,
        'is_recommend' => 1,
        'is_new' => 1,
        'is_premium' => 0,
        'sort' => 4
    ],
    [
        'category_id' => 3,
        'name' => '净水服务年卡',
        'sub_title' => '年卡8折，畅享清净',
        'thumbnail' => '/uploads/products/service_1.jpg',
        'price' => 980.00,
        'original_price' => 1280.00,
        'cost_price' => 600.00,
        'stock' => 999,
        'sales' => 345,
        'description' => '净水服务年卡，包含设备维护、滤芯更换、水质检测等服务',
        'is_on_sale' => 1,
        'is_hot' => 1,
        'is_recommend' => 1,
        'is_new' => 0,
        'is_premium' => 0,
        'sort' => 5
    ]
];

// 创建上传目录
$uploadDir = dirname(__DIR__, 1) . '/public/uploads/products';
if (!file_exists($uploadDir)) {
    mkdir($uploadDir, 0755, true);
}

// 准备示例图片
$sampleImages = [
    '/uploads/products/water_purifier_1.jpg' => 'https://img.itapgo.com/product1.jpg',
    '/uploads/products/water_purifier_2.jpg' => 'https://img.itapgo.com/product2.jpg',
    '/uploads/products/filter_1.jpg' => 'https://img.itapgo.com/product3.jpg',
    '/uploads/products/tester_1.jpg' => 'https://img.itapgo.com/product4.jpg',
    '/uploads/products/service_1.jpg' => 'https://img.itapgo.com/product5.jpg'
];

// 将示例图片保存到上传目录
foreach ($sampleImages as $localPath => $remoteUrl) {
    $fullLocalPath = dirname(__DIR__, 1) . '/public' . $localPath;
    $dir = dirname($fullLocalPath);
    
    if (!file_exists($dir)) {
        mkdir($dir, 0755, true);
    }
    
    // 使用默认图片代替
    $defaultImage = dirname(__DIR__, 1) . '/public/uploads/default-product.jpg';
    if (file_exists($defaultImage)) {
        copy($defaultImage, $fullLocalPath);
    } else {
        // 创建一个空白图片
        $image = imagecreatetruecolor(300, 300);
        $bgColor = imagecolorallocate($image, 245, 245, 245);
        $textColor = imagecolorallocate($image, 50, 50, 50);
        imagefill($image, 0, 0, $bgColor);
        imagestring($image, 5, 100, 140, 'Product Image', $textColor);
        imagejpeg($image, $fullLocalPath);
        imagedestroy($image);
    }
}

// 插入示例商品
$insertedCount = 0;
foreach ($sampleProducts as $product) {
    $sql = "INSERT INTO products (
        category_id, name, sub_title, thumbnail, price, original_price, cost_price,
        stock, sales, description, is_on_sale, is_hot, is_recommend, is_new, is_premium, sort, status
    ) VALUES (
        ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 1
    )";
    
    $stmt = $conn->prepare($sql);
    $stmt->bind_param(
        'isssdddiissiiisi',
        $product['category_id'],
        $product['name'],
        $product['sub_title'],
        $product['thumbnail'],
        $product['price'],
        $product['original_price'],
        $product['cost_price'],
        $product['stock'],
        $product['sales'],
        $product['description'],
        $product['is_on_sale'],
        $product['is_hot'],
        $product['is_recommend'],
        $product['is_new'],
        $product['is_premium'],
        $product['sort']
    );
    
    if ($stmt->execute()) {
        $insertedCount++;
    } else {
        echo "插入商品 {$product['name']} 失败: " . $stmt->error . "<br>";
    }
    
    $stmt->close();
}

echo json_encode([
    'code' => 0,
    'message' => "成功添加 {$insertedCount} 个示例商品",
    'data' => null
]);

$conn->close(); 