<?php
/**
 * 更新设备自用状态API
 * 用于设置设备是销售设备还是自用设备
 */

// 设置HTTP响应头
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
header('Access-Control-Max-Age: 86400'); // 24小时

// 错误处理设置
ini_set('display_errors', 0);
ini_set('log_errors', 1);
ini_set('error_log', dirname(__DIR__) . '/debug_error.log');
error_reporting(E_ALL);

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 检查请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode([
        'code' => 1,
        'message' => '请求方法不允许',
        'data' => null
    ]);
    exit;
}

// 生成唯一请求ID
$request_id = uniqid();
error_log("[$request_id] 更新设备自用状态请求开始");

// 引入Laravel的引导文件
require_once dirname(dirname(dirname(__DIR__))) . '/admin/vendor/autoload.php';
$app = require_once dirname(dirname(dirname(__DIR__))) . '/admin/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Http\Kernel::class);
$response = $kernel->handle(
    $request = Illuminate\Http\Request::capture()
);

// 获取请求中的认证Token
$authHeader = isset($_SERVER['HTTP_AUTHORIZATION']) ? $_SERVER['HTTP_AUTHORIZATION'] : '';
$token = '';

if (preg_match('/Bearer\s(\S+)/', $authHeader, $matches)) {
    $token = $matches[1];
}

// 如果请求头中没有token，尝试从cookie获取
if (empty($token) && isset($_COOKIE['token'])) {
    $token = $_COOKIE['token'];
    error_log("[$request_id] 从cookie中获取token");
}

// 如果还是没有token，尝试从查询参数获取
if (empty($token) && isset($_GET['token'])) {
    $token = $_GET['token'];
    error_log("[$request_id] 从查询参数中获取token");
}

if (empty($token)) {
    http_response_code(401);
    echo json_encode([
        'code' => 1,
        'message' => '未授权访问',
        'data' => null
    ]);
    error_log("[$request_id] 未提供token");
    exit;
}

// 获取用户
$userProvider = new Illuminate\Auth\EloquentUserProvider(
    new Illuminate\Hashing\BcryptHasher(), 
    'App\Models\AppUser'
);

$user = null;
try {
    // 验证令牌
    $tokenObj = \Laravel\Sanctum\PersonalAccessToken::findToken($token);
    
    if ($tokenObj) {
        $user = $tokenObj->tokenable;
        error_log("[$request_id] 使用Sanctum验证令牌成功");
    } else {
        // Sanctum验证失败，尝试JWT验证
        error_log("[$request_id] Sanctum验证失败，尝试JWT验证");
        
        // 解析JWT令牌
        try {
            // JWT格式: header.payload.signature
            $tokenParts = explode('.', $token);
            if (count($tokenParts) === 3) {
                $payload = json_decode(base64_decode($tokenParts[1]), true);
                
                if (isset($payload['user_id'])) {
                    error_log("[$request_id] JWT令牌包含用户ID: " . $payload['user_id']);
                    
                    // 使用用户ID查找用户
                    $user = \App\Models\AppUser::find($payload['user_id']);
                    
                    if ($user) {
                        error_log("[$request_id] 通过JWT用户ID找到用户");
                    } else {
                        error_log("[$request_id] 找不到JWT中指定的用户");
                    }
                } else {
                    error_log("[$request_id] JWT令牌不包含用户ID");
                }
            } else {
                error_log("[$request_id] 无效的JWT格式");
            }
        } catch (\Exception $jwtEx) {
            error_log("[$request_id] JWT解析错误: " . $jwtEx->getMessage());
        }
    }
    
    if (!$user) {
        http_response_code(401);
        echo json_encode([
            'code' => 1,
            'message' => '无效的访问令牌',
            'data' => null
        ]);
        exit;
    }
} catch (\Exception $e) {
    http_response_code(500);
    echo json_encode([
        'code' => 1,
        'message' => '验证令牌时出错',
        'data' => null
    ]);
    error_log("[$request_id] 验证令牌错误: " . $e->getMessage());
    exit;
}

// 获取请求数据
$input = json_decode(file_get_contents('php://input'), true);

// 验证请求数据
if (!isset($input['device_id']) || !isset($input['is_self_use'])) {
    http_response_code(400);
    echo json_encode([
        'code' => 1,
        'message' => '缺少必要参数',
        'data' => null
    ]);
    error_log("[$request_id] 请求缺少必要参数");
    exit;
}

$deviceId = intval($input['device_id']);
$isSelfUse = intval($input['is_self_use']);

// 确保是_self_use是0或1
if ($isSelfUse !== 0 && $isSelfUse !== 1) {
    http_response_code(400);
    echo json_encode([
        'code' => 1,
        'message' => 'is_self_use参数必须为0或1',
        'data' => null
    ]);
    error_log("[$request_id] is_self_use参数无效: {$isSelfUse}");
    exit;
}

try {
    // 查找设备记录
    $device = \DB::table('tapp_devices')->where('id', $deviceId)->first();
    
    if (!$device) {
        http_response_code(404);
        echo json_encode([
            'code' => 1,
            'message' => '设备不存在',
            'data' => null
        ]);
        error_log("[$request_id] 设备ID {$deviceId} 不存在");
        exit;
    }
    
    // 检查用户权限 - 只有设备关联的用户或管理员才能修改
    if ($device->app_user_id != $user->id && !$user->hasRole('admin')) {
        http_response_code(403);
        echo json_encode([
            'code' => 1,
            'message' => '您没有权限修改此设备',
            'data' => null
        ]);
        error_log("[$request_id] 用户 {$user->id} 无权修改设备 {$deviceId}");
        exit;
    }
    
    // 更新设备自用状态
    $updated = \DB::table('tapp_devices')
        ->where('id', $deviceId)
        ->update(['is_self_use' => $isSelfUse]);
    
    if ($updated) {
        error_log("[$request_id] 设备 {$deviceId} 的自用状态已更新为 {$isSelfUse}");
        
        // 返回成功响应
        echo json_encode([
            'code' => 0,
            'message' => '设备自用状态更新成功',
            'data' => [
                'device_id' => $deviceId,
                'is_self_use' => $isSelfUse
            ]
        ]);
    } else {
        // 更新失败，可能是状态没有变化
        error_log("[$request_id] 设备 {$deviceId} 的自用状态未发生变化");
        
        echo json_encode([
            'code' => 0,
            'message' => '设备自用状态未发生变化',
            'data' => [
                'device_id' => $deviceId,
                'is_self_use' => $isSelfUse
            ]
        ]);
    }
} catch (\Exception $e) {
    // 记录错误
    error_log("[$request_id] 更新设备自用状态出错: " . $e->getMessage());
    error_log("[$request_id] 错误详情: " . $e->getTraceAsString());
    
    // 返回错误响应
    http_response_code(500);
    echo json_encode([
        'code' => 1,
        'message' => '更新设备自用状态失败',
        'data' => null
    ]);
} 