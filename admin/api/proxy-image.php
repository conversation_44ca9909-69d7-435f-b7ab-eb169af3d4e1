<?php
/**
 * 图片代理API - 用于解决微信头像等跨域图片的显示问题
 * 主要用于html2canvas截图时的图片处理
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// 处理OPTIONS预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

try {
    // 获取要代理的图片URL
    $imageUrl = $_GET['url'] ?? '';
    
    if (empty($imageUrl)) {
        throw new Exception('缺少图片URL参数');
    }
    
    // 验证URL格式
    if (!filter_var($imageUrl, FILTER_VALIDATE_URL)) {
        throw new Exception('无效的图片URL');
    }
    
    // 只允许代理微信头像域名，增强安全性
    $allowedDomains = [
        'wx.qlogo.cn',
        'thirdwx.qlogo.cn',
        'wx1.sinaimg.cn',
        'wx2.sinaimg.cn',
        'wx3.sinaimg.cn',
        'wx4.sinaimg.cn'
    ];
    
    $urlHost = parse_url($imageUrl, PHP_URL_HOST);
    if (!in_array($urlHost, $allowedDomains)) {
        throw new Exception('不允许的图片域名');
    }
    
    // 初始化cURL
    $ch = curl_init();
    curl_setopt_array($ch, [
        CURLOPT_URL => $imageUrl,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_MAXREDIRS => 3,
        CURLOPT_TIMEOUT => 10,
        CURLOPT_CONNECTTIMEOUT => 5,
        CURLOPT_SSL_VERIFYPEER => false,
        CURLOPT_SSL_VERIFYHOST => false,
        CURLOPT_USERAGENT => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        CURLOPT_HTTPHEADER => [
            'Accept: image/webp,image/apng,image/*,*/*;q=0.8',
            'Accept-Language: zh-CN,zh;q=0.9,en;q=0.8',
            'Cache-Control: no-cache',
            'Pragma: no-cache'
        ]
    ]);
    
    $imageData = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $contentType = curl_getinfo($ch, CURLINFO_CONTENT_TYPE);
    $error = curl_error($ch);
    curl_close($ch);
    
    if ($imageData === false || !empty($error)) {
        throw new Exception('图片下载失败: ' . $error);
    }
    
    if ($httpCode !== 200) {
        throw new Exception('图片请求失败，HTTP状态码: ' . $httpCode);
    }
    
    // 验证是否为图片内容
    if (!$contentType || strpos($contentType, 'image/') !== 0) {
        throw new Exception('返回的不是图片内容');
    }
    
    // 验证图片数据
    if (strlen($imageData) < 100) {
        throw new Exception('图片数据太小，可能无效');
    }
    
    // 将图片转换为base64
    $base64 = base64_encode($imageData);
    $mimeType = $contentType ?: 'image/jpeg';
    $dataUrl = 'data:' . $mimeType . ';base64,' . $base64;
    
    // 返回成功响应
    echo json_encode([
        'code' => 0,
        'message' => '图片代理成功',
        'data' => [
            'dataUrl' => $dataUrl,
            'mimeType' => $mimeType,
            'size' => strlen($imageData),
            'originalUrl' => $imageUrl
        ]
    ]);
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'code' => 1,
        'message' => '图片代理失败: ' . $e->getMessage(),
        'data' => null
    ]);
}
?> 