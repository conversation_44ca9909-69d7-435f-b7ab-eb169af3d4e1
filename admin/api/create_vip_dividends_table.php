<?php
/**
 * 创建VIP分红表脚本
 * 用于创建vip_dividends表，并填充示例数据
 */

// 引入配置文件
require_once __DIR__ . '/config.php';

// 连接数据库
$conn = new mysqli($DB_CONFIG['HOST'], $DB_CONFIG['USER'], $DB_CONFIG['PASSWORD'], $DB_CONFIG['DATABASE'], $DB_CONFIG['PORT']);

// 检查连接
if ($conn->connect_error) {
    die("连接失败: " . $conn->connect_error);
}

// 设置字符集
$conn->set_charset($DB_CONFIG['CHARSET']);

// 创建vip_dividends表
$sql = "CREATE TABLE IF NOT EXISTS vip_dividends (
    id INT(11) UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    user_id INT(11) UNSIGNED NOT NULL COMMENT '用户ID',
    amount DECIMAL(10,2) NOT NULL DEFAULT 0.00 COMMENT '分红金额',
    type ENUM('vip', 'recharge') NOT NULL DEFAULT 'vip' COMMENT '分红类型：vip-VIP招募分红, recharge-充值分红',
    level ENUM('primary', 'middle', 'high') NOT NULL DEFAULT 'primary' COMMENT '分红等级：primary-初级, middle-中级, high-高级',
    status ENUM('pending', 'settled', 'cancelled') NOT NULL DEFAULT 'pending' COMMENT '状态：pending-待结算, settled-已结算, cancelled-已取消',
    description VARCHAR(255) DEFAULT NULL COMMENT '分红描述',
    period VARCHAR(20) DEFAULT NULL COMMENT '分红周期，如：2023-05',
    team_vip_count INT(11) UNSIGNED DEFAULT 0 COMMENT '团队VIP总人数',
    direct_vip_count INT(11) UNSIGNED DEFAULT 0 COMMENT '直推VIP人数',
    month_direct_vip_count INT(11) UNSIGNED DEFAULT 0 COMMENT '本月直推VIP人数',
    team_recharge_count INT(11) UNSIGNED DEFAULT 0 COMMENT '团队充值总数量',
    direct_recharge_count INT(11) UNSIGNED DEFAULT 0 COMMENT '直推充值数量',
    month_direct_recharge_count INT(11) UNSIGNED DEFAULT 0 COMMENT '本月直推充值数量',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_user_id (user_id),
    INDEX idx_type (type),
    INDEX idx_level (level),
    INDEX idx_status (status),
    INDEX idx_period (period),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='VIP分红记录表'";

if ($conn->query($sql) === TRUE) {
    echo "表 vip_dividends 创建成功或已存在\n";
} else {
    echo "创建表错误: " . $conn->error . "\n";
    exit;
}

// 检查是否需要填充示例数据
$checkDataQuery = "SELECT COUNT(*) as count FROM vip_dividends";
$checkDataResult = $conn->query($checkDataQuery);
$dataCount = $checkDataResult->fetch_assoc()['count'];

if ($dataCount > 0) {
    echo "表 vip_dividends 已有 $dataCount 条数据，跳过填充示例数据\n";
} else {
    echo "开始填充示例数据...\n";
    
    // 获取所有VIP用户
    $vipUsersQuery = "SELECT id, name FROM app_users WHERE is_vip = 1 AND is_vip_paid = 1 LIMIT 10";
    $vipUsersResult = $conn->query($vipUsersQuery);
    
    if ($vipUsersResult && $vipUsersResult->num_rows > 0) {
        $insertCount = 0;
        $errorCount = 0;
        
        // 当前月份
        $currentMonth = date('Y-m');
        $lastMonth = date('Y-m', strtotime('-1 month'));
        
        // 处理每个VIP用户
        while ($user = $vipUsersResult->fetch_assoc()) {
            $userId = $user['id'];
            $userName = $user['name'];
            
            // 生成随机分红数据
            $vipAmount = rand(50, 300) / 10; // 5.0 - 30.0
            $rechargeAmount = rand(30, 150) / 10; // 3.0 - 15.0
            
            // 插入VIP招募分红记录（本月）
            $insertVipSql = "INSERT INTO vip_dividends (
                user_id, amount, type, level, status, description, period,
                team_vip_count, direct_vip_count, month_direct_vip_count
            ) VALUES (
                ?, ?, 'vip', 'primary', 'pending', '本月VIP招募分红', ?,
                ?, ?, ?
            )";
            
            $stmt = $conn->prepare($insertVipSql);
            $teamVipCount = rand(3, 15);
            $directVipCount = rand(1, 5);
            $monthDirectVipCount = rand(0, 3);
            
            $stmt->bind_param(
                "iddsii", 
                $userId, 
                $vipAmount, 
                $currentMonth, 
                $teamVipCount, 
                $directVipCount, 
                $monthDirectVipCount
            );
            
            if ($stmt->execute()) {
                $insertCount++;
                echo "为用户 $userName (ID: $userId) 添加了本月VIP招募分红记录: $vipAmount 元\n";
            } else {
                $errorCount++;
                echo "插入VIP招募分红记录错误: " . $stmt->error . "\n";
            }
            
            $stmt->close();
            
            // 插入充值分红记录（上月，已结算）
            $insertRechargeSql = "INSERT INTO vip_dividends (
                user_id, amount, type, level, status, description, period,
                team_recharge_count, direct_recharge_count, month_direct_recharge_count
            ) VALUES (
                ?, ?, 'recharge', 'primary', 'settled', '上月充值分红', ?,
                ?, ?, ?
            )";
            
            $stmt = $conn->prepare($insertRechargeSql);
            $teamRechargeCount = rand(10, 30);
            $directRechargeCount = rand(3, 10);
            $monthDirectRechargeCount = rand(1, 5);
            
            $stmt->bind_param(
                "iddsii", 
                $userId, 
                $rechargeAmount, 
                $lastMonth, 
                $teamRechargeCount, 
                $directRechargeCount, 
                $monthDirectRechargeCount
            );
            
            if ($stmt->execute()) {
                $insertCount++;
                echo "为用户 $userName (ID: $userId) 添加了上月充值分红记录: $rechargeAmount 元\n";
            } else {
                $errorCount++;
                echo "插入充值分红记录错误: " . $stmt->error . "\n";
            }
            
            $stmt->close();
        }
        
        echo "示例数据填充完成。成功插入: $insertCount, 错误: $errorCount\n";
    } else {
        echo "未找到VIP用户，无法填充示例数据\n";
    }
}

// 关闭数据库连接
$conn->close();

echo "处理完成。\n";
