<?php
/**
 * 数据库连接文件
 */

// 数据库配置
$db_config = [
    'host' => '127.0.0.1',
    'port' => 3306,
    'username' => 'ddg.app',
    'password' => '8GmWPjwbwY4waXcT',
    'database' => 'ddg.app'
];

// 记录数据库连接信息
error_log("DB Config: " . json_encode($db_config));

/**
 * 获取数据库连接
 * @return mysqli|null 数据库连接对象，失败返回null
 */
function get_db_connection() {
    global $db_config;

    // 创建日志目录
    $logDir = __DIR__ . '/logs';
    if (!is_dir($logDir)) {
        mkdir($logDir, 0755, true);
    }

    // 写入日志文件
    $logFile = $logDir . '/db_' . date('Y-m-d') . '.log';
    file_put_contents($logFile, date('Y-m-d H:i:s') . " - 尝试连接数据库: " . json_encode($db_config) . "\n", FILE_APPEND);

    try {
        // 使用配置数组中的参数
        $conn = mysqli_connect(
            $db_config['host'],
            $db_config['username'],
            $db_config['password'],
            $db_config['database'],
            $db_config['port']
        );

        if (!$conn) {
            $error = mysqli_connect_error();
            file_put_contents($logFile, date('Y-m-d H:i:s') . " - 数据库连接失败: $error\n", FILE_APPEND);
            return null;
        }

        // 设置字符集
        mysqli_set_charset($conn, 'utf8mb4');
        file_put_contents($logFile, date('Y-m-d H:i:s') . " - 数据库连接成功\n", FILE_APPEND);
        return $conn;
    } catch (Exception $e) {
        file_put_contents($logFile, date('Y-m-d H:i:s') . " - 数据库连接异常: " . $e->getMessage() . "\n", FILE_APPEND);
        return null;
    }
}

/**
 * 发送JSON响应
 */
function send_json_response($code, $message, $data = null) {
    header('Content-Type: application/json');
    $response = [
        'code' => $code,
        'message' => $message,
        'data' => $data
    ];

    echo json_encode($response, JSON_UNESCAPED_UNICODE);
    exit;
}
