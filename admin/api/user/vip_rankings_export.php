<?php
header('Content-Type: application/vnd.ms-excel; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once '../config.php';

try {
    // 获取参数
    $rank_by = $_GET['rank_by'] ?? 'team_vip_count';
    $period = $_GET['period'] ?? 'all';
    $search = $_GET['search'] ?? '';
    
    // 验证排名维度
    $valid_rank_by = [
        'team_vip_count', 'direct_vip_count', 'balance', 
        'month_team_vip', 'last_month_team_vip', 'month_direct_vip',
        'total_dividend', 'month_dividend', 'team_recharge_count'
    ];
    
    if (!in_array($rank_by, $valid_rank_by)) {
        $rank_by = 'team_vip_count';
    }
    
    // 根据周期确定时间范围
    $dateCondition = '';
    switch ($period) {
        case 'month':
            $dateCondition = "AND DATE_FORMAT(u.vip_paid_at, '%Y-%m') = '" . date('Y-m') . "'";
            break;
        case 'last_month':
            $dateCondition = "AND DATE_FORMAT(u.vip_paid_at, '%Y-%m') = '" . date('Y-m', strtotime('-1 month')) . "'";
            break;
        case 'quarter':
            $quarterStart = date('Y-m-01', strtotime('first day of this quarter'));
            $quarterEnd = date('Y-m-t', strtotime('last day of this quarter'));
            $dateCondition = "AND u.vip_paid_at BETWEEN '$quarterStart' AND '$quarterEnd'";
            break;
        case 'year':
            $dateCondition = "AND YEAR(u.vip_paid_at) = " . date('Y');
            break;
        default:
            $dateCondition = '';
    }
    
    // 构建基础查询
    $sql = "SELECT 
                u.id,
                u.name,
                u.phone,
                u.wechat_nickname,
                u.balance,
                u.vip_paid_at,
                u.is_vip_paid
            FROM app_users u 
            WHERE u.is_vip_paid = 1 $dateCondition";
    
    // 添加搜索条件
    if (!empty($search)) {
        $search = mysqli_real_escape_string($conn, $search);
        $sql .= " AND (u.name LIKE '%$search%' OR u.phone LIKE '%$search%' OR u.wechat_nickname LIKE '%$search%')";
    }
    
    $sql .= " ORDER BY u.id";
    
    $result = mysqli_query($conn, $sql);
    if (!$result) {
        throw new Exception('查询用户数据失败: ' . mysqli_error($conn));
    }
    
    $users = [];
    while ($row = mysqli_fetch_assoc($result)) {
        $users[] = $row;
    }
    
    // 计算各项数据
    foreach ($users as &$user) {
        $userId = $user['id'];
        
        // 计算团队VIP总数（递归）
        $user['team_vip_count'] = getTeamVipCount($conn, $userId);
        
        // 计算直推VIP数量
        $user['direct_vip_count'] = getDirectVipCount($conn, $userId);
        
        // 计算本月新增团队VIP
        $user['month_team_vip'] = getMonthTeamVipCount($conn, $userId, date('Y-m'));
        
        // 计算上月新增团队VIP
        $lastMonth = date('Y-m', strtotime('-1 month'));
        $user['last_month_team_vip'] = getMonthTeamVipCount($conn, $userId, $lastMonth);
        
        // 计算本月直推VIP
        $user['month_direct_vip'] = getMonthDirectVipCount($conn, $userId, date('Y-m'));
        
        // 计算团队充值设备数量
        $user['team_recharge_count'] = getTeamRechargeCount($conn, $userId);
        
        // 获取分红数据
        $dividendSql = "SELECT 
                           SUM(amount) as total_dividend,
                           SUM(CASE WHEN DATE_FORMAT(created_at, '%Y-%m') = '" . date('Y-m') . "' THEN amount ELSE 0 END) as month_dividend
                        FROM vip_dividends 
                        WHERE user_id = $userId";
        $dividendResult = mysqli_query($conn, $dividendSql);
        if ($dividendResult) {
            $dividendRow = mysqli_fetch_assoc($dividendResult);
            $user['total_dividend'] = floatval($dividendRow['total_dividend'] ?? 0);
            $user['month_dividend'] = floatval($dividendRow['month_dividend'] ?? 0);
        } else {
            $user['total_dividend'] = 0;
            $user['month_dividend'] = 0;
        }
    }
    
    // 根据选择的维度排序
    usort($users, function($a, $b) use ($rank_by) {
        $aValue = floatval($a[$rank_by] ?? 0);
        $bValue = floatval($b[$rank_by] ?? 0);
        return $bValue <=> $aValue; // 降序排列
    });
    
    // 生成Excel文件
    $filename = 'VIP排行榜_' . getRankByText($rank_by) . '_' . date('Y-m-d') . '.xls';
    header("Content-Disposition: attachment; filename=\"$filename\"");
    
    // 输出Excel内容
    echo "\xEF\xBB\xBF"; // UTF-8 BOM
    echo "<table border='1'>";
    
    // 表头
    echo "<tr>";
    echo "<th>排名</th>";
    echo "<th>姓名</th>";
    echo "<th>手机号</th>";
    echo "<th>微信昵称</th>";
    echo "<th>排名数值</th>";
    echo "<th>团队VIP数量</th>";
    echo "<th>直推VIP数量</th>";
    echo "<th>账户余额</th>";
    echo "<th>本月新增团队VIP</th>";
    echo "<th>上月新增团队VIP</th>";
    echo "<th>本月直推VIP</th>";
    echo "<th>累计分红金额</th>";
    echo "<th>本月分红金额</th>";
    echo "<th>团队充值设备</th>";
    echo "<th>VIP完款时间</th>";
    echo "</tr>";
    
    // 数据行
    foreach ($users as $index => $user) {
        $rank = $index + 1;
        echo "<tr>";
        echo "<td>$rank</td>";
        echo "<td>" . htmlspecialchars($user['name']) . "</td>";
        echo "<td>" . htmlspecialchars($user['phone']) . "</td>";
        echo "<td>" . htmlspecialchars($user['wechat_nickname'] ?? '') . "</td>";
        echo "<td>" . formatRankValue($user, $rank_by) . "</td>";
        echo "<td>" . $user['team_vip_count'] . "</td>";
        echo "<td>" . $user['direct_vip_count'] . "</td>";
        echo "<td>¥" . number_format($user['balance'], 2) . "</td>";
        echo "<td>" . ($user['month_team_vip'] ?? 0) . "</td>";
        echo "<td>" . ($user['last_month_team_vip'] ?? 0) . "</td>";
        echo "<td>" . ($user['month_direct_vip'] ?? 0) . "</td>";
        echo "<td>¥" . number_format($user['total_dividend'], 2) . "</td>";
        echo "<td>¥" . number_format($user['month_dividend'], 2) . "</td>";
        echo "<td>" . ($user['team_recharge_count'] ?? 0) . "</td>";
        echo "<td>" . ($user['vip_paid_at'] ? date('Y-m-d', strtotime($user['vip_paid_at'])) : '') . "</td>";
        echo "</tr>";
    }
    
    echo "</table>";

} catch (Exception $e) {
    header('Content-Type: application/json; charset=utf-8');
    echo json_encode([
        'code' => 1,
        'message' => $e->getMessage(),
        'data' => null
    ], JSON_UNESCAPED_UNICODE);
}

// 获取排名维度文本
function getRankByText($rank_by) {
    $texts = [
        'team_vip_count' => '团队VIP数量',
        'direct_vip_count' => '直推VIP数量',
        'balance' => '账户余额',
        'month_team_vip' => '本月新增团队VIP',
        'last_month_team_vip' => '上月新增团队VIP',
        'month_direct_vip' => '本月直推VIP',
        'total_dividend' => '累计分红金额',
        'month_dividend' => '本月分红金额',
        'team_recharge_count' => '团队充值设备'
    ];
    return $texts[$rank_by] ?? '排行榜';
}

// 格式化排名数值
function formatRankValue($user, $rank_by) {
    switch ($rank_by) {
        case 'team_vip_count':
            return $user['team_vip_count'] . '人';
        case 'direct_vip_count':
            return $user['direct_vip_count'] . '人';
        case 'balance':
            return '¥' . number_format($user['balance'], 2);
        case 'month_team_vip':
            return ($user['month_team_vip'] ?? 0) . '人';
        case 'last_month_team_vip':
            return ($user['last_month_team_vip'] ?? 0) . '人';
        case 'month_direct_vip':
            return ($user['month_direct_vip'] ?? 0) . '人';
        case 'total_dividend':
            return '¥' . number_format($user['total_dividend'] ?? 0, 2);
        case 'month_dividend':
            return '¥' . number_format($user['month_dividend'] ?? 0, 2);
        case 'team_recharge_count':
            return ($user['team_recharge_count'] ?? 0) . '台';
        default:
            return '-';
    }
}

// 递归计算团队VIP总数（包括自己）
function getTeamVipCount($conn, $userId) {
    static $cache = [];
    if (isset($cache[$userId])) {
        return $cache[$userId];
    }
    
    $selfSql = "SELECT is_vip_paid FROM app_users WHERE id = $userId";
    $selfResult = mysqli_query($conn, $selfSql);
    $selfRow = mysqli_fetch_assoc($selfResult);
    
    $count = $selfRow['is_vip_paid'] ? 1 : 0;
    
    $sql = "SELECT id FROM app_users WHERE referrer_id = $userId AND is_vip_paid = 1";
    $result = mysqli_query($conn, $sql);
    
    while ($row = mysqli_fetch_assoc($result)) {
        $count += getTeamVipCount($conn, $row['id']);
    }
    
    $cache[$userId] = $count;
    return $count;
}

// 计算直推VIP数量
function getDirectVipCount($conn, $userId) {
    $sql = "SELECT COUNT(*) as count FROM app_users WHERE referrer_id = $userId AND is_vip_paid = 1";
    $result = mysqli_query($conn, $sql);
    $row = mysqli_fetch_assoc($result);
    return intval($row['count']);
}

// 递归计算指定月份的团队新增VIP数（包括自己）
function getMonthTeamVipCount($conn, $userId, $month) {
    static $cache = [];
    $cacheKey = $userId . '_' . $month;
    if (isset($cache[$cacheKey])) {
        return $cache[$cacheKey];
    }
    
    $selfSql = "SELECT is_vip_paid, vip_paid_at FROM app_users WHERE id = $userId";
    $selfResult = mysqli_query($conn, $selfSql);
    $selfRow = mysqli_fetch_assoc($selfResult);
    
    $count = 0;
    if ($selfRow['is_vip_paid'] && $selfRow['vip_paid_at'] && 
        date('Y-m', strtotime($selfRow['vip_paid_at'])) === $month) {
        $count = 1;
    }
    
    $sql = "SELECT id FROM app_users WHERE referrer_id = $userId";
    $result = mysqli_query($conn, $sql);
    
    while ($row = mysqli_fetch_assoc($result)) {
        $count += getMonthTeamVipCount($conn, $row['id'], $month);
    }
    
    $cache[$cacheKey] = $count;
    return $count;
}

// 计算指定月份的直推VIP数量
function getMonthDirectVipCount($conn, $userId, $month) {
    $sql = "SELECT COUNT(*) as count FROM app_users 
            WHERE referrer_id = $userId 
            AND is_vip_paid = 1 
            AND DATE_FORMAT(vip_paid_at, '%Y-%m') = '$month'";
    $result = mysqli_query($conn, $sql);
    $row = mysqli_fetch_assoc($result);
    return intval($row['count']);
}

// 计算团队充值设备数量
function getTeamRechargeCount($conn, $userId) {
    $sql = "SELECT COUNT(DISTINCT d.id) as count 
            FROM tapp_devices d 
            JOIN app_users u ON d.user_id = u.id 
            WHERE u.referrer_id = $userId 
            AND d.activate_date IS NOT NULL 
            AND d.order_status = 103";
    $result = mysqli_query($conn, $sql);
    if ($result) {
        $row = mysqli_fetch_assoc($result);
        return intval($row['count']);
    }
    return 0;
}
?> 