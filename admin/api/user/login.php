<?php
/**
 * 登录API重定向
 *
 * 此文件将原始的PHP登录API请求重定向到新的Laravel RESTful API
 */

// 设置HTTP响应头
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 检查请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode([
        'code' => 1,
        'message' => '请求方法不允许',
        'data' => null
    ]);
    exit;
}

// 引入相关函数
require_once dirname(__DIR__) . '/functions/wechat.php';
require_once dirname(__DIR__) . '/functions/auth.php';

// 获取请求参数
$data = json_decode(file_get_contents('php://input'), true);

// 验证必需的参数
if (!isset($data['username']) || !isset($data['password'])) {
    echo json_encode([
        'code' => 1,
        'message' => '缺少必需参数',
        'data' => null
    ]);
    exit;
}

$username = $data['username'];
$password = $data['password'];
$remember = isset($data['remember']) ? (bool)$data['remember'] : false;

// 支持手机号或用户名登录
$isPhone = preg_match('/^1[3-9]\d{9}$/', $username);

// 连接数据库
$conn = get_db_connection();

if ($isPhone) {
    // 使用手机号查询用户
    $stmt = $conn->prepare("SELECT * FROM app_users WHERE phone = ?");
} else {
    // 使用用户名查询用户
    $stmt = $conn->prepare("SELECT * FROM app_users WHERE username = ?");
}

$stmt->bind_param("s", $username);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows == 0) {
    $conn->close();
    echo json_encode([
        'code' => 1001,
        'message' => '用户不存在',
        'data' => null
    ]);
    exit;
}

$user = $result->fetch_assoc();

// 验证密码
if (!password_verify($password, $user['password'])) {
    $conn->close();
    echo json_encode([
        'code' => 1002,
        'message' => '密码错误',
        'data' => null
    ]);
    exit;
}

// 更新最后登录时间
$stmt = $conn->prepare("UPDATE app_users SET last_login_time = NOW() WHERE id = ?");
$stmt->bind_param("i", $user['id']);
$stmt->execute();

$conn->close();

// 生成JWT令牌
$expireTime = $remember ? 86400 * 30 : 86400; // 记住我：30天，否则1天

// 构建 JWT payload，包含 user_id 和 referrer_id
$payload = [
    'user_id' => $user['id'],
    'referrer_id' => $user['referrer_id'] ?? null // 从 $user 数组中获取 referrer_id，如果不存在则为 null
];

$token = generate_jwt($payload, $expireTime);

// 获取用户完整信息（包括角色）
$user_with_roles = get_user_with_roles($user['id']);

// 返回登录成功响应
echo json_encode([
    'code' => 0,
    'message' => '登录成功',
    'data' => [
        'token' => $token,
        'user' => $user_with_roles,
        'needBindPhone' => empty($user['phone'])
    ]
]);
?>