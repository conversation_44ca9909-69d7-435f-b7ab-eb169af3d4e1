<?php
/**
 * 用户支付设置API
 * 提供获取用户支付设置的功能
 */

// 设置HTTP响应头
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 错误处理设置
ini_set('display_errors', 0);
ini_set('log_errors', 1);
ini_set('error_log', dirname(__DIR__) . '/debug_error.log');
error_reporting(E_ALL);

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 检查请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    echo json_encode([
        'code' => 1,
        'message' => '请求方法不允许',
        'data' => null
    ]);
    exit;
}

// 生成唯一请求ID
$request_id = uniqid();
error_log("[$request_id] 获取用户支付设置请求开始");

try {
    // 引入相关函数和配置
    require_once dirname(__DIR__) . '/functions/auth.php';
    require_once dirname(__DIR__) . '/functions/db.php';
    require_once dirname(__DIR__) . '/config.php';

    // 获取Authorization头
    $headers = getallheaders();
    $auth_header = isset($headers['Authorization']) ? $headers['Authorization'] : '';
    $token = '';

    if (preg_match('/Bearer\s(\S+)/', $auth_header, $matches)) {
        $token = $matches[1];
        error_log("[$request_id] 获取到令牌: " . substr($token, 0, 10) . "...");
    } else {
        error_log("[$request_id] 未找到令牌");
    }

    if (empty($token)) {
        echo json_encode([
            'code' => 1001,
            'message' => '未授权的请求',
            'data' => null
        ]);
        exit;
    }

    // 验证用户身份
    $user = verify_auth($token);
    if (!$user) {
        echo json_encode([
            'code' => 1002,
            'message' => '无效的令牌或已过期',
            'data' => null
        ]);
        exit;
    }
    
    // 获取用户ID
    $user_id = $user['id'] ?? 0;
    
    if (!$user_id) {
        echo json_encode([
            'code' => 1003,
            'message' => '无效的用户ID',
            'data' => null
        ]);
        exit;
    }
    
    // 获取数据库连接
    $db = get_db_connection();
    if (!$db) {
        echo json_encode([
            'code' => 1004,
            'message' => '数据库连接失败',
            'data' => null
        ]);
        exit;
    }
    
    // 查询用户支付设置
    $stmt = $db->prepare("SELECT * FROM user_payment_settings WHERE user_id = ?");
    $stmt->bind_param("i", $user_id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        // 获取用户支付设置
        $settings = $result->fetch_assoc();
        
        echo json_encode([
            'code' => 0,
            'message' => '获取用户支付设置成功',
            'data' => [
                'enable_points_payment' => $settings['enable_points_payment'] ?? '1',  // 默认开启
                'enable_coupon_payment' => $settings['enable_coupon_payment'] ?? '0'   // 默认关闭
            ]
        ]);
    } else {
        // 没有找到设置记录，返回默认设置
        echo json_encode([
            'code' => 0,
            'message' => '获取用户支付设置成功 (默认设置)',
            'data' => [
                'enable_points_payment' => '1',  // 默认开启
                'enable_coupon_payment' => '0'   // 默认关闭
            ]
        ]);
    }
    
    $stmt->close();
    $db->close();
    
} catch (Exception $e) {
    error_log("[$request_id] 获取用户支付设置异常: " . $e->getMessage() . "\n" . $e->getTraceAsString());
    
    echo json_encode([
        'code' => 9999,
        'message' => '服务器内部错误',
        'data' => null
    ]);
}
?> 