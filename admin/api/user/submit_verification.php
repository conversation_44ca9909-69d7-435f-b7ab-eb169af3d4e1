<?php
// 设置HTTP响应头
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 确保错误处理
ini_set('display_errors', 0);
ini_set('log_errors', 1);
ini_set('error_log', dirname(__DIR__) . '/debug_error.log');
error_reporting(E_ALL);

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 检查请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode([
        'code' => 1,
        'message' => '请求方法不允许',
        'data' => null
    ]);
    exit;
}

// 生成唯一请求ID
$request_id = uniqid();
error_log("[$request_id] 提交实名认证请求开始");

try {
    // 引入相关函数
    require_once dirname(__DIR__) . '/functions/auth.php';
    require_once dirname(__DIR__) . '/config.php';

    // 获取Authorization头
    $headers = getallheaders();
    $auth_header = isset($headers['Authorization']) ? $headers['Authorization'] : '';
    $token = '';

    if (preg_match('/Bearer\s(\S+)/', $auth_header, $matches)) {
        $token = $matches[1];
    }

    if (empty($token)) {
        echo json_encode([
            'code' => 1001,
            'message' => '未授权的请求',
            'data' => null
        ]);
        exit;
    }

    // 验证用户身份
    $user = verify_auth($token);

    if (!$user) {
        echo json_encode([
            'code' => 1002,
            'message' => '无效的令牌或已过期',
            'data' => null
        ]);
        exit;
    }

    // 用户ID
    $user_id = $user['id'];
    error_log("[$request_id] 用户ID: $user_id");

    // 获取提交的数据
    $json_data = file_get_contents('php://input');
    $data = json_decode($json_data, true);

    if (!$data) {
        echo json_encode([
            'code' => 1003,
            'message' => '无效的请求数据',
            'data' => null
        ]);
        exit;
    }

    // 验证必要字段
    if (!isset($data['realName']) || !isset($data['idCard'])) {
        echo json_encode([
            'code' => 1004,
            'message' => '姓名和身份证号是必填项',
            'data' => null
        ]);
        exit;
    }

    // 提取数据
    $real_name = trim($data['realName']);
    $id_card = trim($data['idCard']);
    $front_image = isset($data['frontImage']) ? $data['frontImage'] : '';
    $back_image = isset($data['backImage']) ? $data['backImage'] : '';
    $from_pay_institution = isset($data['fromPayInstitution']) ? (bool)$data['fromPayInstitution'] : false;

    // 验证身份证号格式
    if (!preg_match('/(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/', $id_card)) {
        echo json_encode([
            'code' => 1005,
            'message' => '身份证号格式不正确',
            'data' => null
        ]);
        exit;
    }
    
    // 检查用户是否为支付机构角色
    $is_pay_institution = isset($user['is_pay_institution']) ? intval($user['is_pay_institution']) : 0;
    
    // 连接数据库
    require_once dirname(__DIR__) . '/db.php';
    $db = new DB();
    $pdo = $db->connect();
    
    // 如果是支付机构角色，且来源标识匹配
    if ($is_pay_institution == 1 && $from_pay_institution) {
        error_log("[$request_id] 支付机构用户提交认证: $real_name, $id_card");
        
        try {
            // 更新用户表中的姓名和身份证号
            $stmt = $pdo->prepare("UPDATE app_users SET name = :name, institution_sfz = :id_card, updated_at = NOW() WHERE id = :user_id");
            $stmt->bindParam(':name', $real_name);
            $stmt->bindParam(':id_card', $id_card);
            $stmt->bindParam(':user_id', $user_id);
            $result = $stmt->execute();
            
            if ($result) {
                error_log("[$request_id] 支付机构用户信息更新成功");
                
                // 提取身份证中的生日和性别信息，更新用户资料
                $gender = null;
                $birthday = null;
                
                if (strlen($id_card) == 18) {
                    // 提取生日 (格式: YYYYMMDD)
                    $birth_year = substr($id_card, 6, 4);
                    $birth_month = substr($id_card, 10, 2);
                    $birth_day = substr($id_card, 12, 2);
                    $birthday = "$birth_year-$birth_month-$birth_day";
                    
                    // 提取性别: 倒数第二位，奇数为男性(1)，偶数为女性(2)
                    $gender_code = intval(substr($id_card, 16, 1));
                    $gender = ($gender_code % 2 == 1) ? 1 : 2;
                } elseif (strlen($id_card) == 15) {
                    // 处理15位身份证
                    $birth_year = '19' . substr($id_card, 6, 2);
                    $birth_month = substr($id_card, 8, 2);
                    $birth_day = substr($id_card, 10, 2);
                    $birthday = "$birth_year-$birth_month-$birth_day";
                    
                    // 提取性别: 最后一位，奇数为男性(1)，偶数为女性(2)
                    $gender_code = intval(substr($id_card, 14, 1));
                    $gender = ($gender_code % 2 == 1) ? 1 : 2;
                }
                
                // 如果成功提取了生日和性别，则更新用户资料
                if ($birthday && $gender) {
                    $stmt = $pdo->prepare("UPDATE app_users SET birthday = :birthday, gender = :gender WHERE id = :user_id");
                    $stmt->bindParam(':birthday', $birthday);
                    $stmt->bindParam(':gender', $gender);
                    $stmt->bindParam(':user_id', $user_id);
                    $stmt->execute();
                    error_log("[$request_id] 用户生日和性别信息更新成功");
                }
                
                // 也插入一条用户认证记录，避免查询时候找不到记录
                try {
                    $now = date('Y-m-d H:i:s');
                    $stmt = $pdo->prepare("INSERT INTO user_verifications (user_id, real_name, id_card, status, created_at, updated_at, verified_at) VALUES (:user_id, :real_name, :id_card, 'verified', :created_at, :updated_at, :verified_at)");
                    $stmt->bindParam(':user_id', $user_id);
                    $stmt->bindParam(':real_name', $real_name);
                    $stmt->bindParam(':id_card', $id_card);
                    $stmt->bindParam(':created_at', $now);
                    $stmt->bindParam(':updated_at', $now);
                    $stmt->bindParam(':verified_at', $now);
                    $stmt->execute();
                    error_log("[$request_id] 支付机构用户认证记录插入成功");
                } catch (Exception $e) {
                    error_log("[$request_id] 插入认证记录失败，但不影响用户认证: " . $e->getMessage());
                    // 不影响主流程，继续返回成功
                }
                
                // 成功响应
                echo json_encode([
                    'code' => 0,
                    'message' => '认证成功',
                    'data' => null
                ]);
            } else {
                error_log("[$request_id] 支付机构用户信息更新失败");
                echo json_encode([
                    'code' => 1007,
                    'message' => '认证信息保存失败',
                    'data' => null
                ]);
            }
            
            exit;
        } catch (Exception $e) {
            error_log("[$request_id] 数据库更新异常: " . $e->getMessage());
            echo json_encode([
                'code' => 1008,
                'message' => '数据库操作失败',
                'data' => null
            ]);
            exit;
        }
    }
    
    // 如果非支付机构用户，或支付机构用户但未提供图片
    if (!$from_pay_institution && (empty($front_image) || empty($back_image))) {
        echo json_encode([
            'code' => 1006,
            'message' => '身份证照片是必须的',
            'data' => null
        ]);
        exit;
    }

    // 此处是普通用户认证流程
    try {
        // 写入认证信息到数据库
        $now = date('Y-m-d H:i:s');
        $stmt = $pdo->prepare("INSERT INTO user_verifications (user_id, real_name, id_card, front_image, back_image, status, created_at, updated_at) VALUES (:user_id, :real_name, :id_card, :front_image, :back_image, 'pending', :created_at, :updated_at)");
        $stmt->bindParam(':user_id', $user_id);
        $stmt->bindParam(':real_name', $real_name);
        $stmt->bindParam(':id_card', $id_card);
        $stmt->bindParam(':front_image', $front_image);
        $stmt->bindParam(':back_image', $back_image);
        $stmt->bindParam(':created_at', $now);
        $stmt->bindParam(':updated_at', $now);
        
        if ($stmt->execute()) {
            // 提取身份证中的生日和性别信息，更新用户资料
            try {
                $gender = null;
                $birthday = null;
                
                if (strlen($id_card) == 18) {
                    // 提取生日 (格式: YYYYMMDD)
                    $birth_year = substr($id_card, 6, 4);
                    $birth_month = substr($id_card, 10, 2);
                    $birth_day = substr($id_card, 12, 2);
                    $birthday = "$birth_year-$birth_month-$birth_day";
                    
                    // 提取性别: 倒数第二位，奇数为男性(1)，偶数为女性(2)
                    $gender_code = intval(substr($id_card, 16, 1));
                    $gender = ($gender_code % 2 == 1) ? 1 : 2;
                } elseif (strlen($id_card) == 15) {
                    // 处理15位身份证
                    $birth_year = '19' . substr($id_card, 6, 2);
                    $birth_month = substr($id_card, 8, 2);
                    $birth_day = substr($id_card, 10, 2);
                    $birthday = "$birth_year-$birth_month-$birth_day";
                    
                    // 提取性别: 最后一位，奇数为男性(1)，偶数为女性(2)
                    $gender_code = intval(substr($id_card, 14, 1));
                    $gender = ($gender_code % 2 == 1) ? 1 : 2;
                }
                
                // 如果成功提取了生日和性别，则更新用户资料
                if ($birthday && $gender) {
                    $stmt = $pdo->prepare("UPDATE app_users SET birthday = :birthday, gender = :gender WHERE id = :user_id");
                    $stmt->bindParam(':birthday', $birthday);
                    $stmt->bindParam(':gender', $gender);
                    $stmt->bindParam(':user_id', $user_id);
                    $stmt->execute();
                    error_log("[$request_id] 用户生日和性别信息更新成功");
                }
            } catch (Exception $e) {
                error_log("[$request_id] 提取或更新信息失败，但不影响主流程: " . $e->getMessage());
            }
            
            // 返回成功消息
            echo json_encode([
                'code' => 0,
                'message' => '实名认证提交成功，等待审核',
                'data' => null
            ]);
        } else {
            echo json_encode([
                'code' => 1007,
                'message' => '认证信息保存失败',
                'data' => null
            ]);
        }
    } catch (Exception $e) {
        error_log("[$request_id] 提交认证异常: " . $e->getMessage());
        echo json_encode([
            'code' => 1008,
            'message' => '系统错误，请稍后重试',
            'data' => null
        ]);
    }
    
    error_log("[$request_id] 提交实名认证请求完成");
} catch (Exception $e) {
    $error_message = "[$request_id] 处理请求时发生异常: " . $e->getMessage();
    error_log($error_message);
    
    echo json_encode([
        'code' => 9999,
        'message' => '请求失败: ' . $e->getMessage(),
        'data' => null
    ]);
} 