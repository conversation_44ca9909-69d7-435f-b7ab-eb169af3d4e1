<?php
// 设置HTTP响应头
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 确保错误处理
ini_set('display_errors', 0);
ini_set('log_errors', 1);
ini_set('error_log', dirname(__DIR__) . '/debug_error.log');
error_reporting(E_ALL);

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 检查请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    echo json_encode([
        'code' => 1,
        'message' => '请求方法不允许',
        'data' => null
    ]);
    exit;
}

// 生成唯一请求ID
$request_id = uniqid();
error_log("[$request_id] 获取实名认证状态请求开始");

try {
    // 引入相关函数
    require_once dirname(__DIR__) . '/functions/auth.php';
    require_once dirname(__DIR__) . '/config.php';

    // 获取Authorization头
    $headers = getallheaders();
    $auth_header = isset($headers['Authorization']) ? $headers['Authorization'] : '';
    $token = '';

    if (preg_match('/Bearer\s(\S+)/', $auth_header, $matches)) {
        $token = $matches[1];
    }

    if (empty($token)) {
        echo json_encode([
            'code' => 1001,
            'message' => '未授权的请求',
            'data' => null
        ]);
        exit;
    }

    // 验证用户身份
    $user = verify_auth($token);

    if (!$user) {
        echo json_encode([
            'code' => 1002,
            'message' => '无效的令牌或已过期',
            'data' => null
        ]);
        exit;
    }

    // 用户ID
    $user_id = $user['id'];
    error_log("[$request_id] 用户ID: $user_id");

    // 检查用户是否是支付机构
    $is_pay_institution = isset($user['is_pay_institution']) ? intval($user['is_pay_institution']) : 0;
    
    // 如果是支付机构角色，检查是否有身份证和姓名信息
    if ($is_pay_institution == 1) {
        $name = isset($user['name']) ? $user['name'] : '';
        $id_card = isset($user['institution_sfz']) ? $user['institution_sfz'] : '';
        
        error_log("[$request_id] 支付机构用户: name=" . ($name ? 'yes' : 'no') . ", id_card=" . ($id_card ? 'yes' : 'no'));
        
        // 如果有姓名和身份证信息，视为已认证
        if (!empty($name) && !empty($id_card)) {
            error_log("[$request_id] 支付机构用户已有身份信息，视为已认证");
            
            // 返回已认证状态
            echo json_encode([
                'code' => 0,
                'message' => '获取认证状态成功',
                'data' => [
                    'status' => 'verified',
                    'info' => [
                        'realName' => $name,
                        'idCard' => $id_card,
                        'verifiedAt' => date('Y-m-d H:i:s')
                    ]
                ]
            ]);
            exit;
        }
    }

    // 连接数据库查询认证状态
    require_once dirname(__DIR__) . '/db.php';
    $db = new DB();
    $pdo = $db->connect();
    
    try {
        // 查询实名认证表，获取用户认证状态
        $stmt = $pdo->prepare("SELECT * FROM user_verifications WHERE user_id = :user_id ORDER BY created_at DESC LIMIT 1");
        $stmt->bindParam(':user_id', $user_id);
        $stmt->execute();
        $verification = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($verification) {
            $status = isset($verification['status']) ? $verification['status'] : 'none'; // pending, verified, rejected
            
            $data = [
                'status' => $status
            ];
            
            if ($status === 'verified') {
                $data['info'] = [
                    'realName' => $verification['real_name'] ?? '',
                    'idCard' => $verification['id_card'] ?? '',
                    'verifiedAt' => $verification['verified_at'] ?? $verification['updated_at'] ?? date('Y-m-d H:i:s')
                ];
            } else if ($status === 'rejected') {
                $data['rejectReason'] = $verification['reject_reason'] ?? '身份信息有误';
            }
            
            echo json_encode([
                'code' => 0,
                'message' => '获取认证状态成功',
                'data' => $data
            ]);
            exit;
        }
        
        // 如果没有查询到认证记录，则再检查用户表中是否有身份证和姓名
        $stmt = $pdo->prepare("SELECT name, institution_sfz FROM app_users WHERE id = :user_id");
        $stmt->bindParam(':user_id', $user_id);
        $stmt->execute();
        $user_info = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($user_info && !empty($user_info['name']) && !empty($user_info['institution_sfz'])) {
            // 用户表中有身份信息，返回已认证状态
            echo json_encode([
                'code' => 0,
                'message' => '获取认证状态成功',
                'data' => [
                    'status' => 'verified',
                    'info' => [
                        'realName' => $user_info['name'],
                        'idCard' => $user_info['institution_sfz'],
                        'verifiedAt' => date('Y-m-d H:i:s')
                    ]
                ]
            ]);
            exit;
        }
    
        // 没有认证记录，返回未认证状态
        echo json_encode([
            'code' => 0,
            'message' => '获取认证状态成功',
            'data' => [
                'status' => 'none'
            ]
        ]);
        exit;
    } catch (Exception $e) {
        error_log("[$request_id] 数据库查询异常: " . $e->getMessage());
        // 发生数据库错误时，返回未认证状态而不是错误
        echo json_encode([
            'code' => 0,
            'message' => '获取认证状态成功',
            'data' => [
                'status' => 'none'
            ]
        ]);
        exit;
    }
    
    error_log("[$request_id] 获取实名认证状态请求完成");
} catch (Exception $e) {
    $error_message = "[$request_id] 处理请求时发生异常: " . $e->getMessage();
    error_log($error_message);
    
    echo json_encode([
        'code' => 9999,
        'message' => '请求失败: ' . $e->getMessage(),
        'data' => null
    ]);
} 