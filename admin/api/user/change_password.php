<?php
// 设置HTTP响应头
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 检查请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode([
        'code' => 1,
        'message' => '请求方法不允许',
        'data' => null
    ]);
    exit;
}

// 引入相关函数
require_once dirname(__DIR__) . '/functions/auth.php';

// 获取请求参数
$data = json_decode(file_get_contents('php://input'), true);

// 验证必需的参数
if (!isset($data['oldPassword']) || !isset($data['newPassword'])) {
    echo json_encode([
        'code' => 1,
        'message' => '缺少必需参数',
        'data' => null
    ]);
    exit;
}

$old_password = $data['oldPassword'];
$new_password = $data['newPassword'];

// 验证新密码长度
if (strlen($new_password) < 6) {
    echo json_encode([
        'code' => 1,
        'message' => '新密码长度不能少于6位',
        'data' => null
    ]);
    exit;
}

// 获取Authorization头
$headers = getallheaders();
$auth_header = isset($headers['Authorization']) ? $headers['Authorization'] : '';
$token = '';

if (preg_match('/Bearer\s(\S+)/', $auth_header, $matches)) {
    $token = $matches[1];
}

if (empty($token)) {
    echo json_encode([
        'code' => 1001,
        'message' => '未授权的请求',
        'data' => null
    ]);
    exit;
}

// 验证用户身份
$user = verify_auth($token);

if (!$user) {
    echo json_encode([
        'code' => 1002,
        'message' => '无效的令牌或已过期',
        'data' => null
    ]);
    exit;
}

// 修改密码
$result = change_password($user['userId'], $old_password, $new_password);

// 返回结果
echo json_encode($result);
?> 