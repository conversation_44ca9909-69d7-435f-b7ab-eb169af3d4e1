<?php
/**
 * 获取推荐人详细信息API
 * 根据推荐人ID返回推荐人的详细信息
 */

// --- 强制关闭错误显示 ---
ini_set('display_errors', 0);
error_reporting(0);
// -------------------------

// --- 0. 注册关闭函数以捕获致命错误 ---
register_shutdown_function(function () {
    $error = error_get_last();
    // 只处理致命错误
    if ($error !== null && in_array($error['type'], [E_ERROR, E_PARSE, E_CORE_ERROR, E_COMPILE_ERROR, E_USER_ERROR])) {
        // 记录日志
        try {
             require_once __DIR__ . '/../functions/logger.php';
             log_message("FATAL ERROR: " . $error['message'] . " in " . $error['file'] . " on line " . $error['line'], LOG_CRITICAL, 'fatal_error');
        } catch (Throwable $e) {
            error_log("Logging fatal error failed: " . $e->getMessage());
            error_log("Original Fatal Error: " . print_r($error, true));
        }

        // 尝试发送 JSON 错误响应
        if (!headers_sent()) {
            header('Access-Control-Allow-Origin: *');
            header('Access-Control-Allow-Methods: GET, OPTIONS');
            header('Access-Control-Allow-Headers: Content-Type, Authorization');
            header('Content-Type: application/json');
            http_response_code(500);
        }
        // 清理可能的输出缓冲
        while (ob_get_level() > 0) {
            ob_end_clean();
        }
        echo json_encode(['code' => 500, 'message' => '服务器内部致命错误']);
    }
});
// -------------------------------------

// 开启输出缓冲
ob_start();

// 设置 CORS 头 (脚本开始时设置)
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
// 移除此处 Content-Type，在输出 JSON 前设置
// header('Content-Type: application/json'); 

// 引入日志函数 和 配置
try {
    require_once __DIR__ . '/../functions/logger.php';
    require_once __DIR__ . '/../config.php'; // 确保 DEBUG_MODE 已定义
    require_once __DIR__ . '/../functions/auth.php'; // 包含 get_db_connection 和 verify_auth
} catch (Throwable $e) {
    error_log("Fatal Error: Failed to include required files in get_referrer_info.php: " . $e->getMessage() . "\n" . $e->getTraceAsString());
    // 清理缓冲并发送错误 JSON
    while (ob_get_level() > 0) { ob_end_clean(); }
    if (!headers_sent()) {
        header('Content-Type: application/json');
        http_response_code(500);
    }
    echo json_encode(['code' => 500, 'message' => '服务器内部错误 (Include Fail)']);
    exit;
}

// --- 辅助函数：获取请求头 (兼容性更好) ---
if (!function_exists('get_request_headers')) {
    function get_request_headers() {
        $headers = [];
        if (function_exists('getallheaders')) {
            $headers = getallheaders();
        } else {
            foreach ($_SERVER as $name => $value) {
                if (substr($name, 0, 5) == 'HTTP_') {
                    $key = str_replace(' ', '-', ucwords(strtolower(str_replace('_', ' ', substr($name, 5)))));
                    $headers[$key] = $value;
                } elseif ($name == "CONTENT_TYPE") {
                    $headers["Content-Type"] = $value;
                } elseif ($name == "CONTENT_LENGTH") {
                    $headers["Content-Length"] = $value;
                }
            }
        }
        // 将键名统一为小写以便查找
        return array_change_key_case($headers, CASE_LOWER);
    }
}
// -----------------------------------------

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    // 确保 OPTIONS 响应有 CORS 头 (如果尚未发送)
    if (!headers_sent()) {
         header('Access-Control-Allow-Origin: *');
         header('Access-Control-Allow-Methods: GET, OPTIONS');
         header('Access-Control-Allow-Headers: Content-Type, Authorization');
         header('Access-Control-Max-Age: 86400');
    }
    exit;
}

// --- 再次确认错误报告已关闭 --- 
ini_set('display_errors', 0);
error_reporting(0);
// -----------------------------

$conn = null;
$stmt = null;

try {
    // 获取推荐人ID
    $referrerId = isset($_GET['referrer_id']) ? intval($_GET['referrer_id']) : 0;
    log_message("[get_referrer_info_REAL] 请求开始，referrer_id: {$referrerId}", LOG_INFO, 'user_api');

    // 验证推荐人ID
    if ($referrerId <= 0) {
        log_message("[get_referrer_info_REAL] 错误：推荐人ID无效 ({$referrerId})", LOG_WARNING, 'user_api');
        while (ob_get_level() > 0) { ob_end_clean(); } // 清理
        if (!headers_sent()) {
            header('Content-Type: application/json');
            http_response_code(400);
        }
        echo json_encode(['code' => 1, 'message' => '推荐人ID无效']);
        exit;
    }

    // 获取当前用户信息（可选，用于日志）
    $currentUserId = 'Unknown';
    $headers = get_request_headers(); // 使用兼容函数
    $auth_header = $headers['authorization'] ?? ''; // 使用小写键名
    $token = '';
    if (preg_match('/Bearer\s+(\S+)/i', $auth_header, $matches)) { // 忽略大小写
        $token = $matches[1];
    }
    if (!empty($token)) {
        // 包裹 verify_auth 调用
        try {
            $currentUser = verify_auth($token); 
            if ($currentUser && isset($currentUser['id'])) {
                $currentUserId = $currentUser['id'];
            }
        } catch (Throwable $authError) {
            log_message("[get_referrer_info_REAL] verify_auth 调用失败: " . $authError->getMessage(), LOG_ERROR, 'user_api');
            // 可以选择抛出异常让外层捕获，或直接返回错误
            throw new Exception("Token verification failed", 401, $authError); // 抛给外层处理
        }
    }
    log_message("[get_referrer_info_REAL] 当前用户ID: {$currentUserId}, 请求推荐人ID: {$referrerId}", LOG_INFO, 'user_api');

    // 数据库操作
    log_message("[get_referrer_info_REAL] 尝试连接数据库...", LOG_INFO, 'user_api');
    $conn = get_db_connection();
    if (!$conn) {
        log_message("[get_referrer_info_REAL] 数据库连接失败", LOG_ERROR, 'user_api');
        throw new Exception("Database connection failed");
    }
    if ($conn->connect_error) {
         log_message("[get_referrer_info_REAL] 数据库连接错误: " . $conn->connect_error, LOG_ERROR, 'user_api');
         throw new Exception("Database connection error: " . $conn->connect_error);
    }
    log_message("[get_referrer_info_REAL] 数据库连接成功", LOG_INFO, 'user_api');

    // 再次确认查询字段，基于实际表结构： id, name, is_vip, wechat_nickname, wechat_avatar
    $sql = "
        SELECT 
            id, name, is_vip, 
            wechat_nickname, wechat_avatar
        FROM app_users 
        WHERE id = ? 
        LIMIT 1
    ";
    log_message("[get_referrer_info_REAL] 准备查询语句: {$sql}", LOG_INFO, 'user_api');
    $stmt = $conn->prepare($sql);
    if (!$stmt) {
        log_message("[get_referrer_info_REAL] 准备语句失败: " . $conn->error, LOG_ERROR, 'user_api');
        throw new Exception("Database prepare statement failed: " . $conn->error);
    }
    log_message("[get_referrer_info_REAL] 准备语句成功", LOG_INFO, 'user_api');

    log_message("[get_referrer_info_REAL] 绑定参数: i, {$referrerId}", LOG_INFO, 'user_api');
    if (!$stmt->bind_param("i", $referrerId)) {
        log_message("[get_referrer_info_REAL] 绑定参数失败: " . $stmt->error, LOG_ERROR, 'user_api');
        throw new Exception("Database bind parameter failed: " . $stmt->error);
    }
    log_message("[get_referrer_info_REAL] 绑定参数成功", LOG_INFO, 'user_api');

    log_message("[get_referrer_info_REAL] 执行查询...", LOG_INFO, 'user_api');
    if (!$stmt->execute()) {
        log_message("[get_referrer_info_REAL] 执行查询失败: " . $stmt->error, LOG_ERROR, 'user_api');
        throw new Exception("Database execute query failed: " . $stmt->error);
    }
    log_message("[get_referrer_info_REAL] 执行查询成功", LOG_INFO, 'user_api');

    log_message("[get_referrer_info_REAL] 获取结果集...", LOG_INFO, 'user_api');
    $result = $stmt->get_result();
    if (!$result) {
         log_message("[get_referrer_info_REAL] 获取结果集失败: " . $stmt->error, LOG_ERROR, 'user_api');
         throw new Exception("Database get result failed: " . $stmt->error);
    }
    log_message("[get_referrer_info_REAL] 获取结果集成功", LOG_INFO, 'user_api');
    
    if ($result->num_rows === 0) {
        log_message("[get_referrer_info_REAL] 推荐人不存在 (ID: {$referrerId})", LOG_INFO, 'user_api');
        while (ob_get_level() > 0) { ob_end_clean(); } // 清理
        if (!headers_sent()) {
            header('Content-Type: application/json');
            http_response_code(404);
        }
        echo json_encode(['code' => 2, 'message' => '推荐人不存在']);
        if($stmt) $stmt->close();
        if($conn) $conn->close();
        exit;
    }
    
    log_message("[get_referrer_info_REAL] 获取关联数组...", LOG_INFO, 'user_api');
    $referrer = $result->fetch_assoc();
    if (!$referrer) {
        // fetch_assoc 失败通常不会发生，除非内存问题，但以防万一
        log_message("[get_referrer_info_REAL] 获取关联数组失败", LOG_ERROR, 'user_api');
        throw new Exception("Database fetch assoc failed");
    }
    log_message("[get_referrer_info_REAL] 获取关联数组成功: " . json_encode(array_keys($referrer)), LOG_INFO, 'user_api'); // 记录获取到的字段名
    $stmt->close(); $stmt = null; // 关闭语句
    $conn->close(); $conn = null; // 关闭连接
    log_message("[get_referrer_info_REAL] 数据库连接已关闭", LOG_INFO, 'user_api');
    
    // 处理昵称和头像
    log_message("[get_referrer_info_REAL] 开始处理昵称和头像...", LOG_INFO, 'user_api');
    // 优先使用微信昵称，其次使用name字段，最后使用'用户' + ID
    $nickname = !empty($referrer['wechat_nickname']) ? $referrer['wechat_nickname'] : ($referrer['name'] ?? ('用户' . ($referrer['id'] ?? '')));
    // 直接使用微信头像，如果不存在则为 null
    $avatar = $referrer['wechat_avatar'] ?? null;
    
    // 如果处理后昵称仍然为空（理论上不会，因为有'用户'+ID保底），提供一个默认值
    if (empty($nickname)) {
        $nickname = '用户' . ($referrer['id'] ?? ''); 
    }
    
    log_message("[get_referrer_info_REAL] 处理后昵称: {$nickname}, 头像: {$avatar}", LOG_INFO, 'user_api');
    
    // 构建响应数据
    log_message("[get_referrer_info_REAL] 开始构建响应数据...", LOG_INFO, 'user_api');
    $responseData = [
        'id' => $referrer['id'] ?? null,
        'nickname' => $nickname,
        'avatar' => $avatar,
        'is_vip' => isset($referrer['is_vip']) ? (int)$referrer['is_vip'] : 0
    ];
    log_message("[get_referrer_info_REAL] 构建响应数据完成: " . json_encode($responseData), LOG_INFO, 'user_api');
    
    log_message("[get_referrer_info_REAL] 获取推荐人信息成功, ID: {$referrerId}", LOG_INFO, 'user_api');
    // --- 成功响应前清理缓冲并设置头 ---
    while (ob_get_level() > 0) {
        ob_end_clean();
    }
    if (!headers_sent()) {
        header('Content-Type: application/json');
        http_response_code(200); 
    }
    echo json_encode(['code' => 0, 'message' => '获取推荐人信息成功', 'data' => $responseData]);
    // ---------------------------------

} catch (Throwable $e) { 
    log_message("[get_referrer_info_REAL] 查询推荐人信息时捕获到错误: " . $e->getMessage() . "\nStack Trace:\n" . $e->getTraceAsString(), LOG_ERROR, 'user_api');
    
    // --- 简化 catch 块的响应 --- 
    $errorCode = $e->getCode() >= 400 ? $e->getCode() : 500; // 尝试获取原始HTTP代码
    $errorMessage = '服务器内部错误 (Query Error)'; // 默认消息
    // 可以根据 $e 的类型或代码提供更具体的消息
    if ($e->getPrevious() && $e->getPrevious() instanceof Exception && $e->getCode() == 401) { 
         $errorMessage = 'Token验证失败';
         $errorCode = 401;
    }
    
    // --- catch 块错误响应前清理缓冲并设置头 ---
    while (ob_get_level() > 0) {
        ob_end_clean();
    }
    if (!headers_sent()) {
        header('Access-Control-Allow-Origin: *'); // 确保 CORS 仍在
        header('Access-Control-Allow-Methods: GET, OPTIONS');
        header('Access-Control-Allow-Headers: Content-Type, Authorization');
        header('Content-Type: application/json');
        http_response_code($errorCode);
    }
    echo json_encode(['code' => $errorCode, 'message' => $errorMessage]); 
    // ----------------------------

} finally {
    // 确保资源被释放
    if (isset($stmt) && $stmt instanceof mysqli_stmt) {
        $stmt->close();
        log_message("[get_referrer_info_REAL] Statement closed in finally block.", LOG_INFO, 'user_api');
    }
    if (isset($conn) && $conn instanceof mysqli && $conn->ping()) { 
        $conn->close();
        log_message("[get_referrer_info_REAL] Connection closed in finally block.", LOG_INFO, 'user_api');
    }
} 
?> 