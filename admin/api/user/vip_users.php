<?php
/**
 * VIP用户列表API
 * 
 * 获取系统中的VIP会员列表
 * 
 * @return array 返回VIP会员列表数据
 */

// 设置响应头
header('Content-Type: application/json;charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    exit(0);
}

// 引入配置文件
require_once dirname(__DIR__) . '/config.php';
require_once dirname(__DIR__) . '/functions/common.php';
require_once dirname(__DIR__) . '/functions/db.php';

// 获取请求参数
$page = isset($_GET['page']) ? intval($_GET['page']) : 1;
$limit = isset($_GET['limit']) ? intval($_GET['limit']) : 10;
$search = isset($_GET['search']) ? trim($_GET['search']) : '';

// 计算偏移量
$offset = ($page - 1) * $limit;

try {
    // 连接数据库
    $db = getDbConnection();
    
    // 构建查询条件
    $whereClause = "is_vip = 1";
    $params = [];
    
    if (!empty($search)) {
        $whereClause .= " AND (name LIKE ? OR nickname LIKE ? OR mobile LIKE ? OR phone LIKE ?)";
        $searchParam = "%{$search}%";
        $params = [$searchParam, $searchParam, $searchParam, $searchParam];
    }
    
    // 查询VIP用户总数
    $countSql = "SELECT COUNT(*) as total FROM app_users WHERE {$whereClause}";
    $stmt = $db->prepare($countSql);
    
    if (!empty($params)) {
        foreach ($params as $index => $param) {
            $stmt->bindValue($index + 1, $param);
        }
    }
    
    $stmt->execute();
    $totalResult = $stmt->fetch(PDO::FETCH_ASSOC);
    $total = $totalResult['total'];
    
    // 查询VIP用户列表
    $sql = "SELECT id, name, nickname, avatar, mobile as phone, balance, vip_at, created_at 
            FROM app_users 
            WHERE {$whereClause} 
            ORDER BY vip_at DESC 
            LIMIT {$limit} OFFSET {$offset}";
    
    $stmt = $db->prepare($sql);
    
    if (!empty($params)) {
        foreach ($params as $index => $param) {
            $stmt->bindValue($index + 1, $param);
        }
    }
    
    $stmt->execute();
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // 查询VIP分红统计数据
    $statsSql = "SELECT 
                    COUNT(*) as total_vip,
                    IFNULL(SUM(dividend_amount), 0) as total_dividend,
                    IFNULL(SUM(pending_amount), 0) as pending_amount
                FROM app_users 
                WHERE is_vip = 1";
    $statsStmt = $db->prepare($statsSql);
    $statsStmt->execute();
    $stats = $statsStmt->fetch(PDO::FETCH_ASSOC);
    
    // 处理用户数据
    foreach ($users as &$user) {
        // 格式化日期
        $user['vip_at'] = !empty($user['vip_at']) ? date('Y-m-d H:i:s', strtotime($user['vip_at'])) : '';
        $user['created_at'] = !empty($user['created_at']) ? date('Y-m-d H:i:s', strtotime($user['created_at'])) : '';
        
        // 确保余额是数字格式
        $user['balance'] = floatval($user['balance']);
        
        // 如果没有昵称，使用姓名
        if (empty($user['nickname']) && !empty($user['name'])) {
            $user['nickname'] = $user['name'];
        }
        
        // 如果没有头像，设置默认头像
        if (empty($user['avatar'])) {
            $user['avatar'] = '/app/static/images/default-avatar.png';
        }
    }
    
    // 返回成功响应
    echo json_encode([
        'code' => 0,
        'message' => '获取VIP会员列表成功',
        'data' => [
            'users' => $users,
            'total' => intval($total),
            'totalDividend' => floatval($stats['total_dividend']),
            'pendingAmount' => floatval($stats['pending_amount'])
        ]
    ]);
    
} catch (Exception $e) {
    // 记录错误日志
    error_log("获取VIP会员列表失败: " . $e->getMessage());
    
    // 返回错误响应
    echo json_encode([
        'code' => 500,
        'message' => '获取VIP会员列表失败: ' . $e->getMessage(),
        'data' => [
            'users' => [],
            'total' => 0,
            'totalDividend' => 0,
            'pendingAmount' => 0
        ]
    ]);
}
