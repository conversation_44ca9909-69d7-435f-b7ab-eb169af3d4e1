<?php
/**
 * VIP分红统计API
 * 
 * 获取VIP分红统计数据，包括各等级人数、奖池总额、各等级分红金额等
 * 
 * @return array 返回VIP分红统计数据
 */

// 设置响应头
header('Content-Type: application/json;charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    exit(0);
}

// 引入配置文件
require_once dirname(__DIR__) . '/config.php';
require_once dirname(__DIR__) . '/functions/common.php';
require_once dirname(__DIR__) . '/functions/db.php';

try {
    // 连接数据库
    $db = getDbConnection();
    
    // 获取VIP会员总数
    $vipCountSql = "SELECT COUNT(*) as total FROM app_users WHERE is_vip = 1";
    $vipCountStmt = $db->prepare($vipCountSql);
    $vipCountStmt->execute();
    $vipCountResult = $vipCountStmt->fetch(PDO::FETCH_ASSOC);
    $vipCount = $vipCountResult['total'] ?? 0;
    
    // 获取奖池总额
    $poolTotalSql = "SELECT IFNULL(SUM(amount), 0) as total FROM vip_pool";
    $poolTotalStmt = $db->prepare($poolTotalSql);
    $poolTotalStmt->execute();
    $poolTotalResult = $poolTotalStmt->fetch(PDO::FETCH_ASSOC);
    $poolTotal = $poolTotalResult['total'] ?? 0;
    
    // 获取各等级会员人数
    $levelCountSql = "SELECT 
                        CASE 
                            WHEN vip_level = 1 THEN 'junior'
                            WHEN vip_level = 2 THEN 'middle'
                            WHEN vip_level = 3 THEN 'senior'
                            ELSE 'unknown'
                        END as level,
                        COUNT(*) as count
                      FROM app_users 
                      WHERE is_vip = 1 
                      GROUP BY vip_level";
    $levelCountStmt = $db->prepare($levelCountSql);
    $levelCountStmt->execute();
    $levelCountResults = $levelCountStmt->fetchAll(PDO::FETCH_ASSOC);
    
    // 初始化各等级人数
    $juniorCount = 0;
    $middleCount = 0;
    $seniorCount = 0;
    
    // 处理各等级人数
    foreach ($levelCountResults as $result) {
        switch ($result['level']) {
            case 'junior':
                $juniorCount = intval($result['count']);
                break;
            case 'middle':
                $middleCount = intval($result['count']);
                break;
            case 'senior':
                $seniorCount = intval($result['count']);
                break;
        }
    }
    
    // 获取各等级分红金额
    $levelAmountSql = "SELECT 
                        CASE 
                            WHEN level = 1 THEN 'junior'
                            WHEN level = 2 THEN 'middle'
                            WHEN level = 3 THEN 'senior'
                            ELSE 'unknown'
                        END as level,
                        IFNULL(SUM(amount), 0) as amount
                       FROM vip_dividends 
                       GROUP BY level";
    $levelAmountStmt = $db->prepare($levelAmountSql);
    $levelAmountStmt->execute();
    $levelAmountResults = $levelAmountStmt->fetchAll(PDO::FETCH_ASSOC);
    
    // 初始化各等级分红金额
    $juniorAmount = 0;
    $middleAmount = 0;
    $seniorAmount = 0;
    
    // 处理各等级分红金额
    foreach ($levelAmountResults as $result) {
        switch ($result['level']) {
            case 'junior':
                $juniorAmount = floatval($result['amount']);
                break;
            case 'middle':
                $middleAmount = floatval($result['amount']);
                break;
            case 'senior':
                $seniorAmount = floatval($result['amount']);
                break;
        }
    }
    
    // 构建返回数据
    $data = [
        'vipCount' => intval($vipCount),
        'totalPool' => floatval($poolTotal),
        'levelCounts' => [
            'juniorCount' => $juniorCount,
            'middleCount' => $middleCount,
            'seniorCount' => $seniorCount
        ],
        'levelAmounts' => [
            'juniorAmount' => $juniorAmount,
            'middleAmount' => $middleAmount,
            'seniorAmount' => $seniorAmount
        ]
    ];
    
    // 返回成功响应
    echo json_encode([
        'code' => 0,
        'message' => '获取VIP分红统计数据成功',
        'data' => $data
    ]);
    
} catch (Exception $e) {
    // 记录错误日志
    error_log("获取VIP分红统计数据失败: " . $e->getMessage());
    
    // 返回错误响应
    echo json_encode([
        'code' => 500,
        'message' => '获取VIP分红统计数据失败: ' . $e->getMessage(),
        'data' => [
            'vipCount' => 0,
            'totalPool' => 0,
            'levelCounts' => [
                'juniorCount' => 0,
                'middleCount' => 0,
                'seniorCount' => 0
            ],
            'levelAmounts' => [
                'juniorAmount' => 0,
                'middleAmount' => 0,
                'seniorAmount' => 0
            ]
        ]
    ]);
}
