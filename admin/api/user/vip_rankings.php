<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once '../config.php';

// 建立数据库连接
$conn = mysqli_connect(
    $DB_CONFIG['HOST'],
    $DB_CONFIG['USER'],
    $DB_CONFIG['PASSWORD'],
    $DB_CONFIG['DATABASE'],
    $DB_CONFIG['PORT']
);

if (!$conn) {
    echo json_encode([
        'code' => 1,
        'message' => '数据库连接失败: ' . mysqli_connect_error(),
        'data' => null
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

// 设置字符集
mysqli_set_charset($conn, $DB_CONFIG['CHARSET']);

try {
    // 获取参数
    $rank_by = $_GET['rank_by'] ?? 'team_vip_count';
    $period = $_GET['period'] ?? 'all';
    $limit = intval($_GET['limit'] ?? 50);
    $search = $_GET['search'] ?? '';
    
    // 验证排名维度
    $valid_rank_by = [
        'team_vip_count', 'direct_vip_count', 'balance', 
        'month_team_vip', 'last_month_team_vip', 'month_direct_vip'
    ];
    
    if (!in_array($rank_by, $valid_rank_by)) {
        $rank_by = 'team_vip_count';
    }
    
    // 构建基础查询
    $sql = "SELECT 
                u.id,
                u.name,
                u.phone,
                u.wechat_nickname,
                u.balance,
                u.vip_paid_at,
                u.is_vip_paid
            FROM app_users u 
            WHERE u.is_vip_paid = 1";
    
    // 添加搜索条件
    if (!empty($search)) {
        $search = mysqli_real_escape_string($conn, $search);
        $sql .= " AND (u.name LIKE '%$search%' OR u.phone LIKE '%$search%' OR u.wechat_nickname LIKE '%$search%')";
    }
    
    $sql .= " ORDER BY u.id";
    
    $result = mysqli_query($conn, $sql);
    if (!$result) {
        throw new Exception('查询用户数据失败: ' . mysqli_error($conn));
    }
    
    $users = [];
    while ($row = mysqli_fetch_assoc($result)) {
        $users[] = $row;
    }
    
    // 计算团队VIP数量和直推VIP数量
    foreach ($users as &$user) {
        // 计算团队VIP总数（递归）
        $user['team_vip_count'] = getTeamVipCount($conn, $user['id']);
        
        // 计算直推VIP数量
        $user['direct_vip_count'] = getDirectVipCount($conn, $user['id']);
        
        // 计算本月新增团队VIP
        $user['month_team_vip'] = getMonthTeamVipCount($conn, $user['id'], date('Y-m'));
        
        // 计算上月新增团队VIP
        $lastMonth = date('Y-m', strtotime('-1 month'));
        $user['last_month_team_vip'] = getMonthTeamVipCount($conn, $user['id'], $lastMonth);
        
        // 计算本月直推VIP
        $user['month_direct_vip'] = getMonthDirectVipCount($conn, $user['id'], date('Y-m'));
        
        // 设置默认值
        $user['total_dividend'] = 0;
        $user['month_dividend'] = 0;
        $user['team_recharge_count'] = 0;
        $user['rank_change'] = 0;
    }
    
    // 根据选择的维度排序
    usort($users, function($a, $b) use ($rank_by) {
        $aValue = floatval($a[$rank_by] ?? 0);
        $bValue = floatval($b[$rank_by] ?? 0);
        return $bValue <=> $aValue; // 降序排列
    });
    
    // 限制返回数量
    $rankings = array_slice($users, 0, $limit);
    
    // 计算统计数据
    $stats = [
        'total_vip_users' => count($users),
        'avg_team_vip' => count($users) > 0 ? array_sum(array_column($users, 'team_vip_count')) / count($users) : 0,
        'avg_balance' => count($users) > 0 ? array_sum(array_column($users, 'balance')) / count($users) : 0,
        'top_performer' => !empty($rankings) ? ['name' => $rankings[0]['name']] : null
    ];
    
    echo json_encode([
        'code' => 0,
        'message' => '获取排行榜数据成功',
        'data' => [
            'rankings' => $rankings,
            'stats' => $stats
        ]
    ], JSON_UNESCAPED_UNICODE);

} catch (Exception $e) {
    echo json_encode([
        'code' => 1,
        'message' => $e->getMessage(),
        'data' => null
    ], JSON_UNESCAPED_UNICODE);
}

// 递归计算团队VIP总数（包括自己）
function getTeamVipCount($conn, $userId) {
    // 检查自己是否是VIP
    $selfSql = "SELECT is_vip_paid FROM app_users WHERE id = $userId";
    $selfResult = mysqli_query($conn, $selfSql);
    $selfRow = mysqli_fetch_assoc($selfResult);
    
    $count = $selfRow['is_vip_paid'] ? 1 : 0;
    
    // 获取直推的VIP用户
    $sql = "SELECT id FROM app_users WHERE referrer_id = $userId AND is_vip_paid = 1";
    $result = mysqli_query($conn, $sql);
    
    while ($row = mysqli_fetch_assoc($result)) {
        $count += getTeamVipCount($conn, $row['id']);
    }
    
    return $count;
}

// 计算直推VIP数量
function getDirectVipCount($conn, $userId) {
    $sql = "SELECT COUNT(*) as count FROM app_users WHERE referrer_id = $userId AND is_vip_paid = 1";
    $result = mysqli_query($conn, $sql);
    $row = mysqli_fetch_assoc($result);
    return intval($row['count']);
}

// 递归计算指定月份的团队新增VIP数（包括自己）
function getMonthTeamVipCount($conn, $userId, $month) {
    // 检查自己是否在该月成为VIP
    $selfSql = "SELECT is_vip_paid, vip_paid_at FROM app_users WHERE id = $userId";
    $selfResult = mysqli_query($conn, $selfSql);
    $selfRow = mysqli_fetch_assoc($selfResult);
    
    $count = 0;
    if ($selfRow['is_vip_paid'] && $selfRow['vip_paid_at'] && 
        date('Y-m', strtotime($selfRow['vip_paid_at'])) === $month) {
        $count = 1;
    }
    
    // 获取直推用户
    $sql = "SELECT id FROM app_users WHERE referrer_id = $userId";
    $result = mysqli_query($conn, $sql);
    
    while ($row = mysqli_fetch_assoc($result)) {
        $count += getMonthTeamVipCount($conn, $row['id'], $month);
    }
    
    return $count;
}

// 计算指定月份的直推VIP数量
function getMonthDirectVipCount($conn, $userId, $month) {
    $sql = "SELECT COUNT(*) as count FROM app_users 
            WHERE referrer_id = $userId 
            AND is_vip_paid = 1 
            AND DATE_FORMAT(vip_paid_at, '%Y-%m') = '$month'";
    $result = mysqli_query($conn, $sql);
    $row = mysqli_fetch_assoc($result);
    return intval($row['count']);
}
?> 