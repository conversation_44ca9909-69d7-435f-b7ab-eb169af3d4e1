<?php
/**
 * 获取用户订单统计信息API
 * 
 * 返回用户各种状态的订单数量统计
 */

// 设置HTTP响应头
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 检查请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    echo json_encode([
        'code' => 1,
        'message' => '请求方法不允许',
        'data' => null
    ]);
    exit;
}

// 引入必要的文件
require_once __DIR__ . '/../config.php';
require_once __DIR__ . '/../functions/auth.php';
require_once __DIR__ . '/../functions/db.php';

// 记录请求日志
$request_id = uniqid('order_stats_');
$log_dir = __DIR__ . '/../logs/';
if (!is_dir($log_dir)) {
    mkdir($log_dir, 0755, true);
}
$log_file = $log_dir . 'order_stats_' . date('Ymd') . '.log';
error_log("[$request_id] 开始处理订单统计请求 - " . date('Y-m-d H:i:s') . "\n", 3, $log_file);

// 获取请求参数
$type = isset($_GET['type']) ? $_GET['type'] : 'all';

// 获取请求头中的Authorization
$headers = getallheaders();
$auth_header = isset($headers['Authorization']) ? $headers['Authorization'] : '';
if (empty($auth_header)) {
    error_log("[$request_id] 缺少Authorization头\n", 3, $log_file);
    http_response_code(401);
    echo json_encode([
        'code' => 1,
        'message' => '未授权',
        'data' => null
    ]);
    exit;
}

// 验证token
$token = str_replace('Bearer ', '', $auth_header);

// 测试模式 - 当token为test时返回模拟数据
if ($token === 'test') {
    error_log("[$request_id] 测试模式，返回模拟数据\n", 3, $log_file);
    $order_stats = [
        'unpaid' => 2,
        'unshipped' => 1,
        'unreceived' => 3,
        'unrated' => 5,
        'after_sale' => 1
    ];
    
    echo json_encode([
        'code' => 0,
        'message' => '获取成功',
        'data' => $order_stats
    ]);
    exit;
}

$user_id = verify_token($token);
if (!$user_id) {
    error_log("[$request_id] 无效的token\n", 3, $log_file);
    http_response_code(401);
    echo json_encode([
        'code' => 1,
        'message' => '无效的token',
        'data' => null
    ]);
    exit;
}

try {
    // 连接数据库
    $db = db_connect();
    
    // 统计待支付订单数量
    $unpaid_query = "SELECT COUNT(*) as count FROM orders WHERE user_id = ? AND status = 0";
    $stmt = $db->prepare($unpaid_query);
    $stmt->bind_param('i', $user_id);
    $stmt->execute();
    $unpaid_result = $stmt->get_result();
    $unpaid_count = 0;
    if ($row = $unpaid_result->fetch_assoc()) {
        $unpaid_count = intval($row['count']);
    }
    
    // 统计待发货订单数量
    $unshipped_query = "SELECT COUNT(*) as count FROM orders WHERE user_id = ? AND status = 1";
    $stmt = $db->prepare($unshipped_query);
    $stmt->bind_param('i', $user_id);
    $stmt->execute();
    $unshipped_result = $stmt->get_result();
    $unshipped_count = 0;
    if ($row = $unshipped_result->fetch_assoc()) {
        $unshipped_count = intval($row['count']);
    }
    
    // 统计待收货订单数量
    $unreceived_query = "SELECT COUNT(*) as count FROM orders WHERE user_id = ? AND status = 2";
    $stmt = $db->prepare($unreceived_query);
    $stmt->bind_param('i', $user_id);
    $stmt->execute();
    $unreceived_result = $stmt->get_result();
    $unreceived_count = 0;
    if ($row = $unreceived_result->fetch_assoc()) {
        $unreceived_count = intval($row['count']);
    }
    
    // 统计待评价订单数量
    $unrated_query = "SELECT COUNT(*) as count FROM orders WHERE user_id = ? AND status = 3";
    $stmt = $db->prepare($unrated_query);
    $stmt->bind_param('i', $user_id);
    $stmt->execute();
    $unrated_result = $stmt->get_result();
    $unrated_count = 0;
    if ($row = $unrated_result->fetch_assoc()) {
        $unrated_count = intval($row['count']);
    }
    
    // 统计售后订单数量
    $after_sale_query = "SELECT COUNT(*) as count FROM order_refunds WHERE user_id = ? AND status IN (0, 1)";
    $stmt = $db->prepare($after_sale_query);
    $stmt->bind_param('i', $user_id);
    $stmt->execute();
    $after_sale_result = $stmt->get_result();
    $after_sale_count = 0;
    if ($row = $after_sale_result->fetch_assoc()) {
        $after_sale_count = intval($row['count']);
    }
    
    // 关闭数据库连接
    $db->close();
    
    // 返回订单统计信息
    $order_stats = [
        'unpaid' => $unpaid_count,
        'unshipped' => $unshipped_count,
        'unreceived' => $unreceived_count,
        'unrated' => $unrated_count,
        'after_sale' => $after_sale_count
    ];
    
    error_log("[$request_id] 成功获取订单统计信息: " . json_encode($order_stats) . "\n", 3, $log_file);
    echo json_encode([
        'code' => 0,
        'message' => '获取成功',
        'data' => $order_stats
    ]);
    
} catch (Exception $e) {
    error_log("[$request_id] 获取订单统计信息失败: " . $e->getMessage() . "\n", 3, $log_file);
    echo json_encode([
        'code' => 1,
        'message' => '获取订单统计信息失败',
        'data' => null
    ]);
}
