<?php
/**
 * VIP团队递归查询API
 * 用于计算用户的无限层级团队成员
 */

// 引入公共函数和配置
require_once __DIR__ . '/../functions/functions.php';
require_once __DIR__ . '/../config.php';

// 引入Laravel组件
require_once ADMIN_PATH . '/vendor/autoload.php';
use Illuminate\Database\Capsule\Manager as DB;

// 设置响应头
header('Content-Type: application/json');

// 记录日志函数
function vip_log($message) {
    error_log("[VIP团队递归] " . $message);
}

try {
    // 获取当前月份
    $currentMonth = date('Y-m');
    vip_log("当前月份: $currentMonth");

    // 初始化数据库连接
    $db = new DB;
    $db->addConnection([
        'driver'    => 'mysql',
        'host'      => $DB_CONFIG['HOST'],
        'port'      => $DB_CONFIG['PORT'],
        'database'  => $DB_CONFIG['DATABASE'],
        'username'  => $DB_CONFIG['USER'],
        'password'  => $DB_CONFIG['PASSWORD'],
        'charset'   => $DB_CONFIG['CHARSET'],
        'collation' => 'utf8mb4_unicode_ci',
        'prefix'    => '',
    ]);
    $db->setAsGlobal();
    $db->bootEloquent();
    vip_log("数据库连接成功");

    // 创建临时表来存储团队成员关系
    DB::statement("DROP TEMPORARY TABLE IF EXISTS temp_team_members");
    DB::statement("CREATE TEMPORARY TABLE temp_team_members (
        id INT NOT NULL AUTO_INCREMENT,
        user_id INT NOT NULL,
        team_leader_id INT NOT NULL,
        level INT NOT NULL,
        PRIMARY KEY (id),
        INDEX (user_id),
        INDEX (team_leader_id)
    )");

    // 获取所有VIP用户
    $vipUsers = DB::select("SELECT id, name, wechat_nickname FROM app_users WHERE is_vip_paid = 1");
    vip_log("找到 " . count($vipUsers) . " 个VIP用户");

    // 使用迭代方法构建无限层级团队关系
    foreach ($vipUsers as $leader) {
        $leaderId = $leader->id;
        vip_log("处理用户ID: $leaderId");

        // 清空该用户的团队成员表
        DB::statement("DELETE FROM temp_team_members WHERE team_leader_id = ?", [$leaderId]);

        // 初始化队列，从直接下级开始
        $queue = [];
        $directMembers = DB::select("SELECT id FROM app_users WHERE referrer_id = ?", [$leaderId]);

        foreach ($directMembers as $member) {
            DB::insert("INSERT INTO temp_team_members (user_id, team_leader_id, level) VALUES (?, ?, ?)",
                      [$member->id, $leaderId, 1]);
            $queue[] = $member->id;
        }

        // 广度优先搜索处理队列
        $level = 2;
        while (!empty($queue)) {
            $nextQueue = [];
            foreach ($queue as $parentId) {
                $children = DB::select("SELECT id FROM app_users WHERE referrer_id = ?", [$parentId]);

                foreach ($children as $child) {
                    // 检查是否已经在团队中，避免循环引用
                    $exists = DB::select("SELECT id FROM temp_team_members WHERE user_id = ? AND team_leader_id = ?",
                                       [$child->id, $leaderId]);

                    if (empty($exists)) {
                        DB::insert("INSERT INTO temp_team_members (user_id, team_leader_id, level) VALUES (?, ?, ?)",
                                  [$child->id, $leaderId, $level]);
                        $nextQueue[] = $child->id;
                    }
                }
            }
            $queue = $nextQueue;
            $level++;
        }

        // 计算该用户的团队统计数据
        $teamStats = DB::select("
            SELECT
                COUNT(DISTINCT t.user_id) as total_team_count,
                SUM(CASE WHEN u.is_vip_paid = 1 THEN 1 ELSE 0 END) as total_vip_count,
                SUM(CASE WHEN u.is_vip_paid = 1 AND DATE_FORMAT(u.vip_paid_at, '%Y-%m') = ? THEN 1 ELSE 0 END) as month_vip_count
            FROM
                temp_team_members t
            JOIN
                app_users u ON t.user_id = u.id
            WHERE
                t.team_leader_id = ?
        ", [$currentMonth, $leaderId]);

        // 如果该用户本月完款，则本月新增VIP数量+1（包括自己）
        $userInfo = DB::select("
            SELECT
                is_vip_paid,
                DATE_FORMAT(vip_paid_at, '%Y-%m') as paid_month
            FROM
                app_users
            WHERE
                id = ?
        ", [$leaderId]);

        $selfIsNewThisMonth = 0;
        if (!empty($userInfo) && $userInfo[0]->is_vip_paid == 1 && $userInfo[0]->paid_month == $currentMonth) {
            $selfIsNewThisMonth = 1;
        }

        // 更新团队统计数据，加上自己（如果本月完款）
        $monthVipCount = $teamStats[0]->month_vip_count + $selfIsNewThisMonth;

        // 只保留本月新增VIP达到3人及以上的用户
        if ($monthVipCount >= 3) {
            // 获取直推VIP数量
            $directVips = DB::select("
                SELECT
                    COUNT(*) as count
                FROM
                    app_users
                WHERE
                    referrer_id = ?
                    AND is_vip_paid = 1
            ", [$leaderId]);

            // 获取本月直推VIP数量
            $monthDirectVips = DB::select("
                SELECT
                    COUNT(*) as count
                FROM
                    app_users
                WHERE
                    referrer_id = ?
                    AND is_vip_paid = 1
                    AND DATE_FORMAT(vip_paid_at, '%Y-%m') = ?
            ", [$leaderId, $currentMonth]);

            // 保存结果
            $results[] = [
                'id' => $leaderId,
                'name' => $leader->name,
                'wechat_nickname' => $leader->wechat_nickname,
                'total_team_count' => $teamStats[0]->total_team_count,
                'total_vip_count' => $teamStats[0]->total_vip_count + 1, // 加上自己
                'month_vip_count' => $monthVipCount,
                'direct_vip_count' => $directVips[0]->count,
                'month_direct_vip_count' => $monthDirectVips[0]->count,
                'self_is_new_this_month' => $selfIsNewThisMonth
            ];
        }
    }

    // 按本月新增VIP数量排序
    usort($results, function($a, $b) {
        return $b['month_vip_count'] - $a['month_vip_count'];
    });

    vip_log("找到 " . count($results) . " 个达标用户");

    // 返回成功响应
    echo json_encode([
        'code' => 0,
        'message' => 'success',
        'data' => [
            'month' => $currentMonth,
            'junior_vip_teams' => count($results),
            'qualified_users' => $results
        ]
    ]);

} catch (Exception $e) {
    vip_log("错误: " . $e->getMessage());

    // 返回错误响应
    echo json_encode([
        'code' => 500,
        'message' => $e->getMessage()
    ]);
}
