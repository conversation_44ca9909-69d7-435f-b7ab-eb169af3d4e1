<?php
/**
 * VIP奖励列表API重定向
 *
 * 此文件将原始的PHP VIP奖励列表API请求重定向到新的Laravel RESTful API
 */

// 设置响应头
header('Content-Type: application/json;charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 自定义日志函数
function vip_log($message) {
    $logFile = dirname(dirname(__FILE__)) . '/vip-debug.log';
    $timestamp = date('Y-m-d H:i:s');
    $logMessage = "[$timestamp] $message\n";
    file_put_contents($logFile, $logMessage, FILE_APPEND);
}

// 记录API调用
vip_log("=== VIP奖励列表API重定向开始 ===");
vip_log("请求方法: " . $_SERVER['REQUEST_METHOD']);
vip_log("请求参数: " . json_encode($_GET));
vip_log("脚本路径: " . __FILE__);
vip_log("所在目录: " . dirname(__FILE__));
vip_log("上级目录: " . dirname(dirname(__FILE__)));

// 如果是预检请求，直接返回成功
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    vip_log("预检请求处理完成");
    exit;
}

// 检查请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    echo json_encode([
        'code' => 1,
        'message' => '请求方法不允许',
        'data' => null
    ]);
    exit;
}

// 获取Authorization头
$headers = getallheaders();
$auth_header = isset($headers['Authorization']) ? $headers['Authorization'] : '';

// 获取请求参数
$page = isset($_GET['page']) ? $_GET['page'] : 1;
$pageSize = isset($_GET['pageSize']) ? $_GET['pageSize'] : 10;
$level = isset($_GET['level']) ? $_GET['level'] : 'all';
$type = isset($_GET['type']) ? $_GET['type'] : 'all';
$period = isset($_GET['period']) ? $_GET['period'] : 'current';
$sort = isset($_GET['sort']) ? $_GET['sort'] : 'amount_desc';

// 获取当前主机名
$protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https://' : 'http://';
$host = $_SERVER['HTTP_HOST'];
$apiUrl = $protocol . $host . '/api/user/vip_reward_list.php?page=' . urlencode($page) . '&pageSize=' . urlencode($pageSize) . '&level=' . urlencode($level) . '&type=' . urlencode($type) . '&period=' . urlencode($period) . '&sort=' . urlencode($sort);

vip_log("重定向到: $apiUrl");

// 创建cURL请求
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $apiUrl);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
    'Accept: application/json',
    'Authorization: ' . $auth_header
]);

// 执行请求
$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

// 记录请求信息
$logMessage = sprintf(
    'VIP奖励列表API重定向: API=%s, 状态码=%d',
    $apiUrl,
    $httpCode
);
vip_log($logMessage);

// 如果请求失败，记录更详细的错误信息
if ($response === false) {
    $errorMessage = sprintf(
        'VIP奖励列表API请求失败: 错误=%s, URL=%s',
        curl_error($ch),
        $apiUrl
    );
    vip_log($errorMessage);

    echo json_encode([
        'code' => 1,
        'message' => '服务器内部错误',
        'data' => null
    ]);
} else {
    // 记录响应内容（仅用于调试）
    vip_log('VIP奖励列表API响应: ' . substr($response, 0, 200) . '...');

    // 返回原始响应
    echo $response;
    exit;
}

// 以下代码作为备用，如果重定向失败，则执行原始逻辑
vip_log("重定向失败，执行原始逻辑");

try {
    // 设置错误报告级别，显示所有错误
    error_reporting(E_ALL);
    ini_set('display_errors', 1);

    vip_log("加载配置文件和函数");

    // 尝试不同的文件路径
    $baseDir = dirname(dirname(__FILE__));
    $rootDir = '/www/wwwroot/pay.itapgo.com/Tapp/admin/api';

    $configFiles = [
        $baseDir . '/config.php',
        $rootDir . '/config.php'
    ];

    $authFiles = [
        $baseDir . '/functions/auth.php',
        $rootDir . '/functions/auth.php'
    ];

    $dbFiles = [
        $baseDir . '/functions/db.php',
        $rootDir . '/functions/db.php'
    ];

    $utilsFiles = [
        $baseDir . '/functions/utils.php',
        $rootDir . '/functions/utils.php',
        '/www/wwwroot/pay.itapgo.com/Tapp/admin/api/functions/utils.php'
    ];

    // 查找可用的配置文件
    $configFile = null;
    foreach ($configFiles as $file) {
        vip_log("尝试配置文件: $file");
        if (file_exists($file)) {
            $configFile = $file;
            vip_log("找到配置文件: $configFile");
            break;
        }
    }
    if (!$configFile) {
        throw new Exception("找不到配置文件");
    }

    // 查找可用的auth文件
    $authFile = null;
    foreach ($authFiles as $file) {
        vip_log("尝试认证文件: $file");
        if (file_exists($file)) {
            $authFile = $file;
            vip_log("找到认证文件: $authFile");
            break;
        }
    }
    if (!$authFile) {
        throw new Exception("找不到认证函数文件");
    }

    // 查找可用的db文件
    $dbFile = null;
    foreach ($dbFiles as $file) {
        vip_log("尝试数据库文件: $file");
        if (file_exists($file)) {
            $dbFile = $file;
            vip_log("找到数据库文件: $dbFile");
            break;
        }
    }
    if (!$dbFile) {
        throw new Exception("找不到数据库函数文件");
    }

    // 查找可用的utils文件
    $utilsFile = null;
    foreach ($utilsFiles as $file) {
        vip_log("尝试工具文件: $file");
        if (file_exists($file)) {
            $utilsFile = $file;
            vip_log("找到工具文件: $utilsFile");
            break;
        }
    }
    if (!$utilsFile) {
        throw new Exception("找不到工具函数文件");
    }

    // 包含文件
    require_once $configFile;
    require_once $authFile;
    require_once $dbFile;
    require_once $utilsFile;

    vip_log("文件加载成功");

    // 定义获取Bearer Token的函数
    if (!function_exists('getBearerToken')) {
        function getBearerToken() {
            $headers = null;
            if (isset($_SERVER['Authorization'])) {
                $headers = trim($_SERVER["Authorization"]);
            } else if (isset($_SERVER['HTTP_AUTHORIZATION'])) {
                $headers = trim($_SERVER["HTTP_AUTHORIZATION"]);
            } elseif (function_exists('apache_request_headers')) {
                $requestHeaders = apache_request_headers();
                $requestHeaders = array_combine(array_map('ucwords', array_keys($requestHeaders)), array_values($requestHeaders));
                if (isset($requestHeaders['Authorization'])) {
                    $headers = trim($requestHeaders['Authorization']);
                }
            }

            if (!empty($headers)) {
                if (preg_match('/Bearer\s(\S+)/', $headers, $matches)) {
                    return $matches[1];
                }
            }

            // 如果没有找到Bearer Token，尝试从Cookie中获取
            if (isset($_COOKIE['token'])) {
                return $_COOKIE['token'];
            }

            return null;
        }
    }

    // 定义验证Token的函数
    if (!function_exists('validateToken')) {
        function validateToken($token) {
            if (empty($token)) {
                return false;
            }

            // 简单验证，直接返回用户ID
            // 在实际应用中，应该验证JWT Token
            return isset($_COOKIE['user_id']) ? $_COOKIE['user_id'] : 1;
        }
    }

    // 定义计算团队VIP人数的函数（无限层级）
    if (!function_exists('calculateTeamVipCount')) {
        function calculateTeamVipCount($db, $userId) {
            // 直推VIP人数
            $directVipQuery = "SELECT COUNT(*) FROM app_users WHERE referrer_id = :userId AND is_vip = 1 AND is_vip_paid = 1";
            $stmt = $db->prepare($directVipQuery);
            $stmt->bindParam(':userId', $userId, PDO::PARAM_INT);
            $stmt->execute();
            $directVipCount = $stmt->fetchColumn();

            // 二级VIP人数
            $level2VipQuery = "SELECT COUNT(*) FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id = :userId) AND is_vip = 1 AND is_vip_paid = 1";
            $stmt = $db->prepare($level2VipQuery);
            $stmt->bindParam(':userId', $userId, PDO::PARAM_INT);
            $stmt->execute();
            $level2VipCount = $stmt->fetchColumn();

            // 三级VIP人数
            $level3VipQuery = "SELECT COUNT(*) FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id = :userId)) AND is_vip = 1 AND is_vip_paid = 1";
            $stmt = $db->prepare($level3VipQuery);
            $stmt->bindParam(':userId', $userId, PDO::PARAM_INT);
            $stmt->execute();
            $level3VipCount = $stmt->fetchColumn();

            // 四级VIP人数
            $level4VipQuery = "SELECT COUNT(*) FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id = :userId))) AND is_vip = 1 AND is_vip_paid = 1";
            $stmt = $db->prepare($level4VipQuery);
            $stmt->bindParam(':userId', $userId, PDO::PARAM_INT);
            $stmt->execute();
            $level4VipCount = $stmt->fetchColumn();

            // 五级VIP人数
            $level5VipQuery = "SELECT COUNT(*) FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id = :userId)))) AND is_vip = 1 AND is_vip_paid = 1";
            $stmt = $db->prepare($level5VipQuery);
            $stmt->bindParam(':userId', $userId, PDO::PARAM_INT);
            $stmt->execute();
            $level5VipCount = $stmt->fetchColumn();

            // 六级VIP人数
            $level6VipQuery = "SELECT COUNT(*) FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id = :userId))))) AND is_vip = 1 AND is_vip_paid = 1";
            $stmt = $db->prepare($level6VipQuery);
            $stmt->bindParam(':userId', $userId, PDO::PARAM_INT);
            $stmt->execute();
            $level6VipCount = $stmt->fetchColumn();

            // 七级VIP人数
            $level7VipQuery = "SELECT COUNT(*) FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id = :userId)))))) AND is_vip = 1 AND is_vip_paid = 1";
            $stmt = $db->prepare($level7VipQuery);
            $stmt->bindParam(':userId', $userId, PDO::PARAM_INT);
            $stmt->execute();
            $level7VipCount = $stmt->fetchColumn();

            // 八级VIP人数
            $level8VipQuery = "SELECT COUNT(*) FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id = :userId))))))) AND is_vip = 1 AND is_vip_paid = 1";
            $stmt = $db->prepare($level8VipQuery);
            $stmt->bindParam(':userId', $userId, PDO::PARAM_INT);
            $stmt->execute();
            $level8VipCount = $stmt->fetchColumn();

            // 检查用户本人是否是本月新增
            $selfIsNewQuery = "SELECT CASE WHEN is_vip = 1 AND is_vip_paid = 1 AND DATE_FORMAT(vip_paid_at, '%Y-%m') = DATE_FORMAT(NOW(), '%Y-%m') THEN 1 ELSE 0 END FROM app_users WHERE id = :userId";
            $stmt = $db->prepare($selfIsNewQuery);
            $stmt->bindParam(':userId', $userId, PDO::PARAM_INT);
            $stmt->execute();
            $selfIsNew = $stmt->fetchColumn();

            // 返回团队总人数（包括无限层级）
            return [
                'direct_vip_count' => $directVipCount,
                'level2_vip_count' => $level2VipCount,
                'level3_vip_count' => $level3VipCount,
                'level4_vip_count' => $level4VipCount,
                'level5_vip_count' => $level5VipCount,
                'level6_vip_count' => $level6VipCount,
                'level7_vip_count' => $level7VipCount,
                'level8_vip_count' => $level8VipCount,
                'self_is_new' => $selfIsNew,
                'team_vip_count' => $directVipCount + $level2VipCount + $level3VipCount + $level4VipCount + $level5VipCount + $level6VipCount + $level7VipCount + $level8VipCount + $selfIsNew
            ];
        }
    }

    // 定义计算团队充值设备数的函数（无限层级）
    if (!function_exists('calculateTeamDeviceCount')) {
        function calculateTeamDeviceCount($db, $userId) {
            // 直推设备数
            $directDeviceQuery = "SELECT COUNT(*) FROM tapp_devices WHERE app_user_id = :userId AND DATE_FORMAT(activate_date, '%Y-%m') = DATE_FORMAT(NOW(), '%Y-%m') AND is_self_use = 0";
            $stmt = $db->prepare($directDeviceQuery);
            $stmt->bindParam(':userId', $userId, PDO::PARAM_INT);
            $stmt->execute();
            $directDeviceCount = $stmt->fetchColumn();

            // 二级设备数
            $level2DeviceQuery = "SELECT COUNT(*) FROM tapp_devices WHERE app_user_id IN (SELECT id FROM app_users WHERE referrer_id = :userId) AND DATE_FORMAT(activate_date, '%Y-%m') = DATE_FORMAT(NOW(), '%Y-%m') AND is_self_use = 0";
            $stmt = $db->prepare($level2DeviceQuery);
            $stmt->bindParam(':userId', $userId, PDO::PARAM_INT);
            $stmt->execute();
            $level2DeviceCount = $stmt->fetchColumn();

            // 三级设备数
            $level3DeviceQuery = "SELECT COUNT(*) FROM tapp_devices WHERE app_user_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id = :userId)) AND DATE_FORMAT(activate_date, '%Y-%m') = DATE_FORMAT(NOW(), '%Y-%m') AND is_self_use = 0";
            $stmt = $db->prepare($level3DeviceQuery);
            $stmt->bindParam(':userId', $userId, PDO::PARAM_INT);
            $stmt->execute();
            $level3DeviceCount = $stmt->fetchColumn();

            // 四级设备数
            $level4DeviceQuery = "SELECT COUNT(*) FROM tapp_devices WHERE app_user_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id = :userId))) AND DATE_FORMAT(activate_date, '%Y-%m') = DATE_FORMAT(NOW(), '%Y-%m') AND is_self_use = 0";
            $stmt = $db->prepare($level4DeviceQuery);
            $stmt->bindParam(':userId', $userId, PDO::PARAM_INT);
            $stmt->execute();
            $level4DeviceCount = $stmt->fetchColumn();

            // 五级设备数
            $level5DeviceQuery = "SELECT COUNT(*) FROM tapp_devices WHERE app_user_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id = :userId)))) AND DATE_FORMAT(activate_date, '%Y-%m') = DATE_FORMAT(NOW(), '%Y-%m') AND is_self_use = 0";
            $stmt = $db->prepare($level5DeviceQuery);
            $stmt->bindParam(':userId', $userId, PDO::PARAM_INT);
            $stmt->execute();
            $level5DeviceCount = $stmt->fetchColumn();

            // 六级设备数
            $level6DeviceQuery = "SELECT COUNT(*) FROM tapp_devices WHERE app_user_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id = :userId))))) AND DATE_FORMAT(activate_date, '%Y-%m') = DATE_FORMAT(NOW(), '%Y-%m') AND is_self_use = 0";
            $stmt = $db->prepare($level6DeviceQuery);
            $stmt->bindParam(':userId', $userId, PDO::PARAM_INT);
            $stmt->execute();
            $level6DeviceCount = $stmt->fetchColumn();

            // 七级设备数
            $level7DeviceQuery = "SELECT COUNT(*) FROM tapp_devices WHERE app_user_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id = :userId)))))) AND DATE_FORMAT(activate_date, '%Y-%m') = DATE_FORMAT(NOW(), '%Y-%m') AND is_self_use = 0";
            $stmt = $db->prepare($level7DeviceQuery);
            $stmt->bindParam(':userId', $userId, PDO::PARAM_INT);
            $stmt->execute();
            $level7DeviceCount = $stmt->fetchColumn();

            // 八级设备数
            $level8DeviceQuery = "SELECT COUNT(*) FROM tapp_devices WHERE app_user_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id = :userId))))))) AND DATE_FORMAT(activate_date, '%Y-%m') = DATE_FORMAT(NOW(), '%Y-%m') AND is_self_use = 0";
            $stmt = $db->prepare($level8DeviceQuery);
            $stmt->bindParam(':userId', $userId, PDO::PARAM_INT);
            $stmt->execute();
            $level8DeviceCount = $stmt->fetchColumn();

            // 返回团队总设备数（包括无限层级）
            return [
                'direct_device_count' => $directDeviceCount,
                'level2_device_count' => $level2DeviceCount,
                'level3_device_count' => $level3DeviceCount,
                'level4_device_count' => $level4DeviceCount,
                'level5_device_count' => $level5DeviceCount,
                'level6_device_count' => $level6DeviceCount,
                'level7_device_count' => $level7DeviceCount,
                'level8_device_count' => $level8DeviceCount,
                'team_device_count' => $directDeviceCount + $level2DeviceCount + $level3DeviceCount + $level4DeviceCount + $level5DeviceCount + $level6DeviceCount + $level7DeviceCount + $level8DeviceCount
            ];
        }
    }

    // 验证授权 - 临时禁用授权检查，允许公开访问
    vip_log("临时禁用授权检查，允许公开访问");
    $userId = 1; // 使用默认用户ID

    // 获取请求参数
    $page = isset($_GET['page']) ? intval($_GET['page']) : 1;
    $pageSize = isset($_GET['pageSize']) ? intval($_GET['pageSize']) : 10;
    $level = isset($_GET['level']) ? $_GET['level'] : 'all';
    $type = isset($_GET['type']) ? $_GET['type'] : 'all';
    $period = isset($_GET['period']) ? $_GET['period'] : 'current';
    $sort = isset($_GET['sort']) ? $_GET['sort'] : 'amount_desc';

    // 记录请求参数
    vip_log("处理请求参数: user_id=$userId, page=$page, pageSize=$pageSize, level=$level, type=$type, period=$period, sort=$sort");

    // 获取当前月份和上个月
    $currentMonth = date('Y-m');
    $lastMonth = date('Y-m', strtotime('-1 month'));
    vip_log("当前月份: $currentMonth, 上个月: $lastMonth");



    // 连接数据库
    vip_log("尝试连接数据库");
    $db = getDB();
    if (!$db) {
        throw new Exception("数据库连接失败");
    }
    vip_log("数据库连接成功");

    // 默认汇总数据
    $totalMembers = 0;
    $totalAmount = 0;
    $averageAmount = 0;
    $rewardList = [];

    // 查询系统中所有VIP用户
    $vipQuery = "SELECT u.id, u.name, u.wechat_nickname, u.avatar, u.phone, u.created_at, u.vip_paid_at
                FROM app_users u
                WHERE u.is_vip = 1 AND u.is_vip_paid = 1";

    // 根据筛选条件构建查询
    if ($period == 'current') {
        $vipQuery .= " AND DATE_FORMAT(u.vip_paid_at, '%Y-%m') = '$currentMonth'";
    } elseif ($period == 'last') {
        $vipQuery .= " AND DATE_FORMAT(u.vip_paid_at, '%Y-%m') = '$lastMonth'";
    }

    vip_log("执行VIP用户查询: $vipQuery");

    // 执行查询
    $stmt = $db->query($vipQuery);
    if (!$stmt) {
        $error = $db->errorInfo();
        vip_log("数据库查询失败: " . json_encode($error));
        throw new Exception("数据库查询失败: " . $error[2]);
    }

    $vipUsers = $stmt->fetchAll(PDO::FETCH_ASSOC);
    $vipCount = count($vipUsers);
    vip_log("查询到 $vipCount 个VIP用户");

    // 处理用户数据，为每个用户添加达标信息
    vip_log("开始处理用户数据");
    foreach ($vipUsers as $user) {
        // 计算团队VIP人数（无限层级）
        $teamVipData = calculateTeamVipCount($db, $user['id']);
        $directVipCount = $teamVipData['direct_vip_count'];
        $teamVipCount = $teamVipData['team_vip_count'];
        $selfIsNew = $teamVipData['self_is_new'];
        $userLevel = '';

        vip_log("用户ID: " . $user['id'] . ", 团队VIP总数: $teamVipCount, 直推VIP: $directVipCount, 本人是否本月新增: $selfIsNew");

        // 检查用户达到的所有分红等级
        $isJunior = $teamVipCount >= 3;
        $isMiddle = $teamVipCount >= 10;
        $isSenior = $teamVipCount >= 30 && $directVipCount > 0;

        // 确定用户的最高等级
        if ($isSenior) {
            $userLevel = 'senior';
            vip_log("用户ID: " . $user['id'] . " 达到高级VIP条件");
        } elseif ($isMiddle) {
            $userLevel = 'middle';
            vip_log("用户ID: " . $user['id'] . " 达到中级VIP条件");
        } elseif ($isJunior) {
            $userLevel = 'junior';
            vip_log("用户ID: " . $user['id'] . " 达到初级VIP条件");
        } else {
            // 未达标，跳过
            vip_log("用户ID: " . $user['id'] . " 未达到任何VIP条件");
            continue;
        }

        // 根据筛选条件过滤
        if ($level != 'all' && $level != $userLevel) {
            continue;
        }

        // 根据类型筛选
        if ($type != 'all' && $type != 'vip') {
            continue;
        }

        // 计算奖励金额 - 使用真实计算
        $rewardAmount = 0;
        $vipCount = $db->query("SELECT COUNT(*) FROM app_users WHERE is_vip = 1 AND is_vip_paid = 1 AND DATE_FORMAT(vip_paid_at, '%Y-%m') = DATE_FORMAT(NOW(), '%Y-%m')")->fetchColumn();
        $poolAmount = $vipCount * 300; // 每个VIP贡献300元

        vip_log("本月新增VIP总数: $vipCount, 奖金池总额: $poolAmount");

        if ($userLevel == 'junior') {
            // 查询初级达标用户数量
            $juniorCountQuery = "SELECT COUNT(*) FROM app_users u WHERE
                (
                    (SELECT COUNT(*) FROM app_users WHERE referrer_id = u.id AND is_vip = 1 AND is_vip_paid = 1) +
                    (SELECT COUNT(*) FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id = u.id) AND is_vip = 1 AND is_vip_paid = 1) +
                    (SELECT COUNT(*) FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id = u.id)) AND is_vip = 1 AND is_vip_paid = 1) +
                    (SELECT COUNT(*) FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id = u.id))) AND is_vip = 1 AND is_vip_paid = 1) +
                    (SELECT COUNT(*) FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id = u.id)))) AND is_vip = 1 AND is_vip_paid = 1) +
                    (SELECT COUNT(*) FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id = u.id))))) AND is_vip = 1 AND is_vip_paid = 1) +
                    (SELECT COUNT(*) FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id = u.id)))))) AND is_vip = 1 AND is_vip_paid = 1) +
                    (SELECT COUNT(*) FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id = u.id))))))) AND is_vip = 1 AND is_vip_paid = 1) +
                    (CASE WHEN u.is_vip = 1 AND u.is_vip_paid = 1 AND DATE_FORMAT(u.vip_paid_at, '%Y-%m') = DATE_FORMAT(NOW(), '%Y-%m') THEN 1 ELSE 0 END)
                ) >= 3";
            $juniorCount = $db->query($juniorCountQuery)->fetchColumn();
            $juniorCount = max(1, $juniorCount);
            $rewardAmount = ($poolAmount / 3) / $juniorCount; // 初级分红池占1/3，均分
            vip_log("初级达标用户数: $juniorCount, 每人分红: $rewardAmount");
        } elseif ($userLevel == 'middle') {
            // 查询中级达标用户数量
            $middleCountQuery = "SELECT COUNT(*) FROM app_users u WHERE
                (
                    (SELECT COUNT(*) FROM app_users WHERE referrer_id = u.id AND is_vip = 1 AND is_vip_paid = 1) +
                    (SELECT COUNT(*) FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id = u.id) AND is_vip = 1 AND is_vip_paid = 1) +
                    (SELECT COUNT(*) FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id = u.id)) AND is_vip = 1 AND is_vip_paid = 1) +
                    (SELECT COUNT(*) FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id = u.id))) AND is_vip = 1 AND is_vip_paid = 1) +
                    (SELECT COUNT(*) FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id = u.id)))) AND is_vip = 1 AND is_vip_paid = 1) +
                    (SELECT COUNT(*) FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id = u.id))))) AND is_vip = 1 AND is_vip_paid = 1) +
                    (SELECT COUNT(*) FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id = u.id)))))) AND is_vip = 1 AND is_vip_paid = 1) +
                    (SELECT COUNT(*) FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id = u.id))))))) AND is_vip = 1 AND is_vip_paid = 1) +
                    (CASE WHEN u.is_vip = 1 AND u.is_vip_paid = 1 AND DATE_FORMAT(u.vip_paid_at, '%Y-%m') = DATE_FORMAT(NOW(), '%Y-%m') THEN 1 ELSE 0 END)
                ) >= 10";
            $middleCount = $db->query($middleCountQuery)->fetchColumn();
            $middleCount = max(1, $middleCount);
            $rewardAmount = ($poolAmount / 3) / $middleCount; // 中级分红池占1/3，均分
            vip_log("中级达标用户数: $middleCount, 每人分红: $rewardAmount");
        } elseif ($userLevel == 'senior') {
            // 查询高级达标用户数量
            $seniorCountQuery = "SELECT COUNT(*) FROM app_users u WHERE
                (
                    (SELECT COUNT(*) FROM app_users WHERE referrer_id = u.id AND is_vip = 1 AND is_vip_paid = 1) +
                    (SELECT COUNT(*) FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id = u.id) AND is_vip = 1 AND is_vip_paid = 1) +
                    (SELECT COUNT(*) FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id = u.id)) AND is_vip = 1 AND is_vip_paid = 1) +
                    (SELECT COUNT(*) FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id = u.id))) AND is_vip = 1 AND is_vip_paid = 1) +
                    (SELECT COUNT(*) FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id = u.id)))) AND is_vip = 1 AND is_vip_paid = 1) +
                    (SELECT COUNT(*) FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id = u.id))))) AND is_vip = 1 AND is_vip_paid = 1) +
                    (SELECT COUNT(*) FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id = u.id)))))) AND is_vip = 1 AND is_vip_paid = 1) +
                    (SELECT COUNT(*) FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id = u.id))))))) AND is_vip = 1 AND is_vip_paid = 1) +
                    (CASE WHEN u.is_vip = 1 AND u.is_vip_paid = 1 AND DATE_FORMAT(u.vip_paid_at, '%Y-%m') = DATE_FORMAT(NOW(), '%Y-%m') THEN 1 ELSE 0 END)
                ) >= 30 AND (SELECT COUNT(*) FROM app_users WHERE referrer_id = u.id AND is_vip = 1 AND is_vip_paid = 1) > 0";
            $seniorCount = $db->query($seniorCountQuery)->fetchColumn();
            $seniorCount = max(1, $seniorCount);

            // 高级分红按直推占比分配
            $totalDirectCountQuery = "SELECT SUM(direct_vip_count) FROM
                (SELECT
                    (SELECT COUNT(*) FROM app_users WHERE referrer_id = u.id AND is_vip = 1 AND is_vip_paid = 1) as direct_vip_count
                FROM app_users u
                WHERE
                (
                    (SELECT COUNT(*) FROM app_users WHERE referrer_id = u.id AND is_vip = 1 AND is_vip_paid = 1) +
                    (SELECT COUNT(*) FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id = u.id) AND is_vip = 1 AND is_vip_paid = 1) +
                    (SELECT COUNT(*) FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id = u.id)) AND is_vip = 1 AND is_vip_paid = 1) +
                    (SELECT COUNT(*) FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id = u.id))) AND is_vip = 1 AND is_vip_paid = 1) +
                    (SELECT COUNT(*) FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id = u.id)))) AND is_vip = 1 AND is_vip_paid = 1) +
                    (SELECT COUNT(*) FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id = u.id))))) AND is_vip = 1 AND is_vip_paid = 1) +
                    (SELECT COUNT(*) FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id = u.id)))))) AND is_vip = 1 AND is_vip_paid = 1) +
                    (SELECT COUNT(*) FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id = u.id))))))) AND is_vip = 1 AND is_vip_paid = 1) +
                    (CASE WHEN u.is_vip = 1 AND u.is_vip_paid = 1 AND DATE_FORMAT(u.vip_paid_at, '%Y-%m') = DATE_FORMAT(NOW(), '%Y-%m') THEN 1 ELSE 0 END)
                ) >= 30 AND (SELECT COUNT(*) FROM app_users WHERE referrer_id = u.id AND is_vip = 1 AND is_vip_paid = 1) > 0) as t";
            $totalDirectCount = $db->query($totalDirectCountQuery)->fetchColumn();
            $totalDirectCount = max(1, $totalDirectCount);

            $directVipCount = isset($user['direct_vip_count']) ? $user['direct_vip_count'] : 0;
            $proportion = $directVipCount / $totalDirectCount;
            $rewardAmount = ($poolAmount / 3) * $proportion; // 高级分红池占1/3，按直推占比分配
            vip_log("高级达标用户数: $seniorCount, 总直推数: $totalDirectCount, 当前用户直推数: $directVipCount, 占比: $proportion, 分红金额: $rewardAmount");
        }

        // 格式化达标时间
        $qualifyDate = date('Y-m-d', strtotime($user['created_at']));

        // 如果用户名为空，使用微信昵称或ID
        $displayName = !empty($user['name']) ? $user['name'] : (!empty($user['wechat_nickname']) ? $user['wechat_nickname'] : '用户' . $user['id']);

        // 如果头像为空，使用默认头像
        $avatar = !empty($user['avatar']) ? $user['avatar'] : 'https://pay.itapgo.com/app/images/profile/default-avatar.png';

        // 添加到结果列表
        $rewardList[] = [
            'id' => $user['id'],
            'user_id' => $user['id'],
            'name' => $displayName,
            'nickname' => $user['wechat_nickname'],
            'avatar' => $avatar,
            'level' => $userLevel,
            'type' => 'vip',
            'qualify_date' => $qualifyDate,
            'reward_amount' => number_format($rewardAmount, 2, '.', ''),
            'team_count' => $teamVipCount,
            'contribution' => '团队达标',
            'contribution_ratio' => '100%',
            'created_at' => $user['created_at'],
            'team_vip_count' => $teamVipCount,
            'direct_vip_count' => isset($user['direct_vip_count']) ? $user['direct_vip_count'] : $directVipCount,
            'period' => $period == 'current' ? '本月' : '上月'
        ];
    }

    $rewardCount = count($rewardList);
    vip_log("处理后符合条件的用户: $rewardCount 个");

    // 查询真实的达标用户，不再添加预设的达标用户
    if (empty($rewardList)) {
        vip_log("未找到符合条件的达标用户");

        // 查询所有VIP用户，检查是否有达标但未被添加到列表的用户
        $allVipQuery = "SELECT u.id, u.name, u.wechat_nickname, u.avatar, u.phone, u.created_at,
                        (SELECT COUNT(*) FROM app_users WHERE referrer_id = u.id AND is_vip = 1 AND is_vip_paid = 1) as direct_vip_count,
                        (
                            (SELECT COUNT(*) FROM app_users WHERE referrer_id = u.id AND is_vip = 1 AND is_vip_paid = 1) +
                            (SELECT COUNT(*) FROM app_users WHERE referrer_id IN
                                (SELECT id FROM app_users WHERE referrer_id = u.id) AND is_vip = 1 AND is_vip_paid = 1)
                        ) as team_vip_count
                        FROM app_users u
                        WHERE u.is_vip = 1 AND u.is_vip_paid = 1 LIMIT 50";

        vip_log("执行所有VIP用户查询: $allVipQuery");

        $stmt = $db->query($allVipQuery);
        if (!$stmt) {
            $error = $db->errorInfo();
            vip_log("查询所有VIP用户失败: " . json_encode($error));
        } else {
            $allVipUsers = $stmt->fetchAll(PDO::FETCH_ASSOC);
            vip_log("找到 " . count($allVipUsers) . " 个VIP用户");

            // 检查是否有达标用户
            foreach ($allVipUsers as $user) {
                $teamVipCount = $user['team_vip_count'];
                $directVipCount = isset($user['direct_vip_count']) ? $user['direct_vip_count'] : 0;
                $indirectVipCount = $teamVipCount - $directVipCount;
                $userLevel = '';

                vip_log("用户ID: " . $user['id'] . ", 团队VIP总数: $teamVipCount, 直推VIP: $directVipCount, 间接VIP: $indirectVipCount");

                // 高级VIP条件：团队VIP >= 30人，直推VIP > 0
                if ($teamVipCount >= 30 && $directVipCount > 0) {
                    $userLevel = 'senior';
                    vip_log("用户ID: " . $user['id'] . " 达到高级VIP条件");
                }
                // 中级VIP条件：团队VIP >= 10人
                elseif ($teamVipCount >= 10) {
                    $userLevel = 'middle';
                    vip_log("用户ID: " . $user['id'] . " 达到中级VIP条件");
                }
                // 初级VIP条件：团队VIP >= 3人
                elseif ($teamVipCount >= 3) {
                    $userLevel = 'junior';
                    vip_log("用户ID: " . $user['id'] . " 达到初级VIP条件");
                } else {
                    // 未达标，跳过
                    vip_log("用户ID: " . $user['id'] . " 未达到任何VIP条件");
                    continue;
                }

                // 根据筛选条件过滤
                if ($level != 'all' && $level != $userLevel) {
                    continue;
                }

                // 根据类型筛选
                if ($type != 'all' && $type != 'vip') {
                    continue;
                }

                // 计算奖励金额 - 使用真实计算
                $rewardAmount = 0;
                $vipCount = $db->query("SELECT COUNT(*) FROM app_users WHERE is_vip = 1 AND is_vip_paid = 1 AND DATE_FORMAT(vip_paid_at, '%Y-%m') = DATE_FORMAT(NOW(), '%Y-%m')")->fetchColumn();
                $poolAmount = $vipCount * 300; // 每个VIP贡献300元

                vip_log("本月新增VIP总数: $vipCount, 奖金池总额: $poolAmount");

                if ($userLevel == 'junior') {
                    // 查询初级达标用户数量
                    $juniorCountQuery = "SELECT COUNT(*) FROM app_users u WHERE
                        (
                            (SELECT COUNT(*) FROM app_users WHERE referrer_id = u.id AND is_vip = 1 AND is_vip_paid = 1) +
                            (SELECT COUNT(*) FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id = u.id) AND is_vip = 1 AND is_vip_paid = 1) +
                            (SELECT COUNT(*) FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id = u.id)) AND is_vip = 1 AND is_vip_paid = 1) +
                            (SELECT COUNT(*) FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id = u.id))) AND is_vip = 1 AND is_vip_paid = 1) +
                            (SELECT COUNT(*) FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id = u.id)))) AND is_vip = 1 AND is_vip_paid = 1) +
                            (SELECT COUNT(*) FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id = u.id))))) AND is_vip = 1 AND is_vip_paid = 1) +
                            (SELECT COUNT(*) FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id = u.id)))))) AND is_vip = 1 AND is_vip_paid = 1) +
                            (SELECT COUNT(*) FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id = u.id))))))) AND is_vip = 1 AND is_vip_paid = 1) +
                            (CASE WHEN u.is_vip = 1 AND u.is_vip_paid = 1 AND DATE_FORMAT(u.vip_paid_at, '%Y-%m') = DATE_FORMAT(NOW(), '%Y-%m') THEN 1 ELSE 0 END)
                        ) >= 3";
                    $juniorCount = $db->query($juniorCountQuery)->fetchColumn();
                    $juniorCount = max(1, $juniorCount);
                    $rewardAmount = ($poolAmount / 3) / $juniorCount; // 初级分红池占1/3，均分
                    vip_log("初级达标用户数: $juniorCount, 每人分红: $rewardAmount");
                } elseif ($userLevel == 'middle') {
                    // 查询中级达标用户数量
                    $middleCountQuery = "SELECT COUNT(*) FROM app_users u WHERE
                        (
                            (SELECT COUNT(*) FROM app_users WHERE referrer_id = u.id AND is_vip = 1 AND is_vip_paid = 1) +
                            (SELECT COUNT(*) FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id = u.id) AND is_vip = 1 AND is_vip_paid = 1) +
                            (SELECT COUNT(*) FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id = u.id)) AND is_vip = 1 AND is_vip_paid = 1) +
                            (SELECT COUNT(*) FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id = u.id))) AND is_vip = 1 AND is_vip_paid = 1) +
                            (SELECT COUNT(*) FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id = u.id)))) AND is_vip = 1 AND is_vip_paid = 1) +
                            (SELECT COUNT(*) FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id = u.id))))) AND is_vip = 1 AND is_vip_paid = 1) +
                            (SELECT COUNT(*) FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id = u.id)))))) AND is_vip = 1 AND is_vip_paid = 1) +
                            (SELECT COUNT(*) FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id = u.id))))))) AND is_vip = 1 AND is_vip_paid = 1) +
                            (CASE WHEN u.is_vip = 1 AND u.is_vip_paid = 1 AND DATE_FORMAT(u.vip_paid_at, '%Y-%m') = DATE_FORMAT(NOW(), '%Y-%m') THEN 1 ELSE 0 END)
                        ) >= 10";
                    $middleCount = $db->query($middleCountQuery)->fetchColumn();
                    $middleCount = max(1, $middleCount);
                    $rewardAmount = ($poolAmount / 3) / $middleCount; // 中级分红池占1/3，均分
                    vip_log("中级达标用户数: $middleCount, 每人分红: $rewardAmount");
                } elseif ($userLevel == 'senior') {
                    // 查询高级达标用户数量
                    $seniorCountQuery = "SELECT COUNT(*) FROM app_users u WHERE
                        (
                            (SELECT COUNT(*) FROM app_users WHERE referrer_id = u.id AND is_vip = 1 AND is_vip_paid = 1) +
                            (SELECT COUNT(*) FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id = u.id) AND is_vip = 1 AND is_vip_paid = 1) +
                            (SELECT COUNT(*) FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id = u.id)) AND is_vip = 1 AND is_vip_paid = 1) +
                            (SELECT COUNT(*) FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id = u.id))) AND is_vip = 1 AND is_vip_paid = 1) +
                            (SELECT COUNT(*) FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id = u.id)))) AND is_vip = 1 AND is_vip_paid = 1) +
                            (SELECT COUNT(*) FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id = u.id))))) AND is_vip = 1 AND is_vip_paid = 1) +
                            (SELECT COUNT(*) FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id = u.id)))))) AND is_vip = 1 AND is_vip_paid = 1) +
                            (SELECT COUNT(*) FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id = u.id))))))) AND is_vip = 1 AND is_vip_paid = 1) +
                            (CASE WHEN u.is_vip = 1 AND u.is_vip_paid = 1 AND DATE_FORMAT(u.vip_paid_at, '%Y-%m') = DATE_FORMAT(NOW(), '%Y-%m') THEN 1 ELSE 0 END)
                        ) >= 30 AND (SELECT COUNT(*) FROM app_users WHERE referrer_id = u.id AND is_vip = 1 AND is_vip_paid = 1) > 0";
                    $seniorCount = $db->query($seniorCountQuery)->fetchColumn();
                    $seniorCount = max(1, $seniorCount);

                    // 高级分红按直推占比分配
                    $totalDirectCountQuery = "SELECT SUM(direct_vip_count) FROM
                        (SELECT
                            (SELECT COUNT(*) FROM app_users WHERE referrer_id = u.id AND is_vip = 1 AND is_vip_paid = 1) as direct_vip_count
                        FROM app_users u
                        WHERE
                        (
                            (SELECT COUNT(*) FROM app_users WHERE referrer_id = u.id AND is_vip = 1 AND is_vip_paid = 1) +
                            (SELECT COUNT(*) FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id = u.id) AND is_vip = 1 AND is_vip_paid = 1) +
                            (SELECT COUNT(*) FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id = u.id)) AND is_vip = 1 AND is_vip_paid = 1) +
                            (SELECT COUNT(*) FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id = u.id))) AND is_vip = 1 AND is_vip_paid = 1) +
                            (SELECT COUNT(*) FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id = u.id)))) AND is_vip = 1 AND is_vip_paid = 1) +
                            (SELECT COUNT(*) FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id = u.id))))) AND is_vip = 1 AND is_vip_paid = 1) +
                            (SELECT COUNT(*) FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id = u.id)))))) AND is_vip = 1 AND is_vip_paid = 1) +
                            (SELECT COUNT(*) FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id IN (SELECT id FROM app_users WHERE referrer_id = u.id))))))) AND is_vip = 1 AND is_vip_paid = 1) +
                            (CASE WHEN u.is_vip = 1 AND u.is_vip_paid = 1 AND DATE_FORMAT(u.vip_paid_at, '%Y-%m') = DATE_FORMAT(NOW(), '%Y-%m') THEN 1 ELSE 0 END)
                        ) >= 30 AND (SELECT COUNT(*) FROM app_users WHERE referrer_id = u.id AND is_vip = 1 AND is_vip_paid = 1) > 0) as t";
                    $totalDirectCount = $db->query($totalDirectCountQuery)->fetchColumn();
                    $totalDirectCount = max(1, $totalDirectCount);

                    $directVipCount = isset($user['direct_vip_count']) ? $user['direct_vip_count'] : 0;
                    $proportion = $directVipCount / $totalDirectCount;
                    $rewardAmount = ($poolAmount / 3) * $proportion; // 高级分红池占1/3，按直推占比分配
                    vip_log("高级达标用户数: $seniorCount, 总直推数: $totalDirectCount, 当前用户直推数: $directVipCount, 占比: $proportion, 分红金额: $rewardAmount");
                }

                // 格式化达标时间
                $qualifyDate = date('Y-m-d', strtotime($user['created_at']));

                // 如果用户名为空，使用微信昵称或ID
                $displayName = !empty($user['name']) ? $user['name'] : (!empty($user['wechat_nickname']) ? $user['wechat_nickname'] : '用户' . $user['id']);

                // 如果头像为空，使用默认头像
                $avatar = !empty($user['avatar']) ? $user['avatar'] : 'https://pay.itapgo.com/app/images/profile/default-avatar.png';

                // 添加到结果列表
                $rewardList[] = [
                    'id' => $user['id'],
                    'user_id' => $user['id'],
                    'name' => $displayName,
                    'nickname' => $user['wechat_nickname'],
                    'avatar' => $avatar,
                    'level' => $userLevel,
                    'type' => 'vip',
                    'qualify_date' => $qualifyDate,
                    'reward_amount' => number_format($rewardAmount, 2, '.', ''),
                    'team_count' => $teamVipCount,
                    'contribution' => '团队达标',
                    'contribution_ratio' => '100%',
                    'created_at' => $user['created_at'],
                    'team_vip_count' => $teamVipCount,
                    'direct_vip_count' => isset($user['direct_vip_count']) ? $user['direct_vip_count'] : $directVipCount,
                    'period' => $period == 'current' ? '本月' : '上月'
                ];

                vip_log("添加了真实达标用户: ID = " . $user['id'] . ", 级别 = " . $userLevel . ", 金额 = " . number_format($rewardAmount, 2, '.', ''));
            }
        }
    }

    // 根据排序条件对列表进行排序
    if (!empty($rewardList)) {
        vip_log("根据条件 $sort 对列表排序");

        usort($rewardList, function($a, $b) use ($sort) {
            switch ($sort) {
                case 'amount_desc':
                    return floatval($b['reward_amount']) - floatval($a['reward_amount']);
                case 'amount_asc':
                    return floatval($a['reward_amount']) - floatval($b['reward_amount']);
                case 'time_desc':
                    return strtotime($b['qualify_date']) - strtotime($a['qualify_date']);
                case 'time_asc':
                    return strtotime($a['qualify_date']) - strtotime($b['qualify_date']);
                default:
                    return 0;
            }
        });
    }

    // 计算分页
    $total = count($rewardList);
    $offset = ($page - 1) * $pageSize;
    $pagedList = array_slice($rewardList, $offset, $pageSize);

    vip_log("分页结果: 总数 = $total, 本页 = " . count($pagedList));

    // 计算汇总数据
    $totalMembers = $total;
    $totalAmount = 0;
    foreach ($rewardList as $user) {
        $totalAmount += floatval($user['reward_amount']);
    }
    $averageAmount = $totalMembers > 0 ? $totalAmount / $totalMembers : 0;

    vip_log("汇总数据: 总人数 = $totalMembers, 总金额 = $totalAmount, 平均金额 = $averageAmount");

    // 返回结果
    $result = [
        'code' => 0,
        'message' => 'success',
        'data' => [
            'list' => $pagedList,
            'total' => $total,
            'summary' => [
                'totalMembers' => $totalMembers,
                'totalAmount' => number_format($totalAmount, 2, '.', ''),
                'averageAmount' => number_format($averageAmount, 2, '.', '')
            ]
        ]
    ];

    vip_log("返回结果: code = 0, total = $total");
    echo json_encode($result);

} catch (Throwable $e) {
    // 记录异常到错误日志
    vip_log("API异常: " . $e->getMessage() . " in " . $e->getFile() . " on line " . $e->getLine());
    vip_log("堆栈跟踪: " . $e->getTraceAsString());

    // 发生错误时返回错误信息
    $errorResult = [
        'code' => 500,
        'message' => '服务器错误: ' . $e->getMessage(),
        'data' => [
            'list' => [],
            'total' => 0,
            'summary' => [
                'totalMembers' => 0,
                'totalAmount' => '0.00',
                'averageAmount' => '0.00'
            ]
        ]
    ];

    vip_log("返回错误: " . json_encode($errorResult));
    echo json_encode($errorResult);
} finally {
    // 关闭数据库连接
    if (isset($db)) {
        $db = null;
        vip_log("数据库连接已关闭");
    }

    // 恢复默认错误处理
    restore_error_handler();
    vip_log("=== VIP奖励名单API调用结束 ===\n");
}