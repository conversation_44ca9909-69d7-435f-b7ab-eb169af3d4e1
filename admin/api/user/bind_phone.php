<?php
/**
 * 绑定手机号API
 * 
 * 接收参数：
 * - phone: 手机号
 * - code: 验证码
 * 
 * 返回数据：
 * {
 *   "code": 0,
 *   "message": "绑定成功",
 *   "data": {
 *     "user": {...},
 *     "roles": [...]
 *   }
 * }
 */

// 设置HTTP响应头
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 检查请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode([
        'code' => 1,
        'message' => '请求方法不允许',
        'data' => null
    ]);
    exit;
}

// 引入相关函数
require_once dirname(__DIR__) . '/functions/auth.php';
require_once dirname(__DIR__) . '/functions/sms.php';

// 获取请求参数
$data = json_decode(file_get_contents('php://input'), true);

// 验证必需的参数
if (!isset($data['phone']) || !isset($data['code'])) {
    echo json_encode([
        'code' => 1,
        'message' => '缺少必需参数',
        'data' => null
    ]);
    exit;
}

$phone = $data['phone'];
$code = $data['code'];

// 验证手机号格式
if (!preg_match('/^1[3-9]\d{9}$/', $phone)) {
    echo json_encode([
        'code' => 1,
        'message' => '手机号格式不正确',
        'data' => null
    ]);
    exit;
}

// 验证短信验证码
if (!validate_sms_code($phone, $code, 'bind')) {
    echo json_encode([
        'code' => 1,
        'message' => '验证码不正确或已过期',
        'data' => null
    ]);
    exit;
}

// 获取当前用户ID（从JWT解析）
$headers = getallheaders();
$auth_header = isset($headers['Authorization']) ? $headers['Authorization'] : '';
$token = str_replace('Bearer ', '', $auth_header);

$user = verify_auth($token);
if (!$user || !isset($user['id'])) {
    echo json_encode([
        'code' => 401,
        'message' => '未授权或令牌无效',
        'data' => null
    ]);
    exit;
}

$user_id = $user['id'];

// 连接数据库
$conn = get_db_connection();

// 检查手机号是否已被其他用户绑定
$stmt = $conn->prepare("SELECT id FROM app_users WHERE phone = ? AND id != ?");
$stmt->bind_param("si", $phone, $user_id);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows > 0) {
    $conn->close();
    echo json_encode([
        'code' => 1,
        'message' => '该手机号已被其他用户绑定',
        'data' => null
    ]);
    exit;
}

// 更新用户手机号
$stmt = $conn->prepare("UPDATE app_users SET phone = ? WHERE id = ?");
$stmt->bind_param("si", $phone, $user_id);
$stmt->execute();

$conn->close();

// 清除验证码
clear_sms_code($phone, 'bind');

// 获取更新后的用户信息
$updated_user = get_user_with_roles($user_id);

// 生成角色名称列表
$role_names = [];
if ($updated_user) {
    if (isset($updated_user['is_admin']) && $updated_user['is_admin'] == 1) {
        $role_names[] = '管理员';
    }
    if (isset($updated_user['is_pay_institution']) && $updated_user['is_pay_institution'] == 1) {
        $role_names[] = '支付机构';
    }
    if (isset($updated_user['is_engineer']) && $updated_user['is_engineer'] == 1) {
        $role_names[] = '工程师';
    }
    if (isset($updated_user['is_salesman']) && $updated_user['is_salesman'] == 1) {
        $role_names[] = '业务员';
    }
    if (isset($updated_user['is_vip']) && $updated_user['is_vip'] == 1) {
        $role_names[] = 'VIP会员';
    }
    if (isset($updated_user['is_water_purifier_user']) && $updated_user['is_water_purifier_user'] == 1) {
        $role_names[] = '净水器用户';
    }
    
    // 如果没有任何角色，添加默认角色
    if (empty($role_names)) {
        $role_names[] = '普通用户';
    }
}

// 返回成功响应，包含用户信息
echo json_encode([
    'code' => 0,
    'message' => '手机号绑定成功',
    'data' => [
        'user' => $updated_user,
        'roles' => $role_names
    ]
]);
?>