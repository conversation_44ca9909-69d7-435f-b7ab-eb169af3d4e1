<?php
/**
 * VIP个人资料API重定向
 *
 * 此文件将原始的PHP VIP个人资料API请求重定向到新的Laravel RESTful API
 */

// 设置HTTP响应头
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
header('Access-Control-Max-Age: 86400'); // 24小时

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 获取Authorization头
$headers = getallheaders();
$auth_header = isset($headers['Authorization']) ? $headers['Authorization'] : '';

// 获取当前主机名
$protocol = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on') ? "https://" : "http://";
$host = $_SERVER['HTTP_HOST'];

// 构建API URL
$apiUrl = $protocol . $host . '/api/vip/profile';

// 初始化cURL会话
$ch = curl_init();

// 设置URL
curl_setopt($ch, CURLOPT_URL, $apiUrl);

// 设置返回响应而非直接输出
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

// 设置HTTP方法
curl_setopt($ch, CURLOPT_HTTPGET, true);

// 传递Authorization头
if (!empty($auth_header)) {
    curl_setopt($ch, CURLOPT_HTTPHEADER, ["Authorization: $auth_header"]);
}

// 执行cURL请求
$response = curl_exec($ch);

// 检查是否有错误
if (curl_errno($ch)) {
    http_response_code(500);
    echo json_encode([
        'code' => 1,
        'message' => '请求转发失败: ' . curl_error($ch),
        'data' => null
    ]);
    curl_close($ch);
    exit;
}

// 获取HTTP状态码
$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);

// 关闭cURL会话
curl_close($ch);

// 设置相同的状态码
http_response_code($http_code);

// 输出响应
echo $response; 