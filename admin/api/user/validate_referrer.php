<?php
/**
 * 验证推荐人ID是否有效
 * 
 * 请求方式: POST
 * 请求参数:
 *   referrer_id: 推荐人ID
 * 
 * 返回数据:
 *   code: 0=成功, 非0=失败
 *   message: 提示信息
 *   data: {
 *     referrer_name: 推荐人姓名,
 *     referrer_phone: 推荐人手机号
 *   }
 */

// 开启输出缓冲，防止响应头截断
ob_start();

// 设置响应头
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 抑制错误显示，仅记录到日志
ini_set('display_errors', 0);
error_reporting(E_ALL);

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    ob_end_flush();
    exit;
}

// 引入配置文件
require_once __DIR__ . '/../config.php';

// 引入日志模块
require_once __DIR__ . '/../functions/logger.php';

// 获取请求参数（兼容各种提交格式）
$referrerId = 0;
if (isset($_POST['referrer_id'])) {
    $referrerId = (int)$_POST['referrer_id'];
} else {
    // 尝试从JSON请求体中获取
    $requestBody = file_get_contents('php://input');
    if (!empty($requestBody)) {
        $jsonData = json_decode($requestBody, true);
        if (isset($jsonData['referrer_id'])) {
            $referrerId = (int)$jsonData['referrer_id'];
        }
    }
}

// 调试输出
if (DEBUG_MODE) {
    error_log("验证推荐人ID API请求: " . $referrerId);
    error_log("POST数据: " . print_r($_POST, true));
    error_log("请求体: " . $requestBody);
}

// 检查推荐人ID是否为空
if (empty($referrerId)) {
    echo json_encode(['code' => 1, 'message' => '推荐人ID不能为空']);
    exit;
}

// 连接数据库
try {
    $conn = new mysqli($DB_CONFIG['HOST'], $DB_CONFIG['USER'], $DB_CONFIG['PASSWORD'], $DB_CONFIG['DATABASE'], $DB_CONFIG['PORT']);
    if ($conn->connect_error) {
        error_log("数据库连接失败: " . $conn->connect_error);
        echo json_encode(['code' => 500, 'message' => '数据库连接失败']);
        exit;
    }
    $conn->set_charset($DB_CONFIG['CHARSET']);
} catch (Exception $e) {
    error_log("数据库连接异常: " . $e->getMessage());
    echo json_encode(['code' => 500, 'message' => '数据库服务异常']);
    exit;
}

// 查询当前用户 - 从auth_tokens表获取用户ID
$currentUserId = null;
$headers = getallheaders();
$token = isset($headers['Authorization']) ? str_replace('Bearer ', '', $headers['Authorization']) : '';

if (DEBUG_MODE) {
    error_log("Token: " . $token);
}

if (!empty($token)) {
    // 使用正确的expires_at字段名并检查是否过期
    $stmt = $conn->prepare("SELECT user_id FROM auth_tokens WHERE token = ? AND expires_at > NOW() LIMIT 1");
    if ($stmt) {
        $stmt->bind_param("s", $token);
        $stmt->execute();
        $result = $stmt->get_result();
        if ($result->num_rows > 0) {
            $auth = $result->fetch_assoc();
            $currentUserId = $auth['user_id'];
            if (DEBUG_MODE) {
                error_log("当前用户ID: " . $currentUserId);
            }
        } else {
            error_log("找不到有效的用户令牌，但继续验证推荐人ID，允许未登录使用");
        }
        $stmt->close();
    } else {
        error_log("预处理语句失败: " . $conn->error);
    }
} else {
    error_log("请求中没有提供令牌，但继续验证推荐人ID");
}

// 防止自己推荐自己
if ($currentUserId !== null && $referrerId == $currentUserId) {
    error_log("用户ID {$currentUserId} 尝试将自己设为推荐人，拒绝操作");
    echo json_encode(['code' => 2, 'message' => '不能填写自己的ID作为推荐人']);
    ob_end_flush();
    exit;
}

// 查询推荐人信息
try {
    error_log("查询推荐人ID: {$referrerId} 的信息");
    $stmt = $conn->prepare("SELECT id, name, phone FROM app_users WHERE id = ? LIMIT 1");
    if (!$stmt) {
        error_log("SQL预处理失败: " . $conn->error);
        echo json_encode(['code' => 500, 'message' => '服务器内部错误']);
        exit;
    }
    
    $stmt->bind_param("i", $referrerId);
    
    if (!$stmt->execute()) {
        error_log("SQL执行失败: " . $stmt->error);
        echo json_encode(['code' => 500, 'message' => '服务器内部错误']);
        exit;
    }
    
    $result = $stmt->get_result();
    
    if ($result->num_rows === 0) {
        error_log("推荐人ID: {$referrerId} 不存在");
        echo json_encode(['code' => 3, 'message' => '推荐人不存在']);
        $stmt->close();
        $conn->close();
        exit;
    }
    
    $referrer = $result->fetch_assoc();
    error_log("找到推荐人: ID={$referrer['id']}, 名称={$referrer['name']}");
    $stmt->close();
} catch (Exception $e) {
    error_log("查询推荐人异常: " . $e->getMessage());
    echo json_encode(['code' => 500, 'message' => '服务器内部错误']);
    exit;
} finally {
    if (isset($conn)) {
        $conn->close();
    }
}

// 处理手机号掩码
$maskedPhone = '';
if (!empty($referrer['phone'])) {
    $maskedPhone = substr($referrer['phone'], 0, 3) . '****' . substr($referrer['phone'], -4);
}

// 构建响应数据
$responseData = [
    'code' => 0,
    'message' => '验证成功',
    'data' => [
        'referrer_name' => $referrer['name'] ?? '用户' . $referrer['id'],
        'referrer_phone' => $maskedPhone
    ]
];

// 调试输出
if (DEBUG_MODE) {
    error_log("验证成功, 返回数据: " . json_encode($responseData));
}

// 返回成功
echo json_encode($responseData);

// 结束输出缓冲并发送
ob_end_flush();
?> 