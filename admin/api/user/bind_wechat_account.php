<?php
/**
 * 绑定微信提现账号API
 * 
 * 将微信账号信息设置为当前用户的提现微信账号
 */

// 设置响应头
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 检查请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode([
        'code' => 405,
        'message' => '请求方法不允许',
        'data' => null
    ]);
    exit;
}

// 引入必要的功能文件
require_once dirname(__DIR__) . '/functions/auth.php';
require_once dirname(__DIR__) . '/functions/db.php';

// 验证用户身份
$user_id = verify_token();
if (!$user_id) {
    echo json_encode([
        'code' => 401,
        'message' => '未授权，请先登录',
        'data' => null
    ]);
    exit;
}

// 获取请求数据
$data = json_decode(file_get_contents('php://input'), true);

// 连接数据库
$conn = get_db_connection();

// 查询用户当前的微信信息
$stmt = $conn->prepare("SELECT openid, wechat_nickname, wechat_avatar FROM app_users WHERE id = ?");
$stmt->bind_param("i", $user_id);
$stmt->execute();
$result = $stmt->get_result();
$user = $result->fetch_assoc();
$stmt->close();

if (!$user || empty($user['openid'])) {
    $conn->close();
    echo json_encode([
        'code' => 400,
        'message' => '用户未绑定微信，无法设置为提现账号',
        'data' => null
    ]);
    exit;
}

// 设置微信账号为提现账号
// 如果传入了特定的微信账号，则使用传入的，否则使用当前登录的微信账号
$wechat_user_id = isset($data['wechat_user_id']) ? $data['wechat_user_id'] : $user['openid'];
$wechat_nickname = isset($data['wechat_nickname']) ? $data['wechat_nickname'] : $user['wechat_nickname'];
$wechat_avatar = isset($data['wechat_avatar']) ? $data['wechat_avatar'] : $user['wechat_avatar'];

// 更新提现微信账号信息
$stmt = $conn->prepare("UPDATE app_users SET 
                        withdrawal_wechat_id = ?, 
                        withdrawal_wechat_nickname = ?, 
                        withdrawal_wechat_avatar = ?,
                        withdrawal_wechat_update_time = NOW() 
                        WHERE id = ?");
$stmt->bind_param("sssi", $wechat_user_id, $wechat_nickname, $wechat_avatar, $user_id);

// 执行更新
if ($stmt->execute()) {
    $stmt->close();
    $conn->close();
    echo json_encode([
        'code' => 0,
        'message' => '微信提现账号设置成功',
        'data' => [
            'wechat_user_id' => $wechat_user_id,
            'wechat_nickname' => $wechat_nickname,
            'wechat_avatar' => $wechat_avatar
        ]
    ]);
} else {
    $error = $stmt->error;
    $stmt->close();
    $conn->close();
    echo json_encode([
        'code' => 500,
        'message' => '微信提现账号设置失败: ' . $error,
        'data' => null
    ]);
} 