<?php
/**
 * 设置默认银行卡API
 * 
 * 将指定银行卡设置为当前用户的默认银行卡
 */

// 设置响应头
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 检查请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode([
        'code' => 405,
        'message' => '请求方法不允许',
        'data' => null
    ]);
    exit;
}

// 引入必要的功能文件
require_once dirname(__DIR__) . '/functions/auth.php';
require_once dirname(__DIR__) . '/functions/db.php';

// 验证用户身份
$user_id = verify_token();
if (!$user_id) {
    echo json_encode([
        'code' => 401,
        'message' => '未授权，请先登录',
        'data' => null
    ]);
    exit;
}

// 获取请求数据
$data = json_decode(file_get_contents('php://input'), true);

// 验证必需参数
if (!isset($data['id']) || empty($data['id'])) {
    echo json_encode([
        'code' => 400,
        'message' => '缺少必需参数：银行卡ID',
        'data' => null
    ]);
    exit;
}

$card_id = intval($data['id']);

// 连接数据库
$conn = get_db_connection();

// 检查卡是否存在且属于该用户
$stmt = $conn->prepare("SELECT id FROM user_bank_cards WHERE id = ? AND user_id = ?");
$stmt->bind_param("ii", $card_id, $user_id);
$stmt->execute();
$result = $stmt->get_result();
$card = $result->fetch_assoc();
$stmt->close();

if (!$card) {
    $conn->close();
    echo json_encode([
        'code' => 404,
        'message' => '银行卡不存在或不属于当前用户',
        'data' => null
    ]);
    exit;
}

// 开启事务
$conn->begin_transaction();

try {
    // 先将所有卡设为非默认
    $stmt = $conn->prepare("UPDATE user_bank_cards SET is_default = 0 WHERE user_id = ?");
    $stmt->bind_param("i", $user_id);
    $stmt->execute();
    $stmt->close();
    
    // 将目标卡设为默认
    $stmt = $conn->prepare("UPDATE user_bank_cards SET is_default = 1, update_time = NOW() WHERE id = ?");
    $stmt->bind_param("i", $card_id);
    $stmt->execute();
    $stmt->close();
    
    // 提交事务
    $conn->commit();
    
    echo json_encode([
        'code' => 0,
        'message' => '默认银行卡设置成功',
        'data' => null
    ]);
} catch (Exception $e) {
    // 回滚事务
    $conn->rollback();
    
    echo json_encode([
        'code' => 500,
        'message' => '默认银行卡设置失败: ' . $e->getMessage(),
        'data' => null
    ]);
}

$conn->close(); 