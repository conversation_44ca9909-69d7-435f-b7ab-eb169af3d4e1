<?php
/**
 * 获取用户设备列表API重定向
 *
 * 此文件将原始的PHP获取用户设备列表API请求重定向到新的Laravel RESTful API
 */

// 设置允许跨域访问
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS, PUT, DELETE, PATCH');
header('Access-Control-Allow-Headers: DNT,X-Mx-ReqToken,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization,Accept');
header('Access-Control-Allow-Credentials: true');
header('Access-Control-Max-Age: 86400');

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    header('Content-Type: text/plain charset=UTF-8');
    header('Content-Length: 0');
    exit(0);
}

// 设置响应类型为JSON
header('Content-Type: application/json');

// 设置默认时区
date_default_timezone_set('Asia/Shanghai');

// 生成请求ID用于日志跟踪
$request_id = uniqid('req_');
error_log("[$request_id] 开始获取用户设备列表API重定向");

// 获取请求参数
$page = isset($_GET['page']) ? intval($_GET['page']) : 1;
$pageSize = isset($_GET['pageSize']) ? intval($_GET['pageSize']) : 10;

// 获取Authorization头
$headers = getallheaders();
$auth_header = isset($headers['Authorization']) ? $headers['Authorization'] : '';

// 获取当前主机名
$protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https://' : 'http://';
$host = $_SERVER['HTTP_HOST'];
$apiUrl = $protocol . $host . '/api/device/list?page=' . urlencode($page) . '&pageSize=' . urlencode($pageSize);

error_log("[$request_id] 重定向到: $apiUrl");

// 创建cURL请求
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $apiUrl);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
    'Accept: application/json',
    'Authorization: ' . $auth_header
]);

// 执行请求
$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

// 记录请求信息
$logMessage = sprintf(
    '获取用户设备列表API重定向: API=%s, 状态码=%d',
    $apiUrl,
    $httpCode
);
error_log("[$request_id] $logMessage");

// 如果请求失败，记录更详细的错误信息
if ($response === false) {
    $errorMessage = sprintf(
        '获取用户设备列表API请求失败: 错误=%s, URL=%s',
        curl_error($ch),
        $apiUrl
    );
    error_log("[$request_id] $errorMessage");

    echo json_encode([
        'code' => 1,
        'message' => '服务器内部错误',
        'data' => null
    ]);
} else {
    // 记录响应内容（仅用于调试）
    error_log("[$request_id] 获取用户设备列表API响应: " . substr($response, 0, 200) . "...");

    // 返回原始响应
    echo $response;
}
