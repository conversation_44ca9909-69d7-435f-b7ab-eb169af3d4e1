<?php
// 设置HTTP响应头
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 确保错误处理
ini_set('display_errors', 0);
ini_set('log_errors', 1);
ini_set('error_log', dirname(__DIR__) . '/debug_error.log');
error_reporting(E_ALL);

// 设置自定义错误处理函数
set_error_handler(function($errno, $errstr, $errfile, $errline) {
    $message = date('[Y-m-d H:i:s]') . " [$errno] $errstr in $errfile:$errline";
    error_log($message);
    return true;
});

// 设置自定义异常处理函数
set_exception_handler(function($exception) {
    $message = date('[Y-m-d H:i:s]') . " Uncaught Exception: " . $exception->getMessage() . 
               " in " . $exception->getFile() . ":" . $exception->getLine() . 
               "\nStack trace: " . $exception->getTraceAsString();
    error_log($message);
    
    // 返回JSON错误响应
    header('Content-Type: application/json');
    echo json_encode([
        'code' => 9999,
        'message' => '服务器内部错误',
        'data' => null
    ]);
    exit;
});

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 检查请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    echo json_encode([
        'code' => 1,
        'message' => '请求方法不允许',
        'data' => null
    ]);
    exit;
}

// 生成唯一请求ID
$request_id = uniqid();
error_log("[$request_id] 用户信息请求开始");

try {
    // 引入相关函数
    require_once dirname(__DIR__) . '/functions/auth.php';

    // 确保引入配置
    if (!defined('DEBUG_MODE')) {
        require_once dirname(__DIR__) . '/config.php';
    }

    // 获取Authorization头
    $headers = getallheaders();
    $auth_header = isset($headers['Authorization']) ? $headers['Authorization'] : '';
    $token = '';

    error_log("[$request_id] 请求头: " . json_encode($headers));

    if (preg_match('/Bearer\s(\S+)/', $auth_header, $matches)) {
        $token = $matches[1];
        error_log("[$request_id] 获取到令牌: " . substr($token, 0, 10) . "...");
    } else {
        error_log("[$request_id] 未找到令牌，Authorization头: " . $auth_header);
    }

    if (empty($token)) {
        error_log("[$request_id] 令牌为空");
        echo json_encode([
            'code' => 1001,
            'message' => '未授权的请求',
            'data' => null
        ]);
        exit;
    }

    // 验证用户身份
    error_log("[$request_id] 开始验证用户令牌");
    $user = verify_auth($token);

    if (!$user) {
        error_log("[$request_id] 验证失败: 无效的令牌或已过期");
        
        // 获取详细错误日志
        $token_parts = explode('.', $token);
        $debug_info = [
            'token_length' => strlen($token),
            'token_parts_count' => count($token_parts),
            'request_time' => date('Y-m-d H:i:s')
        ];
        
        if (count($token_parts) == 3) {
            $base64Url = $token_parts[1]; // payload部分
            $base64 = strtr($base64Url, '-_', '+/');
            $base64Padded = str_pad($base64, ceil(strlen($base64) / 4) * 4, '=', STR_PAD_RIGHT);
            $payload_json = base64_decode($base64Padded);
            
            if ($payload_json) {
                $payload = json_decode($payload_json, true);
                if ($payload) {
                    $debug_info['payload'] = $payload;
                    if (isset($payload['exp'])) {
                        $debug_info['exp_time'] = date('Y-m-d H:i:s', $payload['exp']);
                        $debug_info['now'] = date('Y-m-d H:i:s');
                        $debug_info['is_expired'] = $payload['exp'] < time() ? 'true' : 'false';
                    }
                } else {
                    $debug_info['payload_decode_error'] = 'JSON格式错误';
                }
            } else {
                $debug_info['base64_decode_error'] = '无法解码Base64';
            }
        }
        
        error_log("[$request_id] 令牌调试信息: " . json_encode($debug_info));
        
        echo json_encode([
            'code' => 1002,
            'message' => '无效的令牌或已过期',
            'data' => null,
            'debug' => DEBUG_MODE ? $debug_info : null
        ]);
        exit;
    }

    // 确保所有角色值存在且类型正确
    $role_fields = [
        'is_pay_institution', 
        'is_water_purifier_user', 
        'is_engineer', 
        'is_water_purifier_agent', 
        'is_pay_merchant',
        'is_vip',
        'is_salesman',
        'is_admin'
    ];
    
    foreach ($role_fields as $field) {
        // 如果字段不存在或为null，设置默认值
        if (!isset($user[$field])) {
            // 业务员角色默认为1，其他角色默认为0
            $user[$field] = ($field === 'is_salesman') ? 1 : 0;
            error_log("[$request_id] 字段 $field 不存在，设置默认值: " . $user[$field]);
        } else {
            // 确保值为整数
            $user[$field] = intval($user[$field]);
            error_log("[$request_id] 字段 $field 值为: " . $user[$field]);
        }
    }
    
    // 构建中文友好的角色名称数组
    $roles = [];
    
    if ($user['is_admin'] == 1) $roles[] = '管理员';
    if ($user['is_pay_institution'] == 1) $roles[] = '支付机构';
    if ($user['is_water_purifier_agent'] == 1) $roles[] = '净水器渠道商';
    if ($user['is_engineer'] == 1) $roles[] = '工程师';
    if ($user['is_salesman'] == 1) $roles[] = '业务员';
    if ($user['is_pay_merchant'] == 1) $roles[] = '支付商户';
    if ($user['is_vip'] == 1) $roles[] = 'VIP会员';
    if ($user['is_water_purifier_user'] == 1) $roles[] = '净水器用户';
    
    // 如果没有角色，则添加默认角色
    if (empty($roles)) {
        $roles[] = '普通用户';
    }
    
    // 将角色名称数组添加到用户信息中
    $user['roles'] = $roles;
    error_log("[$request_id] 最终角色数组: " . json_encode($roles));

    // 记录成功日志
    error_log("[$request_id] 验证成功，返回用户信息: ID=" . ($user['id'] ?? '未知'));

    // 返回用户信息
    echo json_encode([
        'code' => 0,
        'message' => '获取用户信息成功',
        'data' => $user
    ]);
} catch (Exception $e) {
    $error_message = "[$request_id] 处理请求时发生异常: " . $e->getMessage() . "\n" . $e->getTraceAsString();
    error_log($error_message);
    
    echo json_encode([
        'code' => 9999,
        'message' => '请求失败: ' . $e->getMessage(),
        'data' => null
    ]);
}
?>
