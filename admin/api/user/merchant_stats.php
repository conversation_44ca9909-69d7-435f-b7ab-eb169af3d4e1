<?php
/**
 * 商户统计数据API重定向
 *
 * 此文件将原始的PHP商户统计数据API请求重定向到新的Laravel RESTful API
 */

// 设置HTTP响应头
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 检查请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    echo json_encode([
        'code' => 1,
        'message' => '请求方法不允许',
        'data' => null
    ]);
    exit;
}

// 记录请求日志
$request_id = uniqid();
error_log("[$request_id] 商户统计数据API重定向开始");

// 获取参数
$merchantId = isset($_GET['merchant_id']) ? $_GET['merchant_id'] : null;
$dateType = isset($_GET['date_type']) ? $_GET['date_type'] : 'week';
$startDate = isset($_GET['start_date']) ? $_GET['start_date'] : null;
$endDate = isset($_GET['end_date']) ? $_GET['end_date'] : null;

// 构建查询参数
$queryParams = [];
if ($merchantId) $queryParams['merchant_id'] = $merchantId;
if ($dateType) $queryParams['period'] = $dateType;
if ($startDate) $queryParams['startDate'] = $startDate;
if ($endDate) $queryParams['endDate'] = $endDate;

// 获取Authorization头
$headers = getallheaders();
$auth_header = isset($headers['Authorization']) ? $headers['Authorization'] : '';

// 获取当前主机名
$protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https://' : 'http://';
$host = $_SERVER['HTTP_HOST'];
$apiUrl = $protocol . $host . '/api/merchant/stats?' . http_build_query($queryParams);

error_log("[$request_id] 重定向到: $apiUrl");

// 创建cURL请求
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $apiUrl);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
    'Accept: application/json',
    'Authorization: ' . $auth_header
]);

// 执行请求
$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

// 记录请求信息
$logMessage = sprintf(
    '商户统计数据API重定向: API=%s, 状态码=%d',
    $apiUrl,
    $httpCode
);
error_log("[$request_id] $logMessage");

// 如果请求失败，记录更详细的错误信息
if ($response === false) {
    $errorMessage = sprintf(
        '商户统计数据API请求失败: 错误=%s, URL=%s',
        curl_error($ch),
        $apiUrl
    );
    error_log("[$request_id] $errorMessage");

    echo json_encode([
        'code' => 1,
        'message' => '服务器内部错误',
        'data' => null
    ]);
} else {
    // 记录响应内容（仅用于调试）
    error_log("[$request_id] 商户统计数据API响应: " . substr($response, 0, 200) . "...");

    // 返回原始响应
    echo $response;
}
