<?php
/**
 * VIP 分红记录接口
 * 提供用户的VIP分红记录数据，包括分红类型、金额、时间等
 */

// 引入公共函数和配置
// // 使用统一的数据库连接文件
require_once __DIR__ . '/../../db.php';

require_once __DIR__ . '/../functions/functions.php'; /* 已注释 */
require_once __DIR__ . '/../config.php';

use Firebase\JWT\JWT;
use Firebase\JWT\Key;

// 引入Laravel组件
require_once ADMIN_PATH . '/vendor/autoload.php';
use Illuminate\Database\Capsule\Manager as DB;

// 初始化数据库连接
$db = new DB;
$db->addConnection([
    'driver'    => 'mysql',
    'host'      => $DB_CONFIG['HOST'],
    'port'      => $DB_CONFIG['PORT'],
    'database'  => $DB_CONFIG['DATABASE'],
    'username'  => $DB_CONFIG['USER'],
    'password'  => $DB_CONFIG['PASSWORD'],
    'charset'   => $DB_CONFIG['CHARSET'],
    'collation' => 'utf8mb4_unicode_ci',
    'prefix'    => '',
]);
$db->setAsGlobal();
$db->bootEloquent();

// 记录请求标识，方便排查问题
$request_id = uniqid();
error_log("[$request_id] 访问 VIP 分红记录 API");

// 设置响应头
header('Content-Type: application/json;charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 创建VIP日志函数
function vip_log($message) {
    global $request_id;
    error_log("[$request_id] VIP分红记录: $message");
}

// 处理OPTIONS预检请求
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    exit;
}

// 验证用户身份
$user = null;
try {
    // 从请求头中获取JWT令牌
    $authHeader = $_SERVER['HTTP_AUTHORIZATION'] ?? '';
    $token = str_replace('Bearer ', '', $authHeader);
    
    if (!empty($token)) {
        try {
            // 解析JWT令牌
            $decoded = JWT::decode($token, new Key(JWT_KEY, 'HS256'));
            $userId = $decoded->sub;
            
            // 根据解析出的用户ID获取用户信息
            try {
                $userQuery = "SELECT * FROM app_users WHERE id = ?";
                $userResult = DB::select($userQuery, [$userId]);
            
                if (!empty($userResult)) {
                    $user = $userResult[0];
                    vip_log("已验证用户: ID=$userId, 姓名={$user->name}");
                } else {
                    vip_log("找不到用户ID: $userId");
                }
            } catch (\Exception $queryEx) {
                vip_log("用户查询错误: " . $queryEx->getMessage());
                throw $queryEx;
            }
        } catch (\Exception $jwtEx) {
            vip_log("JWT解析错误: " . $jwtEx->getMessage());
        }
    }
    
    if (!$user) {
        http_response_code(401);
        echo json_encode([
            'code' => 1,
            'message' => '无效的访问令牌',
            'data' => null
        ]);
        exit;
    }
} catch (\Exception $e) {
    http_response_code(500);
    echo json_encode([
        'code' => 1,
        'message' => '验证令牌时出错: ' . $e->getMessage(),
        'data' => null
    ]);
    vip_log("验证令牌错误: " . $e->getMessage());
    exit;
}

try {
    // 获取分页参数
    $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
    $pageSize = isset($_GET['page_size']) ? (int)$_GET['page_size'] : 10;
    
    // 验证分页参数
    if ($page < 1) $page = 1;
    if ($pageSize < 1 || $pageSize > 100) $pageSize = 10;
    
    // 计算偏移量
    $offset = ($page - 1) * $pageSize;
    
    // 检查表是否存在
    try {
        $tableExistsQuery = "SHOW TABLES LIKE 'vip_dividends'";
        $tableExists = DB::select($tableExistsQuery);
        if (empty($tableExists)) {
            vip_log("表vip_dividends不存在");
            // 如果表不存在，返回空数据
            echo json_encode([
                'code' => 0,
                'message' => '获取VIP分红记录成功(表不存在)',
                'data' => [
                    'total' => 0,
                    'page' => $page,
                    'page_size' => $pageSize,
                    'records' => []
                ]
            ]);
            exit;
        }
    } catch (\Exception $tableEx) {
        vip_log("检查表错误: " . $tableEx->getMessage());
    }
    
    // 获取总记录数
    $totalQuery = "SELECT COUNT(*) as count FROM vip_dividends WHERE user_id = ?";
    $totalResult = DB::select($totalQuery, [$user->id]);
    $total = $totalResult[0]->count;
    
    // 获取分红记录
    $recordsQuery = "SELECT id, user_id, amount, type, status, description, created_at, updated_at 
                    FROM vip_dividends 
                    WHERE user_id = ? 
                    ORDER BY created_at DESC 
                    LIMIT ? OFFSET ?";
    $records = DB::select($recordsQuery, [$user->id, $pageSize, $offset]);
    
    // 格式化记录
    $formattedRecords = [];
    foreach ($records as $record) {
        // 格式化类型
        $typeText = '未知';
        switch ($record->type) {
            case 'vip_recruit':
                $typeText = 'VIP招募分红';
                break;
            case 'recharge':
                $typeText = '充值分红';
                break;
            case 'other':
                $typeText = '其他分红';
                break;
        }
        
        // 格式化状态
        $statusText = '未知';
        switch ($record->status) {
            case 'pending':
                $statusText = '待结算';
                break;
            case 'settled':
                $statusText = '已结算';
                break;
            case 'cancelled':
                $statusText = '已取消';
                break;
        }
        
        $formattedRecords[] = [
            'id' => $record->id,
            'amount' => number_format((float)$record->amount, 2, '.', ''),
            'type' => $record->type,
            'type_text' => $typeText,
            'status' => $record->status,
            'status_text' => $statusText,
            'description' => $record->description,
            'created_at' => $record->created_at,
            'updated_at' => $record->updated_at
        ];
    }
    
    // 返回成功响应
    echo json_encode([
        'code' => 0,
        'message' => '获取VIP分红记录成功',
        'data' => [
            'total' => $total,
            'page' => $page,
            'page_size' => $pageSize,
            'records' => $formattedRecords
        ]
    ]);
    
} catch (\Exception $e) {
    http_response_code(500);
    echo json_encode([
        'code' => 1,
        'message' => '获取VIP分红记录失败: ' . $e->getMessage(),
        'data' => null
    ]);
    vip_log("获取分红记录失败: " . $e->getMessage() . "\n" . $e->getTraceAsString());
    exit;
}
