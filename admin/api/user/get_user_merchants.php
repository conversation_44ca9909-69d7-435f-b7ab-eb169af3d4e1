        $items[] = [
            'id' => $merchant->id,
            'merchant_id' => $merchant->merchant_id,
            'name' => $merchant->name,
            'principal_name' => $merchant->principal_name,
            'principal_mobile' => $merchant->principal_mobile,
            'logo' => $merchant->logo,
            'status' => $merchant->status,
            'status_text' => $merchant->status_text,
            'trade_stats' => [
                'total_count' => $merchant->trades()->where('status', 'success')->count(),
                'total_amount' => floatval($merchant->trades()->where('status', 'success')->sum('amount')),
                'today_amount' => floatval($merchant->trades()->where('status', 'success')->whereDate('pay_time', date('Y-m-d'))->sum('amount')),
            ],
            'is_bound' => $merchant->app_user_id == $appUser->id,
            'created_at' => $merchant->created_at ? $merchant->created_at->format('Y-m-d H:i:s') : null,
        ]; 