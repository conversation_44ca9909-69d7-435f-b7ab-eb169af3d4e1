<?php
// 设置HTTP响应头
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 检查请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode([
        'code' => 1,
        'message' => '请求方法不允许',
        'data' => null
    ]);
    exit;
}

// 引入微信登录相关函数
require_once dirname(__DIR__) . '/functions/wechat.php';

// 获取请求参数
$data = json_decode(file_get_contents('php://input'), true);

if (!isset($data['phone']) || !isset($data['type'])) {
    echo json_encode([
        'code' => 1,
        'message' => '缺少必需参数',
        'data' => null
    ]);
    exit;
}

$phone = $data['phone'];
$type = $data['type'];

// 验证手机号格式
if (!preg_match('/^1[3-9]\d{9}$/', $phone)) {
    echo json_encode([
        'code' => 1,
        'message' => '手机号格式不正确',
        'data' => null
    ]);
    exit;
}

// 验证验证码类型
$allowed_types = ['login', 'register', 'reset', 'bind'];
if (!in_array($type, $allowed_types)) {
    echo json_encode([
        'code' => 1,
        'message' => '验证码类型不正确',
        'data' => null
    ]);
    exit;
}

// 发送短信验证码
$result = send_sms_code($phone, $type);

// 返回JSON响应
echo json_encode($result);
?> 