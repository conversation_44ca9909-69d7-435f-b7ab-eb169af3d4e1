<?php
/**
 * 绑定支付宝账号API
 * 
 * 将支付宝账号信息绑定到当前用户，用于提现/结算
 */

// 设置响应头
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 检查请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode([
        'code' => 405,
        'message' => '请求方法不允许',
        'data' => null
    ]);
    exit;
}

// 引入必要的功能文件
require_once dirname(__DIR__) . '/functions/auth.php';
require_once dirname(__DIR__) . '/functions/db.php';

// 验证用户身份
$user_id = verify_token();
if (!$user_id) {
    echo json_encode([
        'code' => 401,
        'message' => '未授权，请先登录',
        'data' => null
    ]);
    exit;
}

// 获取请求数据
$data = json_decode(file_get_contents('php://input'), true);

// 验证必需参数
if (!isset($data['alipay_user_id']) || empty($data['alipay_user_id'])) {
    echo json_encode([
        'code' => 400,
        'message' => '缺少必需参数：支付宝用户ID',
        'data' => null
    ]);
    exit;
}

// 准备支付宝账号信息
$alipay_user_id = $data['alipay_user_id'];
$alipay_openid = isset($data['alipay_openid']) ? $data['alipay_openid'] : $alipay_user_id; // 如果没有提供openid，使用user_id作为备用
$alipay_nickname = isset($data['alipay_nickname']) ? $data['alipay_nickname'] : '';
$alipay_avatar = isset($data['alipay_avatar']) ? $data['alipay_avatar'] : '';

// 连接数据库
$conn = get_db_connection();

// 检查用户是否已经绑定过支付宝
$stmt = $conn->prepare("SELECT alipay_user_id, alipay_openid FROM app_users WHERE id = ?");
$stmt->bind_param("i", $user_id);
$stmt->execute();
$result = $stmt->get_result();
$user = $result->fetch_assoc();
$stmt->close();

if ($user && (!empty($user['alipay_user_id']) || !empty($user['alipay_openid']))) {
    // 如果已经绑定，则更新支付宝信息
    $stmt = $conn->prepare("UPDATE app_users SET alipay_user_id = ?, alipay_openid = ?, alipay_nickname = ?, alipay_avatar = ?, alipay_update_time = NOW() WHERE id = ?");
    $stmt->bind_param("ssssi", $alipay_user_id, $alipay_openid, $alipay_nickname, $alipay_avatar, $user_id);
} else {
    // 如果未绑定，则绑定支付宝
    $stmt = $conn->prepare("UPDATE app_users SET alipay_user_id = ?, alipay_openid = ?, alipay_nickname = ?, alipay_avatar = ?, alipay_bind_time = NOW(), alipay_update_time = NOW() WHERE id = ?");
    $stmt->bind_param("ssssi", $alipay_user_id, $alipay_openid, $alipay_nickname, $alipay_avatar, $user_id);
}

// 执行更新
if ($stmt->execute()) {
    $stmt->close();
    $conn->close();
    echo json_encode([
        'code' => 0,
        'message' => '支付宝账号绑定成功',
        'data' => [
            'alipay_user_id' => $alipay_user_id,
            'alipay_openid' => $alipay_openid,
            'alipay_nickname' => $alipay_nickname,
            'alipay_avatar' => $alipay_avatar
        ]
    ]);
} else {
    $error = $stmt->error;
    $stmt->close();
    $conn->close();
    echo json_encode([
        'code' => 500,
        'message' => '支付宝账号绑定失败: ' . $error,
        'data' => null
    ]);
} 