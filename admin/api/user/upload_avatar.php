<?php
// 设置HTTP响应头
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 确保错误处理
ini_set('display_errors', 0);
ini_set('log_errors', 1);
ini_set('error_log', dirname(__DIR__) . '/debug_error.log');
error_reporting(E_ALL);

// 设置自定义错误处理函数
set_error_handler(function($errno, $errstr, $errfile, $errline) {
    $message = date('[Y-m-d H:i:s]') . " [$errno] $errstr in $errfile:$errline";
    error_log($message);
    return true;
});

// 设置自定义异常处理函数
set_exception_handler(function($exception) {
    $message = date('[Y-m-d H:i:s]') . " Uncaught Exception: " . $exception->getMessage() . 
               " in " . $exception->getFile() . ":" . $exception->getLine() . 
               "\nStack trace: " . $exception->getTraceAsString();
    error_log($message);
    
    // 返回JSON错误响应
    header('Content-Type: application/json');
    echo json_encode([
        'code' => 9999,
        'message' => '服务器内部错误',
        'data' => null
    ]);
    exit;
});

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 检查请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode([
        'code' => 1,
        'message' => '请求方法不允许',
        'data' => null
    ]);
    exit;
}

// 生成唯一请求ID
$request_id = uniqid();
error_log("[$request_id] 上传头像请求开始");

try {
    // 引入相关函数
    require_once dirname(__DIR__) . '/functions/auth.php';

    // 确保引入配置
    if (!defined('DEBUG_MODE')) {
        require_once dirname(__DIR__) . '/config.php';
    }

    // 获取Authorization头
    $headers = getallheaders();
    $auth_header = isset($headers['Authorization']) ? $headers['Authorization'] : '';
    $token = '';

    error_log("[$request_id] 请求头: " . json_encode($headers));

    if (preg_match('/Bearer\s(\S+)/', $auth_header, $matches)) {
        $token = $matches[1];
        error_log("[$request_id] 获取到令牌: " . substr($token, 0, 10) . "...");
    } else {
        error_log("[$request_id] 未找到令牌，Authorization头: " . $auth_header);
    }

    if (empty($token)) {
        error_log("[$request_id] 令牌为空");
        echo json_encode([
            'code' => 1001,
            'message' => '未授权的请求',
            'data' => null
        ]);
        exit;
    }

    // 验证用户身份
    error_log("[$request_id] 开始验证用户令牌");
    $user = verify_auth($token);

    if (!$user) {
        error_log("[$request_id] 验证失败: 无效的令牌或已过期");
        
        echo json_encode([
            'code' => 1002,
            'message' => '无效的令牌或已过期',
            'data' => null
        ]);
        exit;
    }
    
    // 检查是否有文件上传
    if (!isset($_FILES['file']) || empty($_FILES['file'])) {
        // 可能是使用FormData以外的方式提交，尝试从raw body中获取
        $input = file_get_contents('php://input');
        if (empty($input)) {
            error_log("[$request_id] 未找到上传的文件");
            echo json_encode([
                'code' => 1003,
                'message' => '未找到上传的文件',
                'data' => null
            ]);
            exit;
        }
        
        // 创建临时文件保存接收到的二进制数据
        $tmpFile = tempnam(sys_get_temp_dir(), 'avatar_');
        file_put_contents($tmpFile, $input);
        
        // 确定文件类型
        $finfo = finfo_open(FILEINFO_MIME_TYPE);
        $mime = finfo_file($finfo, $tmpFile);
        finfo_close($finfo);
        
        if (!in_array($mime, ['image/jpeg', 'image/png', 'image/gif'])) {
            unlink($tmpFile);
            error_log("[$request_id] 非法的文件类型: $mime");
            echo json_encode([
                'code' => 1004,
                'message' => '只允许上传JPG、PNG或GIF图片',
                'data' => null
            ]);
            exit;
        }
        
        // 准备虚拟的文件信息
        $ext = '';
        switch ($mime) {
            case 'image/jpeg': $ext = 'jpg'; break;
            case 'image/png': $ext = 'png'; break;
            case 'image/gif': $ext = 'gif'; break;
        }
        
        // 模拟$_FILES结构
        $_FILES['file'] = [
            'name' => 'avatar.' . $ext,
            'type' => $mime,
            'tmp_name' => $tmpFile,
            'error' => 0,
            'size' => filesize($tmpFile)
        ];
    }
    
    // 现在我们一定有$_FILES['file']了
    if ($_FILES['file']['error'] !== UPLOAD_ERR_OK) {
        error_log("[$request_id] 文件上传错误: " . $_FILES['file']['error']);
        echo json_encode([
            'code' => 1005,
            'message' => '文件上传失败: ' . $_FILES['file']['error'],
            'data' => null
        ]);
        exit;
    }
    
    // 检查文件类型
    $allowed_types = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
    if (!in_array($_FILES['file']['type'], $allowed_types) && 
        !in_array(mime_content_type($_FILES['file']['tmp_name']), $allowed_types)) {
        error_log("[$request_id] 非法的文件类型: " . $_FILES['file']['type']);
        echo json_encode([
            'code' => 1006,
            'message' => '只允许上传JPG、PNG或GIF图片',
            'data' => null
        ]);
        exit;
    }
    
    // 检查文件大小（2MB）
    if ($_FILES['file']['size'] > 2 * 1024 * 1024) {
        error_log("[$request_id] 文件太大: " . $_FILES['file']['size']);
        echo json_encode([
            'code' => 1007,
            'message' => '头像文件不能超过2MB',
            'data' => null
        ]);
        exit;
    }
    
    // 创建上传目录
    $upload_dir = dirname(__DIR__, 2) . '/public/uploads/avatars/';
    if (!file_exists($upload_dir)) {
        mkdir($upload_dir, 0755, true);
    }
    
    // 生成唯一文件名
    $filename = 'avatar_' . $user['id'] . '_' . time() . '_' . uniqid();
    $ext = pathinfo($_FILES['file']['name'], PATHINFO_EXTENSION);
    $filename .= '.' . $ext;
    $filepath = $upload_dir . $filename;
    
    // 保存文件
    if (!move_uploaded_file($_FILES['file']['tmp_name'], $filepath)) {
        error_log("[$request_id] 保存文件失败: $filepath");
        echo json_encode([
            'code' => 1008,
            'message' => '保存头像文件失败',
            'data' => null
        ]);
        exit;
    }
    
    // 构建URL
    $base_url = 'https://pay.itapgo.com/Tapp/admin/public/uploads/avatars/';
    $avatar_url = $base_url . $filename;
    
    // 更新数据库
    $conn = new mysqli($DB_CONFIG['HOST'], $DB_CONFIG['USER'], $DB_CONFIG['PASSWORD'], $DB_CONFIG['DATABASE'], $DB_CONFIG['PORT']);
    if ($conn->connect_error) {
        error_log("[$request_id] 数据库连接失败: " . $conn->connect_error);
        throw new Exception("数据库连接失败");
    }
    $conn->set_charset($DB_CONFIG['CHARSET']);
    
    // 构建SQL查询语句
    $sql = "UPDATE app_users SET avatar = ?, updated_at = NOW() WHERE id = ?";
    
    // 准备并执行查询
    $stmt = $conn->prepare($sql);
    if (!$stmt) {
        error_log("[$request_id] 准备SQL语句失败: " . $conn->error);
        throw new Exception("准备SQL语句失败");
    }
    
    // 绑定参数
    $stmt->bind_param("si", $avatar_url, $user['id']);
    
    // 执行更新
    if (!$stmt->execute()) {
        error_log("[$request_id] 执行SQL失败: " . $stmt->error);
        throw new Exception("执行SQL失败");
    }
    
    // 关闭数据库连接
    $stmt->close();
    $conn->close();
    
    // 返回成功响应
    echo json_encode([
        'code' => 0,
        'message' => '头像上传成功',
        'data' => [
            'avatarUrl' => $avatar_url
        ]
    ]);
    
} catch (Exception $e) {
    $error_message = "[$request_id] 处理请求时发生异常: " . $e->getMessage() . "\n" . $e->getTraceAsString();
    error_log($error_message);
    
    echo json_encode([
        'code' => 9999,
        'message' => '请求失败: ' . $e->getMessage(),
        'data' => null
    ]);
}
?> 