<?php
/**
 * 解绑支付宝账号API
 * 
 * 将当前用户绑定的支付宝账号解除绑定
 */

// 设置响应头
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 检查请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode([
        'code' => 405,
        'message' => '请求方法不允许',
        'data' => null
    ]);
    exit;
}

// 引入必要的功能文件
require_once dirname(__DIR__) . '/functions/auth.php';
require_once dirname(__DIR__) . '/functions/db.php';

// 验证用户身份
$user_id = verify_token();
if (!$user_id) {
    echo json_encode([
        'code' => 401,
        'message' => '未授权，请先登录',
        'data' => null
    ]);
    exit;
}

// 连接数据库
$conn = get_db_connection();

// 检查用户是否已经绑定支付宝
$stmt = $conn->prepare("SELECT alipay_user_id FROM app_users WHERE id = ?");
$stmt->bind_param("i", $user_id);
$stmt->execute();
$result = $stmt->get_result();
$user = $result->fetch_assoc();
$stmt->close();

if (!$user || empty($user['alipay_user_id'])) {
    $conn->close();
    echo json_encode([
        'code' => 400,
        'message' => '未绑定支付宝账号',
        'data' => null
    ]);
    exit;
}

// 清除支付宝账号信息
$stmt = $conn->prepare("UPDATE app_users SET alipay_user_id = NULL, alipay_openid = NULL, alipay_nickname = NULL, alipay_avatar = NULL, alipay_unbind_time = NOW() WHERE id = ?");
$stmt->bind_param("i", $user_id);

// 执行更新
if ($stmt->execute()) {
    $stmt->close();
    $conn->close();
    echo json_encode([
        'code' => 0,
        'message' => '支付宝账号解绑成功',
        'data' => null
    ]);
} else {
    $error = $stmt->error;
    $stmt->close();
    $conn->close();
    echo json_encode([
        'code' => 500,
        'message' => '支付宝账号解绑失败: ' . $error,
        'data' => null
    ]);
} 