<?php
/**
 * 添加银行卡API
 * 
 * 为当前用户添加一张新的银行卡
 */

// 设置响应头
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 检查请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode([
        'code' => 405,
        'message' => '请求方法不允许',
        'data' => null
    ]);
    exit;
}

// 引入必要的功能文件
require_once dirname(__DIR__) . '/functions/auth.php';
require_once dirname(__DIR__) . '/functions/db.php';

// 验证用户身份
$user_id = verify_token();
if (!$user_id) {
    echo json_encode([
        'code' => 401,
        'message' => '未授权，请先登录',
        'data' => null
    ]);
    exit;
}

// 获取请求数据
$data = json_decode(file_get_contents('php://input'), true);

// 验证必需参数
if (!isset($data['holderName']) || empty($data['holderName']) ||
    !isset($data['cardNumber']) || empty($data['cardNumber']) ||
    !isset($data['bankName']) || empty($data['bankName']) ||
    !isset($data['bankCode']) || empty($data['bankCode']) ||
    !isset($data['phone']) || empty($data['phone'])) {
    echo json_encode([
        'code' => 400,
        'message' => '缺少必需参数',
        'data' => null
    ]);
    exit;
}

// 提取参数
$holder_name = $data['holderName'];
$card_number = preg_replace('/\s+/', '', $data['cardNumber']); // 移除空格
$bank_name = $data['bankName'];
$bank_code = $data['bankCode'];
$phone = $data['phone'];
$is_default = isset($data['isDefault']) ? (bool)$data['isDefault'] : false;

// 验证银行卡号格式
if (!preg_match('/^\d{16,19}$/', $card_number)) {
    echo json_encode([
        'code' => 400,
        'message' => '银行卡号格式不正确',
        'data' => null
    ]);
    exit;
}

// 验证手机号格式
if (!preg_match('/^1[3-9]\d{9}$/', $phone)) {
    echo json_encode([
        'code' => 400,
        'message' => '手机号格式不正确',
        'data' => null
    ]);
    exit;
}

// 连接数据库
$conn = get_db_connection();

// 检查银行卡数量限制
$stmt = $conn->prepare("SELECT COUNT(*) as card_count FROM user_bank_cards WHERE user_id = ?");
$stmt->bind_param("i", $user_id);
$stmt->execute();
$result = $stmt->get_result();
$row = $result->fetch_assoc();
$stmt->close();

if ($row['card_count'] >= 5) {
    $conn->close();
    echo json_encode([
        'code' => 400,
        'message' => '最多只能绑定5张银行卡',
        'data' => null
    ]);
    exit;
}

// 检查是否已绑定过该卡
$stmt = $conn->prepare("SELECT id FROM user_bank_cards WHERE user_id = ? AND card_number = ?");
$stmt->bind_param("is", $user_id, $card_number);
$stmt->execute();
$result = $stmt->get_result();
$stmt->close();

if ($result->num_rows > 0) {
    $conn->close();
    echo json_encode([
        'code' => 400,
        'message' => '该银行卡已绑定',
        'data' => null
    ]);
    exit;
}

// 如果设置为默认卡，需要先将所有卡设为非默认
if ($is_default) {
    $stmt = $conn->prepare("UPDATE user_bank_cards SET is_default = 0 WHERE user_id = ?");
    $stmt->bind_param("i", $user_id);
    $stmt->execute();
    $stmt->close();
}
// 如果是第一张卡，自动设为默认卡
else if ($row['card_count'] == 0) {
    $is_default = true;
}

// 添加银行卡
$stmt = $conn->prepare("INSERT INTO user_bank_cards 
                        (user_id, card_number, bank_name, bank_code, holder_name, phone, is_default, create_time, update_time) 
                        VALUES (?, ?, ?, ?, ?, ?, ?, NOW(), NOW())");
$is_default_int = $is_default ? 1 : 0;
$stmt->bind_param("isssssi", $user_id, $card_number, $bank_name, $bank_code, $holder_name, $phone, $is_default_int);

if ($stmt->execute()) {
    $new_card_id = $stmt->insert_id;
    $stmt->close();
    $conn->close();
    
    // 卡号脱敏
    $masked_card_number = substr($card_number, 0, 4) . ' **** **** ' . substr($card_number, -4);
    
    echo json_encode([
        'code' => 0,
        'message' => '银行卡添加成功',
        'data' => [
            'id' => $new_card_id,
            'cardNumber' => $masked_card_number,
            'bankName' => $bank_name,
            'bankCode' => $bank_code,
            'holderName' => $holder_name,
            'isDefault' => $is_default
        ]
    ]);
} else {
    $error = $stmt->error;
    $stmt->close();
    $conn->close();
    echo json_encode([
        'code' => 500,
        'message' => '银行卡添加失败: ' . $error,
        'data' => null
    ]);
} 