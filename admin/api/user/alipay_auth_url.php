<?php
/**
 * 获取支付宝授权URL API
 * 
 * 生成支付宝授权URL和二维码，用于绑定支付宝账号
 */

// 设置响应头
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 引入必要的功能文件
require_once dirname(__DIR__) . '/functions/auth.php';
require_once dirname(__DIR__) . '/functions/db.php';

// 检查是否是直接访问模式
$direct_mode = isset($_GET['direct']) && ($_GET['direct'] == '1' || $_GET['direct'] == 'true' || $_GET['direct'] == 'yes');

// 获取Authorization头
$headers = getallheaders();
$auth_header = isset($headers['Authorization']) ? $headers['Authorization'] : '';

// 尝试从header提取token
$token = '';
if (preg_match('/Bearer\s(\S+)/', $auth_header, $matches)) {
    $token = $matches[1];
}

// 如果是直接访问但没有提供token，检查URL参数、POST参数、cookie或会话中的token
if (empty($token)) {
    // 优先从POST参数获取
    if (isset($_POST['token']) && !empty($_POST['token'])) {
        $token = $_POST['token'];
    }
    // 然后从URL参数获取
    else if (isset($_GET['token']) && !empty($_GET['token'])) {
        $token = $_GET['token'];
    }
    // 然后尝试从cookie获取
    else if (isset($_COOKIE['token'])) {
        $token = $_COOKIE['token'];
    }
    // 最后从会话中获取，如果有的话
    else if (session_id() == '' && !headers_sent()) {
        session_start();
        if (isset($_SESSION['token'])) {
            $token = $_SESSION['token'];
        }
    }
}

// 记录token到日志，方便调试
error_log('Alipay Auth URL - Token: ' . substr($token, 0, 10) . '...');

// 验证用户身份 - 使用verify_auth替代verify_token
$user = verify_auth($token);
$user_id = $user ? $user['id'] : null;

// 记录用户ID到日志
error_log('Alipay Auth URL - User ID: ' . ($user_id ? $user_id : 'null'));

if (!$user_id) {
    if ($direct_mode) {
        // 直接访问模式下，重定向到登录页面
        header('Location: /app/#/login');
        exit;
    } else {
        echo json_encode([
            'code' => 401,
            'message' => '未授权，请先登录',
            'data' => null
        ]);
        exit;
    }
}

// 连接数据库
$conn = get_db_connection();

// 获取用户信息 - 包括微信头像字段
$stmt = $conn->prepare("SELECT id, name, avatar, wechat_avatar FROM app_users WHERE id = ?");
$stmt->bind_param("i", $user_id);
$stmt->execute();
$result = $stmt->get_result();
$user = $result->fetch_assoc();
$stmt->close();

if (!$user) {
    echo json_encode([
        'code' => 404,
        'message' => '用户不存在',
        'data' => null
    ]);
    exit;
}

try {
    // 支付宝应用参数
    $app_id = '2021004188641378'; // 使用已有的支付宝AppID
    
    // 定义重定向URL，确保使用完整路径
    $redirect_uri = urlencode('https://pay.itapgo.com/Tapp/admin/api/user/alipay_auth_callback.php');
    
    // 生成一个随机状态码，用于防止CSRF攻击
    $state = bin2hex(random_bytes(8));
    
    // 记录授权参数
    error_log('Alipay Auth URL - App ID: ' . $app_id);
    error_log('Alipay Auth URL - Redirect URI: ' . urldecode($redirect_uri));
    error_log('Alipay Auth URL - Generated State: ' . $state);
    
    // 构造授权URL
    $auth_url = "https://openauth.alipay.com/oauth2/publicAppAuthorize.htm?app_id={$app_id}&scope=auth_user&redirect_uri={$redirect_uri}&state={$state}";
    
    // 创建短跳转URL，用于二维码内容
    $qrcode_url = "https://pay.itapgo.com/Tapp/admin/api/user/alipay_redirect.php?state={$state}";
    
    // 生成二维码内容 - 使用短URL
    $qrcode_content = $qrcode_url;
    
    // 保存state到数据库，关联到当前用户
    $stmt = $conn->prepare("INSERT INTO alipay_auth_states (user_id, state, create_time) VALUES (?, ?, NOW())");
    $stmt->bind_param("is", $user_id, $state);
    $stmt->execute();
    $stmt->close();
    
    $conn->close();
    
    // 如果是直接访问模式，展示二维码页面而不是重定向
    if ($direct_mode) {
        // 设置响应头为HTML
        header('Content-Type: text/html; charset=utf-8');
        
        // 输出HTML页面和二维码
        echo '<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>绑定支付宝账号</title>
    <script src="https://cdn.bootcdn.net/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    <script src="https://cdn.bootcdn.net/ajax/libs/qrcodejs/1.0.0/qrcode.min.js"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif;
            background-color: #f5f5f5;
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            color: #333;
        }
        .container {
            max-width: 500px;
            width: 90%;
            background-color: #fff;
            border-radius: 16px;
            box-shadow: 0 8px 24px rgba(0,0,0,0.08);
            padding: 24px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }
        .container::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 6px;
            background: linear-gradient(90deg, #1677FF, #00BFFF);
        }
        h1 {
            color: #1677ff;
            font-size: 24px;
            margin-bottom: 20px;
        }
        p {
            margin: 12px 0;
            font-size: 16px;
            color: #333;
            line-height: 1.6;
        }
        .qrcode-container {
            margin: 20px auto;
            width: 220px;
            height: 220px;
            background-color: #f7f7f7;
            border-radius: 12px;
            padding: 16px;
            overflow: hidden;
            display: flex;
            justify-content: center;
            align-items: center;
            box-shadow: 0 4px 12px rgba(0,0,0,0.05);
            border: 1px solid rgba(0,0,0,0.05);
        }
        .instructions {
            background-color: #f7f7f7;
            padding: 16px;
            border-radius: 12px;
            text-align: left;
            margin-top: 24px;
            position: relative;
            border-left: 4px solid #1677FF;
        }
        .instructions ol {
            margin: 0;
            padding-left: 20px;
        }
        .instructions li {
            margin-bottom: 12px;
            position: relative;
            padding-left: 6px;
        }
        .button {
            display: inline-block;
            background-color: #1677ff;
            color: white;
            padding: 12px 24px;
            border-radius: 8px;
            text-decoration: none;
            margin-top: 16px;
            border: none;
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;
            transition: all 0.3s ease;
            box-shadow: 0 4px 6px rgba(22, 119, 255, 0.1);
        }
        .button:hover {
            background-color: #0056cc;
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(22, 119, 255, 0.2);
        }
        .button:active {
            transform: translateY(0);
        }
        .button-back {
            background-color: #f0f0f0;
            color: #666;
            margin-right: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        }
        .button-back:hover {
            background-color: #e0e0e0;
            box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 28px;
            padding-bottom: 16px;
            border-bottom: 1px solid #f0f0f0;
        }
        .header-title {
            font-size: 22px;
            font-weight: bold;
            color: #333;
            margin-top: 10px;
        }
        .user-info {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 16px;
            background-color: #f9f9f9;
            padding: 12px;
            border-radius: 12px;
        }
        .user-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            margin-right: 12px;
            object-fit: cover;
            border: 2px solid #fff;
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
        }
        .user-name {
            font-size: 18px;
            font-weight: bold;
            color: #333;
        }
        .countdown-container {
            margin: 20px 0;
            padding: 12px;
            background-color: #f0f8ff;
            border-radius: 12px;
            border: 1px solid rgba(22, 119, 255, 0.2);
        }
        .countdown {
            font-size: 20px;
            font-weight: bold;
            color: #1677ff;
        }
        .countdown-date {
            font-size: 14px;
            color: #666;
            margin-top: 4px;
        }
        #countdown {
            display: inline-block;
            min-width: 60px;
            color: #1677ff;
            font-weight: bold;
        }
        @keyframes pulse {
            0% { box-shadow: 0 0 0 0 rgba(22, 119, 255, 0.4); }
            70% { box-shadow: 0 0 0 10px rgba(22, 119, 255, 0); }
            100% { box-shadow: 0 0 0 0 rgba(22, 119, 255, 0); }
        }
        .btn-container {
            display: flex;
            justify-content: center;
            margin-top: 24px;
        }
        .alipay-icon {
            color: #1677ff;
            margin-right: 6px;
        }
        .highlight-text {
            color: #1677ff;
            font-weight: bold;
            padding: 2px 4px;
            background-color: rgba(22, 119, 255, 0.1);
            border-radius: 4px;
        }
        .icon {
            display: inline-block;
            font-style: normal;
            margin-right: 4px;
        }
        #countdown {
            animation: blink 1s infinite alternate;
        }
        @keyframes blink {
            from { opacity: 1; }
            to { opacity: 0.6; }
        }
        .progress-bar {
            height: 6px;
            background-color: #eee;
            border-radius: 3px;
            margin: 10px 0;
            overflow: hidden;
        }
        .progress {
            height: 100%;
            background: linear-gradient(90deg, #1677FF, #00BFFF);
            width: 100%;
            border-radius: 3px;
            transition: width 1s linear;
        }
        .alipay-icon-container {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            margin-right: 4px;
        }
        .alipay-icon {
            vertical-align: middle;
        }
        @media (max-width: 480px) {
            .container {
                width: 95%;
                padding: 16px;
            }
            .user-avatar {
                width: 40px;
                height: 40px;
            }
            .user-name {
                font-size: 16px;
            }
            .qrcode-container {
                width: 200px;
                height: 200px;
            }
            .button {
                padding: 10px 16px;
                font-size: 14px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="user-info">
                <img src="' . ($user['wechat_avatar'] ? $user['wechat_avatar'] : ($user['avatar'] ? $user['avatar'] : '/Tapp/app/images/default-avatar.png')) . '" class="user-avatar" alt="用户头像">
                <div class="user-name">' . htmlspecialchars($user['name']) . '</div>
            </div>
            <div class="header-title">绑定支付宝账号</div>
        </div>
        
        <p>请使用支付宝APP扫描下方二维码完成授权绑定</p>
        
        <div class="qrcode-container" id="qrcode"></div>
        
        <div class="countdown-container">
            <p><span class="alipay-icon-container"><svg class="alipay-icon" viewBox="0 0 1024 1024" width="18" height="18"><path d="M1023.795 853.64v6.348a163.807 163.807 0 0 1-163.807 163.807h-696.18A163.807 163.807 0 0 1 0 859.988v-696.18A163.807 163.807 0 0 1 163.807 0h696.181a163.807 163.807 0 0 1 163.807 163.807V853.64z" fill="#009FE9"></path><path d="M844.836 648.267c-40.952-14.333-95.623-34.809-156.846-57.128a949.058 949.058 0 0 0 90.094-222.573H505.173v-60.14h273.49v-35.647H505.173v-97.233h-145.12v97.233H130.866v35.647h229.187v60.14H135.757v35.647h601.857a891.887 891.887 0 0 1-64.068 155.312c-128.376-40.952-266.186-77.834-354.043-55.080a619.28 619.28 0 0 0-69.957 20.475c-96.977 116.694-166.177 256.576-166.177 423.55v18.302h718.36A160.2 160.2 0 0 1 789.1 1024H132.913C159.245 871.539 247.598 453.55 844.836 648.267z" fill="#FFFFFF"></path></svg></span> 请在 <span id="countdown">02:00</span> 内完成绑定</p>
            <div class="progress-bar">
                <div class="progress" id="progress-bar"></div>
            </div>
            <p class="countdown-date" id="current-time"></p>
        </div>
        
        <div class="instructions">
            <ol>
                <li>打开支付宝APP，点击右上角的<span class="highlight-text">扫一扫</span></li>
                <li>对准上方二维码进行扫描</li>
                <li>按照支付宝内提示完成授权</li>
                <li>授权成功后会自动绑定到您的账号</li>
            </ol>
        </div>
        
        <div class="btn-container">
            <a href="/app/#/user/settings/bank-card" class="button button-back">
                <i class="icon">↩</i> 返回
            </a>
            <button class="button" id="saveQRCode">
                <i class="icon">⬇</i> 保存二维码到相册
            </button>
        </div>
    </div>
    
    <script>
        // 创建二维码
        function generateQRCode() {
            try {
                // 清空之前的内容
                $("#qrcode").empty();
                
                // 短链接版本
                var shortUrl = "' . $qrcode_url . '";
                
                // 创建QR码实例
                var qrcode = new QRCode(document.getElementById("qrcode"), {
                    text: shortUrl,
                    width: 200,
                    height: 200,
                    colorDark: "#000000",
                    colorLight: "#ffffff",
                    correctLevel: QRCode.CorrectLevel.L // 使用较低的纠错级别减少复杂度
                });
                
                console.log("QR码已创建");
            } catch(e) {
                console.error("创建QR码失败", e);
                $("#qrcode").html("<p style=\'color:red\'>生成二维码失败</p><p>请点击下方按钮直接前往支付宝授权</p>");
                // 添加直接前往支付宝授权的按钮
                $("<a>").attr({
                    href: "' . $qrcode_url . '",
                    target: "_blank",
                    class: "button",
                    style: "margin-top: 10px; display: block;"
                }).text("前往支付宝授权").appendTo("#qrcode");
            }
        }
        
        // 初始化倒计时
        function initCountdown() {
            var minutes = 2;
            var seconds = 0;
            var totalSeconds = minutes * 60 + seconds;
            var initialTotalSeconds = totalSeconds;
            
            function updateCountdown() {
                var minutesLeft = Math.floor(totalSeconds / 60);
                var secondsLeft = totalSeconds % 60;
                
                var minutesStr = minutesLeft < 10 ? "0" + minutesLeft : minutesLeft;
                var secondsStr = secondsLeft < 10 ? "0" + secondsLeft : secondsLeft;
                
                $("#countdown").text(minutesStr + ":" + secondsStr);
                
                // 更新进度条
                var progressPercent = (totalSeconds / initialTotalSeconds) * 100;
                $("#progress-bar").css("width", progressPercent + "%");
                
                // 根据剩余时间改变进度条颜色
                if (totalSeconds < 30) {
                    $("#progress-bar").css("background", "linear-gradient(90deg, #ff4d4f, #ff7875)");
                    $("#countdown").css("color", "#ff4d4f");
                }
                
                if (totalSeconds <= 0) {
                    clearInterval(countdownInterval);
                    // 倒计时结束，显示提示信息
                    $("#qrcode").html("<p style=\'color:red; font-size:16px; margin:20px 0;\'>链接已过期</p><p>请返回重新获取二维码</p>");
                    $(".countdown-container").html("<p style=\'color:red; font-weight:bold;\'>链接已过期，请重新获取</p>");
                    $(".progress-bar").hide();
                } else {
                    totalSeconds--;
                }
            }
            
            // 每秒更新一次倒计时
            updateCountdown();
            var countdownInterval = setInterval(updateCountdown, 1000);
            
            // 显示当前时间，精确到秒
            function updateCurrentTime() {
                var now = new Date();
                var year = now.getFullYear();
                var month = (now.getMonth() + 1).toString().padStart(2, "0");
                var day = now.getDate().toString().padStart(2, "0");
                var hours = now.getHours().toString().padStart(2, "0");
                var minutes = now.getMinutes().toString().padStart(2, "0");
                var seconds = now.getSeconds().toString().padStart(2, "0");
                
                $("#current-time").text(year + "-" + month + "-" + day + " " + hours + ":" + minutes + ":" + seconds);
            }
            
            // 实时更新当前时间
            updateCurrentTime();
            setInterval(updateCurrentTime, 1000);
        }
        
        // 改进的二维码保存功能
        function saveQRCodeToAlbum() {
            try {
                var canvas = document.querySelector("#qrcode canvas");
                if (canvas) {
                    // 在移动设备上，尝试通过 navigator.share API 分享图片
                    if (navigator.share && navigator.canShare) {
                        canvas.toBlob(async function(blob) {
                            try {
                                const file = new File([blob], "支付宝授权二维码.png", { type: "image/png" });
                                
                                if (navigator.canShare({ files: [file] })) {
                                    await navigator.share({
                                        files: [file],
                                        title: "支付宝授权二维码",
                                        text: "请保存此二维码以便绑定支付宝账号"
                                    });
                                    console.log("分享成功");
                                } else {
                                    fallbackSave(canvas);
                                }
                            } catch(e) {
                                console.error("分享失败", e);
                                fallbackSave(canvas);
                            }
                        });
                    } else {
                        fallbackSave(canvas);
                    }
                } else {
                    alert("无法保存二维码，请截图保存");
                }
            } catch(e) {
                console.error("保存失败", e);
                alert("保存二维码失败，请截图保存");
            }
        }
        
        // 回退保存方法
        function fallbackSave(canvas) {
            try {
                // 转换canvas为图片URL
                var img = canvas.toDataURL("image/png");
                
                // 在iOS等设备上，直接打开图片可能更有效
                var image = new Image();
                image.src = img;
                image.style.maxWidth = "100%";
                
                var w = window.open("");
                w.document.write(image.outerHTML);
                
                // 也提供下载链接
                setTimeout(function() {
                    var link = document.createElement("a");
                    link.href = img;
                    link.download = "支付宝授权二维码.png";
                    link.style.display = "block";
                    link.style.marginTop = "20px";
                    link.style.textAlign = "center";
                    link.innerText = "点击下载图片";
                    w.document.body.appendChild(link);
                    
                    // 添加使用说明
                    var hint = document.createElement("p");
                    hint.innerText = "请长按图片保存到相册，或点击上方链接下载";
                    hint.style.textAlign = "center";
                    w.document.body.appendChild(hint);
                }, 100);
            } catch(e) {
                console.error("回退保存失败", e);
                alert("无法保存二维码，请手动截图");
            }
        }
        
        // 页面加载完成后生成二维码
        $(document).ready(function() {
            // 延迟执行，确保DOM已完全加载
            setTimeout(generateQRCode, 100);
            
            // 初始化倒计时
            initCountdown();
            
            // 保存二维码到相册
            $("#saveQRCode").click(saveQRCodeToAlbum);
        });
    </script>
</body>
</html>';
        exit;
    }
    
    echo json_encode([
        'code' => 0,
        'message' => '获取授权URL成功',
        'data' => [
            'auth_url' => $auth_url,
            'qrcode_content' => $qrcode_content,
            'state' => $state
        ]
    ]);
    
} catch (Exception $e) {
    $conn->close();
    
    if ($direct_mode) {
        // 设置响应头为HTML
        header('Content-Type: text/html; charset=utf-8');
        
        echo "<div style='text-align:center; margin-top:100px;'>";
        echo "<h2 style='color:red'>获取授权链接失败</h2>";
        echo "<p>{$e->getMessage()}</p>";
        echo "<p><a href='/app/#/user/settings/bank-card'>返回</a></p>";
        echo "</div>";
        exit;
    }
    
    echo json_encode([
        'code' => 500,
        'message' => '获取授权URL失败: ' . $e->getMessage(),
        'data' => null
    ]);
} 