<?php
/**
 * VIP分红详情API
 * 获取单个分红记录的详细信息
 */

// 引入配置文件
require_once '../config.php';

// 设置响应头
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

try {
    // 验证请求方法
    if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
        throw new Exception('只支持GET请求');
    }

    // 验证用户身份
    $user = validateUser();
    if (!$user) {
        throw new Exception('用户未登录或登录已过期');
    }

    // 获取分红记录ID
    $dividendId = $_GET['id'] ?? '';
    if (empty($dividendId)) {
        throw new Exception('分红记录ID不能为空');
    }

    // 连接数据库
    $pdo = connectDatabase();

    // 查询分红记录详情
    $sql = "SELECT 
                d.*,
                u.nickname,
                u.phone,
                u.avatar
            FROM vip_dividend_records d
            LEFT JOIN app_users u ON d.user_id = u.id
            WHERE d.id = :dividend_id AND d.user_id = :user_id";
    
    $stmt = $pdo->prepare($sql);
    $stmt->bindParam(':dividend_id', $dividendId, PDO::PARAM_INT);
    $stmt->bindParam(':user_id', $user['id'], PDO::PARAM_INT);
    $stmt->execute();
    
    $dividend = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$dividend) {
        throw new Exception('分红记录不存在或无权限访问');
    }

    // 格式化数据
    $result = [
        'id' => $dividend['id'],
        'type' => $dividend['dividend_type'],
        'level' => $dividend['dividend_level'],
        'period' => $dividend['settlement_month'],
        'amount' => number_format($dividend['amount'], 2, '.', ''),
        'status' => $dividend['status'],
        'team_vip_count' => $dividend['team_vip_count'] ?? 0,
        'team_device_count' => $dividend['team_device_count'] ?? 0,
        'direct_vip_count' => $dividend['direct_vip_count'] ?? 0,
        'direct_device_count' => $dividend['direct_device_count'] ?? 0,
        'month_direct_vip_count' => $dividend['month_direct_vip_count'] ?? 0,
        'month_team_vip_count' => $dividend['month_team_vip_count'] ?? 0,
        'month_direct_device_count' => $dividend['month_direct_device_count'] ?? 0,
        'month_team_device_count' => $dividend['month_team_device_count'] ?? 0,
        'pool_amount' => $dividend['pool_amount'] ? number_format($dividend['pool_amount'], 2, '.', '') : null,
        'qualified_users_count' => $dividend['qualified_users_count'] ?? null,
        'direct_ratio' => $dividend['direct_ratio'] ?? null,
        'qualification_reason' => $dividend['qualification_reason'] ?? '',
        'calculation_detail' => $dividend['calculation_detail'] ? json_decode($dividend['calculation_detail'], true) : null,
        'created_at' => $dividend['created_at'],
        'settled_at' => $dividend['settled_at'],
        'settlement_remark' => $dividend['settlement_remark'] ?? '',
        'user_info' => [
            'nickname' => $dividend['nickname'],
            'phone' => $dividend['phone'],
            'avatar' => $dividend['avatar']
        ]
    ];

    // 返回成功响应
    echo json_encode([
        'code' => 0,
        'msg' => '获取分红详情成功',
        'data' => $result
    ], JSON_UNESCAPED_UNICODE);

} catch (Exception $e) {
    // 记录错误日志
    error_log("VIP分红详情API错误: " . $e->getMessage());
    
    // 返回错误响应
    http_response_code(500);
    echo json_encode([
        'code' => -1,
        'msg' => $e->getMessage(),
        'data' => null
    ], JSON_UNESCAPED_UNICODE);
}

/**
 * 验证用户身份
 */
function validateUser() {
    // 从请求头获取token
    $headers = getallheaders();
    $token = $headers['Authorization'] ?? $_GET['token'] ?? '';
    
    if (empty($token)) {
        return false;
    }
    
    // 移除Bearer前缀
    if (strpos($token, 'Bearer ') === 0) {
        $token = substr($token, 7);
    }
    
    try {
        // 这里应该验证JWT token，简化处理
        // 实际项目中需要使用JWT库验证token
        
        // 临时处理：从数据库查询用户
        global $pdo;
        if (!$pdo) {
            $pdo = connectDatabase();
        }
        
        // 假设token就是用户ID（实际应该解析JWT）
        $userId = intval($token);
        if ($userId <= 0) {
            return false;
        }
        
        $stmt = $pdo->prepare("SELECT * FROM app_users WHERE id = :user_id AND is_vip_paid = 1");
        $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
        $stmt->execute();
        
        return $stmt->fetch(PDO::FETCH_ASSOC);
        
    } catch (Exception $e) {
        error_log("Token验证失败: " . $e->getMessage());
        return false;
    }
}

/**
 * 连接数据库
 */
function connectDatabase() {
    try {
        $host = 'localhost';
        $dbname = 'ddg.app';
        $username = 'root';
        $password = 'Tapgo2024!';
        
        $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
        
        return $pdo;
    } catch (PDOException $e) {
        throw new Exception('数据库连接失败: ' . $e->getMessage());
    }
}
?> 