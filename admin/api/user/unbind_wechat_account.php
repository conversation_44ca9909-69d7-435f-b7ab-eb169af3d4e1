<?php
/**
 * 解绑微信提现账号API
 * 
 * 清除当前用户设置的提现微信账号
 */

// 设置响应头
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 检查请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode([
        'code' => 405,
        'message' => '请求方法不允许',
        'data' => null
    ]);
    exit;
}

// 引入必要的功能文件
require_once dirname(__DIR__) . '/functions/auth.php';
require_once dirname(__DIR__) . '/functions/db.php';

// 验证用户身份
$user_id = verify_token();
if (!$user_id) {
    echo json_encode([
        'code' => 401,
        'message' => '未授权，请先登录',
        'data' => null
    ]);
    exit;
}

// 连接数据库
$conn = get_db_connection();

// 检查用户是否设置了提现微信账号
$stmt = $conn->prepare("SELECT withdrawal_wechat_id FROM app_users WHERE id = ?");
$stmt->bind_param("i", $user_id);
$stmt->execute();
$result = $stmt->get_result();
$user = $result->fetch_assoc();
$stmt->close();

if (!$user || empty($user['withdrawal_wechat_id'])) {
    $conn->close();
    echo json_encode([
        'code' => 400,
        'message' => '未设置提现微信账号',
        'data' => null
    ]);
    exit;
}

// 清除提现微信账号信息
$stmt = $conn->prepare("UPDATE app_users SET 
                       withdrawal_wechat_id = NULL, 
                       withdrawal_wechat_nickname = NULL, 
                       withdrawal_wechat_avatar = NULL,
                       withdrawal_wechat_unbind_time = NOW() 
                       WHERE id = ?");
$stmt->bind_param("i", $user_id);

// 执行更新
if ($stmt->execute()) {
    $stmt->close();
    $conn->close();
    echo json_encode([
        'code' => 0,
        'message' => '微信提现账号解绑成功',
        'data' => null
    ]);
} else {
    $error = $stmt->error;
    $stmt->close();
    $conn->close();
    echo json_encode([
        'code' => 500,
        'message' => '微信提现账号解绑失败: ' . $error,
        'data' => null
    ]);
} 