<?php
/**
 * 用户注册API重定向
 *
 * 此文件将原始的PHP用户注册API请求重定向到新的Laravel RESTful API
 */

// 设置HTTP响应头
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 检查请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode([
        'code' => 1,
        'message' => '请求方法不允许',
        'data' => null
    ]);
    exit;
}

// 获取请求参数
$data = json_decode(file_get_contents('php://input'), true);

// 获取当前主机名
$protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https://' : 'http://';
$host = $_SERVER['HTTP_HOST'];
$apiUrl = $protocol . $host . '/api/auth/register';

// 创建cURL请求
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $apiUrl);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
    'Accept: application/json'
]);

// 执行请求
$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

// 记录请求信息
$logMessage = sprintf(
    '用户注册API重定向: API=%s, 状态码=%d',
    $apiUrl,
    $httpCode
);
error_log($logMessage);

// 如果请求失败，记录更详细的错误信息
if ($response === false) {
    $errorMessage = sprintf(
        '用户注册API请求失败: 错误=%s, URL=%s',
        curl_error($ch),
        $apiUrl
    );
    error_log($errorMessage);

    echo json_encode([
        'code' => 1,
        'message' => '服务器内部错误',
        'data' => null
    ]);
} else {
    // 记录响应内容（仅用于调试）
    error_log('用户注册API响应: ' . substr($response, 0, 200) . '...');

    // 返回原始响应
    echo $response;
}
?>