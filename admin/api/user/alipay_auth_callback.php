<?php
/**
 * 支付宝授权回调处理API
 * 
 * 处理支付宝授权后的回调，获取用户的支付宝openid并绑定到用户账号
 */

header('Content-Type: text/html; charset=utf-8');

// 设置时区
date_default_timezone_set('Asia/Shanghai');

// 引入必要的功能文件
require_once dirname(__DIR__) . '/functions/db.php';

// 获取支付宝回调参数
$auth_code = isset($_GET['auth_code']) ? $_GET['auth_code'] : '';
$state = isset($_GET['state']) ? $_GET['state'] : '';

if (empty($auth_code) || empty($state)) {
    echo "<div class='result-message error'>授权失败：缺少必要参数</div>";
    exit;
}

// 连接数据库
$conn = get_db_connection();

// 验证state参数，防止CSRF攻击
$stmt = $conn->prepare("SELECT user_id FROM alipay_auth_states WHERE state = ? AND create_time > DATE_SUB(NOW(), INTERVAL 1 HOUR)");
$stmt->bind_param("s", $state);
$stmt->execute();
$result = $stmt->get_result();
$auth_state = $result->fetch_assoc();
$stmt->close();

if (!$auth_state) {
    echo "<div class='result-message error'>授权失败：无效的请求状态或授权已过期</div>";
    exit;
}

$user_id = $auth_state['user_id'];

// 查询用户信息
$stmt = $conn->prepare("SELECT id, name, wechat_avatar FROM app_users WHERE id = ?");
$stmt->bind_param("i", $user_id);
$stmt->execute();
$result = $stmt->get_result();
$user = $result->fetch_assoc();
$stmt->close();

if (!$user) {
    echo "<div class='result-message error'>授权失败：用户不存在</div>";
    exit;
}

// 记录请求参数，帮助调试
error_log('Alipay Auth Callback - auth_code: ' . substr($auth_code, 0, 4) . '...');
error_log('Alipay Auth Callback - state: ' . $state);

$app_id = '2021004188641378';
// 使用新生成的 PKCS#8 格式私钥，确保与支付宝后台上传的公钥配对
$private_key_path = '/www/wwwroot/pay.itapgo.com/cert/private_key_pkcs8.pem'; // 使用绝对路径
$alipay_public_key_path = '/www/wwwroot/pay.itapgo.com/cert/alipayCertPublicKey_RSA2.crt'; // 使用绝对路径

// 获取证书序列号
function getCertSN($certPath) {
    $cert = file_get_contents($certPath);
    $certResource = openssl_x509_read($cert);
    $certData = openssl_x509_parse($certResource);
    $issuer = $certData['issuer'];
    $serialNumber = $certData['serialNumberHex'];
    return md5($issuer['CN'] . $serialNumber);
}

function getRootCertSN($certPath) {
    $certContent = file_get_contents($certPath);
    $certs = explode("-----END CERTIFICATE-----", $certContent);
    $certSNs = [];

    foreach ($certs as $cert) {
        if (trim($cert)) {
            $cert .= "-----END CERTIFICATE-----";
            $certResource = openssl_x509_read($cert);
            $certData = openssl_x509_parse($certResource);
            $issuer = $certData['issuer'];
            $serialNumber = $certData['serialNumberHex'];
            $certSNs[] = md5($issuer['CN'] . $serialNumber);
        }
    }

    return implode("_", $certSNs);
}

$app_cert_path = '/www/wwwroot/pay.itapgo.com/cert/appCertPublicKey_2021004188641378.crt';
$alipay_root_cert_path = '/www/wwwroot/pay.itapgo.com/cert/alipayRootCert.crt';

$app_cert_sn = getCertSN($app_cert_path);
$alipay_root_cert_sn = getRootCertSN($alipay_root_cert_path);

// 记录实际证书路径
error_log('Alipay Auth Callback - Private Key Path: ' . $private_key_path);
error_log('Alipay Auth Callback - Alipay Public Key Path: ' . $alipay_public_key_path);

// 加载私钥
if (file_exists($private_key_path)) {
    $private_key = file_get_contents($private_key_path);
    if (!$private_key) {
        error_log('Alipay Auth Callback - Error: Private key exists but cannot be read');
        echo "<div class='result-message error'>系统错误：无法读取私钥内容</div>";
        exit;
    }
} else {
    error_log('Alipay Auth Callback - Error: Private key file does not exist');
    echo "<div class='result-message error'>系统错误：私钥文件不存在</div>";
    exit;
}

// 检查私钥是否有效
if (strpos($private_key, '-----BEGIN') === false) {
    error_log('Alipay Auth Callback - Error: Private key content is invalid');
    echo "<div class='result-message error'>系统错误：私钥内容无效</div>";
    exit;
}

error_log('Alipay Auth Callback - Successfully loaded private key');

// 使用简化的参数设置，减少出错几率
$params = [
    'app_id' => $app_id,
    'method' => 'alipay.system.oauth.token',
    'charset' => 'utf-8',
    'sign_type' => 'RSA2',
    'timestamp' => date('Y-m-d H:i:s'),
    'version' => '1.0',
    'grant_type' => 'authorization_code',
    'code' => $auth_code,
    'app_cert_sn' => '53ed1952f35aff5d7a33d04a5b8aa761',
    'alipay_root_cert_sn' => '687b59193f3f462dd5336e5abf83c5d8_02941eef3187dddf3d3b83462e1dfcf6',
    'nonce' => bin2hex(random_bytes(16)),
];

// 排序并生成签名
ksort($params);
$string_to_sign = '';
foreach ($params as $k => $v) {
    if ($k != 'sign' && $v !== '' && !is_null($v)) {
        $string_to_sign .= $k . '=' . $v . '&';
    }
}
$string_to_sign = rtrim($string_to_sign, '&');

// 添加详细日志
error_log('Alipay Auth Callback - String to sign: ' . $string_to_sign);

// 签名
$sign_result = openssl_sign($string_to_sign, $sign, $private_key, OPENSSL_ALGO_SHA256);
if (!$sign_result) {
    error_log('Alipay Auth Callback - Failed to sign request: ' . openssl_error_string());
    echo "<div class='result-message error'>授权失败：签名生成失败</div>";
    exit;
}

// 编码签名
$params['sign'] = base64_encode($sign);

// 添加调试日志
error_log('Alipay Auth Callback - Request params: ' . json_encode($params));

// 发送请求到支付宝 - 简化请求配置，聚焦关键参数
$gateway = 'https://openapi.alipay.com/gateway.do';
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $gateway);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($params));
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
curl_setopt($ch, CURLOPT_TIMEOUT, 30);
curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/x-www-form-urlencoded;charset=utf-8']);

$response = curl_exec($ch);

// 详细错误处理
if (curl_errno($ch)) {
    $curl_error = curl_error($ch);
    error_log('Alipay Auth Callback - CURL ERROR: ' . $curl_error);
    echo "<div class='result-message error'>授权失败：网络错误 - " . $curl_error . "</div>";
    exit;
}

$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

if ($http_code != 200) {
    error_log('Alipay Auth Callback - HTTP ERROR: ' . $http_code . ', Response: ' . $response);
    echo "<div class='result-message error'>授权失败：HTTP错误 " . $http_code . "</div>";
    exit;
}

// 记录原始响应
error_log('Alipay Auth Callback - Raw response: ' . $response);

// 尝试解析JSON响应
$alipay_response = json_decode($response, true);
if (json_last_error() !== JSON_ERROR_NONE) {
    error_log('Alipay Auth Callback - JSON parse error: ' . json_last_error_msg() . ', Response: ' . $response);
    
    // 显示原始响应内容以便调试
    echo "<div class='result-message error'>
            <h2>授权失败：返回数据解析错误</h2>
            <p>错误信息：" . json_last_error_msg() . "</p>
            <div class='error-details'>
                <p>请将以下信息提供给技术支持：</p>
                <pre>" . htmlspecialchars(substr($response, 0, 300)) . (strlen($response) > 300 ? '...' : '') . "</pre>
            </div>
            <div class='button-container'>
                <a href='/app/#/user/settings/bank-card' class='button'>返回</a>
            </div>
          </div>";
    exit;
}

// 直接尝试从响应中提取必要的信息 - 简化处理流程
try {
    // 直接记录完整的响应以便调试
    error_log('Alipay Auth Callback - Complete Response: ' . json_encode($alipay_response));
    
    // 检查响应中是否包含我们需要的信息
    if (!isset($alipay_response['alipay_system_oauth_token_response'])) {
        if (isset($alipay_response['error_response'])) {
            $error_code = $alipay_response['error_response']['code'] ?? '';
            $error_msg = $alipay_response['error_response']['sub_msg'] ?? $alipay_response['error_response']['msg'] ?? '支付宝返回错误';
            throw new Exception("支付宝返回错误（{$error_code}）：{$error_msg}");
        } else {
            throw new Exception("支付宝返回数据格式不正确");
        }
    }
    
    $response_data = $alipay_response['alipay_system_oauth_token_response'];
    
    // 提取支付宝用户ID和OpenID
    $alipay_user_id = $response_data['user_id'] ?? '';
    $alipay_openid = $response_data['open_id'] ?? $alipay_user_id;
    
    error_log('Alipay Auth Callback - Open ID: ' . $alipay_openid);
    
    // 从响应中获取access_token
    $access_token = $response_data['access_token'] ?? '';
    
    // 开始事务
    $conn->begin_transaction();
    
    // 初始化头像URL
    $alipay_avatar = '';
    
    // 如果有access_token，获取用户头像信息
    if (!empty($access_token)) {
        // 构建获取用户信息的请求
        $user_info_params = [
            'app_id' => $app_id,
            'method' => 'alipay.user.info.share',
            'charset' => 'utf-8',
            'sign_type' => 'RSA2',
            'timestamp' => date('Y-m-d H:i:s'),
            'version' => '1.0',
            'auth_token' => $access_token
        ];
        
        // 生成签名
        ksort($user_info_params);
        $user_info_string = '';
        foreach ($user_info_params as $k => $v) {
            if ($k != 'sign' && $v !== '' && !is_null($v)) {
                $user_info_string .= $k . '=' . $v . '&';
            }
        }
        $user_info_string = rtrim($user_info_string, '&');
        
        // 签名
        if (!openssl_sign($user_info_string, $user_info_sign, $private_key, OPENSSL_ALGO_SHA256)) {
            error_log('Alipay Auth Callback - Failed to sign user info request: ' . openssl_error_string());
        } else {
            $user_info_params['sign'] = base64_encode($user_info_sign);
            
            // 发送请求获取用户信息
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $gateway);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($user_info_params));
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
            curl_setopt($ch, CURLOPT_TIMEOUT, 30);
            curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/x-www-form-urlencoded;charset=utf-8']);
            
            $user_info_response = curl_exec($ch);
            curl_close($ch);
            
            // 记录用户信息响应
            error_log('Alipay Auth Callback - User Info Response: ' . $user_info_response);
            
            // 解析用户信息响应
            $user_info_data = json_decode($user_info_response, true);
            
            // 提取头像URL
            if (isset($user_info_data['alipay_user_info_share_response'])) {
                $user_info = $user_info_data['alipay_user_info_share_response'];
                $alipay_avatar = isset($user_info['avatar']) ? trim((string)$user_info['avatar']) : '';
                error_log('Alipay Auth Callback - Final Avatar to Save: ' . $alipay_avatar);
                error_log('Alipay Auth Callback - Raw Avatar Field: ' . json_encode($user_info));
                error_log('Alipay Auth Callback - Parsed Avatar URL: ' . $alipay_avatar);
            } else {
                error_log('Alipay Auth Callback - alipay_user_info_share_response not found in user info response');
            }
        }
    }
    
    // 更新用户表，只保存openid和avatar两个必要字段
    try {
        // 记录要保存的值
        error_log('Alipay Auth Callback - Saving to DB - OpenID: ' . $alipay_openid);
        error_log('Alipay Auth Callback - Saving to DB - Avatar: ' . $alipay_avatar);
        
        // 将支付宝OpenID和头像URL保存到用户表
        $stmt = $conn->prepare("UPDATE app_users SET 
                           alipay_openid = ?,
                           alipay_avatar = ?,
                           updated_at = NOW() 
                           WHERE id = ?");
        $stmt->bind_param("ssi", $alipay_openid, $alipay_avatar, $user_id);
        
        if (!$stmt->execute()) {
            throw new Exception("数据库更新失败: " . $stmt->error);
        }
        $stmt->close();
        
        // 清除使用过的state
        $stmt = $conn->prepare("DELETE FROM alipay_auth_states WHERE state = ?");
        $stmt->bind_param("s", $state);
        $stmt->execute();
        $stmt->close();
        
        // 提交事务
        $conn->commit();
        
        // 如果成功处理完所有步骤，显示成功页面
        echo "<div class='result-message success'>
               <div class='success-icon'>✓</div>
               <h2>支付宝账号绑定成功！</h2>
               <div class='user-profile'>
                 <div class='account-info'>
                   <div class='account-item'>
                     <img src='" . htmlspecialchars($user['wechat_avatar'] ?: '/Tapp/app/images/default-avatar.png') . "' alt='微信头像' class='avatar-img' />
                     <div class='account-details'>
                       <div class='account-name'>" . htmlspecialchars($user['name']) . "</div>
                       <div class='account-type'>微信账号</div>
                     </div>
                   </div>
                   <div class='account-item'>
                     <div class='alipay-icon-wrapper'>
                       <svg viewBox='0 0 1024 1024' width='40' height='40'>
                         <path d='M1023.795 853.64v6.348a163.807 163.807 0 0 1-163.807 163.807h-696.18A163.807 163.807 0 0 1 0 859.988v-696.18A163.807 163.807 0 0 1 163.807 0h696.181a163.807 163.807 0 0 1 163.807 163.807V853.64z' fill='#009FE9'></path>
                         <path d='M844.836 648.267c-40.952-14.333-95.623-34.809-156.846-57.128a949.058 949.058 0 0 0 90.094-222.573H505.173v-60.14h273.49v-35.647H505.173v-97.233h-145.12v97.233H130.866v35.647h229.187v60.14H135.757v35.647h601.857a891.887 891.887 0 0 1-64.068 155.312c-128.376-40.952-266.186-77.834-354.043-55.08a619.28 619.28 0 0 0-69.957 20.475c-96.977 116.694-166.177 256.576-166.177 423.55v18.302h718.36A160.2 160.2 0 0 1 789.1 1024H132.913C159.245 871.539 247.598 453.55 844.836 648.267z' fill='#FFFFFF'></path>
                       </svg>
                     </div>
                     <div class='account-details'>
                       <div class='account-name'>支付宝用户</div>
                       <div class='account-openid'>OpenID: " . substr($alipay_openid, 0, 6) . "****" . substr($alipay_openid, -4) . "</div>
                     </div>
                   </div>
                 </div>
               </div>
               <div class='bind-info'>
                 <div class='info-title'>提现说明</div>
                 <div class='info-content'>此支付宝账号仅在点点够公众号里提现使用</div>
               </div>
               <div class='button-container'>
                 <a href='/app/#/user/settings/bank-card?alipay_bind=success' class='button'>关闭窗口</a>
               </div>
               <script>
                 // 使用多种方式尝试通知父窗口
                 try {
                   // 方式1: 使用opener对象（如果是从window.open打开的）
                   if (window.opener && !window.opener.closed) {
                     window.opener.postMessage('ALIPAY_BIND_SUCCESS', '*');
                     window.opener.postMessage({type: 'ALIPAY_BIND_SUCCESS', status: 'success'}, '*');
                     
                     // 如果有可能,尝试直接修改父窗口的URL
                     try {
                       window.opener.location.href = '/app/#/user/settings/bank-card?alipay_bind=success';
                     } catch(e) {
                       console.log('无法修改opener位置:', e);
                     }
                   }
                   
                   // 方式2: 使用parent窗口（如果是在iframe中）
                   if (window.parent && window.parent !== window) {
                     window.parent.postMessage('ALIPAY_BIND_SUCCESS', '*');
                     window.parent.postMessage({type: 'ALIPAY_BIND_SUCCESS', status: 'success'}, '*');
                   }
                   
                   // 方式3: 作为备选，5秒后自动关闭窗口
                   setTimeout(function() {
                     window.close();
                   }, 5000);
                 } catch(err) {
                   console.error('发送消息时出错:', err);
                 }
               </script>
             </div>

             <style>
               body {
                 font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
                 background-color: #f5f5f5;
                 color: #333;
                 margin: 0;
                 padding: 0;
                 display: flex;
                 justify-content: center;
                 align-items: center;
                 min-height: 100vh;
               }
               
               .result-message {
                 max-width: 480px;
                 width: 90%;
                 background: #fff;
                 border-radius: 16px;
                 box-shadow: 0 8px 30px rgba(0, 0, 0, 0.08);
                 padding: 30px;
                 text-align: center;
                 position: relative;
                 overflow: hidden;
               }
               
               .result-message::before {
                 content: '';
                 position: absolute;
                 top: 0;
                 left: 0;
                 right: 0;
                 height: 6px;
                 background: linear-gradient(90deg, #1677FF, #00BFFF);
               }
               
               .success-icon {
                 width: 70px;
                 height: 70px;
                 border-radius: 50%;
                 background: #52c41a;
                 color: white;
                 font-size: 40px;
                 display: flex;
                 align-items: center;
                 justify-content: center;
                 margin: 0 auto 20px;
                 font-weight: bold;
               }
               
               h2 {
                 color: #333;
                 font-size: 24px;
                 margin-bottom: 25px;
               }
               
               .user-profile {
                 margin: 25px 0;
                 padding: 16px;
                 background-color: #f9f9f9;
                 border-radius: 12px;
               }
               
               .account-info {
                 display: flex;
                 flex-direction: column;
                 gap: 16px;
               }
               
               .account-item {
                 display: flex;
                 align-items: center;
                 padding: 10px;
                 background: white;
                 border-radius: 10px;
                 box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
               }
               
               .avatar-img {
                 width: 40px;
                 height: 40px;
                 border-radius: 50%;
                 object-fit: cover;
                 margin-right: 12px;
               }
               
               .alipay-icon-wrapper {
                 width: 40px;
                 height: 40px;
                 margin-right: 12px;
                 display: flex;
                 align-items: center;
                 justify-content: center;
               }
               
               .account-details {
                 flex: 1;
                 text-align: left;
               }
               
               .account-name {
                 font-weight: bold;
                 font-size: 16px;
                 color: #333;
               }
               
               .account-type, .account-openid {
                 font-size: 13px;
                 color: #888;
                 margin-top: 2px;
               }
               
               .bind-info {
                 margin: 20px 0;
                 background-color: rgba(22, 119, 255, 0.05);
                 border-radius: 10px;
                 padding: 15px;
                 text-align: left;
                 border-left: 4px solid #1677FF;
               }
               
               .info-title {
                 font-weight: bold;
                 font-size: 16px;
                 color: #1677FF;
                 margin-bottom: 6px;
               }
               
               .info-content {
                 font-size: 14px;
                 color: #666;
               }
               
               .button-container {
                 margin-top: 30px;
               }
               
               .button {
                 display: inline-block;
                 background-color: #1677FF;
                 color: white;
                 padding: 12px 30px;
                 border-radius: 8px;
                 text-decoration: none;
                 font-size: 16px;
                 font-weight: 500;
                 transition: all 0.3s ease;
               }
               
               .button:hover {
                 background-color: #0056cc;
                 transform: translateY(-2px);
                 box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
               }
               
               @media (max-width: 480px) {
                 .result-message {
                   padding: 20px;
                 }
                 
                 .success-icon {
                   width: 60px;
                   height: 60px;
                   font-size: 30px;
                 }
                 
                 h2 {
                   font-size: 20px;
                 }
               }
             </style>";
        
    } catch (Exception $e) {
        // 如果事务已经开始，则回滚
        if ($conn->inTransaction()) {
            $conn->rollback();
        }
        
        // 记录错误
        error_log('Alipay Auth Error: ' . $e->getMessage());
        
        // 显示错误信息
        echo "<div class='result-message error'>
                <div class='error-icon'>!</div>
                <h2>授权绑定失败</h2>
                <div class='error-details'>
                  <div class='error-message'>" . htmlspecialchars($e->getMessage()) . "</div>
                </div>
                <div class='button-container'>
                  <a href='/app/#/user/settings/bank-card' class='button button-back'>返回</a>
                  <a href='javascript:void(0)' onclick='window.close()' class='button'>关闭窗口</a>
                </div>
              </div>
              
              <style>
                body {
                  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
                  background-color: #f5f5f5;
                  color: #333;
                  margin: 0;
                  padding: 0;
                  display: flex;
                  justify-content: center;
                  align-items: center;
                  min-height: 100vh;
                }
                
                .result-message {
                  max-width: 480px;
                  width: 90%;
                  background: #fff;
                  border-radius: 16px;
                  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.08);
                  padding: 30px;
                  text-align: center;
                  position: relative;
                  overflow: hidden;
                }
                
                .result-message::before {
                  content: '';
                  position: absolute;
                  top: 0;
                  left: 0;
                  right: 0;
                  height: 6px;
                  background: linear-gradient(90deg, #ff4d4f, #ff7a45);
                }
                
                .result-message.error::before {
                  background: linear-gradient(90deg, #ff4d4f, #ff7a45);
                }
                
                .error-icon {
                  width: 70px;
                  height: 70px;
                  border-radius: 50%;
                  background: #ff4d4f;
                  color: white;
                  font-size: 40px;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  margin: 0 auto 20px;
                  font-weight: bold;
                }
                
                h2 {
                  color: #333;
                  font-size: 24px;
                  margin-bottom: 25px;
                }
                
                .error-details {
                  margin: 25px 0;
                  padding: 16px;
                  background-color: rgba(255, 77, 79, 0.05);
                  border-radius: 12px;
                  border-left: 4px solid #ff4d4f;
                  text-align: left;
                }
                
                .error-message {
                  font-size: 14px;
                  color: #666;
                  line-height: 1.6;
                }
                
                .button-container {
                  margin-top: 30px;
                  display: flex;
                  justify-content: center;
                  gap: 15px;
                }
                
                .button {
                  display: inline-block;
                  background-color: #1677FF;
                  color: white;
                  padding: 12px 30px;
                  border-radius: 8px;
                  text-decoration: none;
                  font-size: 16px;
                  font-weight: 500;
                  transition: all 0.3s ease;
                }
                
                .button-back {
                  background-color: #f0f0f0;
                  color: #333;
                }
                
                .button:hover {
                  transform: translateY(-2px);
                  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
                }
                
                .button-back:hover {
                  background-color: #e6e6e6;
                }
                
                @media (max-width: 480px) {
                  .result-message {
                    padding: 20px;
                  }
                  
                  .error-icon {
                    width: 60px;
                    height: 60px;
                    font-size: 30px;
                  }
                  
                  h2 {
                    font-size: 20px;
                  }
                  
                  .button {
                    padding: 10px 20px;
                    font-size: 14px;
                  }
                }
              </style>";
    }
    
} catch (Exception $e) {
    // 回滚事务
    $conn->rollback();
    
    // 记录错误
    error_log('Alipay Auth Error: ' . $e->getMessage());
    
    // 显示错误信息
    echo "<div class='result-message error'>
          <div class='error-icon'>!</div>
          <h2>授权绑定失败</h2>
          <div class='error-details'>
            <div class='error-message'>" . htmlspecialchars($e->getMessage()) . "</div>
          </div>
          <div class='button-container'>
            <a href='/app/#/user/settings/bank-card' class='button button-back'>返回</a>
            <a href='javascript:void(0)' onclick='window.close()' class='button'>关闭窗口</a>
          </div>
        </div>
        
        <style>
          body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background-color: #f5f5f5;
            color: #333;
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
          }
          
          .result-message {
            max-width: 480px;
            width: 90%;
            background: #fff;
            border-radius: 16px;
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.08);
            padding: 30px;
            text-align: center;
            position: relative;
            overflow: hidden;
          }
          
          .result-message::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 6px;
            background: linear-gradient(90deg, #ff4d4f, #ff7a45);
          }
          
          .result-message.error::before {
            background: linear-gradient(90deg, #ff4d4f, #ff7a45);
          }
          
          .error-icon {
            width: 70px;
            height: 70px;
            border-radius: 50%;
            background: #ff4d4f;
            color: white;
            font-size: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            font-weight: bold;
          }
          
          h2 {
            color: #333;
            font-size: 24px;
            margin-bottom: 25px;
          }
          
          .error-details {
            margin: 25px 0;
            padding: 16px;
            background-color: rgba(255, 77, 79, 0.05);
            border-radius: 12px;
            border-left: 4px solid #ff4d4f;
            text-align: left;
          }
          
          .error-message {
            font-size: 14px;
            color: #666;
            line-height: 1.6;
          }
          
          .button-container {
            margin-top: 30px;
            display: flex;
            justify-content: center;
            gap: 15px;
          }
          
          .button {
            display: inline-block;
            background-color: #1677FF;
            color: white;
            padding: 12px 30px;
            border-radius: 8px;
            text-decoration: none;
            font-size: 16px;
            font-weight: 500;
            transition: all 0.3s ease;
          }
          
          .button-back {
            background-color: #f0f0f0;
            color: #333;
          }
          
          .button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
          }
          
          .button-back:hover {
            background-color: #e6e6e6;
          }
          
          @media (max-width: 480px) {
            .result-message {
              padding: 20px;
            }
            
            .error-icon {
              width: 60px;
              height: 60px;
              font-size: 30px;
            }
            
            h2 {
              font-size: 20px;
            }
            
            .button {
              padding: 10px 20px;
              font-size: 14px;
            }
          }
        </style>";
}

// 确保关闭数据库连接
$conn->close();

// 添加样式
echo "<style>
body {
    font-family: 'PingFang SC', 'Helvetica Neue', Helvetica, 'Hiragino Sans GB', 'Microsoft YaHei', Arial, sans-serif;
    background-color: #f8f8f8;
    margin: 0;
    padding: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
}

.result-message {
    background-color: #fff;
    border-radius: 16px;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
    padding: 40px 30px;
    max-width: 500px;
    width: 90%;
    text-align: center;
    margin: 20px auto;
}

.success-icon, .error-icon {
    margin: 0 auto 30px;
    width: 100px;
    height: 100px;
}

h2 {
    font-size: 28px;
    color: #333;
    margin: 0 0 30px;
    font-weight: 600;
}

.success-message, .error-message {
    font-size: 20px;
    color: #333;
    margin-bottom: 30px;
    line-height: 1.5;
}

.result-message.success .success-message {
    color: #52c41a;
    font-weight: 500;
}

.result-message.error .error-message {
    color: #ff4d4f;
    font-weight: 500;
}

.user-info, .alipay-info {
    font-size: 18px;
    color: #666;
    margin: 12px 0;
    line-height: 1.5;
}

.user-info span, .alipay-info span {
    font-weight: 600;
    color: #333;
}

.error-solution {
    margin: 30px 0;
    text-align: left;
    background-color: #f9f9f9;
    padding: 20px;
    border-radius: 12px;
}

.error-solution p {
    font-size: 18px;
    color: #333;
    margin: 0 0 15px;
    font-weight: 500;
}

.error-solution ul {
    margin: 0;
    padding: 0 0 0 25px;
}

.error-solution li {
    font-size: 16px;
    color: #666;
    margin-bottom: 12px;
    line-height: 1.6;
}

.button-container {
    margin: 35px 0 20px;
}

.close-button {
    background-color: #1989fa;
    color: white;
    border: none;
    border-radius: 30px;
    padding: 14px 32px;
    font-size: 18px;
    cursor: pointer;
    font-weight: 500;
    transition: background-color 0.2s;
    box-shadow: 0 4px 12px rgba(25, 137, 250, 0.3);
}

.close-button:hover {
    background-color: #0e7bef;
}

.instruction {
    margin: 20px 0 0;
    font-size: 18px;
    color: #999;
    font-weight: 500;
}

.error-details {
    margin: 20px 0;
    padding: 20px;
    background-color: #f9f9f9;
    border-radius: 12px;
    text-align: left;
    border-left: 4px solid #ff4d4f;
    overflow: auto;
}

.error-details pre {
    margin: 10px 0 0;
    padding: 15px;
    background-color: #f5f5f5;
    border-radius: 8px;
    font-size: 14px;
    overflow-x: auto;
    white-space: pre-wrap;
    word-wrap: break-word;
    color: #666;
}

/* 针对移动设备的调整 */
@media (max-width: 480px) {
    .result-message {
        padding: 30px 20px;
    }
    
    h2 {
        font-size: 24px;
    }
    
    .success-message, .error-message {
        font-size: 18px;
    }
    
    .user-info, .alipay-info {
        font-size: 16px;
    }
    
    .close-button {
        padding: 12px 28px;
        font-size: 16px;
    }
    
    .instruction {
        font-size: 16px;
    }
}
</style>

<script type='text/javascript'>
// 关闭窗口函数
function closeWindow() {
    // 先发送成功消息
    if (window.opener) {
        window.opener.postMessage('ALIPAY_BIND_SUCCESS', '*');
    }
    
    // 如果是被嵌入在iframe中，尝试通知父窗口
    if (window.parent !== window) {
        window.parent.postMessage('ALIPAY_BIND_SUCCESS', '*');
    }
    
    // 尝试关闭窗口
    window.close();
}

// 页面加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    // 如果是被嵌入在iframe中，尝试通知父窗口
    if (window.parent !== window) {
        window.parent.postMessage('ALIPAY_BIND_SUCCESS', '*');
    }
});
</script>";
