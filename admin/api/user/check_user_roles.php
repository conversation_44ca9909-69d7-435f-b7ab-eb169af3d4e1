<?php
/**
 * 检查用户角色API
 * 
 * 检查用户拥有哪些角色，包括普通用户、商家、业务员等
 */

// 设置HTTP响应头
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 引入相关函数
require_once dirname(__DIR__) . '/functions/auth.php';
require_once dirname(__DIR__) . '/functions/database.php';

// 简单的JWT解码函数
function decode_jwt($token) {
    $parts = explode('.', $token);
    if (count($parts) !== 3) {
        return false;
    }
    
    // 解码payload部分
    $base64Url = $parts[1];
    $base64 = strtr($base64Url, '-_', '+/');
    $base64Padded = str_pad($base64, ceil(strlen($base64) / 4) * 4, '=', STR_PAD_RIGHT);
    
    $payload_json = base64_decode($base64Padded);
    if (!$payload_json) {
        return false;
    }
    
    $payload = json_decode($payload_json, true);
    return $payload;
}

// 获取请求参数
$data = json_decode(file_get_contents('php://input'), true) ?: [];
$mobile = isset($data['mobile']) ? $data['mobile'] : '';

// 如果未提供手机号，尝试从令牌中获取用户
if (empty($mobile)) {
    // 获取当前用户ID（从JWT解析）
    $headers = getallheaders();
    $auth_header = isset($headers['Authorization']) ? $headers['Authorization'] : '';
    $token = str_replace('Bearer ', '', $auth_header);
    
    if (!empty($token)) {
        $payload = decode_jwt($token);
        if ($payload && isset($payload['user_id'])) {
            $user_id = $payload['user_id'];
            
            // 查询用户手机号
            $conn = get_db_connection();
            $stmt = $conn->prepare("SELECT phone FROM app_users WHERE id = ?");
            $stmt->bind_param("i", $user_id);
            $stmt->execute();
            $result = $stmt->get_result();
            if ($row = $result->fetch_assoc()) {
                $mobile = $row['phone'];
            }
            $conn->close();
        }
    }
}

if (empty($mobile)) {
    echo json_encode([
        'code' => 1,
        'message' => '手机号不能为空',
        'data' => null
    ]);
    exit;
}

try {
    // 连接数据库
    $conn = get_db_connection();
    
    // 查询用户
    $stmt = $conn->prepare("SELECT * FROM app_users WHERE phone = ?");
    $stmt->bind_param("s", $mobile);
    $stmt->execute();
    $result = $stmt->get_result();
    $user = $result->fetch_assoc();
    
    $roles = [];
    $roleDetails = [];
    
    if ($user) {
        // 获取用户的角色
        $roles = !empty($user['roles']) ? explode(',', $user['roles']) : [];
        
        // 检查是否商户角色
        $stmt = $conn->prepare("SELECT * FROM merchants WHERE principal_mobile = ? OR app_user_id = ?");
        $stmt->bind_param("si", $mobile, $user['id']);
        $stmt->execute();
        $merchants_result = $stmt->get_result();
        
        if ($merchants_result->num_rows > 0 && !in_array('merchant', $roles)) {
            $roles[] = 'merchant';
            // 更新用户角色
            $roles_str = implode(',', array_unique($roles));
            $update_stmt = $conn->prepare("UPDATE app_users SET roles = ? WHERE id = ?");
            $update_stmt->bind_param("si", $roles_str, $user['id']);
            $update_stmt->execute();
        }
        
        // 获取商户信息
        if (in_array('merchant', $roles)) {
            $merchantList = [];
            $merchants_result->data_seek(0); // 重新定位结果集指针到开始
            while ($merchant = $merchants_result->fetch_assoc()) {
                $merchantList[] = [
                    'id' => $merchant['id'],
                    'merchant_id' => $merchant['merchant_id'],
                    'name' => $merchant['name'],
                    'logo' => $merchant['logo'] ?: 'https://img.itapgo.com/profile/default-merchant.png',
                    'status' => $merchant['status'],
                    'balance' => floatval($merchant['balance'])
                ];
            }
            $roleDetails['merchant'] = [
                'count' => count($merchantList),
                'items' => $merchantList
            ];
        }
        
        // 检查业务员角色
        if (in_array('salesman', $roles)) {
            $stmt = $conn->prepare("SELECT * FROM salesmen WHERE mobile = ?");
            $stmt->bind_param("s", $mobile);
            $stmt->execute();
            $salesman_result = $stmt->get_result();
            
            if ($salesman_result->num_rows > 0) {
                $salesman = $salesman_result->fetch_assoc();
                $roleDetails['salesman'] = [
                    'has_role' => true,
                    'id' => $salesman['id'],
                    'name' => $salesman['name']
                ];
            } else {
                $roleDetails['salesman'] = [
                    'has_role' => true
                ];
            }
        }
        
        // 检查代理商角色
        if (in_array('agent', $roles)) {
            $stmt = $conn->prepare("SELECT * FROM agents WHERE mobile = ?");
            $stmt->bind_param("s", $mobile);
            $stmt->execute();
            $agent_result = $stmt->get_result();
            
            if ($agent_result->num_rows > 0) {
                $agent = $agent_result->fetch_assoc();
                $roleDetails['agent'] = [
                    'has_role' => true,
                    'id' => $agent['id'],
                    'name' => $agent['name']
                ];
            } else {
                $roleDetails['agent'] = [
                    'has_role' => true
                ];
            }
        }
    }
    
    $conn->close();
    
    // 返回用户角色信息
    echo json_encode([
        'code' => 0,
        'message' => '获取成功',
        'data' => [
            'has_user' => !empty($user),
            'user_id' => $user ? $user['id'] : null,
            'roles' => $roles,
            'role_details' => $roleDetails
        ]
    ]);
} catch (Exception $e) {
    echo json_encode([
        'code' => 9999,
        'message' => '检查用户角色失败: ' . $e->getMessage(),
        'data' => null
    ]);
}
?>