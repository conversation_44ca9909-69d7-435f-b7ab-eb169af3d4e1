<?php
/**
 * 支付宝授权重定向脚本
 * 
 * 用于提供短URL供二维码使用，并重定向到实际的支付宝授权URL
 */

// 设置响应头
header('Content-Type: text/html; charset=utf-8');

// 引入必要的功能文件
require_once dirname(__DIR__) . '/functions/db.php';

// 检查state参数是否存在
if (!isset($_GET['state']) || empty($_GET['state'])) {
    echo '<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>参数错误</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            background-color: #f5f5f5;
            color: #333;
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }
        .container {
            max-width: 480px;
            width: 90%;
            background-color: #fff;
            border-radius: 16px;
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.08);
            padding: 30px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }
        .container::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 6px;
            background: linear-gradient(90deg, #ff4d4f, #ff7a45);
        }
        .error-icon {
            width: 70px;
            height: 70px;
            border-radius: 50%;
            background: #ff4d4f;
            color: white;
            font-size: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            font-weight: bold;
        }
        h2 {
            color: #333;
            font-size: 22px;
            margin-bottom: 16px;
        }
        p {
            margin: 12px 0;
            font-size: 15px;
            color: #666;
            line-height: 1.5;
        }
        .button {
            display: inline-block;
            background-color: #1677ff;
            color: white;
            padding: 12px 24px;
            border-radius: 8px;
            text-decoration: none;
            margin-top: 20px;
            font-size: 15px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        .button:hover {
            background-color: #0056cc;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }
        @media (max-width: 480px) {
            .container {
                padding: 20px;
            }
            h2 {
                font-size: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="error-icon">!</div>
        <h2>参数错误</h2>
        <p>缺少必要的授权参数</p>
        <a href="/app/#/user/settings/bank-card" class="button">返回</a>
    </div>
</body>
</html>';
    exit;
}

// 获取state参数
$state = $_GET['state'];

try {
    // 连接数据库
    $conn = get_db_connection();
    
    // 查询state是否有效
    $stmt = $conn->prepare("SELECT user_id, used FROM alipay_auth_states WHERE state = ? ORDER BY create_time DESC LIMIT 1");
    $stmt->bind_param("s", $state);
    $stmt->execute();
    $result = $stmt->get_result();
    $auth_state = $result->fetch_assoc();
    $stmt->close();
    
    // 如果state不存在或已被使用
    if (!$auth_state || $auth_state['used'] == 1) {
        $conn->close();
        echo '<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>授权链接已失效</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            background-color: #f5f5f5;
            color: #333;
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }
        .container {
            max-width: 480px;
            width: 90%;
            background-color: #fff;
            border-radius: 16px;
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.08);
            padding: 30px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }
        .container::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 6px;
            background: linear-gradient(90deg, #ff4d4f, #ff7a45);
        }
        .error-icon {
            width: 70px;
            height: 70px;
            border-radius: 50%;
            background: #ff4d4f;
            color: white;
            font-size: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            font-weight: bold;
        }
        h2 {
            color: #333;
            font-size: 22px;
            margin-bottom: 16px;
        }
        p {
            margin: 12px 0;
            font-size: 15px;
            color: #666;
            line-height: 1.5;
        }
        .button {
            display: inline-block;
            background-color: #1677ff;
            color: white;
            padding: 12px 24px;
            border-radius: 8px;
            text-decoration: none;
            margin-top: 20px;
            font-size: 15px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        .button:hover {
            background-color: #0056cc;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }
        @media (max-width: 480px) {
            .container {
                padding: 20px;
            }
            h2 {
                font-size: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="error-icon">!</div>
        <h2>授权链接已失效</h2>
        <p>请返回应用重新获取授权链接</p>
        <a href="/app/#/user/settings/bank-card" class="button">返回</a>
    </div>
</body>
</html>';
        exit;
    }
    
    // 支付宝应用参数
    $app_id = '****************'; // 使用已有的支付宝AppID
    
    // 定义重定向URL，确保使用完整路径（添加状态参数）
    $redirect_uri = urlencode('https://pay.itapgo.com/Tapp/admin/api/user/alipay_auth_callback.php');
    
    // 添加日志记录
    error_log('Alipay Redirect - Using state: ' . $state);
    error_log('Alipay Redirect - Redirect URI: ' . urldecode($redirect_uri));
    
    // 构造授权URL (优化参数传递)
    $auth_url = "https://openauth.alipay.com/oauth2/publicAppAuthorize.htm";
    $auth_params = [
        'app_id' => $app_id,
        'scope' => 'auth_user',
        'redirect_uri' => urldecode($redirect_uri), // 确保URL正确解码
        'state' => $state
    ];
    
    // 记录请求日志
    error_log('Alipay Redirect - State: ' . $state);
    error_log('Alipay Redirect - Auth URL: ' . $auth_url . '?' . http_build_query($auth_params));
    
    // 使用http_build_query构建URL查询参数，确保正确编码
    $full_auth_url = $auth_url . '?' . http_build_query($auth_params);
    
    // 输出中间页面，确保跳转正确执行
    echo '<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>正在跳转到支付宝授权页面...</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            background-color: #f5f5f5;
            color: #333;
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }
        .container {
            max-width: 480px;
            width: 90%;
            background-color: #fff;
            border-radius: 16px;
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.08);
            padding: 30px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }
        .container::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 6px;
            background: linear-gradient(90deg, #1677FF, #00BFFF);
        }
        h2 {
            color: #333;
            font-size: 22px;
            margin-bottom: 16px;
        }
        p {
            margin: 12px 0;
            font-size: 15px;
            color: #666;
            line-height: 1.5;
        }
        .loading {
            display: inline-block;
            width: 50px;
            height: 50px;
            border: 3px solid rgba(22, 119, 255, 0.15);
            border-radius: 50%;
            border-top-color: #1677ff;
            animation: spin 1s ease-in-out infinite;
            margin: 20px 0;
        }
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        .alipay-icon {
            margin: 20px 0;
            display: inline-block;
            background-color: #f0f9ff;
            padding: 16px;
            border-radius: 50%;
        }
        .alipay-icon svg {
            width: 50px;
            height: 50px;
        }
        .redirect-link {
            display: inline-block;
            background-color: #1677ff;
            color: white;
            padding: 12px 24px;
            border-radius: 8px;
            text-decoration: none;
            margin-top: 20px;
            font-size: 15px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        .redirect-link:hover {
            background-color: #0056cc;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }
        .timer {
            font-size: 14px;
            color: #999;
            margin-top: 20px;
        }
        #countdown {
            color: #1677ff;
            font-weight: bold;
        }
        @media (max-width: 480px) {
            .container {
                padding: 20px;
            }
            h2 {
                font-size: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="alipay-icon">
            <svg viewBox="0 0 1024 1024" width="50" height="50">
                <path d="M1023.795 853.64v6.348a163.807 163.807 0 0 1-163.807 163.807h-696.18A163.807 163.807 0 0 1 0 859.988v-696.18A163.807 163.807 0 0 1 163.807 0h696.181a163.807 163.807 0 0 1 163.807 163.807V853.64z" fill="#009FE9"></path>
                <path d="M844.836 648.267c-40.952-14.333-95.623-34.809-156.846-57.128a949.058 949.058 0 0 0 90.094-222.573H505.173v-60.14h273.49v-35.647H505.173v-97.233h-145.12v97.233H130.866v35.647h229.187v60.14H135.757v35.647h601.857a891.887 891.887 0 0 1-64.068 155.312c-128.376-40.952-266.186-77.834-354.043-55.08a619.28 619.28 0 0 0-69.957 20.475c-96.977 116.694-166.177 256.576-166.177 423.55v18.302h718.36A160.2 160.2 0 0 1 789.1 1024H132.913C159.245 871.539 247.598 453.55 844.836 648.267z" fill="#FFFFFF"></path>
            </svg>
        </div>
        <h2>正在跳转到支付宝授权页面</h2>
        <p>请稍候，系统正在为您连接支付宝...</p>
        <div class="loading"></div>
        <p>如果没有自动跳转，请点击下方按钮</p>
        <a href="' . $full_auth_url . '" id="redirect-link" class="redirect-link">前往支付宝授权</a>
        <div class="timer">页面将在 <span id="countdown">3</span> 秒后自动跳转</div>
    </div>
    <script>
        // 倒计时
        let seconds = 3;
        const countdownElement = document.getElementById("countdown");
        const countdownInterval = setInterval(function() {
            seconds--;
            countdownElement.textContent = seconds;
            if (seconds <= 0) {
                clearInterval(countdownInterval);
                window.location.href = "' . $full_auth_url . '";
            }
        }, 1000);
        
        // 确保页面显示后跳转
        window.onload = function() {
            setTimeout(function() {
                if (seconds > 0) {
                    // 如果倒计时还没结束，等待完成
                    console.log("页面已加载，等待倒计时完成后跳转");
                }
            }, 300);
        };
    </script>
</body>
</html>';
    exit;
    
} catch (Exception $e) {
    echo '<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>重定向失败</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            background-color: #f5f5f5;
            color: #333;
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }
        .container {
            max-width: 480px;
            width: 90%;
            background-color: #fff;
            border-radius: 16px;
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.08);
            padding: 30px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }
        .container::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 6px;
            background: linear-gradient(90deg, #ff4d4f, #ff7a45);
        }
        .error-icon {
            width: 70px;
            height: 70px;
            border-radius: 50%;
            background: #ff4d4f;
            color: white;
            font-size: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            font-weight: bold;
        }
        h2 {
            color: #333;
            font-size: 22px;
            margin-bottom: 16px;
        }
        p {
            margin: 12px 0;
            font-size: 15px;
            color: #666;
            line-height: 1.5;
        }
        .error-details {
            margin: 20px 0;
            padding: 15px;
            background-color: rgba(255, 77, 79, 0.05);
            border-radius: 10px;
            text-align: left;
            border-left: 4px solid #ff4d4f;
        }
        .button {
            display: inline-block;
            background-color: #1677ff;
            color: white;
            padding: 12px 24px;
            border-radius: 8px;
            text-decoration: none;
            margin-top: 20px;
            font-size: 15px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        .button:hover {
            background-color: #0056cc;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }
        @media (max-width: 480px) {
            .container {
                padding: 20px;
            }
            h2 {
                font-size: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="error-icon">!</div>
        <h2>重定向失败</h2>
        <div class="error-details">
            <p>' . htmlspecialchars($e->getMessage()) . '</p>
        </div>
        <a href="/app/#/user/settings/bank-card" class="button">返回</a>
    </div>
</body>
</html>';
    exit;
} 