<?php
// 设置HTTP响应头
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 确保错误处理
ini_set('display_errors', 0);
ini_set('log_errors', 1);
ini_set('error_log', dirname(__DIR__) . '/debug_error.log');
error_reporting(E_ALL);

// 设置自定义错误处理函数
set_error_handler(function($errno, $errstr, $errfile, $errline) {
    $message = date('[Y-m-d H:i:s]') . " [$errno] $errstr in $errfile:$errline";
    error_log($message);
    return true;
});

// 设置自定义异常处理函数
set_exception_handler(function($exception) {
    $message = date('[Y-m-d H:i:s]') . " Uncaught Exception: " . $exception->getMessage() . 
               " in " . $exception->getFile() . ":" . $exception->getLine() . 
               "\nStack trace: " . $exception->getTraceAsString();
    error_log($message);
    
    // 返回JSON错误响应
    header('Content-Type: application/json');
    echo json_encode([
        'code' => 9999,
        'message' => '服务器内部错误',
        'data' => null
    ]);
    exit;
});

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 检查请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode([
        'code' => 1,
        'message' => '请求方法不允许',
        'data' => null
    ]);
    exit;
}

// 生成唯一请求ID
$request_id = uniqid();
error_log("[$request_id] 更新用户资料请求开始");

try {
    // 引入相关函数
    require_once dirname(__DIR__) . '/functions/auth.php';

    // 确保引入配置
    if (!defined('DEBUG_MODE')) {
        require_once dirname(__DIR__) . '/config.php';
    }

    // 获取Authorization头
    $headers = getallheaders();
    $auth_header = isset($headers['Authorization']) ? $headers['Authorization'] : '';
    $token = '';

    error_log("[$request_id] 请求头: " . json_encode($headers));

    if (preg_match('/Bearer\s(\S+)/', $auth_header, $matches)) {
        $token = $matches[1];
        error_log("[$request_id] 获取到令牌: " . substr($token, 0, 10) . "...");
    } else {
        error_log("[$request_id] 未找到令牌，Authorization头: " . $auth_header);
    }

    if (empty($token)) {
        error_log("[$request_id] 令牌为空");
        echo json_encode([
            'code' => 1001,
            'message' => '未授权的请求',
            'data' => null
        ]);
        exit;
    }

    // 验证用户身份
    error_log("[$request_id] 开始验证用户令牌");
    $user = verify_auth($token);

    if (!$user) {
        error_log("[$request_id] 验证失败: 无效的令牌或已过期");
        
        echo json_encode([
            'code' => 1002,
            'message' => '无效的令牌或已过期',
            'data' => null
        ]);
        exit;
    }
    
    // 获取POST数据
    $input = file_get_contents('php://input');
    $data = json_decode($input, true);
    
    error_log("[$request_id] 接收到的数据: " . json_encode($data));
    
    // 验证必要字段
    if (!isset($data['name']) || empty(trim($data['name']))) {
        echo json_encode([
            'code' => 1003,
            'message' => '姓名不能为空',
            'data' => null
        ]);
        exit;
    }
    
    // 验证邮箱格式
    if (isset($data['email']) && !empty($data['email']) && !filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
        echo json_encode([
            'code' => 1004,
            'message' => '邮箱格式不正确',
            'data' => null
        ]);
        exit;
    }
    
    // 验证性别值
    if (isset($data['gender']) && !in_array($data['gender'], [0, 1, 2])) {
        echo json_encode([
            'code' => 1005,
            'message' => '性别值不正确',
            'data' => null
        ]);
        exit;
    }
    
    // 验证生日格式
    if (isset($data['birthday']) && !empty($data['birthday'])) {
        $date = date_parse($data['birthday']);
        if ($date['error_count'] > 0 || !checkdate($date['month'], $date['day'], $date['year'])) {
            echo json_encode([
                'code' => 1006,
                'message' => '生日格式不正确',
                'data' => null
            ]);
            exit;
        }
    }
    
    // 准备更新数据
    $updateFields = [];
    $params = [];
    
    // 姓名
    $updateFields[] = "name = ?";
    $params[] = trim($data['name']);
    
    // 邮箱
    if (isset($data['email'])) {
        $updateFields[] = "email = ?";
        $params[] = $data['email'];
    }
    
    // 生日
    if (isset($data['birthday'])) {
        $updateFields[] = "birthday = ?";
        $params[] = $data['birthday'];
    }
    
    // 性别
    if (isset($data['gender'])) {
        $updateFields[] = "gender = ?";
        $params[] = intval($data['gender']);
    }
    
    // 更新时间
    $updateFields[] = "updated_at = NOW()";
    
    // 只有在有字段需要更新时才执行更新
    if (count($updateFields) > 0) {
        // 创建数据库连接
        $conn = new mysqli($DB_CONFIG['HOST'], $DB_CONFIG['USER'], $DB_CONFIG['PASSWORD'], $DB_CONFIG['DATABASE'], $DB_CONFIG['PORT']);
        if ($conn->connect_error) {
            error_log("[$request_id] 数据库连接失败: " . $conn->connect_error);
            throw new Exception("数据库连接失败");
        }
        $conn->set_charset($DB_CONFIG['CHARSET']);
        
        // 构建SQL查询语句
        $sql = "UPDATE app_users SET " . implode(", ", $updateFields) . " WHERE id = ?";
        $params[] = $user['id'];
        
        error_log("[$request_id] SQL查询: $sql");
        error_log("[$request_id] 参数: " . json_encode($params));
        
        // 准备并执行查询
        $stmt = $conn->prepare($sql);
        if (!$stmt) {
            error_log("[$request_id] 准备SQL语句失败: " . $conn->error);
            throw new Exception("准备SQL语句失败");
        }
        
        // 绑定参数
        $types = str_repeat('s', count($params) - 1) . 'i'; // 最后一个参数是用户ID，是整数
        $bindParams = array_merge([$types], $params);
        $stmt->bind_param(...$bindParams);
        
        // 执行更新
        if (!$stmt->execute()) {
            error_log("[$request_id] 执行SQL失败: " . $stmt->error);
            throw new Exception("执行SQL失败");
        }
        
        // 检查更新结果
        $affectedRows = $stmt->affected_rows;
        error_log("[$request_id] 影响的行数: $affectedRows");
        
        // 关闭数据库连接
        $stmt->close();
        $conn->close();
        
        // 获取更新后的用户信息
        $updatedUser = $user;
        $updatedUser['name'] = $data['name'];
        if (isset($data['email'])) $updatedUser['email'] = $data['email'];
        if (isset($data['birthday'])) $updatedUser['birthday'] = $data['birthday'];
        if (isset($data['gender'])) $updatedUser['gender'] = intval($data['gender']);
        
        // 返回成功响应
        echo json_encode([
            'code' => 0,
            'message' => '更新成功',
            'data' => $updatedUser
        ]);
    } else {
        echo json_encode([
            'code' => 0,
            'message' => '没有数据需要更新',
            'data' => $user
        ]);
    }
} catch (Exception $e) {
    $error_message = "[$request_id] 处理请求时发生异常: " . $e->getMessage() . "\n" . $e->getTraceAsString();
    error_log($error_message);
    
    echo json_encode([
        'code' => 9999,
        'message' => '请求失败: ' . $e->getMessage(),
        'data' => null
    ]);
}
?> 