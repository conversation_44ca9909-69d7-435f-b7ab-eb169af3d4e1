<?php
/**
 * 业务员目标管理API
 * 提供业务员目标相关的数据
 */

// 设置HTTP响应头
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
header('Access-Control-Max-Age: 86400'); // 24小时

// 错误处理设置
ini_set('display_errors', 0);
ini_set('log_errors', 1);
ini_set('error_log', dirname(__DIR__) . '/debug_error.log');
error_reporting(E_ALL);

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 检查请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    echo json_encode([
        'code' => 1,
        'message' => '请求方法不允许',
        'data' => null
    ]);
    exit;
}

// 生成唯一请求ID
$request_id = uniqid();
error_log("[$request_id] 业务员目标数据请求开始");

// 引入Laravel的引导文件
require_once dirname(dirname(dirname(__DIR__))) . '/admin/vendor/autoload.php';
$app = require_once dirname(dirname(dirname(__DIR__))) . '/admin/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Http\Kernel::class);
$response = $kernel->handle(
    $request = Illuminate\Http\Request::capture()
);

// 获取请求中的认证Token
$authHeader = isset($_SERVER['HTTP_AUTHORIZATION']) ? $_SERVER['HTTP_AUTHORIZATION'] : '';
$token = '';

if (preg_match('/Bearer\s(\S+)/', $authHeader, $matches)) {
    $token = $matches[1];
}

// 如果请求头中没有token，尝试从cookie获取
if (empty($token) && isset($_COOKIE['token'])) {
    $token = $_COOKIE['token'];
    error_log("[$request_id] 从cookie中获取token");
}

// 如果还是没有token，尝试从查询参数获取
if (empty($token) && isset($_GET['token'])) {
    $token = $_GET['token'];
    error_log("[$request_id] 从查询参数中获取token");
}

if (empty($token)) {
    http_response_code(401);
    echo json_encode([
        'code' => 1,
        'message' => '未授权访问',
        'data' => null
    ]);
    error_log("[$request_id] 未提供token");
    exit;
}

// 记录token信息（仅记录前8位，保护隐私）
error_log("[$request_id] 使用token: " . substr($token, 0, 8) . '...');

// 获取用户
$userProvider = new Illuminate\Auth\EloquentUserProvider(
    new Illuminate\Hashing\BcryptHasher(), 
    'App\Models\AppUser'
);

$user = null;
try {
    // 尝试使用Laravel Sanctum验证令牌
    $tokenObj = \Laravel\Sanctum\PersonalAccessToken::findToken($token);
    
    if ($tokenObj) {
        $user = $tokenObj->tokenable;
        error_log("[$request_id] 使用Sanctum验证令牌成功");
    } else {
        // Sanctum验证失败，尝试JWT验证
        error_log("[$request_id] Sanctum验证失败，尝试JWT验证");
        
        // 解析JWT令牌
        try {
            // JWT格式: header.payload.signature
            $tokenParts = explode('.', $token);
            if (count($tokenParts) === 3) {
                $payload = json_decode(base64_decode($tokenParts[1]), true);
                
                if (isset($payload['user_id'])) {
                    error_log("[$request_id] JWT令牌包含用户ID: " . $payload['user_id']);
                    
                    // 使用用户ID查找用户
                    $user = \App\Models\AppUser::find($payload['user_id']);
                    
                    if ($user) {
                        error_log("[$request_id] 通过JWT用户ID找到用户");
                    } else {
                        error_log("[$request_id] 找不到JWT中指定的用户");
                    }
                } else {
                    error_log("[$request_id] JWT令牌不包含用户ID");
                }
            } else {
                error_log("[$request_id] 无效的JWT格式");
            }
        } catch (\Exception $jwtEx) {
            error_log("[$request_id] JWT解析错误: " . $jwtEx->getMessage());
        }
    }
    
    if (!$user) {
        http_response_code(401);
        echo json_encode([
            'code' => 1,
            'message' => '无效的访问令牌',
            'data' => null
        ]);
        exit;
    }
} catch (\Exception $e) {
    http_response_code(500);
    echo json_encode([
        'code' => 1,
        'message' => '验证令牌时出错',
        'data' => null
    ]);
    error_log("[$request_id] 验证令牌错误: " . $e->getMessage());
    exit;
}

// 检查用户是否为业务员
if (!$user->hasRole('sales')) {
    http_response_code(403);
    echo json_encode([
        'code' => 1,
        'message' => '您不是业务员，无权访问此资源',
        'data' => null
    ]);
    exit;
}

try {
    // 获取业务员信息
    $salesman = $user->salesman;
    
    // 如果用户没有业务员记录，创建一个新的
    if (!$salesman) {
        $salesman = new \App\Models\Salesman();
        $salesman->user_id = $user->id;
        $salesman->employee_id = 'S' . str_pad($user->id, 6, '0', STR_PAD_LEFT);
        $salesman->title = '业务员';
        $salesman->status = 'active';
        $salesman->save();
    }
    
    // 记录业务员和用户信息，便于调试
    error_log("[$request_id] 用户ID: {$user->id}, 业务员ID: {$salesman->id}");
    
    // 获取分页参数
    $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
    $pageSize = isset($_GET['pageSize']) ? (int)$_GET['pageSize'] : 10;
    
    if ($page < 1) $page = 1;
    if ($pageSize < 1 || $pageSize > 50) $pageSize = 10;
    
    $offset = ($page - 1) * $pageSize;
    
    // 获取当前月度目标
    $currentMonthTarget = \App\Models\SalesmanTarget::getCurrentMonthTarget($salesman->id);
    
    // 如果没有设置目标，创建默认目标
    if (!$currentMonthTarget) {
        $currentMonthTarget = new \App\Models\SalesmanTarget();
        $currentMonthTarget->salesman_id = $salesman->id;
        $currentMonthTarget->target_quantity = 30; // 默认月目标30台
        $currentMonthTarget->target_amount = 30000; // 默认月目标3万元
        $currentMonthTarget->period_type = 'month';
        $currentMonthTarget->period = date('Y-m');
        $currentMonthTarget->start_date = date('Y-m-01');
        $currentMonthTarget->end_date = date('Y-m-t');
        $currentMonthTarget->status = 'in_progress';
        $currentMonthTarget->save();
    }
    
    // 获取月销量成就
    $monthStart = date('Y-m-01');
    $monthEnd = date('Y-m-t');
    $monthDevicesSql = "SELECT COUNT(*) as count FROM tapp_devices WHERE app_user_id = {$user->id} AND DATE(activate_date) BETWEEN '{$monthStart}' AND '{$monthEnd}' AND is_self_use = 0";
    $monthDevicesResults = \DB::select($monthDevicesSql);
    $monthDevicesCount = $monthDevicesResults[0]->count ?: 0;
    
    // 更新月度目标成就
    $currentMonthTarget->achievement = $monthDevicesCount;
    
    // 根据成就更新目标状态
    if ($currentMonthTarget->achievement >= $currentMonthTarget->target_quantity) {
        $currentMonthTarget->status = 'completed';
    } else if ($currentMonthTarget->end_date < now() && $currentMonthTarget->achievement < $currentMonthTarget->target_quantity) {
        $currentMonthTarget->status = 'failed';
    } else {
        $currentMonthTarget->status = 'in_progress';
    }
    
    $currentMonthTarget->save();
    
    // 获取当前年度目标
    $currentYearTarget = \App\Models\SalesmanTarget::getCurrentYearTarget($salesman->id);
    
    // 如果没有设置目标，创建默认目标
    if (!$currentYearTarget) {
        $currentYearTarget = new \App\Models\SalesmanTarget();
        $currentYearTarget->salesman_id = $salesman->id;
        $currentYearTarget->target_quantity = 360; // 默认年目标360台
        $currentYearTarget->target_amount = 360000; // 默认年目标36万元
        $currentYearTarget->period_type = 'year';
        $currentYearTarget->period = date('Y');
        $currentYearTarget->start_date = date('Y-01-01');
        $currentYearTarget->end_date = date('Y-12-31');
        $currentYearTarget->status = 'in_progress';
        $currentYearTarget->save();
    }
    
    // 获取年销量成就
    $yearStart = date('Y-01-01');
    $yearEnd = date('Y-12-31');
    $yearDevicesSql = "SELECT COUNT(*) as count FROM tapp_devices WHERE app_user_id = {$user->id} AND DATE(activate_date) BETWEEN '{$yearStart}' AND '{$yearEnd}' AND is_self_use = 0";
    $yearDevicesResults = \DB::select($yearDevicesSql);
    $yearDevicesCount = $yearDevicesResults[0]->count ?: 0;
    
    // 更新年度目标成就
    $currentYearTarget->achievement = $yearDevicesCount;
    
    // 根据成就更新目标状态
    if ($currentYearTarget->achievement >= $currentYearTarget->target_quantity) {
        $currentYearTarget->status = 'completed';
    } else if ($currentYearTarget->end_date < now() && $currentYearTarget->achievement < $currentYearTarget->target_quantity) {
        $currentYearTarget->status = 'failed';
    } else {
        $currentYearTarget->status = 'in_progress';
    }
    
    $currentYearTarget->save();
    
    // 获取历史目标列表
    $historicalTargets = \App\Models\SalesmanTarget::where('salesman_id', $salesman->id)
        ->where(function($query) use ($currentMonthTarget, $currentYearTarget) {
            // 排除当前月度和年度目标
            $query->where('id', '!=', $currentMonthTarget->id)
                  ->where('id', '!=', $currentYearTarget->id);
        })
        ->orderBy('period', 'desc')
        ->offset($offset)
        ->limit($pageSize + 1) // 获取多一条用于判断是否有更多数据
        ->get();
    
    // 检查是否有更多数据
    $hasMore = count($historicalTargets) > $pageSize;
    
    // 如果取出的数据超过了pageSize，就去掉最后一条
    if ($hasMore) {
        $historicalTargets = $historicalTargets->slice(0, $pageSize);
    }
    
    // 转换为数组
    $historicalTargets = $historicalTargets->toArray();
    
    // 准备返回的响应数据
    $targetData = [
        'currentMonthTarget' => $currentMonthTarget->toArray(),
        'currentYearTarget' => $currentYearTarget->toArray(),
        'historicalTargets' => $historicalTargets,
        'hasMore' => $hasMore,
        'pagination' => [
            'page' => $page,
            'pageSize' => $pageSize,
            'total' => \App\Models\SalesmanTarget::where('salesman_id', $salesman->id)
                        ->where('id', '!=', $currentMonthTarget->id)
                        ->where('id', '!=', $currentYearTarget->id)
                        ->count()
        ]
    ];
    
    // 返回成功响应
    echo json_encode([
        'code' => 0,
        'message' => '获取业务员目标数据成功',
        'data' => $targetData
    ]);
    
} catch (\Exception $e) {
    // 记录错误
    error_log("[$request_id] 获取业务员目标数据出错: " . $e->getMessage());
    error_log("[$request_id] 错误详情: " . $e->getTraceAsString());
    
    // 返回错误响应
    http_response_code(500);
    echo json_encode([
        'code' => 1,
        'message' => '获取业务员目标数据失败',
        'data' => null
    ]);
} 