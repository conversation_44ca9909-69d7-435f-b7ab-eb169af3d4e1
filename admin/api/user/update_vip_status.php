<?php
/**
 * 更新用户VIP状态API
 * 简化版本 - 降低错误风险
 */

// 设置响应头
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 引入必要的文件
require_once dirname(__DIR__) . '/config.php';
require_once dirname(__DIR__) . '/functions/logger.php';

// 错误日志记录
function logError($message, $data = []) {
    error_log("VIP状态更新错误: " . $message . " - 数据: " . json_encode($data));
    if (function_exists('log_message')) {
        log_message("VIP状态更新错误: " . $message, LOG_ERROR);
    }
}

// 获取请求数据
$input = file_get_contents('php://input');
$data = json_decode($input, true);

// 简单参数验证
if (!isset($data['user_id']) || !isset($data['is_vip'])) {
    echo json_encode(['code' => 1, 'msg' => '参数不完整']);
    exit;
}

// 提取参数
$user_id = (int)$data['user_id'];
$is_vip = (int)$data['is_vip'];
$order_no = isset($data['order_no']) ? $data['order_no'] : '';

try {
    // 连接数据库
    $conn = get_db_connection();
    if (!$conn) {
        echo json_encode(['code' => 500, 'msg' => '数据库连接失败']);
        exit;
    }
    
    // 简单更新操作 - 无需过多验证
    $sql = "UPDATE app_users SET is_vip = ?, vip_at = NOW() WHERE id = ?";
    $stmt = $conn->prepare($sql);
    if (!$stmt) {
        logError("SQL准备失败: " . $conn->error, ['user_id' => $user_id]);
        echo json_encode(['code' => 500, 'msg' => 'SQL准备失败']);
        exit;
    }
    
    $stmt->bind_param("ii", $is_vip, $user_id);
    $success = $stmt->execute();
    
    if (!$success) {
        logError("SQL执行失败: " . $stmt->error, ['user_id' => $user_id]);
        echo json_encode(['code' => 500, 'msg' => 'SQL执行失败']);
    } else {
        // 成功更新
        error_log("成功更新用户 $user_id 的VIP状态为 $is_vip, 影响行数: " . $stmt->affected_rows);
        echo json_encode(['code' => 0, 'msg' => '更新成功']);
    }
    
    $stmt->close();
    $conn->close();
    
} catch (Exception $e) {
    logError("异常: " . $e->getMessage(), ['user_id' => $user_id]);
    echo json_encode(['code' => 500, 'msg' => '系统异常']);
}
?> 