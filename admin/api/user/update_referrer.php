<?php
/**
 * 更新用户推荐人ID
 * 
 * 请求方式: POST
 * 请求参数:
 *   - referrer_id: 推荐人ID
 * 
 * 返回数据:
 *   - code: 0=成功, 非0=失败
 *   - message: 提示信息
 */

// 开启输出缓冲，防止响应头截断
ob_start();

// 设置响应头
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 抑制错误显示，仅记录到日志
ini_set('display_errors', 0);
error_reporting(E_ALL);

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    ob_end_flush();
    exit;
}

// 引入配置文件
require_once __DIR__ . '/../config.php';
require_once __DIR__ . '/../functions/auth.php';

// 引入日志模块
require_once __DIR__ . '/../functions/logger.php';

// 获取当前用户ID
$currentUserId = null;
$headers = getallheaders();
$token = isset($headers['Authorization']) ? str_replace('Bearer ', '', $headers['Authorization']) : '';

if (empty($token)) {
    echo json_encode(['code' => 401, 'message' => '请先登录']);
    exit;
}

// 验证用户身份
$user = verify_auth($token);
if (!$user) {
    echo json_encode(['code' => 401, 'message' => '登录已过期，请重新登录']);
    exit;
}

$currentUserId = $user['id'];

// 获取请求参数
$referrerId = 0;
if (isset($_POST['referrer_id'])) {
    $referrerId = (int)$_POST['referrer_id'];
} else {
    // 尝试从JSON请求体中获取
    $requestBody = file_get_contents('php://input');
    if (!empty($requestBody)) {
        $jsonData = json_decode($requestBody, true);
        if (isset($jsonData['referrer_id'])) {
            $referrerId = (int)$jsonData['referrer_id'];
        }
    }
}

// 调试输出
if (DEBUG_MODE) {
    error_log("更新推荐人ID API请求: 用户ID={$currentUserId}, 推荐人ID={$referrerId}");
    error_log("POST数据: " . print_r($_POST, true));
    error_log("请求体: " . $requestBody);
}

// 防止自己推荐自己
if ($referrerId == $currentUserId) {
    echo json_encode(['code' => 2, 'message' => '不能将自己设为推荐人']);
    exit;
}

// 连接数据库
try {
    $conn = get_db_connection();
    if (!$conn) {
        error_log("数据库连接失败");
        echo json_encode(['code' => 500, 'message' => '服务器内部错误']);
        exit;
    }
} catch (Exception $e) {
    error_log("数据库连接异常: " . $e->getMessage());
    echo json_encode(['code' => 500, 'message' => '数据库服务异常']);
    exit;
}

// 开始事务
$conn->begin_transaction();

try {
    // 验证推荐人是否存在
    if ($referrerId > 0) {
        $stmt = $conn->prepare("SELECT id FROM app_users WHERE id = ? LIMIT 1");
        $stmt->bind_param("i", $referrerId);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result->num_rows === 0) {
            $conn->rollback();
            echo json_encode(['code' => 3, 'message' => '推荐人不存在']);
            exit;
        }
        
        $stmt->close();
    }
    
    // 检查用户是否已有推荐人
    $stmt = $conn->prepare("SELECT referrer_id FROM app_users WHERE id = ? LIMIT 1");
    $stmt->bind_param("i", $currentUserId);
    $stmt->execute();
    $result = $stmt->get_result();
    $userData = $result->fetch_assoc();
    $stmt->close();
    
    $currentReferrerId = $userData['referrer_id'] ?? 0;
    
    // 如果用户已经有推荐人，且不是更改为0，则不允许修改
    if ($currentReferrerId > 0 && $referrerId > 0 && $currentReferrerId != $referrerId) {
        $conn->rollback();
        echo json_encode(['code' => 4, 'message' => '您已经有推荐人，不能修改']);
        exit;
    }
    
    // 更新用户的推荐人ID
    $stmt = $conn->prepare("UPDATE app_users SET referrer_id = ?, updated_at = NOW() WHERE id = ?");
    $stmt->bind_param("ii", $referrerId, $currentUserId);
    
    if (!$stmt->execute()) {
        $conn->rollback();
        error_log("更新推荐人失败: " . $stmt->error);
        echo json_encode(['code' => 500, 'message' => '更新失败，请稍后重试']);
        exit;
    }
    
    $stmt->close();
    
    // 提交事务
    $conn->commit();
    
    echo json_encode([
        'code' => 0,
        'message' => '推荐人更新成功',
        'data' => ['referrer_id' => $referrerId]
    ]);
} catch (Exception $e) {
    $conn->rollback();
    error_log("更新推荐人异常: " . $e->getMessage());
    echo json_encode(['code' => 500, 'message' => '服务器内部错误']);
} finally {
    if (isset($conn)) {
        $conn->close();
    }
    
    // 结束输出缓冲并发送
    ob_end_flush();
} 