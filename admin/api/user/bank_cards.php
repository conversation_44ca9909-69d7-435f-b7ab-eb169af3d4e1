<?php
/**
 * 获取银行卡列表API
 * 
 * 获取当前用户绑定的银行卡列表
 */

// 设置响应头
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 检查请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    echo json_encode([
        'code' => 405,
        'message' => '请求方法不允许',
        'data' => null
    ]);
    exit;
}

// 引入必要的功能文件
require_once dirname(__DIR__) . '/functions/auth.php';
require_once dirname(__DIR__) . '/functions/db.php';

// 验证用户身份
$user_id = verify_token();
if (!$user_id) {
    echo json_encode([
        'code' => 401,
        'message' => '未授权，请先登录',
        'data' => null
    ]);
    exit;
}

// 连接数据库
$conn = get_db_connection();

// 查询用户的银行卡列表
$stmt = $conn->prepare("SELECT 
                            id, 
                            card_number, 
                            bank_name, 
                            bank_code,
                            holder_name,
                            phone,
                            is_default,
                            create_time,
                            update_time
                        FROM 
                            user_bank_cards 
                        WHERE 
                            user_id = ? 
                        ORDER BY 
                            is_default DESC, 
                            create_time DESC");
$stmt->bind_param("i", $user_id);
$stmt->execute();
$result = $stmt->get_result();

$bank_cards = [];
while ($row = $result->fetch_assoc()) {
    // 银行卡号脱敏处理
    $card_number = $row['card_number'];
    $masked_card_number = substr($card_number, 0, 4) . ' **** **** ' . substr($card_number, -4);
    
    // 手机号脱敏处理
    $phone = $row['phone'];
    $masked_phone = substr($phone, 0, 3) . '****' . substr($phone, -4);
    
    $bank_cards[] = [
        'id' => $row['id'],
        'cardNumber' => $masked_card_number,
        'bankName' => $row['bank_name'],
        'bankCode' => $row['bank_code'],
        'holderName' => $row['holder_name'],
        'phone' => $masked_phone,
        'isDefault' => (bool)$row['is_default'],
        'createTime' => $row['create_time'],
        'updateTime' => $row['update_time']
    ];
}

$stmt->close();
$conn->close();

echo json_encode([
    'code' => 0,
    'message' => '获取成功',
    'data' => $bank_cards
]); 