<?php
header('Content-Type: application/vnd.ms-excel; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    echo json_encode(['code' => 405, 'message' => '方法不允许']);
    exit;
}

require_once '../config.php';

try {
    $month = $_GET['month'] ?? '';
    
    if (empty($month)) {
        echo json_encode(['code' => 400, 'message' => '请选择月份']);
        exit;
    }
    
    // 解析月份
    list($year, $monthNum) = explode('-', $month);
    $startDate = "$year-$monthNum-01";
    $endDate = date('Y-m-t', strtotime($startDate));
    
    // 设置下载文件名
    $filename = "VIP分红表_{$year}年{$monthNum}月_" . date('YmdHis') . ".csv";
    header("Content-Disposition: attachment; filename=\"$filename\"");
    
    // 输出CSV头部
    echo "\xEF\xBB\xBF"; // UTF-8 BOM
    
    // 重新计算分红数据
    include_once 'calculate-monthly.php';
    
    $poolData = calculateMonthlyPool($pdo, $startDate, $endDate);
    $qualifiedUsers = getQualifiedUsers($pdo, $startDate, $endDate, $poolData);
    $userSummary = calculateUserSummary($qualifiedUsers);
    
    // 输出奖金池概览
    echo "VIP分红管理系统 - {$year}年{$monthNum}月分红报表\n\n";
    echo "奖金池概览\n";
    echo "项目,数量,金额(元)\n";
    echo "新增VIP人数,{$poolData['newVipCount']},\n";
    echo "新增充值设备,{$poolData['newDeviceCount']},\n";
    echo "VIP招募分红池,,{$poolData['vipPool']}\n";
    echo "充值套餐分红池,,{$poolData['rechargePool']}\n";
    echo "总奖金池,,{$poolData['totalPool']}\n\n";
    
    // 输出个人分红汇总
    echo "个人分红汇总\n";
    echo "用户ID,用户姓名,手机号,VIP招募分红(元),充值套餐分红(元),分红总计(元),状态\n";
    
    foreach ($userSummary as $user) {
        echo "{$user['user_id']},{$user['user_name']},{$user['phone']},";
        echo number_format($user['total_vip_dividend'], 2) . ",";
        echo number_format($user['total_recharge_dividend'], 2) . ",";
        echo number_format($user['total_dividend'], 2) . ",";
        echo ($user['status'] === 'settled' ? '已结算' : '待结算') . "\n";
    }
    
    echo "\n";
    
    // 输出VIP招募分红详情
    echo "VIP招募分红详情\n";
    
    // 初级分红
    if (!empty($qualifiedUsers['vip']['junior'])) {
        echo "\n初级分红 (团队VIP满3人)\n";
        echo "用户ID,用户姓名,手机号,团队VIP数,本月直推VIP,分红金额(元),状态\n";
        foreach ($qualifiedUsers['vip']['junior'] as $user) {
            echo "{$user['user_id']},{$user['user_name']},{$user['phone']},";
            echo "{$user['team_vip_count']},{$user['month_direct_vip']},";
            echo number_format($user['vip_junior_dividend'], 2) . ",";
            echo ($user['status'] === 'settled' ? '已结算' : '待结算') . "\n";
        }
    }
    
    // 中级分红
    if (!empty($qualifiedUsers['vip']['middle'])) {
        echo "\n中级分红 (团队VIP满10人)\n";
        echo "用户ID,用户姓名,手机号,团队VIP数,本月直推VIP,分红金额(元),状态\n";
        foreach ($qualifiedUsers['vip']['middle'] as $user) {
            echo "{$user['user_id']},{$user['user_name']},{$user['phone']},";
            echo "{$user['team_vip_count']},{$user['month_direct_vip']},";
            echo number_format($user['vip_middle_dividend'], 2) . ",";
            echo ($user['status'] === 'settled' ? '已结算' : '待结算') . "\n";
        }
    }
    
    // 高级分红
    if (!empty($qualifiedUsers['vip']['senior'])) {
        echo "\n高级分红 (团队VIP满30人且本月直推≠0)\n";
        echo "用户ID,用户姓名,手机号,团队VIP数,本月直推VIP,直推占比,分红金额(元),状态\n";
        foreach ($qualifiedUsers['vip']['senior'] as $user) {
            echo "{$user['user_id']},{$user['user_name']},{$user['phone']},";
            echo "{$user['team_vip_count']},{$user['month_direct_vip']},";
            echo number_format($user['direct_ratio'] * 100, 1) . "%,";
            echo number_format($user['vip_senior_dividend'], 2) . ",";
            echo ($user['status'] === 'settled' ? '已结算' : '待结算') . "\n";
        }
    }
    
    echo "\n";
    
    // 输出充值套餐分红详情
    echo "充值套餐分红详情\n";
    
    // 初级分红
    if (!empty($qualifiedUsers['recharge']['junior'])) {
        echo "\n初级分红 (团队充值满10台)\n";
        echo "用户ID,用户姓名,手机号,团队充值数,本月直推充值,分红金额(元),状态\n";
        foreach ($qualifiedUsers['recharge']['junior'] as $user) {
            echo "{$user['user_id']},{$user['user_name']},{$user['phone']},";
            echo "{$user['team_recharge_count']},{$user['month_direct_recharge']},";
            echo number_format($user['recharge_junior_dividend'], 2) . ",";
            echo ($user['status'] === 'settled' ? '已结算' : '待结算') . "\n";
        }
    }
    
    // 中级分红
    if (!empty($qualifiedUsers['recharge']['middle'])) {
        echo "\n中级分红 (团队充值满30台)\n";
        echo "用户ID,用户姓名,手机号,团队充值数,本月直推充值,分红金额(元),状态\n";
        foreach ($qualifiedUsers['recharge']['middle'] as $user) {
            echo "{$user['user_id']},{$user['user_name']},{$user['phone']},";
            echo "{$user['team_recharge_count']},{$user['month_direct_recharge']},";
            echo number_format($user['recharge_middle_dividend'], 2) . ",";
            echo ($user['status'] === 'settled' ? '已结算' : '待结算') . "\n";
        }
    }
    
    // 高级分红
    if (!empty($qualifiedUsers['recharge']['senior'])) {
        echo "\n高级分红 (团队充值满80台且本月直推≠0)\n";
        echo "用户ID,用户姓名,手机号,团队充值数,本月直推充值,直推占比,分红金额(元),状态\n";
        foreach ($qualifiedUsers['recharge']['senior'] as $user) {
            echo "{$user['user_id']},{$user['user_name']},{$user['phone']},";
            echo "{$user['team_recharge_count']},{$user['month_direct_recharge']},";
            echo number_format($user['direct_ratio'] * 100, 1) . "%,";
            echo number_format($user['recharge_senior_dividend'], 2) . ",";
            echo ($user['status'] === 'settled' ? '已结算' : '待结算') . "\n";
        }
    }
    
    echo "\n\n";
    echo "导出时间: " . date('Y-m-d H:i:s') . "\n";
    echo "系统: 点点够VIP分红管理系统\n";
    
} catch (Exception $e) {
    error_log("导出分红数据错误: " . $e->getMessage());
    header('Content-Type: application/json; charset=utf-8');
    echo json_encode(['code' => 500, 'message' => '导出失败: ' . $e->getMessage()]);
}
?> 