<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['code' => 405, 'message' => '方法不允许']);
    exit;
}

require_once '../config.php';

try {
    // 获取请求数据
    $input = json_decode(file_get_contents('php://input'), true);
    $month = $input['month'] ?? '';
    
    if (empty($month)) {
        echo json_encode(['code' => 400, 'message' => '请选择月份']);
        exit;
    }
    
    // 解析月份
    list($year, $monthNum) = explode('-', $month);
    $startDate = "$year-$monthNum-01";
    $endDate = date('Y-m-t', strtotime($startDate));
    
    // 开始事务
    $pdo->beginTransaction();
    
    try {
        // 重新计算分红数据
        include_once 'calculate-monthly.php';
        
        $poolData = calculateMonthlyPool($pdo, $startDate, $endDate);
        $qualifiedUsers = getQualifiedUsers($pdo, $startDate, $endDate, $poolData);
        $userSummary = calculateUserSummary($qualifiedUsers);
        
        // 检查是否已经结算过
        $checkStmt = $pdo->prepare("
            SELECT COUNT(*) FROM vip_dividend_records 
            WHERE settlement_month = ? AND status = 'settled'
        ");
        $checkStmt->execute([$month]);
        $settledCount = $checkStmt->fetchColumn();
        
        if ($settledCount > 0) {
            echo json_encode(['code' => 400, 'message' => '该月份已经结算过，请勿重复结算']);
            exit;
        }
        
        // 清除该月份的旧记录
        $deleteStmt = $pdo->prepare("
            DELETE FROM vip_dividend_records 
            WHERE settlement_month = ?
        ");
        $deleteStmt->execute([$month]);
        
        $settledUsers = 0;
        $totalAmount = 0;
        
        // 插入分红记录
        $insertStmt = $pdo->prepare("
            INSERT INTO vip_dividend_records (
                user_id, settlement_month, dividend_type, dividend_level,
                amount, team_count, direct_count, month_direct_count,
                status, created_at, settled_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'settled', NOW(), NOW())
        ");
        
        // 处理VIP分红
        foreach ($qualifiedUsers['vip'] as $level => $users) {
            foreach ($users as $user) {
                $amount = 0;
                switch ($level) {
                    case 'junior':
                        $amount = $user['vip_junior_dividend'] ?? 0;
                        break;
                    case 'middle':
                        $amount = $user['vip_middle_dividend'] ?? 0;
                        break;
                    case 'senior':
                        $amount = $user['vip_senior_dividend'] ?? 0;
                        break;
                }
                
                if ($amount > 0) {
                    $insertStmt->execute([
                        $user['user_id'],
                        $month,
                        'vip',
                        $level,
                        $amount,
                        $user['team_vip_count'],
                        $user['direct_vip_count'],
                        $user['month_direct_vip'],
                    ]);
                    
                    $settledUsers++;
                    $totalAmount += $amount;
                    
                    // 更新用户余额
                    updateUserBalance($pdo, $user['user_id'], $amount, "VIP{$level}分红", $month);
                }
            }
        }
        
        // 处理充值分红
        foreach ($qualifiedUsers['recharge'] as $level => $users) {
            foreach ($users as $user) {
                $amount = 0;
                switch ($level) {
                    case 'junior':
                        $amount = $user['recharge_junior_dividend'] ?? 0;
                        break;
                    case 'middle':
                        $amount = $user['recharge_middle_dividend'] ?? 0;
                        break;
                    case 'senior':
                        $amount = $user['recharge_senior_dividend'] ?? 0;
                        break;
                }
                
                if ($amount > 0) {
                    $insertStmt->execute([
                        $user['user_id'],
                        $month,
                        'recharge',
                        $level,
                        $amount,
                        $user['team_recharge_count'],
                        $user['direct_recharge_count'],
                        $user['month_direct_recharge'],
                    ]);
                    
                    $settledUsers++;
                    $totalAmount += $amount;
                    
                    // 更新用户余额
                    updateUserBalance($pdo, $user['user_id'], $amount, "充值{$level}分红", $month);
                }
            }
        }
        
        // 记录结算日志
        $logStmt = $pdo->prepare("
            INSERT INTO vip_dividend_settlement_logs (
                settlement_month, total_users, total_amount, 
                vip_pool, recharge_pool, status, created_at
            ) VALUES (?, ?, ?, ?, ?, 'completed', NOW())
        ");
        $logStmt->execute([
            $month,
            $settledUsers,
            $totalAmount,
            $poolData['vipPool'],
            $poolData['rechargePool']
        ]);
        
        // 提交事务
        $pdo->commit();
        
        echo json_encode([
            'code' => 0,
            'message' => '分红结算完成',
            'data' => [
                'settledUsers' => $settledUsers,
                'totalAmount' => $totalAmount,
                'month' => $month
            ]
        ]);
        
    } catch (Exception $e) {
        // 回滚事务
        $pdo->rollBack();
        throw $e;
    }
    
} catch (Exception $e) {
    error_log("VIP分红结算错误: " . $e->getMessage());
    echo json_encode(['code' => 500, 'message' => '结算失败: ' . $e->getMessage()]);
}

/**
 * 更新用户余额
 */
function updateUserBalance($pdo, $userId, $amount, $description, $month) {
    // 更新用户余额
    $updateBalanceStmt = $pdo->prepare("
        UPDATE users SET balance = balance + ? WHERE id = ?
    ");
    $updateBalanceStmt->execute([$amount, $userId]);
    
    // 记录余额变动
    $insertBalanceLogStmt = $pdo->prepare("
        INSERT INTO user_balance_logs (
            user_id, type, amount, balance_before, balance_after,
            description, created_at
        ) VALUES (?, 'dividend', ?, 
            (SELECT balance - ? FROM users WHERE id = ?),
            (SELECT balance FROM users WHERE id = ?),
            ?, NOW())
    ");
    $insertBalanceLogStmt->execute([
        $userId, $amount, $amount, $userId, $userId, 
        "{$description}({$month})"
    ]);
}
?> 