<?php
/**
 * 点点够App API路由器
 * 处理所有.php结尾的请求，转发到index.php
 */

// 设置响应头
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 获取请求路径
$request_uri = $_SERVER['REQUEST_URI'];
$script_name = basename($_SERVER['SCRIPT_NAME']);

// 记录请求日志
error_log("API请求: $request_uri, 脚本: $script_name");

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 解析请求中的action
$action = '';

// 如果是index.php，直接从query参数获取action
if ($script_name === 'index.php' && isset($_GET['action'])) {
    $action = $_GET['action'];
}
// 否则从文件名解析action
else {
    // 移除.php后缀获取action
    $action = str_replace('.php', '', $script_name);
    
    // 特殊情况处理
    if ($action === 'router') {
        // 如果直接访问router.php，返回错误
        echo json_encode([
            'code' => 400,
            'message' => '无效的API请求',
            'data' => null
        ]);
        exit;
    }
}

// 将action参数添加到$_GET
$_GET['action'] = $action;

// 加载并执行index.php
require_once 'index.php'; 