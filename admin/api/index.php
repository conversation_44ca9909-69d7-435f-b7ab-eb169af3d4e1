<?php
// 设置响应头
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 引入配置
require_once 'config.php';

// 引入函数库
require_once 'functions/mall.php';
require_once 'functions/banners.php';
require_once 'functions/products.php';
require_once 'functions/categories.php';

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 获取请求参数
$action = isset($_GET['action']) ? $_GET['action'] : '';

// 路由到对应的处理函数
switch ($action) {
    case 'mall_info':
        echo json_encode(get_mall_info());
        break;
    case 'banners':
        echo json_encode(get_banners());
        break;
    case 'products':
        // 检查是否请求精选商品
        $featured = isset($_GET['featured']) && $_GET['featured'] == 1;
        $limit = isset($_GET['limit']) ? intval($_GET['limit']) : 10;
        
        if ($featured) {
            // 获取精选商品
            echo json_encode(get_products($limit));
        } else {
            // 获取普通商品列表
            echo json_encode(get_products());
        }
        break;
    case 'product_list':
        // 获取请求参数
        $page = isset($_GET['page']) ? intval($_GET['page']) : 1;
        $page_size = isset($_GET['page_size']) ? intval($_GET['page_size']) : 10;
        $sort = isset($_GET['sort']) ? $_GET['sort'] : 'id';
        $sort_direction = isset($_GET['sort_direction']) ? $_GET['sort_direction'] : 'desc';
        $category_id = isset($_GET['category_id']) ? intval($_GET['category_id']) : 0;
        $keyword = isset($_GET['keyword']) ? $_GET['keyword'] : '';
        
        echo json_encode(get_product_list($page, $page_size, $sort, $sort_direction, $category_id, $keyword));
        break;
    case 'categories':
        // 检查是否请求单个分类
        $id = isset($_GET['id']) ? intval($_GET['id']) : 0;
        
        if ($id > 0) {
            // 获取单个分类详情
            echo json_encode(get_category($id));
        } else {
            // 获取分类列表
            echo json_encode(get_categories());
        }
        break;
    default:
        echo json_encode([
            'code' => 404,
            'message' => '未找到请求的接口',
            'data' => null
        ]);
}