<?php
// 设置HTTP响应头
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 检查请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode([
        'code' => 1,
        'message' => '请求方法不允许',
        'data' => null
    ]);
    exit;
}

// 引入必要的函数库
require_once __DIR__ . '/functions/db.php';
require_once __DIR__ . '/config.php';

// 获取请求参数
$data = json_decode(file_get_contents('php://input'), true);

if (!isset($data['phone']) || !isset($data['type'])) {
    echo json_encode([
        'code' => 1,
        'message' => '缺少必需参数',
        'data' => null
    ]);
    exit;
}

$phone = $data['phone'];
$type = $data['type'];

// 验证手机号格式
if (!preg_match('/^1[3-9]\d{9}$/', $phone)) {
    echo json_encode([
        'code' => 1,
        'message' => '手机号格式不正确',
        'data' => null
    ]);
    exit;
}

// 验证验证码类型
$allowed_types = ['login', 'register', 'reset', 'bind'];
if (!in_array($type, $allowed_types)) {
    echo json_encode([
        'code' => 1,
        'message' => '验证码类型不正确',
        'data' => null
    ]);
    exit;
}

// 检查是否在冷却时间内（60秒）
$conn = get_db_connection();
if (!$conn) {
    echo json_encode([
        'code' => 500,
        'message' => '数据库连接失败',
        'data' => null
    ]);
    exit;
}

$stmt = $conn->prepare("SELECT id FROM sms_codes WHERE phone = ? AND type = ? AND created_at > DATE_SUB(NOW(), INTERVAL 60 SECOND)");
$stmt->bind_param("ss", $phone, $type);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows > 0) {
    $conn->close();
    echo json_encode([
        'code' => 1009,
        'message' => '发送过于频繁，请稍后再试',
        'data' => null
    ]);
    exit;
}

// 生成6位随机验证码
$code = sprintf("%06d", mt_rand(0, 999999));

// 保存验证码到数据库 sms_codes 表
$stmt = $conn->prepare("INSERT INTO sms_codes (phone, code, type, is_used, created_at) VALUES (?, ?, ?, 0, NOW())");
$stmt->bind_param("sss", $phone, $code, $type);

if (!$stmt->execute()) {
    $error = $stmt->error;
    $stmt->close();
    $conn->close();
    
    echo json_encode([
        'code' => 500,
        'message' => '保存验证码失败: ' . $error,
        'data' => null
    ]);
    exit;
}

$code_id = $conn->insert_id;

// 准备发送短信
$request_data = json_encode($data);
$request_ip = $_SERVER['REMOTE_ADDR'];
$status = 0; // 默认为失败
$channel = 'aliyun';
$message = "";

// 阿里云短信发送逻辑
global $SMS_CONFIG;
$response_data = ""; // 用于存储短信API响应
$debug_logs = []; // 调试日志

try {
    $debug_logs[] = "开始发送短信验证码，手机号: $phone, 验证码: $code, 类型: $type";
    error_log("开始发送短信验证码，手机号: $phone, 验证码: $code, 类型: $type");
    
    // 组装发送短信的请求参数
    $accessKeyId = $SMS_CONFIG['ACCESS_KEY'];
    $accessKeySecret = $SMS_CONFIG['ACCESS_SECRET'];
    $signName = $SMS_CONFIG['SIGN_NAME'];
    $templateCode = $SMS_CONFIG['TEMPLATE_CODE'];
    $templateParam = json_encode(['code' => $code]);
    
    // 记录详细日志
    $debug_logs[] = "阿里云短信参数: AccessKey=$accessKeyId, Sign=$signName, Template=$templateCode, Param=$templateParam";
    error_log("阿里云短信参数: AccessKey=$accessKeyId, Sign=$signName, Template=$templateCode, Param=$templateParam");
    
    // 组装请求参数
    $params = [
        'AccessKeyId' => $accessKeyId,
        'Action' => 'SendSms',
        'Format' => 'JSON',
        'RegionId' => 'cn-hangzhou',
        'SignName' => $signName,
        'SignatureMethod' => 'HMAC-SHA1',
        'SignatureNonce' => uniqid(),
        'SignatureVersion' => '1.0',
        'TemplateCode' => $templateCode,
        'TemplateParam' => $templateParam,
        'Timestamp' => gmdate('Y-m-d\TH:i:s\Z'),
        'Version' => '2017-05-25',
        'PhoneNumbers' => $phone
    ];
    
    // 排序参数
    ksort($params);
    
    // 组装待签名字符串
    $stringToSign = 'GET&' . urlencode('/') . '&';
    $tmp = [];
    foreach ($params as $key => $value) {
        $tmp[] = urlencode($key) . '=' . urlencode($value);
    }
    $stringToSign .= urlencode(implode('&', $tmp));
    
    // 计算签名
    $signature = base64_encode(hash_hmac('sha1', $stringToSign, $accessKeySecret . '&', true));
    $params['Signature'] = $signature;
    
    // 构建请求URL
    $queryString = http_build_query($params);
    $url = "https://dysmsapi.aliyuncs.com/?" . $queryString;
    
    // 记录请求URL（去掉敏感信息）
    $logUrl = preg_replace('/AccessKeyId=[^&]*&/', 'AccessKeyId=***&', $url);
    $logUrl = preg_replace('/Signature=[^&]*&/', 'Signature=***&', $logUrl);
    $debug_logs[] = "阿里云短信请求URL: $logUrl";
    error_log("阿里云短信请求URL: $logUrl");
    
    // 发送HTTP请求
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10); // 设置超时时间
    curl_setopt($ch, CURLOPT_VERBOSE, true); // 开启详细日志
    
    $verbose = fopen('php://temp', 'w+');
    curl_setopt($ch, CURLOPT_STDERR, $verbose);
    
    $response = curl_exec($ch);
    $curl_errno = curl_errno($ch);
    $curl_error = curl_error($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    
    // 获取详细日志
    rewind($verbose);
    $verboseLog = stream_get_contents($verbose);
    fclose($verbose);
    $debug_logs[] = "CURL详细日志: " . $verboseLog;
    error_log("CURL详细日志: " . $verboseLog);
    
    curl_close($ch);
    
    // 检查cURL错误
    if ($curl_errno) {
        $debug_logs[] = "cURL Error ($curl_errno): $curl_error";
        error_log("cURL Error ($curl_errno): $curl_error");
        throw new Exception("cURL Error ($curl_errno): $curl_error");
    }
    
    // 检查HTTP状态码
    $debug_logs[] = "HTTP状态码: $http_code";
    error_log("HTTP状态码: $http_code");
    if ($http_code != 200) {
        throw new Exception("HTTP Error: $http_code");
    }
    
    // 解析响应
    $debug_logs[] = "响应内容: $response";
    error_log("响应内容: $response");
    $responseObj = json_decode($response, true);
    if (!$responseObj) {
        throw new Exception("解析响应失败: $response");
    }
    
    // 记录完整响应
    $response_data = $response;
    $debug_logs[] = "阿里云短信响应: " . json_encode($responseObj, JSON_UNESCAPED_UNICODE);
    error_log("阿里云短信响应: " . json_encode($responseObj, JSON_UNESCAPED_UNICODE));
    
    // 检查响应状态
    if (isset($responseObj['Code']) && $responseObj['Code'] == 'OK') {
        // 短信发送成功
        $status = 1; // 设置为成功
        $message = '验证码发送成功';
        $debug_logs[] = "短信发送成功: BizId=" . $responseObj['BizId'];
        error_log("短信发送成功: BizId=" . $responseObj['BizId']);
    } else {
        // 短信发送失败
        $errorCode = $responseObj['Code'] ?? 'Unknown';
        $errorMessage = $responseObj['Message'] ?? 'Unknown error';
        $debug_logs[] = "阿里云短信错误: $errorCode - $errorMessage";
        error_log("阿里云短信错误: $errorCode - $errorMessage");
        throw new Exception("阿里云短信错误: $errorCode - $errorMessage");
    }
} catch (Exception $e) {
    // 捕获所有异常
    $status = 0; // 确保状态为失败
    $message = '短信发送失败: ' . $e->getMessage();
    $debug_logs[] = "短信发送异常: " . $e->getMessage();
    error_log("短信发送异常: " . $e->getMessage());
    
    // 在开发模式下，仍然返回成功，以便测试
    if (DEBUG_MODE) {
        $debug_logs[] = "开发模式下，忽略短信发送失败，继续流程";
        error_log("开发模式下，忽略短信发送失败，继续流程");
        $status = 1; // 伪造成功
        $message = '验证码发送成功(开发模式)';
    }
}

// 更新短信日志
try {
    // 检查sms_logs表是否存在channel列
    $stmt = $conn->prepare("SHOW COLUMNS FROM sms_logs LIKE 'channel'");
    $stmt->execute();
    $column_exists = $stmt->get_result()->num_rows > 0;
    $stmt->close();
    
    if ($column_exists) {
        // 如果channel列存在，使用完整SQL
        $stmt = $conn->prepare("INSERT INTO sms_logs (phone, code, type, channel, status, request_ip, request_data, response_data, error_message, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())");
        $error_message = $status == 1 ? '' : $message;
        $stmt->bind_param("ssssissss", $phone, $code, $type, $channel, $status, $request_ip, $request_data, $response_data, $error_message);
    } else {
        // 如果channel列不存在，使用兼容SQL（不包含channel字段）
        $stmt = $conn->prepare("INSERT INTO sms_logs (phone, code, type, status, request_ip, request_data, response_data, error_message, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW())");
        $error_message = $status == 1 ? '' : $message;
        $stmt->bind_param("sssissss", $phone, $code, $type, $status, $request_ip, $request_data, $response_data, $error_message);
    }

    if (!$stmt->execute()) {
        // 日志记录失败不影响验证码使用，只记录错误
        error_log("SMS日志记录失败: " . $stmt->error);
    }

    $stmt->close();
} catch (Exception $e) {
    error_log("SMS日志记录异常: " . $e->getMessage());
    // 日志记录失败不影响主流程
}

$conn->close();

// 在开发环境下，直接返回验证码，不论实际发送状态
if (DEBUG_MODE) {
    echo json_encode([
        'code' => 0,
        'message' => $message,
        'data' => [
            'code' => $code, // 仅在开发环境下返回验证码
            'phone' => $phone,
            'debug_logs' => $debug_logs // 添加调试日志
        ]
    ]);
    exit;
}

// 在生产环境，根据实际发送结果返回
if ($status == 1) {
    // 发送成功
    echo json_encode([
        'code' => 0,
        'message' => '验证码发送成功',
        'data' => [
            'phone' => $phone,
            'debug_logs' => $debug_logs // 添加调试日志
        ]
    ]);
} else {
    // 发送失败
    echo json_encode([
        'code' => 1012,
        'message' => '验证码发送失败，请稍后重试',
        'data' => [
            'debug_logs' => $debug_logs // 添加调试日志
        ]
    ]);
}
?> 