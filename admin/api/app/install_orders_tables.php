<?php
/**
 * 订单相关表创建脚本
 * 1. orders - 订单表
 * 2. order_items - 订单项表
 */

// 设置内容类型
header('Content-Type: text/plain');

// 引入配置
require_once dirname(__DIR__) . '/config.php';

echo "开始创建订单相关表...\n\n";

// 连接数据库
try {
    $conn = new mysqli(
        $DB_CONFIG['HOST'],
        $DB_CONFIG['USER'],
        $DB_CONFIG['PASSWORD'],
        $DB_CONFIG['DATABASE'],
        $DB_CONFIG['PORT']
    );

    if ($conn->connect_error) {
        die("数据库连接失败: " . $conn->connect_error . "\n");
    }

    $conn->set_charset($DB_CONFIG['CHARSET']);
    echo "数据库连接成功！\n";
} catch (Exception $e) {
    die("数据库连接异常: " . $e->getMessage() . "\n");
}

// 创建订单表
$orders_sql = "
CREATE TABLE IF NOT EXISTS `orders` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '订单ID',
  `order_no` varchar(32) NOT NULL COMMENT '订单编号',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `total_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '订单总金额',
  `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '订单状态: 0=待付款, 1=待发货, 2=待收货, 3=已完成, 4=已取消, 5=已关闭',
  `payment_status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '支付状态: 0=未支付, 1=支付中, 2=已支付, 3=支付失败, 4=已退款',
  `payment_time` datetime DEFAULT NULL COMMENT '支付时间',
  `payment_method` varchar(20) DEFAULT NULL COMMENT '支付方式',
  `transaction_id` varchar(100) DEFAULT NULL COMMENT '支付交易号',
  `refund_time` datetime DEFAULT NULL COMMENT '退款时间',
  `refund_amount` decimal(10,2) DEFAULT '0.00' COMMENT '退款金额',
  `refund_status` tinyint(1) DEFAULT '0' COMMENT '退款状态: 0=无退款, 1=退款中, 2=已退款, 3=退款失败',
  `receiver_name` varchar(50) DEFAULT NULL COMMENT '收货人姓名',
  `receiver_phone` varchar(20) DEFAULT NULL COMMENT '收货人手机号',
  `receiver_province` varchar(50) DEFAULT NULL COMMENT '收货省份',
  `receiver_city` varchar(50) DEFAULT NULL COMMENT '收货城市',
  `receiver_district` varchar(50) DEFAULT NULL COMMENT '收货区县',
  `receiver_address` varchar(255) DEFAULT NULL COMMENT '详细地址',
  `shipping_method` varchar(50) DEFAULT NULL COMMENT '配送方式',
  `shipping_company` varchar(50) DEFAULT NULL COMMENT '物流公司',
  `shipping_no` varchar(50) DEFAULT NULL COMMENT '物流单号',
  `shipping_time` datetime DEFAULT NULL COMMENT '发货时间',
  `received_time` datetime DEFAULT NULL COMMENT '收货时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '订单备注',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_order_no` (`order_no`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单表';
";

// 创建订单项表
$order_items_sql = "
CREATE TABLE IF NOT EXISTS `order_items` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '订单项ID',
  `order_id` int(11) NOT NULL COMMENT '订单ID',
  `product_id` int(11) NOT NULL COMMENT '商品ID',
  `product_name` varchar(100) NOT NULL COMMENT '商品名称',
  `product_image` varchar(255) DEFAULT NULL COMMENT '商品图片',
  `price` decimal(10,2) NOT NULL COMMENT '商品价格',
  `quantity` int(11) NOT NULL DEFAULT '1' COMMENT '购买数量',
  `total_amount` decimal(10,2) GENERATED ALWAYS AS (price * quantity) STORED COMMENT '总金额',
  `specs` varchar(500) DEFAULT NULL COMMENT '规格信息(JSON)',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_product_id` (`product_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单项表';
";

// 创建表
try {
    // 订单表
    if ($conn->query($orders_sql) === TRUE) {
        echo "订单表(orders)创建成功！\n";
    } else {
        echo "订单表创建失败: " . $conn->error . "\n";
    }

    // 订单项表
    if ($conn->query($order_items_sql) === TRUE) {
        echo "订单项表(order_items)创建成功！\n";
    } else {
        echo "订单项表创建失败: " . $conn->error . "\n";
    }

    // 检查是否存在商品表，如果不存在则创建
    $conn->query("SHOW TABLES LIKE 'products'");
    if ($conn->affected_rows == 0) {
        // 创建商品表
        $products_sql = "
        CREATE TABLE IF NOT EXISTS `products` (
          `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '商品ID',
          `name` varchar(100) NOT NULL COMMENT '商品名称',
          `description` text COMMENT '商品描述',
          `price` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '商品价格',
          `original_price` decimal(10,2) DEFAULT NULL COMMENT '原价',
          `stock` int(11) NOT NULL DEFAULT '0' COMMENT '库存数量',
          `sold_count` int(11) NOT NULL DEFAULT '0' COMMENT '销量',
          `image` varchar(255) DEFAULT NULL COMMENT '主图',
          `images` text COMMENT '商品图片(JSON)',
          `category_id` int(11) DEFAULT NULL COMMENT '分类ID',
          `tags` varchar(255) DEFAULT NULL COMMENT '标签(逗号分隔)',
          `specs` text COMMENT '规格信息(JSON)',
          `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态: 0=下架, 1=上架',
          `sort_order` int(11) NOT NULL DEFAULT '0' COMMENT '排序值',
          `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
          `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
          PRIMARY KEY (`id`),
          KEY `idx_category_id` (`category_id`),
          KEY `idx_status` (`status`),
          KEY `idx_sort_order` (`sort_order`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品表';
        ";
        
        if ($conn->query($products_sql) === TRUE) {
            echo "商品表(products)创建成功！\n";
            
            // 添加示例商品数据
            $sample_product_sql = "
            INSERT INTO `products` (`name`, `description`, `price`, `original_price`, `stock`, `image`, `status`, `created_at`)
            VALUES 
            ('基础VIP会员', '点点够基础VIP会员，享受多项特权', 399.00, 499.00, 9999, '/static/images/products/vip_basic.png', 1, NOW()),
            ('高级VIP会员', '点点够高级VIP会员，享受所有特权', 999.00, 1299.00, 9999, '/static/images/products/vip_premium.png', 1, NOW()),
            ('净水器通用滤芯', '适用于多种品牌的净水器滤芯', 199.00, 249.00, 200, '/static/images/products/filter.png', 1, NOW()),
            ('智能水质检测仪', '实时监测水质，保障家庭饮水安全', 599.00, 699.00, 50, '/static/images/products/water_tester.png', 1, NOW())
            ";
            
            if ($conn->query($sample_product_sql) === TRUE) {
                echo "示例商品数据添加成功！\n";
            } else {
                echo "示例商品数据添加失败: " . $conn->error . "\n";
            }
        } else {
            echo "商品表创建失败: " . $conn->error . "\n";
        }
    } else {
        echo "商品表(products)已存在，跳过创建。\n";
    }
    
    echo "\n所有表创建完成！\n";
} catch (Exception $e) {
    echo "创建表时发生异常: " . $e->getMessage() . "\n";
} finally {
    // 关闭数据库连接
    $conn->close();
}
?> 