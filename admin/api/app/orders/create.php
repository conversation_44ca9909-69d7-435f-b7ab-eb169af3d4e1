<?php
/**
 * 创建订单API重定向
 *
 * 此文件将原始的PHP创建订单API请求重定向到新的Laravel RESTful API
 */

// 设置HTTP响应头
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 启用详细错误日志
$debug_log_file = dirname(__DIR__, 2) . '/logs/order_create_debug.log';
function debug_log($message, $data = null) {
    global $debug_log_file;
    $log_dir = dirname($debug_log_file);
    
    // 确保日志目录存在
    if (!is_dir($log_dir)) {
        mkdir($log_dir, 0755, true);
    }
    
    $timestamp = date('Y-m-d H:i:s');
    $log_message = "[$timestamp] $message";
    
    if ($data !== null) {
        if (is_array($data) || is_object($data)) {
            $log_message .= " - " . json_encode($data, JSON_UNESCAPED_UNICODE);
        } else {
            $log_message .= " - $data";
        }
    }
    
    $log_message .= PHP_EOL;
    
    // 写入日志
    file_put_contents($debug_log_file, $log_message, FILE_APPEND);
}

// 记录请求开始
debug_log('订单创建API重定向开始', [
    'method' => $_SERVER['REQUEST_METHOD'],
    'uri' => $_SERVER['REQUEST_URI'],
    'ip' => $_SERVER['REMOTE_ADDR']
]);

// 记录请求头
debug_log('请求头', getallheaders());

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    header('Access-Control-Allow-Origin: *');
    header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
    header('Access-Control-Allow-Headers: Content-Type, Authorization');
    header('Access-Control-Max-Age: 86400');
    exit;
}

// 检查请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode([
        'code' => 1,
        'message' => '请求方法不允许',
        'data' => null
    ]);
    exit;
}

// 获取请求数据
$request_body = file_get_contents('php://input');
debug_log('原始请求数据', $request_body);

// 获取Authorization头
$headers = getallheaders();
$auth_header = isset($headers['Authorization']) ? $headers['Authorization'] : '';

// 获取当前主机名
$protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https://' : 'http://';
$host = $_SERVER['HTTP_HOST'];
$apiUrl = $protocol . $host . '/api/orders/create';

debug_log("重定向到: $apiUrl");

// 创建cURL请求
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $apiUrl);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, $request_body);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
    'Accept: application/json',
    'Authorization: ' . $auth_header
]);

// 执行请求
$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

// 记录请求信息
$logMessage = sprintf(
    '创建订单API重定向: API=%s, 状态码=%d',
    $apiUrl,
    $httpCode
);
debug_log($logMessage);

// 如果请求失败，记录更详细的错误信息
if ($response === false) {
    $errorMessage = sprintf(
        '创建订单API请求失败: 错误=%s, URL=%s',
        curl_error($ch),
        $apiUrl
    );
    debug_log($errorMessage);

    echo json_encode([
        'code' => 1,
        'message' => '服务器内部错误',
        'data' => null
    ]);
} else {
    // 记录响应内容（仅用于调试）
    debug_log('创建订单API响应: ' . substr($response, 0, 200) . '...');

    // 返回原始响应
    echo $response;
}
