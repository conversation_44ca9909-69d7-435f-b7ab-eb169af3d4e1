<?php
/**
 * API函数库，提供公共功能
 */

// 设置默认时区
date_default_timezone_set('Asia/Shanghai');

// 开启输出缓冲，用于捕获任何可能的输出
ob_start();

// 设置错误处理
ini_set('display_errors', 0); // 禁止直接显示错误
error_reporting(E_ALL); // 报告所有错误类型

// 设置错误处理函数
set_error_handler(function($errno, $errstr, $errfile, $errline) {
    // 记录到日志
    error_log("PHP Error [$errno]: $errstr in $errfile:$errline");
    
    // 清空输出缓冲
    ob_clean();
    
    // 返回JSON错误
    header('Content-Type: application/json; charset=utf-8');
    echo json_encode([
        'code' => 500,
        'message' => 'Internal Server Error: ' . $errstr,
        'error_details' => [
            'type' => $errno,
            'file' => basename($errfile),
            'line' => $errline
        ]
    ]);
    exit;
});

// 设置异常处理函数
set_exception_handler(function($exception) {
    // 记录到日志
    error_log("Uncaught Exception: " . $exception->getMessage() . " in " . $exception->getFile() . ":" . $exception->getLine());
    
    // 清空输出缓冲
    ob_clean();
    
    // 返回JSON错误
    header('Content-Type: application/json; charset=utf-8');
    echo json_encode([
        'code' => 500,
        'message' => 'Internal Server Error: ' . $exception->getMessage(),
        'error_details' => [
            'type' => get_class($exception),
            'file' => basename($exception->getFile()),
            'line' => $exception->getLine()
        ]
    ]);
    exit;
});

// 设置默认CORS头
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
header('Content-Type: application/json; charset=utf-8');

// 处理OPTIONS请求
if (isset($_SERVER['REQUEST_METHOD']) && $_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

/**
 * 获取数据库连接
 * @param string $dbType 数据库类型，可选值: main(默认), water(净水器系统)
 * @return mysqli|null 数据库连接对象，失败返回null
 */
if (!function_exists('get_db_connection')) {
function get_db_connection($dbType = 'main') {
    // 仅在调试模式下记录日志
    if (defined('DEBUG_MODE') && DEBUG_MODE) {
        file_put_contents(__DIR__ . '/../logs/debug.log', "\n[调试] 进入get_db_connection\n", FILE_APPEND);
    }
    // 获取.env文件中的数据库配置
    // 尝试多个可能的路径
    $possiblePaths = [
        realpath(__DIR__ . '/../../.env'),  // 原始路径
        realpath(__DIR__ . '/../../../.env'), // 往上一级目录
        '/www/wwwroot/pay.itapgo.com/Tapp/admin/.env' // 绝对路径
    ];
    
    $envPath = null;
    foreach ($possiblePaths as $path) {
        if ($path && file_exists($path)) {
            $envPath = $path;
            break;
        }
    }
    
    if (!$envPath) {
        error_log("无法找到.env文件，尝试了以下路径: " . implode(", ", array_filter($possiblePaths)));
        return null;
    }
    
    error_log("从配置文件加载数据库配置: " . $envPath);
    
    $envContent = file_get_contents($envPath);
    $lines = explode(PHP_EOL, $envContent);
    $config = [];
    
    foreach ($lines as $line) {
        $line = trim($line);
        if (empty($line) || strpos($line, '#') === 0) {
            continue;
        }
        
        list($key, $value) = explode('=', $line, 2) + [null, null];
        if ($key !== null && $value !== null) {
            $config[trim($key)] = trim($value);
        }
    }
    
    if ($dbType === 'water') {
        // 净水器系统数据库配置
        $host = $config['DB_WATER_HOST'] ?? null;
        $port = $config['DB_WATER_PORT'] ?? 3306;
        $database = $config['DB_WATER_DATABASE'] ?? null;
        $username = $config['DB_WATER_USERNAME'] ?? null;
        $password = $config['DB_WATER_PASSWORD'] ?? null;
        
        error_log("使用净水器数据库配置 - 主机: $host, 端口: $port, 数据库: $database, 用户名: $username");
    } else {
        // 主数据库配置
        $host = $config['DB_HOST'] ?? null;
        $port = $config['DB_PORT'] ?? 3306;
        $database = $config['DB_DATABASE'] ?? null;
        $username = $config['DB_USERNAME'] ?? null;
        $password = $config['DB_PASSWORD'] ?? null;
        
        error_log("使用主数据库配置 - 主机: $host, 端口: $port, 数据库: $database, 用户名: $username");
        // 新增：写入debug.log
        file_put_contents(__DIR__ . '/../logs/debug.log', "\n[调试] 实际数据库配置: host=$host, port=$port, db=$database, user=$username\n", FILE_APPEND);
    }
    
    if (!$host || !$database || !$username) {
        error_log("数据库配置不完整，无法连接");
        return null;
    }
    
    try {
        error_log("尝试连接数据库: $host:$port, 数据库: $database");
        
        // 使用直接的定义方式，避免mysqli在某些PHP环境下的兼容性问题
        $conn = mysqli_connect($host, $username, $password, $database, $port);
        
        if (!$conn) {
            error_log("数据库连接失败: " . mysqli_connect_error());
            return null;
        }
        
        // 设置字符集
        mysqli_set_charset($conn, 'utf8mb4');
        error_log("数据库连接成功");
        
        return $conn;
    } catch (Exception $e) {
        error_log("数据库连接异常: " . $e->getMessage());
        return null;
    }
}
}

/**
 * 打印变量信息到错误日志
 */
function debug_log($var, $label = '') {
    $output = ($label ? "$label: " : '') . var_export($var, true);
    error_log($output);
}

/**
 * 校验JWT Token，返回用户信息或false
 */
function validateToken() {
    $headers = function_exists('getallheaders') ? getallheaders() : [];
    $authHeader = $headers['Authorization'] ?? $headers['authorization'] ?? '';
    $token = '';
    if (strpos($authHeader, 'Bearer ') === 0) {
        $token = substr($authHeader, 7);
    } elseif (!empty($_COOKIE['token'])) {
        $token = $_COOKIE['token'];
    } elseif (!empty($_POST['token'])) {
        $token = $_POST['token'];
    }
    if (!$token) return false;
    // 解析JWT
    $parts = explode('.', $token);
    if (count($parts) !== 3) return false;
    $payload = json_decode(base64_decode(strtr($parts[1], '-_', '+/')), true);
    if (!$payload || !isset($payload['user_id']) || !isset($payload['exp'])) return false;
    if ($payload['exp'] < time()) return false;
    return [
        'id' => $payload['user_id'],
        'user_id' => $payload['user_id'],
        'openid' => $payload['openid'] ?? null,
        'open_id' => $payload['openid'] ?? null
    ];
}

/**
 * 全局API成功响应
 */
if (!function_exists('responseSuccess')) {
function responseSuccess($data = null) {
    // 设置状态码为200
    http_response_code(200);
    // 清除缓存
    header('Cache-Control: no-store, no-cache, must-revalidate, max-age=0');
    header('Pragma: no-cache');
    // 设置内容类型
    header('Content-Type: application/json; charset=utf-8');
    // 禁用浏览器XSS过滤
    header('X-XSS-Protection: 0');
    // 输出JSON
    $response = [
        'code' => 0,
        'message' => 'success',
        'data' => $data
    ];
    echo json_encode($response, JSON_UNESCAPED_UNICODE);
    exit;
}
}

/**
 * 全局API错误响应
 */
if (!function_exists('responseError')) {
function responseError($code, $message, $data = null) {
    // 设置状态码
    $httpCode = 200; // 默认HTTP状态码为200
    if ($code == 401) $httpCode = 401;
    else if ($code == 403) $httpCode = 403;
    else if ($code == 404) $httpCode = 404;
    else if ($code >= 500) $httpCode = 500;
    http_response_code($httpCode);
    // 清除缓存
    header('Cache-Control: no-store, no-cache, must-revalidate, max-age=0');
    header('Pragma: no-cache');
    // 设置内容类型
    header('Content-Type: application/json; charset=utf-8');
    // 禁用浏览器XSS过滤
    header('X-XSS-Protection: 0');
    // 输出JSON
    $response = [
        'code' => $code,
        'message' => $message
    ];
    if ($data !== null) {
        $response['data'] = $data;
    }
    echo json_encode($response, JSON_UNESCAPED_UNICODE);
    exit;
}
}
