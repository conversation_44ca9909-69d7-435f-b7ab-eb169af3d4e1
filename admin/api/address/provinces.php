<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: X-Requested-With, Content-Type, Authorization');

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 腾讯地图API配置
$tencentMapKey = 'KBXBZ-QLM67-MEHX4-P2QC2-552E2-W3FH6';
$baseUrl = 'https://apis.map.qq.com/ws/district/v1';

/**
 * 备用省份数据
 */
function getBackupProvinces() {
    return [
        ['id' => '110000', 'name' => '北京市', 'fullname' => '北京市'],
        ['id' => '120000', 'name' => '天津市', 'fullname' => '天津市'],
        ['id' => '130000', 'name' => '河北省', 'fullname' => '河北省'],
        ['id' => '140000', 'name' => '山西省', 'fullname' => '山西省'],
        ['id' => '150000', 'name' => '内蒙古自治区', 'fullname' => '内蒙古自治区'],
        ['id' => '210000', 'name' => '辽宁省', 'fullname' => '辽宁省'],
        ['id' => '220000', 'name' => '吉林省', 'fullname' => '吉林省'],
        ['id' => '230000', 'name' => '黑龙江省', 'fullname' => '黑龙江省'],
        ['id' => '310000', 'name' => '上海市', 'fullname' => '上海市'],
        ['id' => '320000', 'name' => '江苏省', 'fullname' => '江苏省'],
        ['id' => '330000', 'name' => '浙江省', 'fullname' => '浙江省'],
        ['id' => '340000', 'name' => '安徽省', 'fullname' => '安徽省'],
        ['id' => '350000', 'name' => '福建省', 'fullname' => '福建省'],
        ['id' => '360000', 'name' => '江西省', 'fullname' => '江西省'],
        ['id' => '370000', 'name' => '山东省', 'fullname' => '山东省'],
        ['id' => '410000', 'name' => '河南省', 'fullname' => '河南省'],
        ['id' => '420000', 'name' => '湖北省', 'fullname' => '湖北省'],
        ['id' => '430000', 'name' => '湖南省', 'fullname' => '湖南省'],
        ['id' => '440000', 'name' => '广东省', 'fullname' => '广东省'],
        ['id' => '450000', 'name' => '广西壮族自治区', 'fullname' => '广西壮族自治区'],
        ['id' => '460000', 'name' => '海南省', 'fullname' => '海南省'],
        ['id' => '500000', 'name' => '重庆市', 'fullname' => '重庆市'],
        ['id' => '510000', 'name' => '四川省', 'fullname' => '四川省'],
        ['id' => '520000', 'name' => '贵州省', 'fullname' => '贵州省'],
        ['id' => '530000', 'name' => '云南省', 'fullname' => '云南省'],
        ['id' => '540000', 'name' => '西藏自治区', 'fullname' => '西藏自治区'],
        ['id' => '610000', 'name' => '陕西省', 'fullname' => '陕西省'],
        ['id' => '620000', 'name' => '甘肃省', 'fullname' => '甘肃省'],
        ['id' => '630000', 'name' => '青海省', 'fullname' => '青海省'],
        ['id' => '640000', 'name' => '宁夏回族自治区', 'fullname' => '宁夏回族自治区'],
        ['id' => '650000', 'name' => '新疆维吾尔自治区', 'fullname' => '新疆维吾尔自治区'],
        ['id' => '710000', 'name' => '台湾省', 'fullname' => '台湾省'],
        ['id' => '810000', 'name' => '香港特别行政区', 'fullname' => '香港特别行政区'],
        ['id' => '820000', 'name' => '澳门特别行政区', 'fullname' => '澳门特别行政区']
    ];
}

try {
    // 尝试调用腾讯地图API
    $url = $baseUrl . '/list?key=' . $tencentMapKey;
    $context = stream_context_create([
        'http' => [
            'timeout' => 10,
            'method' => 'GET',
            'header' => 'User-Agent: Mozilla/5.0 (compatible; AddressAPI/1.0)'
        ]
    ]);
    
    $response = @file_get_contents($url, false, $context);
    
    if ($response !== false) {
        $data = json_decode($response, true);
        if ($data && $data['status'] === 0 && isset($data['result'][0])) {
            // API调用成功，返回腾讯地图数据
            echo json_encode([
                'code' => 0,
                'message' => '获取成功',
                'data' => $data['result'][0]
            ], JSON_UNESCAPED_UNICODE);
            exit;
        }
    }
    
    // API调用失败，使用备用数据
    echo json_encode([
        'code' => 0,
        'message' => '获取成功（使用备用数据）',
        'data' => getBackupProvinces()
    ], JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    // 发生异常，使用备用数据
    echo json_encode([
        'code' => 0,
        'message' => '获取成功（使用备用数据）',
        'data' => getBackupProvinces()
    ], JSON_UNESCAPED_UNICODE);
}
?> 