<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: X-Requested-With, Content-Type, Authorization');

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 腾讯地图API配置
$tencentMapKey = 'KBXBZ-QLM67-MEHX4-P2QC2-552E2-W3FH6';
$baseUrl = 'https://apis.map.qq.com/ws/district/v1';

// 获取省份ID参数
$provinceId = $_GET['province_id'] ?? '';

if (empty($provinceId)) {
    echo json_encode([
        'code' => 1,
        'message' => '省份ID不能为空',
        'data' => []
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

/**
 * 备用城市数据
 */
function getBackupCities($provinceId) {
    $cities = [
        // 直辖市 - 返回区县作为"城市"
        '110000' => [ // 北京市
            ['id' => '110101', 'name' => '东城区', 'fullname' => '东城区'],
            ['id' => '110102', 'name' => '西城区', 'fullname' => '西城区'],
            ['id' => '110105', 'name' => '朝阳区', 'fullname' => '朝阳区'],
            ['id' => '110106', 'name' => '丰台区', 'fullname' => '丰台区'],
            ['id' => '110107', 'name' => '石景山区', 'fullname' => '石景山区'],
            ['id' => '110108', 'name' => '海淀区', 'fullname' => '海淀区'],
            ['id' => '110109', 'name' => '门头沟区', 'fullname' => '门头沟区'],
            ['id' => '110111', 'name' => '房山区', 'fullname' => '房山区'],
            ['id' => '110112', 'name' => '通州区', 'fullname' => '通州区'],
            ['id' => '110113', 'name' => '顺义区', 'fullname' => '顺义区'],
            ['id' => '110114', 'name' => '昌平区', 'fullname' => '昌平区'],
            ['id' => '110115', 'name' => '大兴区', 'fullname' => '大兴区'],
            ['id' => '110116', 'name' => '怀柔区', 'fullname' => '怀柔区'],
            ['id' => '110117', 'name' => '平谷区', 'fullname' => '平谷区'],
            ['id' => '110118', 'name' => '密云区', 'fullname' => '密云区'],
            ['id' => '110119', 'name' => '延庆区', 'fullname' => '延庆区']
        ],
        '120000' => [ // 天津市
            ['id' => '120101', 'name' => '和平区', 'fullname' => '和平区'],
            ['id' => '120102', 'name' => '河东区', 'fullname' => '河东区'],
            ['id' => '120103', 'name' => '河西区', 'fullname' => '河西区'],
            ['id' => '120104', 'name' => '南开区', 'fullname' => '南开区'],
            ['id' => '120105', 'name' => '河北区', 'fullname' => '河北区'],
            ['id' => '120106', 'name' => '红桥区', 'fullname' => '红桥区'],
            ['id' => '120110', 'name' => '东丽区', 'fullname' => '东丽区'],
            ['id' => '120111', 'name' => '西青区', 'fullname' => '西青区'],
            ['id' => '120112', 'name' => '津南区', 'fullname' => '津南区'],
            ['id' => '120113', 'name' => '北辰区', 'fullname' => '北辰区'],
            ['id' => '120114', 'name' => '武清区', 'fullname' => '武清区'],
            ['id' => '120115', 'name' => '宝坻区', 'fullname' => '宝坻区'],
            ['id' => '120116', 'name' => '滨海新区', 'fullname' => '滨海新区'],
            ['id' => '120117', 'name' => '宁河区', 'fullname' => '宁河区'],
            ['id' => '120118', 'name' => '静海区', 'fullname' => '静海区'],
            ['id' => '120119', 'name' => '蓟州区', 'fullname' => '蓟州区']
        ],
        '310000' => [ // 上海市
            ['id' => '310101', 'name' => '黄浦区', 'fullname' => '黄浦区'],
            ['id' => '310104', 'name' => '徐汇区', 'fullname' => '徐汇区'],
            ['id' => '310105', 'name' => '长宁区', 'fullname' => '长宁区'],
            ['id' => '310106', 'name' => '静安区', 'fullname' => '静安区'],
            ['id' => '310107', 'name' => '普陀区', 'fullname' => '普陀区'],
            ['id' => '310109', 'name' => '虹口区', 'fullname' => '虹口区'],
            ['id' => '310110', 'name' => '杨浦区', 'fullname' => '杨浦区'],
            ['id' => '310112', 'name' => '闵行区', 'fullname' => '闵行区'],
            ['id' => '310113', 'name' => '宝山区', 'fullname' => '宝山区'],
            ['id' => '310114', 'name' => '嘉定区', 'fullname' => '嘉定区'],
            ['id' => '310115', 'name' => '浦东新区', 'fullname' => '浦东新区'],
            ['id' => '310116', 'name' => '金山区', 'fullname' => '金山区'],
            ['id' => '310117', 'name' => '松江区', 'fullname' => '松江区'],
            ['id' => '310118', 'name' => '青浦区', 'fullname' => '青浦区'],
            ['id' => '310120', 'name' => '奉贤区', 'fullname' => '奉贤区'],
            ['id' => '310151', 'name' => '崇明区', 'fullname' => '崇明区']
        ],
        '500000' => [ // 重庆市
            ['id' => '500101', 'name' => '万州区', 'fullname' => '万州区'],
            ['id' => '500102', 'name' => '涪陵区', 'fullname' => '涪陵区'],
            ['id' => '500103', 'name' => '渝中区', 'fullname' => '渝中区'],
            ['id' => '500104', 'name' => '大渡口区', 'fullname' => '大渡口区'],
            ['id' => '500105', 'name' => '江北区', 'fullname' => '江北区'],
            ['id' => '500106', 'name' => '沙坪坝区', 'fullname' => '沙坪坝区'],
            ['id' => '500107', 'name' => '九龙坡区', 'fullname' => '九龙坡区'],
            ['id' => '500108', 'name' => '南岸区', 'fullname' => '南岸区'],
            ['id' => '500109', 'name' => '北碚区', 'fullname' => '北碚区'],
            ['id' => '500110', 'name' => '綦江区', 'fullname' => '綦江区'],
            ['id' => '500111', 'name' => '大足区', 'fullname' => '大足区'],
            ['id' => '500112', 'name' => '渝北区', 'fullname' => '渝北区'],
            ['id' => '500113', 'name' => '巴南区', 'fullname' => '巴南区'],
            ['id' => '500114', 'name' => '黔江区', 'fullname' => '黔江区'],
            ['id' => '500115', 'name' => '长寿区', 'fullname' => '长寿区'],
            ['id' => '500116', 'name' => '江津区', 'fullname' => '江津区'],
            ['id' => '500117', 'name' => '合川区', 'fullname' => '合川区'],
            ['id' => '500118', 'name' => '永川区', 'fullname' => '永川区'],
            ['id' => '500119', 'name' => '南川区', 'fullname' => '南川区'],
            ['id' => '500120', 'name' => '璧山区', 'fullname' => '璧山区'],
            ['id' => '500151', 'name' => '铜梁区', 'fullname' => '铜梁区'],
            ['id' => '500152', 'name' => '潼南区', 'fullname' => '潼南区'],
            ['id' => '500153', 'name' => '荣昌区', 'fullname' => '荣昌区'],
            ['id' => '500154', 'name' => '开州区', 'fullname' => '开州区'],
            ['id' => '500155', 'name' => '梁平区', 'fullname' => '梁平区'],
            ['id' => '500156', 'name' => '武隆区', 'fullname' => '武隆区']
        ],
        // 省份 - 返回地级市
        '350000' => [ // 福建省
            ['id' => '350100', 'name' => '福州市', 'fullname' => '福州市'],
            ['id' => '350200', 'name' => '厦门市', 'fullname' => '厦门市'],
            ['id' => '350300', 'name' => '莆田市', 'fullname' => '莆田市'],
            ['id' => '350400', 'name' => '三明市', 'fullname' => '三明市'],
            ['id' => '350500', 'name' => '泉州市', 'fullname' => '泉州市'],
            ['id' => '350600', 'name' => '漳州市', 'fullname' => '漳州市'],
            ['id' => '350700', 'name' => '南平市', 'fullname' => '南平市'],
            ['id' => '350800', 'name' => '龙岩市', 'fullname' => '龙岩市'],
            ['id' => '350900', 'name' => '宁德市', 'fullname' => '宁德市']
        ],
        '440000' => [ // 广东省
            ['id' => '440100', 'name' => '广州市', 'fullname' => '广州市'],
            ['id' => '440200', 'name' => '韶关市', 'fullname' => '韶关市'],
            ['id' => '440300', 'name' => '深圳市', 'fullname' => '深圳市'],
            ['id' => '440400', 'name' => '珠海市', 'fullname' => '珠海市'],
            ['id' => '440500', 'name' => '汕头市', 'fullname' => '汕头市'],
            ['id' => '440600', 'name' => '佛山市', 'fullname' => '佛山市'],
            ['id' => '440700', 'name' => '江门市', 'fullname' => '江门市'],
            ['id' => '440800', 'name' => '湛江市', 'fullname' => '湛江市'],
            ['id' => '440900', 'name' => '茂名市', 'fullname' => '茂名市'],
            ['id' => '441200', 'name' => '肇庆市', 'fullname' => '肇庆市'],
            ['id' => '441300', 'name' => '惠州市', 'fullname' => '惠州市'],
            ['id' => '441400', 'name' => '梅州市', 'fullname' => '梅州市'],
            ['id' => '441500', 'name' => '汕尾市', 'fullname' => '汕尾市'],
            ['id' => '441600', 'name' => '河源市', 'fullname' => '河源市'],
            ['id' => '441700', 'name' => '阳江市', 'fullname' => '阳江市'],
            ['id' => '441800', 'name' => '清远市', 'fullname' => '清远市'],
            ['id' => '441900', 'name' => '东莞市', 'fullname' => '东莞市'],
            ['id' => '442000', 'name' => '中山市', 'fullname' => '中山市'],
            ['id' => '445100', 'name' => '潮州市', 'fullname' => '潮州市'],
            ['id' => '445200', 'name' => '揭阳市', 'fullname' => '揭阳市'],
            ['id' => '445300', 'name' => '云浮市', 'fullname' => '云浮市']
        ]
    ];
    
    return $cities[$provinceId] ?? [];
}

try {
    // 尝试调用腾讯地图API
    $url = $baseUrl . '/getchildren?id=' . urlencode($provinceId) . '&key=' . $tencentMapKey;
    $context = stream_context_create([
        'http' => [
            'timeout' => 10,
            'method' => 'GET',
            'header' => 'User-Agent: Mozilla/5.0 (compatible; AddressAPI/1.0)'
        ]
    ]);
    
    $response = @file_get_contents($url, false, $context);
    
    if ($response !== false) {
        $data = json_decode($response, true);
        if ($data && $data['status'] === 0 && isset($data['result'][0])) {
            // API调用成功，返回腾讯地图数据
            echo json_encode([
                'code' => 0,
                'message' => '获取成功',
                'data' => $data['result'][0]
            ], JSON_UNESCAPED_UNICODE);
            exit;
        }
    }
    
    // API调用失败，使用备用数据
    echo json_encode([
        'code' => 0,
        'message' => '获取成功（使用备用数据）',
        'data' => getBackupCities($provinceId)
    ], JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    // 发生异常，使用备用数据
    echo json_encode([
        'code' => 0,
        'message' => '获取成功（使用备用数据）',
        'data' => getBackupCities($provinceId)
    ], JSON_UNESCAPED_UNICODE);
}
?> 