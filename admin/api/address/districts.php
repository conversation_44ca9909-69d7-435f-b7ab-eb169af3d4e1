<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: X-Requested-With, Content-Type, Authorization');

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 腾讯地图API配置
$tencentMapKey = 'KBXBZ-QLM67-MEHX4-P2QC2-552E2-W3FH6';
$baseUrl = 'https://apis.map.qq.com/ws/district/v1';

// 获取城市ID参数
$cityId = $_GET['city_id'] ?? '';

if (empty($cityId)) {
    echo json_encode([
        'code' => 1,
        'message' => '城市ID不能为空',
        'data' => []
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

/**
 * 检查是否为直辖市的区
 */
function isDirectMunicipalityDistrict($cityId) {
    // 直辖市的区县ID范围
    $ranges = [
        ['110101', '110119'], // 北京市各区
        ['120101', '120119'], // 天津市各区
        ['310101', '310151'], // 上海市各区
        ['500101', '500156']  // 重庆市各区
    ];
    
    foreach ($ranges as $range) {
        if ($cityId >= $range[0] && $cityId <= $range[1]) {
            return true;
        }
    }
    return false;
}

/**
 * 备用区县数据
 */
function getBackupDistricts($cityId) {
    // 如果是直辖市的区，返回空数组（区下面不再有区县）
    if (isDirectMunicipalityDistrict($cityId)) {
        return [];
    }
    
    $districts = [
        // 福建省各城市区县数据
        '350100' => [ // 福州市
            ['id' => '350102', 'name' => '鼓楼区', 'fullname' => '鼓楼区'],
            ['id' => '350103', 'name' => '台江区', 'fullname' => '台江区'],
            ['id' => '350104', 'name' => '仓山区', 'fullname' => '仓山区'],
            ['id' => '350105', 'name' => '马尾区', 'fullname' => '马尾区'],
            ['id' => '350111', 'name' => '晋安区', 'fullname' => '晋安区'],
            ['id' => '350112', 'name' => '长乐区', 'fullname' => '长乐区'],
            ['id' => '350121', 'name' => '闽侯县', 'fullname' => '闽侯县'],
            ['id' => '350122', 'name' => '连江县', 'fullname' => '连江县'],
            ['id' => '350123', 'name' => '罗源县', 'fullname' => '罗源县'],
            ['id' => '350124', 'name' => '闽清县', 'fullname' => '闽清县'],
            ['id' => '350125', 'name' => '永泰县', 'fullname' => '永泰县'],
            ['id' => '350128', 'name' => '平潭县', 'fullname' => '平潭县'],
            ['id' => '350181', 'name' => '福清市', 'fullname' => '福清市']
        ],
        '350200' => [ // 厦门市
            ['id' => '350203', 'name' => '思明区', 'fullname' => '思明区'],
            ['id' => '350205', 'name' => '海沧区', 'fullname' => '海沧区'],
            ['id' => '350206', 'name' => '湖里区', 'fullname' => '湖里区'],
            ['id' => '350211', 'name' => '集美区', 'fullname' => '集美区'],
            ['id' => '350212', 'name' => '同安区', 'fullname' => '同安区'],
            ['id' => '350213', 'name' => '翔安区', 'fullname' => '翔安区']
        ],
        '350300' => [ // 莆田市
            ['id' => '350302', 'name' => '城厢区', 'fullname' => '城厢区'],
            ['id' => '350303', 'name' => '涵江区', 'fullname' => '涵江区'],
            ['id' => '350304', 'name' => '荔城区', 'fullname' => '荔城区'],
            ['id' => '350305', 'name' => '秀屿区', 'fullname' => '秀屿区'],
            ['id' => '350322', 'name' => '仙游县', 'fullname' => '仙游县']
        ],
        '350400' => [ // 三明市
            ['id' => '350402', 'name' => '梅列区', 'fullname' => '梅列区'],
            ['id' => '350403', 'name' => '三元区', 'fullname' => '三元区'],
            ['id' => '350421', 'name' => '明溪县', 'fullname' => '明溪县'],
            ['id' => '350423', 'name' => '清流县', 'fullname' => '清流县'],
            ['id' => '350424', 'name' => '宁化县', 'fullname' => '宁化县'],
            ['id' => '350425', 'name' => '大田县', 'fullname' => '大田县'],
            ['id' => '350426', 'name' => '尤溪县', 'fullname' => '尤溪县'],
            ['id' => '350427', 'name' => '沙县', 'fullname' => '沙县'],
            ['id' => '350428', 'name' => '将乐县', 'fullname' => '将乐县'],
            ['id' => '350429', 'name' => '泰宁县', 'fullname' => '泰宁县'],
            ['id' => '350430', 'name' => '建宁县', 'fullname' => '建宁县'],
            ['id' => '350481', 'name' => '永安市', 'fullname' => '永安市']
        ],
        '350500' => [ // 泉州市
            ['id' => '350502', 'name' => '鲤城区', 'fullname' => '鲤城区'],
            ['id' => '350503', 'name' => '丰泽区', 'fullname' => '丰泽区'],
            ['id' => '350504', 'name' => '洛江区', 'fullname' => '洛江区'],
            ['id' => '350505', 'name' => '泉港区', 'fullname' => '泉港区'],
            ['id' => '350521', 'name' => '惠安县', 'fullname' => '惠安县'],
            ['id' => '350524', 'name' => '安溪县', 'fullname' => '安溪县'],
            ['id' => '350525', 'name' => '永春县', 'fullname' => '永春县'],
            ['id' => '350526', 'name' => '德化县', 'fullname' => '德化县'],
            ['id' => '350527', 'name' => '金门县', 'fullname' => '金门县'],
            ['id' => '350581', 'name' => '石狮市', 'fullname' => '石狮市'],
            ['id' => '350582', 'name' => '晋江市', 'fullname' => '晋江市'],
            ['id' => '350583', 'name' => '南安市', 'fullname' => '南安市']
        ],
        '350600' => [ // 漳州市
            ['id' => '350602', 'name' => '芗城区', 'fullname' => '芗城区'],
            ['id' => '350603', 'name' => '龙文区', 'fullname' => '龙文区'],
            ['id' => '350622', 'name' => '云霄县', 'fullname' => '云霄县'],
            ['id' => '350623', 'name' => '漳浦县', 'fullname' => '漳浦县'],
            ['id' => '350624', 'name' => '诏安县', 'fullname' => '诏安县'],
            ['id' => '350625', 'name' => '长泰县', 'fullname' => '长泰县'],
            ['id' => '350626', 'name' => '东山县', 'fullname' => '东山县'],
            ['id' => '350627', 'name' => '南靖县', 'fullname' => '南靖县'],
            ['id' => '350628', 'name' => '平和县', 'fullname' => '平和县'],
            ['id' => '350629', 'name' => '华安县', 'fullname' => '华安县'],
            ['id' => '350681', 'name' => '龙海市', 'fullname' => '龙海市']
        ],
        '350700' => [ // 南平市
            ['id' => '350702', 'name' => '延平区', 'fullname' => '延平区'],
            ['id' => '350703', 'name' => '建阳区', 'fullname' => '建阳区'],
            ['id' => '350721', 'name' => '顺昌县', 'fullname' => '顺昌县'],
            ['id' => '350722', 'name' => '浦城县', 'fullname' => '浦城县'],
            ['id' => '350723', 'name' => '光泽县', 'fullname' => '光泽县'],
            ['id' => '350724', 'name' => '松溪县', 'fullname' => '松溪县'],
            ['id' => '350725', 'name' => '政和县', 'fullname' => '政和县'],
            ['id' => '350781', 'name' => '邵武市', 'fullname' => '邵武市'],
            ['id' => '350782', 'name' => '武夷山市', 'fullname' => '武夷山市'],
            ['id' => '350783', 'name' => '建瓯市', 'fullname' => '建瓯市']
        ],
        '350800' => [ // 龙岩市
            ['id' => '350802', 'name' => '新罗区', 'fullname' => '新罗区'],
            ['id' => '350803', 'name' => '永定区', 'fullname' => '永定区'],
            ['id' => '350821', 'name' => '长汀县', 'fullname' => '长汀县'],
            ['id' => '350823', 'name' => '上杭县', 'fullname' => '上杭县'],
            ['id' => '350824', 'name' => '武平县', 'fullname' => '武平县'],
            ['id' => '350825', 'name' => '连城县', 'fullname' => '连城县'],
            ['id' => '350881', 'name' => '漳平市', 'fullname' => '漳平市']
        ],
        '350900' => [ // 宁德市
            ['id' => '350902', 'name' => '蕉城区', 'fullname' => '蕉城区'],
            ['id' => '350921', 'name' => '霞浦县', 'fullname' => '霞浦县'],
            ['id' => '350922', 'name' => '古田县', 'fullname' => '古田县'],
            ['id' => '350923', 'name' => '屏南县', 'fullname' => '屏南县'],
            ['id' => '350924', 'name' => '寿宁县', 'fullname' => '寿宁县'],
            ['id' => '350925', 'name' => '周宁县', 'fullname' => '周宁县'],
            ['id' => '350926', 'name' => '柘荣县', 'fullname' => '柘荣县'],
            ['id' => '350981', 'name' => '福安市', 'fullname' => '福安市'],
            ['id' => '350982', 'name' => '福鼎市', 'fullname' => '福鼎市']
        ],
        '440100' => [ // 广州市
            ['id' => '440103', 'name' => '荔湾区', 'fullname' => '荔湾区'],
            ['id' => '440104', 'name' => '越秀区', 'fullname' => '越秀区'],
            ['id' => '440105', 'name' => '海珠区', 'fullname' => '海珠区'],
            ['id' => '440106', 'name' => '天河区', 'fullname' => '天河区'],
            ['id' => '440111', 'name' => '白云区', 'fullname' => '白云区'],
            ['id' => '440112', 'name' => '黄埔区', 'fullname' => '黄埔区'],
            ['id' => '440113', 'name' => '番禺区', 'fullname' => '番禺区'],
            ['id' => '440114', 'name' => '花都区', 'fullname' => '花都区'],
            ['id' => '440115', 'name' => '南沙区', 'fullname' => '南沙区'],
            ['id' => '440117', 'name' => '从化区', 'fullname' => '从化区'],
            ['id' => '440118', 'name' => '增城区', 'fullname' => '增城区']
        ],
        '440300' => [ // 深圳市
            ['id' => '440303', 'name' => '罗湖区', 'fullname' => '罗湖区'],
            ['id' => '440304', 'name' => '福田区', 'fullname' => '福田区'],
            ['id' => '440305', 'name' => '南山区', 'fullname' => '南山区'],
            ['id' => '440306', 'name' => '宝安区', 'fullname' => '宝安区'],
            ['id' => '440307', 'name' => '龙岗区', 'fullname' => '龙岗区'],
            ['id' => '440308', 'name' => '盐田区', 'fullname' => '盐田区'],
            ['id' => '440309', 'name' => '龙华区', 'fullname' => '龙华区'],
            ['id' => '440310', 'name' => '坪山区', 'fullname' => '坪山区'],
            ['id' => '440311', 'name' => '光明区', 'fullname' => '光明区'],
            ['id' => '440312', 'name' => '大鹏新区', 'fullname' => '大鹏新区']
        ]
    ];
    
    return $districts[$cityId] ?? [];
}

try {
    // 如果是直辖市的区，直接返回空数组
    if (isDirectMunicipalityDistrict($cityId)) {
        echo json_encode([
            'code' => 0,
            'message' => '直辖市区县无下级区划',
            'data' => []
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // 尝试调用腾讯地图API
    $url = $baseUrl . '/getchildren?id=' . urlencode($cityId) . '&key=' . $tencentMapKey;
    $context = stream_context_create([
        'http' => [
            'timeout' => 10,
            'method' => 'GET',
            'header' => 'User-Agent: Mozilla/5.0 (compatible; AddressAPI/1.0)'
        ]
    ]);
    
    $response = @file_get_contents($url, false, $context);
    
    if ($response !== false) {
        $data = json_decode($response, true);
        if ($data && $data['status'] === 0 && isset($data['result'][0])) {
            // API调用成功，返回腾讯地图数据
            echo json_encode([
                'code' => 0,
                'message' => '获取成功',
                'data' => $data['result'][0]
            ], JSON_UNESCAPED_UNICODE);
            exit;
        }
    }
    
    // API调用失败，使用备用数据
    echo json_encode([
        'code' => 0,
        'message' => '获取成功（使用备用数据）',
        'data' => getBackupDistricts($cityId)
    ], JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    // 发生异常，使用备用数据
    echo json_encode([
        'code' => 0,
        'message' => '获取成功（使用备用数据）',
        'data' => getBackupDistricts($cityId)
    ], JSON_UNESCAPED_UNICODE);
}
?> 