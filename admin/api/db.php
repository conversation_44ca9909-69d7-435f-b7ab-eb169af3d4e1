<?php
/**
 * 数据库配置文件
 * 提供统一的数据库连接配置
 */

// 数据库配置
$DB_CONFIG = [
    'HOST' => '127.0.0.1',
    'PORT' => 3306,
    'USER' => 'ddg.app',
    'PASSWORD' => '8GmWPjwbwY4waXcT',
    'DATABASE' => 'ddg.app',
    'CHARSET' => 'utf8mb4'
];

// 记录数据库连接信息
// error_log("DB Config: " . json_encode($DB_CONFIG));

/**
 * 获取数据库连接
 * @return mysqli|null 数据库连接对象，失败返回null
 */
function get_db_connection() {
    global $DB_CONFIG;

    // 创建日志目录
    $logDir = __DIR__ . '/logs';
    if (!is_dir($logDir)) {
        mkdir($logDir, 0755, true);
    }

    // 写入日志文件
    $logFile = $logDir . '/db_' . date('Y-m-d') . '.log';
    file_put_contents($logFile, date('Y-m-d H:i:s') . " - 尝试连接数据库: " . json_encode($DB_CONFIG) . "\n", FILE_APPEND);

    try {
        // 使用配置数组中的参数
        $conn = mysqli_connect(
            $DB_CONFIG['HOST'],
            $DB_CONFIG['USER'],
            $DB_CONFIG['PASSWORD'],
            $DB_CONFIG['DATABASE'],
            $DB_CONFIG['PORT']
        );

        if (!$conn) {
            $error = mysqli_connect_error();
            file_put_contents($logFile, date('Y-m-d H:i:s') . " - 数据库连接失败: $error\n", FILE_APPEND);
            return null;
        }

        // 设置字符集
        mysqli_set_charset($conn, $DB_CONFIG['CHARSET']);
        file_put_contents($logFile, date('Y-m-d H:i:s') . " - 数据库连接成功\n", FILE_APPEND);
        return $conn;
    } catch (Exception $e) {
        file_put_contents($logFile, date('Y-m-d H:i:s') . " - 数据库连接异常: " . $e->getMessage() . "\n", FILE_APPEND);
        return null;
    }
}

/**
 * 发送JSON响应
 */
function send_json_response($code, $message, $data = null) {
    header('Content-Type: application/json');
    $response = [
        'code' => $code,
        'message' => $message,
        'data' => $data
    ];

    echo json_encode($response, JSON_UNESCAPED_UNICODE);
    exit;
}
