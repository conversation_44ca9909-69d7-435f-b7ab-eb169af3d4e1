<?php
/**
 * 数据库配置文件
 * 这个文件用于原生PHP API，不依赖Laravel框架
 */

// 数据库连接配置
$database = [
    'driver'    => 'mysql',
    'host'      => '127.0.0.1',
    'port'      => '3306',
    'database'  => 'ddg.app',
    'username'  => 'ddg.app',
    'password'  => '8GmWPjwbwY4waXcT',
    'charset'   => 'utf8mb4',
    'collation' => 'utf8mb4_unicode_ci',
    'prefix'    => '',
];

// 净水器系统数据库连接
$water_database = [
    'driver'    => 'mysql',
    'host'      => '***********',
    'port'      => '3306',
    'database'  => 'jzq_water_plat',
    'username'  => 'jzq_water_plat',
    'password'  => 'FtWhdyw2Nn2pFaJN',
    'charset'   => 'utf8mb4',
    'collation' => 'utf8mb4_unicode_ci',
    'prefix'    => '',
];

// 返回数据库配置
return $database;
