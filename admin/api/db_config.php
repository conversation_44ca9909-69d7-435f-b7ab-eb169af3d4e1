<?php
// 数据库连接配置
$db_host = 'localhost';
$db_user = 'ddg_app';
$db_pass = 'Tapp@2023';
$db_name = 'ddg.app';

// 错误日志函数
function log_db_error($message, $data = null) {
    $logDir = __DIR__ . '/logs';
    if (!is_dir($logDir)) {
        mkdir($logDir, 0755, true);
    }
    
    $logFile = $logDir . '/db_connection_' . date('Y-m-d') . '.log';
    $logMessage = '[' . date('Y-m-d H:i:s') . '] ' . $message;
    
    if ($data !== null) {
        if (is_array($data) || is_object($data)) {
            $logMessage .= ' - ' . json_encode($data, JSON_UNESCAPED_UNICODE);
        } else {
            $logMessage .= ' - ' . $data;
        }
    }
    
    file_put_contents($logFile, $logMessage . "\n", FILE_APPEND);
}

/**
 * 获取数据库连接(单例模式)
 * @return PDO|null PDO连接对象，失败返回null
 */
function getDbConnection() {
    static $pdo = null;
    
    if ($pdo === null) {
        // 只记录首次连接信息
        log_db_error('首次创建数据库连接');
        
        // 数据库配置信息
        $host = '127.0.0.1';
        $port = 3306;
        $database = 'ddg.app';
        $username = 'ddg.app';
        $password = '8GmWPjwbwY4waXcT';
        
        try {
            // 构建DSN字符串
            $dsn = "mysql:host={$host};port={$port};dbname={$database};charset=utf8mb4";
            
            // 创建PDO连接
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci",
                PDO::ATTR_PERSISTENT => true // 使用持久连接
            ];
            
            $pdo = new PDO($dsn, $username, $password, $options);
            log_db_error('数据库连接创建成功');
        } catch (PDOException $e) {
            log_db_error('数据库连接失败', $e->getMessage());
            error_log('数据库连接错误: ' . $e->getMessage());
            return null;
        }
    }
    
    return $pdo;
}

// 记录错误日志
function logError($message) {
    $logFile = __DIR__ . '/logs/error_' . date('Y-m-d') . '.log';
    $timestamp = date('Y-m-d H:i:s');
    $logMessage = "[$timestamp] $message\n";
    
    // 确保日志目录存在
    if (!is_dir(dirname($logFile))) {
        mkdir(dirname($logFile), 0755, true);
    }
    
    file_put_contents($logFile, $logMessage, FILE_APPEND);
}

// 记录访问日志
function logAccess($api, $params = []) {
    $logFile = __DIR__ . '/logs/access_' . date('Y-m-d') . '.log';
    $timestamp = date('Y-m-d H:i:s');
    $ip = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
    $paramStr = json_encode($params);
    $logMessage = "[$timestamp] IP: $ip, API: $api, Params: $paramStr\n";
    
    // 确保日志目录存在
    if (!is_dir(dirname($logFile))) {
        mkdir(dirname($logFile), 0755, true);
    }
    
    file_put_contents($logFile, $logMessage, FILE_APPEND);
}

// 标准API响应格式
function apiResponse($code, $message, $data = null) {
    header('Content-Type: application/json');
    $response = [
        'code' => $code,
        'message' => $message,
        'data' => $data
    ];
    
    echo json_encode($response);
    exit;
}
