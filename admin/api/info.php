<?php
/**
 * 用户信息API - 返回当前登录用户的信息
 */

// 设置HTTP响应头
header("Content-Type: application/json");
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With");

// 处理OPTIONS请求
if ($_SERVER["REQUEST_METHOD"] === "OPTIONS") {
    http_response_code(200);
    exit;
}

// 引入配置和函数
require_once __DIR__ . "/config.php";
require_once __DIR__ . "/functions/db.php";
require_once __DIR__ . "/functions/auth.php";

// 记录访问日志
$request_time = date("Y-m-d H:i:s");
$request_ip = $_SERVER["REMOTE_ADDR"];
$request_method = $_SERVER["REQUEST_METHOD"];
$request_uri = $_SERVER["REQUEST_URI"];
$request_id = uniqid('info_');

// 记录请求信息
$log_dir = __DIR__ . '/logs/';
if (!is_dir($log_dir)) {
    mkdir($log_dir, 0755, true);
}
$log_file = $log_dir . 'info_api_' . date('Ymd') . '.log';
file_put_contents($log_file, "[{$request_time}] [{$request_id}] {$request_ip} {$request_method} {$request_uri} - 用户信息API请求\n", FILE_APPEND);

// 获取Authorization头
$headers = getallheaders();
$token = null;

// 从请求头中获取token
if (isset($headers["Authorization"])) {
    $auth_header = $headers["Authorization"];
    if (strpos($auth_header, "Bearer ") === 0) {
        $token = substr($auth_header, 7);
    }
} else if (isset($headers["authorization"])) {
    $auth_header = $headers["authorization"];
    if (strpos($auth_header, "Bearer ") === 0) {
        $token = substr($auth_header, 7);
    }
}

// 如果请求头中没有token，尝试从GET或POST参数中获取
if (!$token && isset($_GET["token"])) {
    $token = $_GET["token"];
} else if (!$token && isset($_POST["token"])) {
    $token = $_POST["token"];
}

// 记录token信息（仅记录前10个字符，避免泄露完整token）
if ($token) {
    $token_prefix = substr($token, 0, 10);
    file_put_contents($log_file, "[{$request_time}] [{$request_id}] 获取到Token前缀: {$token_prefix}...\n", FILE_APPEND);
} else {
    file_put_contents($log_file, "[{$request_time}] [{$request_id}] 未获取到Token\n", FILE_APPEND);
    
    // 未提供token，返回错误
    echo json_encode([
        "code" => 401,
        "message" => "未授权，请先登录",
        "data" => null,
        "rid" => $request_id
    ]);
    exit;
}

try {
    // 验证token并获取用户ID
    $user_id = verify_token($token);
    
    if (!$user_id) {
        // token无效
        file_put_contents($log_file, "[{$request_time}] [{$request_id}] Token验证失败\n", FILE_APPEND);
        echo json_encode([
            "code" => 401,
            "message" => "登录已过期，请重新登录",
            "data" => null,
            "rid" => $request_id
        ]);
        exit;
    }
    
    file_put_contents($log_file, "[{$request_time}] [{$request_id}] Token验证成功，用户ID: {$user_id}\n", FILE_APPEND);
    
    // 从数据库获取用户信息
    $conn = get_db_connection();
    if (!$conn) {
        file_put_contents($log_file, "[{$request_time}] [{$request_id}] 数据库连接失败\n", FILE_APPEND);
        echo json_encode([
            "code" => 500,
            "message" => "数据库连接失败",
            "data" => null,
            "rid" => $request_id
        ]);
        exit;
    }
    
    // 查询用户基本信息
    $stmt = $conn->prepare("SELECT * FROM app_users WHERE id = ?");
    $stmt->bind_param("i", $user_id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows === 0) {
        $conn->close();
        file_put_contents($log_file, "[{$request_time}] [{$request_id}] 用户不存在: {$user_id}\n", FILE_APPEND);
        echo json_encode([
            "code" => 404,
            "message" => "用户不存在",
            "data" => null,
            "rid" => $request_id
        ]);
        exit;
    }
    
    $user = $result->fetch_assoc();
    file_put_contents($log_file, "[{$request_time}] [{$request_id}] 获取到用户基本信息: " . json_encode(['id' => $user['id'], 'phone' => $user['phone']]) . "\n", FILE_APPEND);
    
    // 获取用户角色信息
    $user_with_roles = get_user_with_roles($user['phone'] ?: $user_id);
    
    if (!$user_with_roles) {
        file_put_contents($log_file, "[{$request_time}] [{$request_id}] 获取用户角色失败\n", FILE_APPEND);
        // 如果获取角色失败，使用基本用户信息
        $user_with_roles = $user;
        $user_with_roles['roles'] = [['id' => 'default_user', 'code' => 'user', 'name' => '普通用户']];
    } else {
        file_put_contents($log_file, "[{$request_time}] [{$request_id}] 获取用户角色成功: " . json_encode(array_map(function($role) { return $role['code']; }, $user_with_roles['roles'])) . "\n", FILE_APPEND);
    }
    
    // 关闭数据库连接
    $conn->close();
    
    // 返回成功响应
    echo json_encode([
        "code" => 0,
        "message" => "获取用户信息成功",
        "data" => $user_with_roles,
        "rid" => $request_id
    ]);
    
} catch (Exception $e) {
    file_put_contents($log_file, "[{$request_time}] [{$request_id}] 获取用户信息异常: " . $e->getMessage() . "\n" . $e->getTraceAsString() . "\n", FILE_APPEND);
    
    echo json_encode([
        "code" => 500,
        "message" => "服务器错误: " . $e->getMessage(),
        "data" => null,
        "rid" => $request_id
    ]);
}
?>
