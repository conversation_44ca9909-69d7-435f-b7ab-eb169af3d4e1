<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// 日志记录函数
function writeLog($message) {
    $logFile = '../logs/wechat_cache_clear_' . date('Y-m-d') . '.log';
    $logDir = dirname($logFile);
    if (!is_dir($logDir)) {
        mkdir($logDir, 0755, true);
    }
    $timestamp = date('Y-m-d H:i:s');
    file_put_contents($logFile, "[$timestamp] $message\n", FILE_APPEND | LOCK_EX);
}

try {
    writeLog("开始清除微信JSSDK缓存");
    
    $clearedFiles = [];
    $errors = [];
    
    // 清除access_token缓存
    $accessTokenFile = '../cache/access_token.json';
    if (file_exists($accessTokenFile)) {
        if (unlink($accessTokenFile)) {
            $clearedFiles[] = 'access_token.json';
            writeLog("成功删除access_token缓存文件");
        } else {
            $errors[] = '删除access_token缓存文件失败';
            writeLog("删除access_token缓存文件失败");
        }
    }
    
    // 清除jsapi_ticket缓存
    $ticketFile = '../cache/jsapi_ticket.json';
    if (file_exists($ticketFile)) {
        if (unlink($ticketFile)) {
            $clearedFiles[] = 'jsapi_ticket.json';
            writeLog("成功删除jsapi_ticket缓存文件");
        } else {
            $errors[] = '删除jsapi_ticket缓存文件失败';
            writeLog("删除jsapi_ticket缓存文件失败");
        }
    }
    
    // 清除其他可能的缓存文件
    $cacheDir = '../cache/';
    if (is_dir($cacheDir)) {
        $files = glob($cacheDir . 'wechat_*.json');
        foreach ($files as $file) {
            if (unlink($file)) {
                $clearedFiles[] = basename($file);
                writeLog("成功删除缓存文件: " . basename($file));
            } else {
                $errors[] = '删除缓存文件失败: ' . basename($file);
                writeLog("删除缓存文件失败: " . basename($file));
            }
        }
    }
    
    $result = [
        'success' => true,
        'message' => '缓存清除完成',
        'cleared_files' => $clearedFiles,
        'errors' => $errors,
        'timestamp' => time()
    ];
    
    writeLog("缓存清除完成，清除文件: " . implode(', ', $clearedFiles));
    
    echo json_encode($result, JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    $error = [
        'success' => false,
        'message' => '清除缓存失败: ' . $e->getMessage(),
        'error_code' => 'CACHE_CLEAR_ERROR'
    ];
    
    writeLog("清除缓存失败: " . $e->getMessage());
    
    http_response_code(500);
    echo json_encode($error, JSON_UNESCAPED_UNICODE);
}
?> 