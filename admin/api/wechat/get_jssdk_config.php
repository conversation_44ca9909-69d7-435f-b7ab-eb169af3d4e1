<?php
/**
 * 获取微信JSSDK配置
 *
 * 接收参数：
 * - url: 当前页面URL（不包含#及其后的内容）
 *
 * 返回数据：
 * {
 *   "code": 0,
 *   "message": "success",
 *   "data": {
 *     "appId": "wx123456789",
 *     "timestamp": 1234567890,
 *     "nonceStr": "abcdefg",
 *     "signature": "sha1签名"
 *   }
 * }
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// 引入配置文件和公共函数
// 使用统一的数据库连接文件
// require_once __DIR__ . '/../../db.php'; // 注释掉无效的数据库引用

require_once '../config.php';
// require_once dirname(dirname(__FILE__)) . '/functions.php'; /* 已注释 */

// 确保日志目录存在
$logDir = dirname(dirname(__FILE__)) . '/logs';
if (!file_exists($logDir)) {
    mkdir($logDir, 0777, true);
}

// 日志记录函数
function writeLog($message) {
    $logFile = '../logs/wechat_jssdk_' . date('Y-m-d') . '.log';
    $logDir = dirname($logFile);
    if (!is_dir($logDir)) {
        mkdir($logDir, 0755, true);
    }
    $timestamp = date('Y-m-d H:i:s');
    file_put_contents($logFile, "[$timestamp] $message\n", FILE_APPEND | LOCK_EX);
}

// 标准化URL处理函数
function normalizeUrl($url) {
    // 移除hash部分
    $url = preg_replace('/#.*$/', '', $url);
    
    // 移除查询参数
    $url = preg_replace('/\?.*$/', '', $url);
    
    // 统一域名格式
    $url = preg_replace('/^https?:\/\/[^\/]+/', 'https://pay.itapgo.com', $url);
    
    // 关键修复：将所有/app/相关路径都转换为根域名
    // 因为微信公众号后台配置的JS接口安全域名是 pay.itapgo.com
    if (strpos($url, '/app') !== false || strpos($url, '/Tapp') !== false) {
        $url = 'https://pay.itapgo.com/';
    }
    
    // 确保URL以/结尾（如果是根路径）
    if ($url === 'https://pay.itapgo.com' || $url === 'https://pay.itapgo.com/') {
        $url = 'https://pay.itapgo.com/';
    }
    
    return $url;
}

try {
    // 获取URL参数
    $url = isset($_GET['url']) ? trim($_GET['url']) : '';
    
    if (empty($url)) {
        throw new Exception('URL参数不能为空');
    }
    
    // 标准化URL
    $normalizedUrl = normalizeUrl($url);
    
    writeLog("原始URL: $url");
    writeLog("标准化URL: $normalizedUrl");
    
    // 微信配置
    $appId = WECHAT_APPID;
    $appSecret = WECHAT_APPSECRET;
    
    if (empty($appId) || empty($appSecret)) {
        throw new Exception('微信配置信息不完整');
    }
    
    // 获取access_token
    $accessTokenCacheFile = '../cache/access_token.json';
    $accessToken = '';
    $needRefreshToken = true;
    
    if (file_exists($accessTokenCacheFile)) {
        $tokenData = json_decode(file_get_contents($accessTokenCacheFile), true);
        if ($tokenData && isset($tokenData['access_token']) && isset($tokenData['expires_time'])) {
            if (time() < $tokenData['expires_time'] - 300) { // 提前5分钟刷新
                $accessToken = $tokenData['access_token'];
                $needRefreshToken = false;
                writeLog("使用缓存的access_token: " . substr($accessToken, 0, 20) . "...");
            }
        }
    }
    
    if ($needRefreshToken) {
        $tokenUrl = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=$appId&secret=$appSecret";
        $tokenResponse = file_get_contents($tokenUrl);
        
        if ($tokenResponse === false) {
            throw new Exception('获取access_token失败：网络请求失败');
        }
        
        $tokenData = json_decode($tokenResponse, true);
        
        if (!$tokenData || isset($tokenData['errcode'])) {
            $errorMsg = isset($tokenData['errmsg']) ? $tokenData['errmsg'] : '未知错误';
            throw new Exception("获取access_token失败：{$errorMsg}");
        }
        
        $accessToken = $tokenData['access_token'];
        
        // 缓存access_token
        $cacheData = [
            'access_token' => $accessToken,
            'expires_time' => time() + $tokenData['expires_in']
        ];
        
        $cacheDir = dirname($accessTokenCacheFile);
        if (!is_dir($cacheDir)) {
            mkdir($cacheDir, 0755, true);
        }
        
        file_put_contents($accessTokenCacheFile, json_encode($cacheData));
        writeLog("获取新的access_token: " . substr($accessToken, 0, 20) . "...");
    }
    
    // 获取jsapi_ticket
    $ticketCacheFile = '../cache/jsapi_ticket.json';
    $jsapiTicket = '';
    $needRefreshTicket = true;
    
    if (file_exists($ticketCacheFile)) {
        $ticketData = json_decode(file_get_contents($ticketCacheFile), true);
        if ($ticketData && isset($ticketData['ticket']) && isset($ticketData['expires_time'])) {
            if (time() < $ticketData['expires_time'] - 300) { // 提前5分钟刷新
                $jsapiTicket = $ticketData['ticket'];
                $needRefreshTicket = false;
                writeLog("使用缓存的jsapi_ticket: " . substr($jsapiTicket, 0, 20) . "...");
            }
        }
    }
    
    if ($needRefreshTicket) {
        $ticketUrl = "https://api.weixin.qq.com/cgi-bin/ticket/getticket?access_token=$accessToken&type=jsapi";
        $ticketResponse = file_get_contents($ticketUrl);
        
        if ($ticketResponse === false) {
            throw new Exception('获取jsapi_ticket失败：网络请求失败');
        }
        
        $ticketData = json_decode($ticketResponse, true);
        
        if (!$ticketData || $ticketData['errcode'] !== 0) {
            $errorMsg = isset($ticketData['errmsg']) ? $ticketData['errmsg'] : '未知错误';
            throw new Exception("获取jsapi_ticket失败：{$errorMsg}");
        }
        
        $jsapiTicket = $ticketData['ticket'];
        
        // 缓存jsapi_ticket
        $cacheData = [
            'ticket' => $jsapiTicket,
            'expires_time' => time() + $ticketData['expires_in']
        ];
        
        $cacheDir = dirname($ticketCacheFile);
        if (!is_dir($cacheDir)) {
            mkdir($cacheDir, 0755, true);
        }
        
        file_put_contents($ticketCacheFile, json_encode($cacheData));
        writeLog("获取新的jsapi_ticket: " . substr($jsapiTicket, 0, 20) . "...");
    }
    
    // 生成签名
    $timestamp = time();
    $nonceStr = md5(uniqid() . mt_rand());
    
    // 按字典序排序参数
    $params = [
        'jsapi_ticket' => $jsapiTicket,
        'noncestr' => $nonceStr,
        'timestamp' => $timestamp,
        'url' => $normalizedUrl
    ];
    
    ksort($params);
    
    // 拼接字符串
    $string = '';
    foreach ($params as $key => $value) {
        $string .= "$key=$value&";
    }
    $string = rtrim($string, '&');
    
    // 生成签名
    $signature = sha1($string);
    
    writeLog("签名字符串: $string");
    writeLog("生成签名: $signature");
    
    // 返回配置 - 使用标准的API响应格式
    $response = [
        'code' => 0,
        'message' => 'success',
        'data' => [
            'appId' => $appId,
            'timestamp' => $timestamp,
            'nonceStr' => $nonceStr,
            'signature' => $signature,
            'url' => $normalizedUrl
        ]
    ];
    
    writeLog("返回配置成功: " . json_encode($response, JSON_UNESCAPED_UNICODE));
    
    echo json_encode($response, JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    $error = [
        'code' => -1,
        'message' => $e->getMessage(),
        'data' => null
    ];
    
    writeLog("配置失败: " . $e->getMessage());
    
    http_response_code(500);
    echo json_encode($error, JSON_UNESCAPED_UNICODE);
}
