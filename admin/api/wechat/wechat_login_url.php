<?php
/**
 * 获取微信登录URL
 * 
 * 接收参数：
 * - redirect_uri: 登录成功后的回调地址
 * - state: 状态参数，会原样返回给回调地址
 * 
 * 返回数据：
 * {
 *   "code": 0,
 *   "message": "success",
 *   "data": {
 *     "url": "https://open.weixin.qq.com/connect/oauth2/authorize?..."
 *   }
 * }
 */

// 设置响应头
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
header('Cache-Control: no-cache, no-store, must-revalidate');
header('Pragma: no-cache');
header('Expires: 0');

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 记录请求信息
error_log("微信登录URL请求: " . json_encode($_GET));

// 微信配置
$wechatConfig = [
    'appid' => 'wx501332efbaae387c',
'appsecret' => 'f70ad4faefb54e68e3a5e7b5885a7c28'
];

// 获取参数
$redirectUri = isset($_GET['redirect_uri']) ? $_GET['redirect_uri'] : '';
$state = isset($_GET['state']) ? $_GET['state'] : '';

// 参数验证
if (empty($redirectUri)) {
    echo json_encode([
        'code' => 1,
        'message' => '缺少必要参数：redirect_uri',
        'data' => null
    ]);
    exit;
}

// 构建微信授权URL
$scope = 'snsapi_userinfo'; // 或者 snsapi_base，取决于你需要的权限
$wechatUrl = "https://open.weixin.qq.com/connect/oauth2/authorize?appid={$wechatConfig['appid']}&redirect_uri=" . urlencode($redirectUri) . "&response_type=code&scope={$scope}&state={$state}#wechat_redirect";

// 记录生成的URL
error_log("生成微信登录URL: " . $wechatUrl);

// 返回结果
echo json_encode([
    'code' => 0,
    'message' => 'success',
    'data' => [
        'url' => $wechatUrl
    ]
]);
exit;
