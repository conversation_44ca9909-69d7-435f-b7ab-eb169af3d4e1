<?php
/**
 * 微信支付回调通知接口
 * API路径: /api/wechat/pay_notify.php
 * 请求方式: POST
 * 
 * 微信支付成功后异步通知接口处理
 */

// 添加日志记录
function log_notify($message, $data = null) {
    $logDir = __DIR__ . '/../logs';
    if (!is_dir($logDir)) {
        mkdir($logDir, 0755, true);
    }
    
    $logFile = $logDir . '/wechat_pay_notify_' . date('Y-m-d') . '.log';
    $logMessage = '[' . date('Y-m-d H:i:s') . '] ' . $message;
    
    if ($data !== null) {
        if (is_array($data) || is_object($data)) {
            $logMessage .= ' - ' . json_encode($data, JSON_UNESCAPED_UNICODE);
        } else {
            $logMessage .= ' - ' . $data;
        }
    }
    
    file_put_contents($logFile, $logMessage . "\n", FILE_APPEND);
}

// 开启错误报告
ini_set('display_errors', 0); // 生产环境不输出错误
error_reporting(E_ALL);

// 引入必要文件
// // 使用统一的数据库连接文件
require_once __DIR__ . '/../../db.php';

require_once __DIR__ . '/../functions.php'; /* 已注释 */
require_once __DIR__ . '/../db_config.php';
require_once __DIR__ . '/../functions/commission_handler.php';

// 记录API开始执行
log_notify('微信支付回调通知开始处理');
log_notify('请求方法: ' . $_SERVER['REQUEST_METHOD']);

try {
    log_notify('进入try块');
    // 接收XML数据
    $xmlData = file_get_contents('php://input');
    log_notify('收到微信支付通知原始数据', $xmlData);
    
    if (empty($xmlData)) {
        log_notify('未接收到通知数据');
        exit('FAIL');
    }
    
    // 解析XML数据
    libxml_disable_entity_loader(true);
    $notifyData = @simplexml_load_string($xmlData, 'SimpleXMLElement', LIBXML_NOCDATA);
    
    if (!$notifyData) {
        $xmlError = libxml_get_errors();
        libxml_clear_errors();
        log_notify('解析XML失败', ['errors' => $xmlError]);
        exit('FAIL');
    }
    
    // 转换为数组
    $notifyArray = json_decode(json_encode($notifyData), true);
    log_notify('解析后的通知数据', $notifyArray);
    
    // 验证签名 - 增强安全性
    if (!isset($notifyArray['sign']) || empty($notifyArray['sign'])) {
        log_notify('缺少签名参数');
        exit('FAIL');
    }
    
    // 获取签名
    $receivedSign = $notifyArray['sign'];
    
    // 去除签名字段，重新计算签名
    unset($notifyArray['sign']);
    
    // 按照ASCII码排序
    ksort($notifyArray);
    
    // 构建签名字符串
    $signStr = '';
    foreach ($notifyArray as $key => $value) {
        if (!empty($value)) {
            $signStr .= "{$key}={$value}&";
        }
    }
    
    // 添加密钥
    if (!defined('WECHAT_MCH_KEY')) {
        define('WECHAT_MCH_KEY', '3fuwounqbmzq31qfjpg3obh030c0mv3y');
    }
    $signStr .= "key=" . WECHAT_MCH_KEY;
    
    // 计算MD5签名
    $calculatedSign = strtoupper(md5($signStr));
    
    // 验证签名是否匹配
    if ($receivedSign !== $calculatedSign) {
        log_notify('签名验证失败', ['received' => $receivedSign, 'calculated' => $calculatedSign]);
        exit('FAIL');
    }
    
    log_notify('签名验证通过');
    
    // 验证签名 - 这里需要加上验证签名的逻辑，暂时简单验证返回状态
    if ($notifyArray['return_code'] !== 'SUCCESS') {
        log_notify('通信标识非成功', $notifyArray['return_msg']);
        exit('FAIL');
    }
    
    if (!isset($notifyArray['result_code']) || $notifyArray['result_code'] !== 'SUCCESS') {
        log_notify('业务结果非成功', $notifyArray['err_code_des'] ?? '未知错误');
        exit('FAIL');
    }
    
    // 验证必要字段
    $requiredFields = ['out_trade_no', 'transaction_id', 'total_fee', 'openid'];
    foreach ($requiredFields as $field) {
        if (!isset($notifyArray[$field]) || empty($notifyArray[$field])) {
            log_notify('缺少必要字段: ' . $field);
            exit('FAIL');
        }
    }
    
    // 获取订单号和交易号
    $orderNo = $notifyArray['out_trade_no'];
    $transactionId = $notifyArray['transaction_id'];
    $totalFee = $notifyArray['total_fee']; // 单位为分
    $openid = $notifyArray['openid'];
    
    log_notify('支付成功，开始处理订单', [
        'order_no' => $orderNo, 
        'transaction_id' => $transactionId,
        'total_fee' => $totalFee
    ]);
    
    // 添加请求锁，防止并发处理同一订单
    $lockFile = sys_get_temp_dir() . '/pay_notify_lock_' . $orderNo . '.lock';
    $lockFp = fopen($lockFile, 'w+');
    if(!$lockFp || !flock($lockFp, LOCK_EX | LOCK_NB)) {
        // 如果无法获取锁，说明另一个请求正在处理
        log_notify('该订单已有请求正在处理', ['order_no' => $orderNo]);
        exit('SUCCESS'); // 返回SUCCESS避免微信重复通知
    }
    
    // 附加数据处理
    $attachData = [];
    if (isset($notifyArray['attach']) && !empty($notifyArray['attach'])) {
        $attachStr = $notifyArray['attach'];
        // 附加数据解析前
        log_notify('附加数据', $attachStr);
        
        // 解析附加数据格式 key1=value1&key2=value2
        $attachParts = explode('&', $attachStr);
        foreach ($attachParts as $part) {
            $keyValue = explode('=', $part);
            if (count($keyValue) === 2) {
                $attachData[$keyValue[0]] = $keyValue[1];
            }
        }
        
        log_notify('解析后的附加数据', $attachData);
    }
    // 附加数据处理后
    
    // 处理订单支付
    try {
        // 使用单例数据库连接
        $db = getDbConnection();
        if (!$db) {
            log_notify('数据库连接失败');
            error_log('支付回调: 数据库连接失败');
            
            // 释放锁
            if (isset($lockFp)) {
                flock($lockFp, LOCK_UN);
                fclose($lockFp);
                @unlink($lockFile);
            }
            
            exit('FAIL');
        }
        $db->beginTransaction();
        
        // 查询订单前
        // 查询订单
        $orderSql = "SELECT * FROM orders WHERE order_no = ?";
        log_notify('准备查询订单', ['sql'=>$orderSql, 'order_no'=>$orderNo]);
        $orderStmt = $db->prepare($orderSql);
        if (!$orderStmt) {
            log_notify('订单查询SQL prepare失败', $db->errorInfo());
            $db->rollBack();
            
            // 释放锁
            if (isset($lockFp)) {
                flock($lockFp, LOCK_UN);
                fclose($lockFp);
                @unlink($lockFile);
            }
            
            exit('FAIL');
        }
        $orderStmt->bindParam(1, $orderNo, PDO::PARAM_STR);
        $orderStmt->execute();
        $order = $orderStmt->fetch(PDO::FETCH_ASSOC);
        if (!$order) {
            log_notify('未找到订单', $orderNo);
            $db->rollBack();
            
            // 释放锁
            if (isset($lockFp)) {
                flock($lockFp, LOCK_UN);
                fclose($lockFp);
                @unlink($lockFile);
            }
            
            exit('FAIL');
        }
        log_notify('找到订单', $order);

        // 验证订单金额（分转元）
        $orderAmount = floatval($order['amount']);
        $notifyAmount = $totalFee / 100;
        log_notify('准备校验金额', ['order_amount'=>$orderAmount, 'notify_amount'=>$notifyAmount]);
        if (abs($orderAmount - $notifyAmount) > 0.01) {
            log_notify('订单金额不匹配', [
                'order_amount' => $orderAmount,
                'notify_amount' => $notifyAmount
            ]);
            $db->rollBack();
            
            // 释放锁
            if (isset($lockFp)) {
                flock($lockFp, LOCK_UN);
                fclose($lockFp);
                @unlink($lockFile);
            }
            
            exit('FAIL');
        }
        log_notify('金额校验通过');

        // 检查订单是否已处理
        log_notify('检查订单是否已处理', ['payment_status'=>$order['payment_status'], 'status'=>$order['status']]);
        if ($order['payment_status'] === 'paid' || $order['status'] == 1) {
            log_notify('订单已支付，无需重复处理');
            $db->commit();
            
            // 释放锁
            if (isset($lockFp)) {
                flock($lockFp, LOCK_UN);
                fclose($lockFp);
                @unlink($lockFile);
            }
            
            echo 'SUCCESS';
            exit;
        }
        
        // ====== 测试SQL写入权限 ======
        $testSql = "UPDATE orders SET updated_at=NOW() WHERE order_no=?";
        log_notify('准备执行测试SQL', ['sql'=>$testSql, 'order_no'=>$orderNo]);
        $testStmt = $db->prepare($testSql);
        if (!$testStmt) {
            log_notify('测试SQL prepare失败', $db->errorInfo());
        } else {
            $testStmt->bindParam(1, $orderNo, PDO::PARAM_STR);
            $testSuccess = $testStmt->execute();
            log_notify('测试SQL执行结果', ['success'=>$testSuccess, 'errorInfo'=>$testStmt->errorInfo()]);
        }
        // ====== 测试SQL写入权限 END ======

        // 检查是否是临时预约支付
        if (isset($attachData['booking_id']) && $attachData['booking_id'] === 'temp' && isset($attachData['temp_id'])) {
            log_notify('检测到临时预约支付', $attachData);
            
            // 对于临时预约，只需更新订单状态
            // 创建一个支付记录或标记为已支付
            $tempId = $attachData['temp_id'];
            log_notify('临时预约ID', ['temp_id' => $tempId]);
            
            // 由于是临时数据，我们不需要更新数据库，只记录交易成功即可
            $db->commit();
            log_notify('临时预约支付处理完成', ['order_no' => $orderNo, 'transaction_id' => $transactionId]);
            echo 'SUCCESS';
            exit;
        }
        
        // 处理正常预约支付
        // 查询订单
        $orderSql = "SELECT * FROM orders WHERE order_no = ?";
        $orderStmt = $db->prepare($orderSql);
        $orderStmt->bindParam(1, $orderNo, PDO::PARAM_STR);
        $orderStmt->execute();
        
        $order = $orderStmt->fetch(PDO::FETCH_ASSOC);
        if (!$order) {
            log_notify('未找到订单', $orderNo);
            $db->rollBack();
            exit('FAIL');
        }
        
        log_notify('找到订单', [
            'order_id' => $order['id'],
            'order_status' => $order['status'],
            'payment_status' => $order['payment_status']
        ]);
        
        // 验证订单金额（分转元）
        $orderAmount = floatval($order['amount']);
        $notifyAmount = $totalFee / 100;
        
        // 允许1分钱的误差（四舍五入问题）
        if (abs($orderAmount - $notifyAmount) > 0.01) {
            log_notify('订单金额不匹配', [
                'order_amount' => $orderAmount,
                'notify_amount' => $notifyAmount
            ]);
            $db->rollBack();
            exit('FAIL');
        }
        
        // 检查订单是否已处理
        if ($order['payment_status'] === 'paid' || $order['status'] == 1) {
            log_notify('订单已支付，无需重复处理');
            $db->commit();
            echo 'SUCCESS';
            exit;
        }
        
        // 更新订单状态（修正：status=1，paid_at=NOW()，payment_status='paid'）
        $updateOrderSql = "
            UPDATE orders 
            SET status = 1,
                payment_status = 'paid',
                paid_at = NOW(),
                payment_time = NOW(),
                pay_time = NOW(),
                payment_method = 'wechat',
                transaction_id = ?,
                updated_at = NOW()
            WHERE order_no = ?
        ";
        log_notify('准备执行订单状态更新SQL', ['sql'=>$updateOrderSql, 'order_no'=>$orderNo, 'transaction_id'=>$transactionId]);
        $updateOrderStmt = $db->prepare($updateOrderSql);
        if (!$updateOrderStmt) {
            log_notify('订单状态SQL prepare失败', $db->errorInfo());
            $db->rollBack();
            exit('FAIL');
        }
        $updateOrderStmt->bindParam(1, $transactionId, PDO::PARAM_STR);
        $updateOrderStmt->bindParam(2, $orderNo, PDO::PARAM_STR);
        $success = $updateOrderStmt->execute();
        log_notify('订单状态SQL执行结果', ['success'=>$success, 'errorInfo'=>$updateOrderStmt->errorInfo()]);
        if (!$success) {
            log_notify('订单状态SQL执行失败', $updateOrderStmt->errorInfo());
            $db->rollBack();
            exit('FAIL');
        }
        log_notify('订单状态已更新', $orderNo);

        // ===== 新增：VIP订单自动升级用户VIP状态 =====
        $isVipOrder = false;
        $referrerId = 0;
        // 判断product_type字段
        if (isset($order['product_type']) && $order['product_type'] === 'vip') {
            $isVipOrder = true;
        }
        // 判断data字段（JSON）
        if (!$isVipOrder && isset($order['data']) && $order['data']) {
            $dataArr = json_decode($order['data'], true);
            if (is_array($dataArr) && isset($dataArr['product_type']) && $dataArr['product_type'] === 'vip') {
                $isVipOrder = true;
                if (isset($dataArr['referrer_id'])) {
                    $referrerId = intval($dataArr['referrer_id']);
                }
            }
        }
        if ($isVipOrder) {
            $userId = $order['user_id'];
            // 取订单表的referrer_id优先
            if (isset($order['referrer_id']) && $order['referrer_id']) {
                $referrerId = intval($order['referrer_id']);
            }
            $updateUserSql = "UPDATE app_users SET is_vip=1, is_vip_paid=1, vip_at=NOW(), vip_paid_at=NOW()";
            if ($referrerId > 0) {
                $updateUserSql .= ", referrer_id=" . $referrerId;
            }
            $updateUserSql .= " WHERE id=?";
            log_notify('准备执行VIP用户升级SQL', ['sql'=>$updateUserSql, 'user_id'=>$userId, 'referrer_id'=>$referrerId]);
            $updateUserStmt = $db->prepare($updateUserSql);
            if (!$updateUserStmt) {
                log_notify('VIP用户升级SQL prepare失败', $db->errorInfo());
                $db->rollBack();
                exit('FAIL');
            }
            $updateUserStmt->bindParam(1, $userId, PDO::PARAM_INT);
            $successUser = $updateUserStmt->execute();
            log_notify('VIP用户升级SQL执行结果', ['success'=>$successUser, 'errorInfo'=>$updateUserStmt->errorInfo()]);
            if (!$successUser) {
                log_notify('VIP用户升级SQL执行失败', $updateUserStmt->errorInfo());
                $db->rollBack();
                exit('FAIL');
            }
            log_notify('VIP订单，已自动升级用户VIP状态', ['user_id'=>$userId, 'referrer_id'=>$referrerId]);
        }
        // ===== END =====
        
        // 如果是安装预约支付，更新预约状态
        if (isset($attachData['booking_id']) && !empty($attachData['booking_id'])) {
            $bookingId = $attachData['booking_id'];
            log_notify('处理关联的安装预约', $bookingId);
            
            // 查询预约信息
            $bookingSql = "SELECT * FROM install_bookings WHERE id = ?";
            $bookingStmt = $db->prepare($bookingSql);
            $bookingStmt->bindParam(1, $bookingId, PDO::PARAM_INT);
            $bookingStmt->execute();
            
            $booking = $bookingStmt->fetch(PDO::FETCH_ASSOC);
            if ($booking) {
                // 更新预约支付状态
                $updateBookingSql = "
                    UPDATE install_bookings 
                    SET payment_status = 'paid',
                        payment_time = NOW(),
                        payment_method = 'wechat',
                        transaction_id = ?,
                        updated_at = NOW()
                    WHERE id = ?
                ";
                
                $updateBookingStmt = $db->prepare($updateBookingSql);
                $updateBookingStmt->bindParam(1, $transactionId, PDO::PARAM_STR);
                $updateBookingStmt->bindParam(2, $bookingId, PDO::PARAM_INT);
                $updateBookingStmt->execute();
                
                log_notify('预约支付状态已更新', $bookingId);
                
                // 处理分润
                if (!empty($booking['referrer_id'])) {
                    log_notify('开始处理分润', [
                        'booking_id' => $bookingId,
                        'referrer_id' => $booking['referrer_id'],
                        'order_id' => $order['id'],
                        'amount' => $order['amount']
                    ]);
                    
                    // 调用分润处理函数
                    $commissionResult = processInstallationCommission(
                        $order['id'],
                        $orderNo,
                        $booking['referrer_id'],
                        floatval($order['amount']),
                        $db
                    );
                    
                    log_notify('分润处理结果', [
                        'success' => $commissionResult ? 'true' : 'false',
                        'booking_id' => $bookingId,
                        'referrer_id' => $booking['referrer_id']
                    ]);
                } else {
                    log_notify('无推荐人信息，跳过分润处理', ['booking_id' => $bookingId]);
                }
            } else {
                log_notify('未找到相关预约', $bookingId);
                // 不影响订单处理，继续执行
            }
        }
        
        // 提交事务
        log_notify('准备提交事务');
        $db->commit();
        log_notify('支付处理完成', $orderNo);
        
        // 释放锁
        if (isset($lockFp)) {
            flock($lockFp, LOCK_UN);
            fclose($lockFp);
            @unlink($lockFile);
        }
        
        echo 'SUCCESS';
    } catch (Exception $e) {
        // 回滚事务
        if (isset($db) && $db->inTransaction()) {
            $db->rollBack();
        }
        
        // 记录错误
        log_notify('订单处理异常', ['error' => $e->getMessage(), 'trace' => $e->getTraceAsString()]);
        
        // 释放锁
        if (isset($lockFp)) {
            flock($lockFp, LOCK_UN);
            fclose($lockFp);
            @unlink($lockFile);
        }
        
        exit('FAIL');
    }
} catch (Exception $e) {
    // 记录异常
    log_notify('通知处理全局异常', ['error' => $e->getMessage(), 'trace' => $e->getTraceAsString()]);
    
    // 释放锁
    if (isset($lockFp)) {
        flock($lockFp, LOCK_UN);
        fclose($lockFp);
        @unlink($lockFile);
    }
    
    exit('FAIL');
}
