<?php
/**
 * 微信JSSDK配置接口
 * 用于获取微信分享所需的配置信息
 */

// 设置错误报告级别，避免在生产环境显示错误
error_reporting(E_ERROR);
ini_set('display_errors', 0);

// 自定义日志级别设置
define('LOG_LEVEL_ERROR', true);   // 是否记录错误日志
define('LOG_LEVEL_INFO', false);   // 是否记录信息日志

// 自定义日志函数
function custom_log($message, $level = 'INFO') {
    if (($level === 'ERROR' && LOG_LEVEL_ERROR) ||
        ($level === 'INFO' && LOG_LEVEL_INFO)) {
        error_log("PHP message: " . $message);
    }
}

// 设置返回类型为JSON
header('Content-Type: application/json; charset=utf-8');

// 允许跨域请求
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With");

// 添加缓存控制头
header("Cache-Control: no-cache, no-store, must-revalidate");
header("Pragma: no-cache");
header("Expires: 0");

// 如果是OPTIONS请求，直接返回成功
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 引入配置文件
require_once dirname(dirname(__FILE__)) . '/config.php';

/**
 * 获取微信JSSDK配置
 * @param string $url 需要签名的URL
 * @return array 配置信息
 */
function get_wechat_js_config($url) {
    global $WECHAT_CONFIG;

    // 检查参数
    if (empty($url)) {
        return [
            'code' => 1001,
            'message' => '参数错误：缺少URL参数',
            'data' => null
        ];
    }

    try {
        // 检查微信配置是否存在
        if (!isset($WECHAT_CONFIG) || !isset($WECHAT_CONFIG['APP_ID']) || !isset($WECHAT_CONFIG['APP_SECRET'])) {
            return [
                'code' => 1002,
                'message' => '微信配置缺失',
                'data' => null
            ];
        }

        // 获取access_token
        $access_token = get_wechat_access_token();
        if (!$access_token) {
            return [
                'code' => 1003,
                'message' => '获取微信access_token失败',
                'data' => null
            ];
        }

        // 获取jsapi_ticket
        $jsapi_ticket = get_wechat_jsapi_ticket($access_token);
        if (!$jsapi_ticket) {
            return [
                'code' => 1004,
                'message' => '获取微信jsapi_ticket失败',
                'data' => null
            ];
        }

        // 生成签名参数
        $timestamp = time();
        $nonceStr = create_nonce_str();

        // 按照字典序排序参数
        $string = "jsapi_ticket=$jsapi_ticket&noncestr=$nonceStr&timestamp=$timestamp&url=$url";
        $signature = sha1($string);

        // 返回配置信息
        return [
            'code' => 0,
            'message' => '获取微信JSSDK配置成功',
            'data' => [
                'appId' => $WECHAT_CONFIG['APP_ID'],
                'timestamp' => $timestamp,
                'nonceStr' => $nonceStr,
                'signature' => $signature,
                'jsApiList' => ['updateAppMessageShareData', 'updateTimelineShareData', 'onMenuShareTimeline', 'onMenuShareAppMessage', 'chooseWXPay', 'getBrandWCPayRequest']
            ]
        ];
    } catch (Exception $e) {
        return [
            'code' => 1099,
            'message' => '获取微信JSSDK配置失败：' . $e->getMessage(),
            'data' => null
        ];
    }
}

/**
 * 获取微信access_token
 * @return string|bool access_token或失败返回false
 */
function get_wechat_access_token() {
    global $WECHAT_CONFIG;

    // 首先尝试从缓存获取
    $cache_file = dirname(dirname(__FILE__)) . '/cache/wechat_access_token.json';
    if (file_exists($cache_file)) {
        $data = json_decode(file_get_contents($cache_file), true);
        if ($data && isset($data['access_token']) && isset($data['expire_time']) && $data['expire_time'] > time()) {
            // 缓存有效
            return $data['access_token'];
        }
    }

    // 缓存无效，重新获取
    $url = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid={$WECHAT_CONFIG['APP_ID']}&secret={$WECHAT_CONFIG['APP_SECRET']}";

    // 使用curl而不是file_get_contents，确保更好的错误处理
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10); // 设置超时
    $response = curl_exec($ch);
    curl_close($ch);

    if (!$response) {
        error_log('获取微信access_token失败: curl请求失败');
        return false;
    }

    $result = json_decode($response, true);

    if (isset($result['access_token'])) {
        // 保存到缓存
        $data = [
            'access_token' => $result['access_token'],
            'expire_time' => time() + 7000 // 有效期设为2小时以内，微信官方为7200秒
        ];

        // 确保缓存目录存在
        if (!is_dir(dirname($cache_file))) {
            mkdir(dirname($cache_file), 0755, true);
        }

        file_put_contents($cache_file, json_encode($data));
        return $result['access_token'];
    } else {
        error_log('获取微信access_token失败: ' . json_encode($result));
        return false;
    }
}

/**
 * 获取微信jsapi_ticket
 * @param string $access_token 微信access_token
 * @return string|bool jsapi_ticket或失败返回false
 */
function get_wechat_jsapi_ticket($access_token) {
    // 首先尝试从缓存获取
    $cache_file = dirname(dirname(__FILE__)) . '/cache/wechat_jsapi_ticket.json';
    if (file_exists($cache_file)) {
        $data = json_decode(file_get_contents($cache_file), true);
        if ($data && isset($data['ticket']) && isset($data['expire_time']) && $data['expire_time'] > time()) {
            // 缓存有效
            return $data['ticket'];
        }
    }

    // 缓存无效，重新获取
    $url = "https://api.weixin.qq.com/cgi-bin/ticket/getticket?access_token={$access_token}&type=jsapi";

    // 使用curl而不是file_get_contents
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    $response = curl_exec($ch);
    curl_close($ch);

    if (!$response) {
        error_log('获取微信jsapi_ticket失败: curl请求失败');
        return false;
    }

    $result = json_decode($response, true);

    if (isset($result['ticket'])) {
        // 保存到缓存
        $data = [
            'ticket' => $result['ticket'],
            'expire_time' => time() + 7000 // 有效期设为2小时以内，微信官方为7200秒
        ];

        // 确保缓存目录存在
        if (!is_dir(dirname($cache_file))) {
            mkdir(dirname($cache_file), 0755, true);
        }

        file_put_contents($cache_file, json_encode($data));
        return $result['ticket'];
    } else {
        error_log('获取微信jsapi_ticket失败: ' . json_encode($result));
        return false;
    }
}

/**
 * 生成随机字符串
 * @param int $length 字符串长度
 * @return string 随机字符串
 */
function create_nonce_str($length = 16) {
    $chars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
    $str = "";
    for ($i = 0; $i < $length; $i++) {
        $str .= substr($chars, mt_rand(0, strlen($chars) - 1), 1);
    }
    return $str;
}

// 处理请求
$url = isset($_GET['url']) ? $_GET['url'] : '';

// 如果没有提供URL，尝试从请求头中获取
if (empty($url) && isset($_SERVER['HTTP_REFERER'])) {
    $url = $_SERVER['HTTP_REFERER'];
    // 删除URL中的锚点部分
    $url = preg_replace('/#.*$/', '', $url);
    custom_log("从HTTP_REFERER获取URL: " . $url, 'INFO');
}

// 记录请求信息
custom_log("处理URL分享配置: " . $url, 'INFO');
custom_log("请求方法: " . $_SERVER['REQUEST_METHOD'], 'INFO');
custom_log("请求头: " . json_encode(getallheaders()), 'INFO');

// 获取配置
$result = get_wechat_js_config($url);
custom_log("微信配置结果: " . json_encode($result), 'INFO');

// 输出结果
echo json_encode($result, JSON_UNESCAPED_UNICODE);
exit;