<?php
/**
 * 微信登录回调处理
 * 
 * 接收参数：
 * - code: 微信授权码
 * - state: 状态码，防止CSRF攻击
 * 
 * 返回数据：
 * {
 *   "code": 0,
 *   "message": "登录成功",
 *   "data": {
 *     "token": "JWT令牌",
 *     "user": {用户信息},
 *     "needBindPhone": true/false
 *   }
 * }
 */

// 记录调试信息
error_log("微信登录回调开始处理: " . date('Y-m-d H:i:s'));
error_log("POST数据: " . file_get_contents('php://input'));
error_log("GET数据: " . json_encode($_GET));

// 设置响应头
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 微信配置
define('WECHAT_APP_ID', 'wx501332efbaae387c');
define('WECHAT_APP_SECRET', 'f70ad4faefb54e68e3a5e7b5885a7c28');

// 获取请求参数
$data = json_decode(file_get_contents('php://input'), true) ?: [];
// 如果POST为空，尝试从GET获取
if (empty($data)) {
    $data = $_GET;
}
$code = isset($data['code']) ? $data['code'] : '';
$state = isset($data['state']) ? $data['state'] : '';

error_log("处理参数: code=" . $code . ", state=" . $state);

// 验证必需的参数
if (empty($code)) {
    echo json_encode([
        'code' => 1,
        'message' => '缺少参数code',
        'data' => null
    ]);
    exit;
}

// 数据库连接函数
function get_db_connection() {
    static $conn = null;
    if ($conn === null) {
        // 记录调试信息
        error_log("尝试连接数据库...");

        // 使用环境变量中的配置
        $db_host = '127.0.0.1';
        $db_user = 'ddg.app';
        $db_pass = '8GmWPjwbwY4waXcT';
        $db_name = 'ddg.app';
        $db_port = 3306;
        
        try {
            // 使用mysqli连接
            $conn = new mysqli($db_host, $db_user, $db_pass, $db_name, $db_port);
            if ($conn->connect_error) {
                error_log("数据库连接错误(mysqli): " . $conn->connect_error);
                
                // 如果mysqli连接失败，尝试使用PDO
                try {
                    $dsn = "mysql:host=$db_host;port=$db_port;dbname=$db_name;charset=utf8mb4";
                    $options = [
                        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                        PDO::ATTR_EMULATE_PREPARES => false,
                    ];
                    $pdo = new PDO($dsn, $db_user, $db_pass, $options);
                    error_log("PDO连接成功");
                    return $pdo;
                } catch (PDOException $e) {
                    error_log("数据库连接错误(PDO): " . $e->getMessage());
                    return null;
                }
            }
            
            // 设置字符集
            $conn->set_charset('utf8mb4');
            error_log("数据库连接成功");
            
            // 测试查询
            $test_query = "SELECT 1 AS test";
            $test_result = $conn->query($test_query);
            if ($test_result && $test_result->fetch_assoc()) {
                error_log("数据库查询测试成功");
            } else {
                error_log("数据库查询测试失败: " . $conn->error);
            }
            
            return $conn;
        } catch (Exception $e) {
            error_log("数据库连接异常: " . $e->getMessage());
            return null;
        }
    }
    return $conn;
}

// JWT生成函数
function generate_jwt($payload, $expire_seconds = 86400) {
    $header = [
        'typ' => 'JWT',
        'alg' => 'HS256'
    ];
    
    // 添加过期时间
    $payload['exp'] = time() + $expire_seconds;
    
    // 对header和payload进行base64编码
    $header_b64 = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode(json_encode($header)));
    $payload_b64 = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode(json_encode($payload)));
    
    // 生成签名
    $jwt_secret = 'tapp_jwt_secret_key_2023';
    $signature = hash_hmac('sha256', $header_b64 . '.' . $payload_b64, $jwt_secret, true);
    $signature_b64 = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($signature));
    
    // 拼接JWT令牌
    return $header_b64 . '.' . $payload_b64 . '.' . $signature_b64;
}

// 获取用户角色函数
function get_user_with_roles($user_id) {
    $conn = get_db_connection();
    if (!$conn) {
        return null;
    }
    
    try {
        // 获取用户基本信息
        $stmt = $conn->prepare("SELECT * FROM app_users WHERE id = ?");
        $stmt->bind_param("i", $user_id);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result->num_rows === 0) {
            $stmt->close();
            return null;
        }
        
        $user = $result->fetch_assoc();
        $stmt->close();
        
        // 获取用户角色
        $roles = !empty($user['roles']) ? explode(',', $user['roles']) : [];
        
        // 屏蔽敏感信息
        unset($user['password']);
        
        // 添加角色信息
        $user['roles'] = $roles;
        
        return $user;
    } catch (Exception $e) {
        error_log("获取用户信息异常: " . $e->getMessage());
        return null;
    } finally {
        if ($conn) {
            $conn->close();
        }
    }
}

// 辅助函数：发送GET请求
function curl_get($url) {
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    $response = curl_exec($ch);
    
    if ($response === false) {
        error_log("CURL请求失败: " . curl_error($ch) . ", URL: " . $url);
    }
    
    curl_close($ch);
    return $response;
}

try {
    // 获取微信访问令牌和OpenID
    $tokenUrl = "https://api.weixin.qq.com/sns/oauth2/access_token?appid=" . WECHAT_APP_ID . "&secret=" . WECHAT_APP_SECRET . "&code=" . $code . "&grant_type=authorization_code";
    $tokenResponse = curl_get($tokenUrl);
    error_log("微信token响应: " . $tokenResponse);
    
    $tokenData = json_decode($tokenResponse, true);
    
    if (!isset($tokenData['access_token']) || !isset($tokenData['openid'])) {
        echo json_encode([
            'code' => 1,
            'message' => '获取微信访问令牌失败: ' . ($tokenData['errmsg'] ?? '未知错误'),
            'data' => null
        ]);
        exit;
    }
    
    $accessToken = $tokenData['access_token'];
    $openId = $tokenData['openid'];
    $unionId = $tokenData['unionid'] ?? null;
    
    error_log("成功获取微信令牌: access_token=" . substr($accessToken, 0, 10) . "..., openid=" . $openId . ", unionid=" . ($unionId ?: 'null'));
    
    // 获取微信用户信息
    $userInfoUrl = "https://api.weixin.qq.com/sns/userinfo?access_token=" . $accessToken . "&openid=" . $openId . "&lang=zh_CN";
    $userInfoResponse = curl_get($userInfoUrl);
    error_log("微信用户信息响应: " . $userInfoResponse);
    
    $userInfo = json_decode($userInfoResponse, true);
    
    if (!isset($userInfo['nickname'])) {
        echo json_encode([
            'code' => 1,
            'message' => '获取微信用户信息失败: ' . ($userInfo['errmsg'] ?? '未知错误'),
            'data' => null
        ]);
        exit;
    }
    
    // 连接数据库
    $conn = get_db_connection();
    if (!$conn) {
        echo json_encode([
            'code' => 9999,
            'message' => '数据库连接失败',
            'data' => null
        ]);
        exit;
    }
    
    // 根据OpenID或UnionID查找用户
    $stmt = null;
    $user = null;
    
    if ($unionId) {
        $stmt = $conn->prepare("SELECT * FROM app_users WHERE union_id = ?");
        $stmt->bind_param("s", $unionId);
        $stmt->execute();
        $result = $stmt->get_result();
        $user = $result->fetch_assoc();
        $stmt->close();
    }
    
    if (!$user) {
        $stmt = $conn->prepare("SELECT * FROM app_users WHERE open_id = ?");
        $stmt->bind_param("s", $openId);
        $stmt->execute();
        $result = $stmt->get_result();
        $user = $result->fetch_assoc();
        $stmt->close();
    }
    
    error_log("查询用户结果: " . ($user ? "找到用户ID=" . $user['id'] : "未找到用户"));
    
    if (!$user) {
        // 创建新用户
        $nickname = $userInfo['nickname'];
        $avatar = $userInfo['headimgurl'];
        $gender = $userInfo['sex']; // 1为男性，2为女性，0为未知
        
        error_log("准备创建新用户: nickname=" . $nickname);
        
        $username = 'wx_' . time() . rand(1000, 9999);
        $stmt = $conn->prepare("INSERT INTO app_users (username, nickname, avatar, gender, open_id, union_id, registered_time, last_login_time) VALUES (?, ?, ?, ?, ?, ?, NOW(), NOW())");
        $stmt->bind_param("sssiss", $username, $nickname, $avatar, $gender, $openId, $unionId);
        $stmt->execute();
        
        if ($stmt->affected_rows > 0) {
            $userId = $conn->insert_id;
            error_log("成功创建新用户: ID=" . $userId);
            
            // 获取新创建的用户
            $stmt->close();
            $stmt = $conn->prepare("SELECT * FROM app_users WHERE id = ?");
            $stmt->bind_param("i", $userId);
            $stmt->execute();
            $result = $stmt->get_result();
            $user = $result->fetch_assoc();
            $stmt->close();
        } else {
            error_log("创建用户失败: " . $stmt->error);
            $stmt->close();
            echo json_encode([
                'code' => 9999,
                'message' => '创建用户失败',
                'data' => null
            ]);
            $conn->close();
            exit;
        }
    } else {
        // 更新用户的微信信息
        $nickname = $userInfo['nickname'];
        $avatar = $userInfo['headimgurl'];
        
        if ($unionId && empty($user['union_id'])) {
            $stmt = $conn->prepare("UPDATE app_users SET union_id = ?, nickname = ?, avatar = ?, last_login_time = NOW() WHERE id = ?");
            $stmt->bind_param("sssi", $unionId, $nickname, $avatar, $user['id']);
        } else {
            $stmt = $conn->prepare("UPDATE app_users SET nickname = ?, avatar = ?, last_login_time = NOW() WHERE id = ?");
            $stmt->bind_param("ssi", $nickname, $avatar, $user['id']);
        }
        $stmt->execute();
        $stmt->close();
        
        error_log("更新用户信息: ID=" . $user['id']);
    }
    
    // 生成JWT令牌
    $payload = [
        'user_id' => $user['id'],
        'referrer_id' => $user['referrer_id'] ?? null
    ];
    
    $token = generate_jwt($payload, 86400 * 30); // 30天有效期
    
    error_log("生成JWT令牌: " . substr($token, 0, 20) . "...");
    
    // 获取用户完整信息（包括角色）
    $user_with_roles = get_user_with_roles($user['id']);
    
    $conn->close();
    
    // 返回登录成功响应
    echo json_encode([
        'code' => 0,
        'message' => '登录成功',
        'data' => [
            'token' => $token,
            'user' => $user_with_roles,
            'needBindPhone' => empty($user['phone'])
        ]
    ]);
    
    error_log("微信登录完成，用户ID: " . $user['id']);
} catch (Exception $e) {
    error_log("微信登录异常: " . $e->getMessage() . "\n" . $e->getTraceAsString());
    echo json_encode([
        'code' => 9999,
        'message' => '处理异常: ' . $e->getMessage(),
        'data' => null
    ]);
}
?> 