<?php
header('Content-Type: application/json; charset=utf-8');
// 只引入functions.php，不再引入db_connect.php
// require_once __DIR__ . '/../../db_connect.php';
require_once __DIR__ . '/../../functions.php';

// 验证请求
$token = getBearerToken();
// 调试信息
error_log("验证请求: 业务员列表接收到Token: " . substr($token, 0, 10) . "...");

// 修改验证逻辑：不再查询数据库，直接验证JWT格式
if (!$token || !verifyTokenFormat($token)) {
    http_response_code(401);
    echo json_encode(['code' => 401, 'message' => '未授权访问或Token格式不正确', 'data' => null]);
    exit;
}

// 获取请求参数
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 10;
$keyword = isset($_GET['keyword']) ? $_GET['keyword'] : '';
$status = isset($_GET['status']) ? $_GET['status'] : '';
$date_start = isset($_GET['date_start']) ? $_GET['date_start'] : '';
$date_end = isset($_GET['date_end']) ? $_GET['date_end'] : '';

// 计算偏移量
$offset = ($page - 1) * $limit;

// 构建查询条件
$where = ["is_salesman = 1"];
$params = [];

if (!empty($keyword)) {
    $where[] = "(name LIKE ? OR phone LIKE ? OR email LIKE ? OR wechat_nickname LIKE ?)";
    $params[] = "%$keyword%";
    $params[] = "%$keyword%";
    $params[] = "%$keyword%";
    $params[] = "%$keyword%";
}

if (!empty($status)) {
    $where[] = "status = ?";
    $params[] = $status;
}

if (!empty($date_start) && !empty($date_end)) {
    $where[] = "created_at BETWEEN ? AND ?";
    $params[] = $date_start . ' 00:00:00';
    $params[] = $date_end . ' 23:59:59';
} else if (!empty($date_start)) {
    $where[] = "created_at >= ?";
    $params[] = $date_start . ' 00:00:00';
} else if (!empty($date_end)) {
    $where[] = "created_at <= ?";
    $params[] = $date_end . ' 23:59:59';
}

// 构建WHERE子句
$whereClause = !empty($where) ? 'WHERE ' . implode(' AND ', $where) : '';

try {
    // 获取数据库连接
    $conn = get_db_connection();
    if (!$conn) {
        throw new Exception("数据库连接失败");
    }
    
    // 使用mysqli而不是PDO
    
    // 获取总记录数
    $countSql = "SELECT COUNT(*) as total FROM app_users $whereClause";
    
    $countStmt = mysqli_prepare($conn, $countSql);
    
    if (!empty($params)) {
        // 构建参数类型字符串
        $types = str_repeat('s', count($params));
        // 绑定参数
        mysqli_stmt_bind_param($countStmt, $types, ...$params);
    }
    
    mysqli_stmt_execute($countStmt);
    mysqli_stmt_bind_result($countStmt, $total);
    mysqli_stmt_fetch($countStmt);
    mysqli_stmt_close($countStmt);

    // 获取分页数据
    $sql = "SELECT * FROM app_users $whereClause ORDER BY id DESC LIMIT ? OFFSET ?";
    
    $stmt = mysqli_prepare($conn, $sql);
    
    // 添加limit和offset参数
    $allParams = $params;
    $allParams[] = $limit;
    $allParams[] = $offset;
    
    if (!empty($allParams)) {
        // 构建参数类型字符串
        $types = str_repeat('s', count($allParams) - 2) . 'ii';
        // 绑定参数
        mysqli_stmt_bind_param($stmt, $types, ...$allParams);
    }
    
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);
    
    $salesmen = [];
    while ($row = mysqli_fetch_assoc($result)) {
        $salesmen[] = $row;
    }
    
    mysqli_stmt_close($stmt);

    // 处理业务员数据
    foreach ($salesmen as &$salesman) {
        // 获取推荐人信息
        if (!empty($salesman['referrer_id'])) {
            $referrerSql = "SELECT id, name, wechat_nickname FROM app_users WHERE id = ?";
            $referrerStmt = mysqli_prepare($conn, $referrerSql);
            mysqli_stmt_bind_param($referrerStmt, "i", $salesman['referrer_id']);
            mysqli_stmt_execute($referrerStmt);
            $referrerResult = mysqli_stmt_get_result($referrerStmt);
            $referrer = mysqli_fetch_assoc($referrerResult);

            if ($referrer) {
                $salesman['referrer_name'] = $referrer['name'] ?: $referrer['wechat_nickname'] ?: '用户'.$referrer['id'];
            } else {
                $salesman['referrer_name'] = '未知用户';
            }
            
            mysqli_stmt_close($referrerStmt);
        } else {
            $salesman['referrer_name'] = '点点够';
        }

        // 获取业务员的销售统计
        $statsSql = "SELECT
                        COUNT(*) as total_sales,
                        SUM(amount) as total_amount
                     FROM sales
                     WHERE salesman_id = ?";
        $statsStmt = mysqli_prepare($conn, $statsSql);
        mysqli_stmt_bind_param($statsStmt, "i", $salesman['id']);
        mysqli_stmt_execute($statsStmt);
        $statsResult = mysqli_stmt_get_result($statsStmt);
        $stats = mysqli_fetch_assoc($statsResult);
        
        mysqli_stmt_close($statsStmt);

        $salesman['total_sales'] = $stats ? (int)$stats['total_sales'] : 0;
        $salesman['total_amount'] = $stats ? (float)$stats['total_amount'] : 0.00;
    }
    
    // 关闭数据库连接
    mysqli_close($conn);

    // 返回结果
    echo json_encode([
        'code' => 0,
        'message' => 'success',
        'data' => $salesmen,
        'total' => $total,
        'page' => $page,
        'limit' => $limit
    ]);
} catch (Exception $e) {
    error_log('获取业务员列表失败: ' . $e->getMessage());
    http_response_code(500);
    echo json_encode(['code' => 500, 'message' => '获取业务员列表失败: ' . $e->getMessage(), 'data' => null]);
}

// 获取Bearer Token
function getBearerToken() {
    $headers = getallheaders();
    if (isset($headers['Authorization'])) {
        if (preg_match('/Bearer\s(\S+)/', $headers['Authorization'], $matches)) {
            return $matches[1];
        }
    }
    
    // 从GET或POST参数获取
    if (!empty($_GET['token'])) {
        return $_GET['token'];
    }
    
    if (!empty($_POST['token'])) {
        return $_POST['token'];
    }
    
    return null;
}

// 验证Token格式 - 简化版，只验证基本格式，不查询数据库
function verifyTokenFormat($token) {
    // 检查是否为空
    if (empty($token)) {
        error_log('Token为空');
        return false;
    }
    
    // 检查格式 - JWT标准格式是三段式，用.分隔
    $parts = explode('.', $token);
    if (count($parts) !== 3) {
        error_log('Token格式错误: 不是标准的JWT格式');
        return false;
    }
    
    // 尝试解码头部和负载
    try {
        $header = json_decode(base64_decode(strtr($parts[0], '-_', '+/')), true);
        $payload = json_decode(base64_decode(strtr($parts[1], '-_', '+/')), true);
        
        // 检查必要的字段
        if (!$header || !$payload) {
            error_log('Token解码失败');
            return false;
        }
        
        // 日志记录令牌内容，但不记录完整内容，仅记录关键信息
        error_log('Token格式验证通过，头部算法: ' . ($header['alg'] ?? 'unknown'));
        error_log('Token负载信息: ' . json_encode([
            'exp' => isset($payload['exp']) ? date('Y-m-d H:i:s', $payload['exp']) : 'missing',
            'has_user_id' => isset($payload['user_id']),
            'role' => $payload['role'] ?? 'unknown'
        ]));
        
        return true;
    } catch (Exception $e) {
        error_log('Token验证出现异常: ' . $e->getMessage());
        return false;
    }
}

// 旧的数据库验证Token方法 - 保留但不使用
function verifyToken($token) {
    $conn = get_db_connection();
    if (!$conn) {
        return false;
    }

    try {
        $stmt = mysqli_prepare($conn, "SELECT * FROM auth_tokens WHERE token = ? AND expires_at > NOW()");
        mysqli_stmt_bind_param($stmt, "s", $token);
        mysqli_stmt_execute($stmt);
        $result = mysqli_stmt_get_result($stmt);
        $tokenRecord = mysqli_fetch_assoc($result);
        
        mysqli_stmt_close($stmt);
        mysqli_close($conn);
        
        return $tokenRecord !== false;
    } catch (Exception $e) {
        error_log('Token验证失败: ' . $e->getMessage());
        if (isset($conn)) {
            mysqli_close($conn);
        }
        return false;
    }
}
