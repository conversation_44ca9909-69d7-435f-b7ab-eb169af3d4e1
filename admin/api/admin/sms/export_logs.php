<?php
// 设置响应头
header('Content-Type: application/json');

// 允许跨域请求
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 如果是OPTIONS请求，直接返回200
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 构建响应
$response = [
    'code' => 0,
    'message' => 'Export functionality is under development',
    'data' => null
];

// 返回JSON响应
echo json_encode($response);
