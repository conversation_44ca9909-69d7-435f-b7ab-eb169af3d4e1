<?php
// 设置响应头
header('Content-Type: application/json');

// 允许跨域请求
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 如果是OPTIONS请求，直接返回200
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 获取日志ID
$id = isset($_GET['id']) ? intval($_GET['id']) : 0;

// 模拟数据
$mockData = [
    1 => [
        'id' => 1,
        'phone' => '13800138000',
        'content' => '您的验证码是123456，有效期5分钟，请勿泄露。',
        'type' => 'login',
        'status' => 'success',
        'provider' => 'aliyun',
        'request_ip' => '***********',
        'created_at' => '2025-04-01 10:00:00',
        'request_data' => '{"phone":"13800138000","code":"123456","type":"login"}',
        'response_data' => '{"code":0,"message":"success","requestId":"abcd1234"}'
    ],
    2 => [
        'id' => 2,
        'phone' => '13900139000',
        'content' => '您的验证码是654321，有效期5分钟，请勿泄露。',
        'type' => 'register',
        'status' => 'success',
        'provider' => 'tencent',
        'request_ip' => '***********',
        'created_at' => '2025-04-01 11:00:00',
        'request_data' => '{"phone":"13900139000","code":"654321","type":"register"}',
        'response_data' => '{"code":0,"message":"success","requestId":"efgh5678"}'
    ],
    3 => [
        'id' => 3,
        'phone' => '13700137000',
        'content' => '您的验证码是111222，有效期5分钟，请勿泄露。',
        'type' => 'reset',
        'status' => 'failed',
        'provider' => 'aliyun',
        'request_ip' => '***********',
        'created_at' => '2025-04-01 12:00:00',
        'request_data' => '{"phone":"13700137000","code":"111222","type":"reset"}',
        'response_data' => '{"code":1,"message":"failed","requestId":"ijkl9012"}'
    ],
    4 => [
        'id' => 4,
        'phone' => '13600136000',
        'content' => '您的验证码是333444，有效期5分钟，请勿泄露。',
        'type' => 'bind',
        'status' => 'success',
        'provider' => 'tencent',
        'request_ip' => '***********',
        'created_at' => '2025-04-01 13:00:00',
        'request_data' => '{"phone":"13600136000","code":"333444","type":"bind"}',
        'response_data' => '{"code":0,"message":"success","requestId":"mnop3456"}'
    ],
    5 => [
        'id' => 5,
        'phone' => '13500135000',
        'content' => '您的验证码是555666，有效期5分钟，请勿泄露。',
        'type' => 'verify',
        'status' => 'success',
        'provider' => 'aliyun',
        'request_ip' => '***********',
        'created_at' => '2025-04-01 14:00:00',
        'request_data' => '{"phone":"13500135000","code":"555666","type":"verify"}',
        'response_data' => '{"code":0,"message":"success","requestId":"qrst7890"}'
    ]
];

// 查找日志
$logDetail = isset($mockData[$id]) ? $mockData[$id] : null;

// 构建响应
if ($logDetail) {
    $response = [
        'code' => 0,
        'message' => 'success',
        'data' => $logDetail
    ];
} else {
    $response = [
        'code' => 1,
        'message' => 'Log not found',
        'data' => null
    ];
}

// 返回JSON响应
echo json_encode($response);
