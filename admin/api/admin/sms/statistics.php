<?php
require_once __DIR__ . '/../../../api/db_config.php';
$pdo = getDbConnection();
if (!$pdo) {
    echo json_encode([
        'code' => 1,
        'message' => '数据库连接失败',
        'data' => []
    ]);
    exit;
}

header('Content-Type: application/json');

$response = [
    'code' => 0,
    'message' => '操作成功',
    'data' => []
];

try {
    // 获取请求参数
    $params = [
        'start_date' => $_GET['start_date'] ?? '',
        'end_date' => $_GET['end_date'] ?? '',
        'type' => $_GET['type'] ?? 'daily'
    ];

    // 验证日期范围
    if (!empty($params['start_date'])) {
        $startDate = new DateTime($params['start_date']);
    } else {
        $startDate = new DateTime();
        $startDate->modify('-30 days');
    }

    if (!empty($params['end_date'])) {
        $endDate = new DateTime($params['end_date']);
    } else {
        $endDate = new DateTime();
    }

    // 1. 获取总统计数据
    $totalStats = getTotalStats($startDate, $endDate);
    
    // 2. 获取成功率统计数据
    $successRate = getSuccessRate($startDate, $endDate, $params['type']);
    
    // 3. 获取短信类型分布
    $typeDistribution = getTypeDistribution($startDate, $endDate);
    
    // 4. 获取高频手机号
    $frequentPhones = getFrequentPhones($startDate, $endDate);
    
    // 5. 获取每日发送量趋势
    $dailySending = getDailySending($startDate, $endDate);

    $response['data'] = [
        'totalStats' => $totalStats,
        'successRate' => $successRate,
        'typeDistribution' => $typeDistribution,
        'frequentPhones' => $frequentPhones,
        'dailySending' => $dailySending
    ];

    } catch (Exception $e) {
        $response['code'] = 1;
        $response['message'] = $e->getMessage();
        error_log("API Error: " . $e->getMessage());
        error_log("Trace: " . $e->getTraceAsString());
    }

echo json_encode($response, JSON_UNESCAPED_UNICODE);

/**
 * 获取总统计数据
 */
function getTotalStats(DateTime $startDate, DateTime $endDate): array {
    global $pdo;
    
    $sql = "SELECT 
                COUNT(*) AS total,
                SUM(CASE WHEN status = 'success' THEN 1 ELSE 0 END) AS success,
                SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END) AS failed
            FROM sms_logs
            WHERE sent_at BETWEEN :start_date AND :end_date";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute([
        ':start_date' => $startDate->format('Y-m-d 00:00:00'),
        ':end_date' => $endDate->format('Y-m-d 23:59:59')
    ]);
    
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // 计算成功率
    $result['successRate'] = $result['total'] > 0 
        ? round(($result['success'] / $result['total']) * 100, 2) 
        : 0;
    
    return $result;
}

/**
 * 获取成功率统计数据
 */
function getSuccessRate(DateTime $startDate, DateTime $endDate, string $type): array {
    global $pdo;
    
    $format = $type === 'monthly' ? '%Y-%m' : ($type === 'weekly' ? '%Y-%u' : '%Y-%m-%d');
    
    $sql = "SELECT 
                DATE_FORMAT(sent_at, :format) AS date,
                COUNT(*) AS total,
                SUM(CASE WHEN status = 'success' THEN 1 ELSE 0 END) AS success,
                SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END) AS failed
            FROM sms_logs
            WHERE sent_at BETWEEN :start_date AND :end_date
            GROUP BY DATE_FORMAT(sent_at, :format)
            ORDER BY sent_at ASC";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute([
        ':format' => $format,
        ':start_date' => $startDate->format('Y-m-d 00:00:00'),
        ':end_date' => $endDate->format('Y-m-d 23:59:59')
    ]);
    
    $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // 计算成功率
    foreach ($results as &$row) {
        $row['successRate'] = $row['total'] > 0 
            ? round(($row['success'] / $row['total']) * 100, 2) 
            : 0;
    }
    
    return $results;
}

/**
 * 获取短信类型分布
 */
function getTypeDistribution(DateTime $startDate, DateTime $endDate): array {
    global $pdo;
    
    $sql = "SELECT 
                type, 
                COUNT(*) AS count
            FROM sms_logs
            WHERE sent_at BETWEEN :start_date AND :end_date
            GROUP BY type
            ORDER BY count DESC";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute([
        ':start_date' => $startDate->format('Y-m-d 00:00:00'),
        ':end_date' => $endDate->format('Y-m-d 23:59:59')
    ]);
    
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

/**
 * 获取高频手机号
 */
function getFrequentPhones(DateTime $startDate, DateTime $endDate): array {
    global $pdo;
    
    $sql = "SELECT 
                phone, 
                COUNT(*) AS value
            FROM sms_logs
            WHERE sent_at BETWEEN :start_date AND :end_date
            GROUP BY phone
            ORDER BY value DESC
            LIMIT 10";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute([
        ':start_date' => $startDate->format('Y-m-d 00:00:00'),
        ':end_date' => $endDate->format('Y-m-d 23:59:59')
    ]);
    
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

/**
 * 获取每日发送量趋势
 */
function getDailySending(DateTime $startDate, DateTime $endDate): array {
    global $pdo;
    
    $sql = "SELECT 
                DATE(sent_at) AS date,
                COUNT(*) AS count
            FROM sms_logs
            WHERE sent_at BETWEEN :start_date AND :end_date
            GROUP BY DATE(sent_at)
            ORDER BY date ASC";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute([
        ':start_date' => $startDate->format('Y-m-d 00:00:00'),
        ':end_date' => $endDate->format('Y-m-d 23:59:59')
    ]);
    
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}
