<?php
// 设置响应头
header('Content-Type: application/json');

// 允许跨域请求
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 如果是OPTIONS请求，直接返回200
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 模拟统计数据
$statsData = [
    'total' => 500,
    'used' => 320,
    'unused' => 150,
    'expired' => 30,
    'usageRate' => 64
];

// 构建响应
$response = [
    'code' => 0,
    'message' => 'success',
    'data' => $statsData
];

// 返回JSON响应
echo json_encode($response);
