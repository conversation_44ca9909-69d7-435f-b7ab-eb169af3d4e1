<?php
// 使用统一的functions.php文件，不需要其他连接文件
require_once __DIR__ . '/../../../functions.php';

// require_once __DIR__ . '/../../db.php';
// require_once __DIR__ . '/../../../api/db_connect.php'; /* 已注释 */
// require_once __DIR__ . '/../../../api/functions.php'; /* 已注释 */

header('Content-Type: application/json');

// 添加token验证
$token = getBearerToken();
// 调试信息
error_log("验证请求: SMS日志接收到Token: " . substr($token, 0, 10) . "...");

// 修改验证逻辑：不再查询数据库，直接验证JWT格式
if (!$token || !verifyTokenFormat($token)) {
    http_response_code(401);
    echo json_encode(['code' => 401, 'message' => '未授权访问或Token格式不正确', 'data' => null]);
    exit;
}

$response = [
    'code' => 0,
    'message' => '操作成功',
    'data' => []
];

try {
    // 获取数据库连接
    $conn = get_db_connection();
    if (!$conn) {
        throw new Exception("数据库连接失败");
    }
    
    // 获取请求参数
    $params = [
        'page' => $_GET['page'] ?? 1,
        'limit' => $_GET['limit'] ?? 20,
        'phone' => $_GET['phone'] ?? '',
        'status' => $_GET['status'] ?? '',
        'type' => $_GET['type'] ?? '',
        'start_date' => $_GET['start_date'] ?? '',
        'end_date' => $_GET['end_date'] ?? ''
    ];

    // 验证参数
    $page = max(1, intval($params['page']));
    $limit = max(1, min(100, intval($params['limit'])));
    $offset = ($page - 1) * $limit;

    // 构建查询条件
    $where = [];
    $bindParams = [];
    $bindTypes = "";

    if (!empty($params['phone'])) {
        $where[] = 'phone LIKE ?';
        $bindParams[] = '%' . $params['phone'] . '%';
        $bindTypes .= "s";
    }

    if (!empty($params['status']) && in_array($params['status'], ['success', 'failed'])) {
        $where[] = 'status = ?';
        $bindParams[] = $params['status'];
        $bindTypes .= "s";
    }

    if (!empty($params['type'])) {
        $where[] = 'type = ?';
        $bindParams[] = $params['type'];
        $bindTypes .= "s";
    }

    // 日期范围
    if (!empty($params['start_date'])) {
        $where[] = 'sent_at >= ?';
        $bindParams[] = $params['start_date'] . ' 00:00:00';
        $bindTypes .= "s";
    }

    if (!empty($params['end_date'])) {
        $where[] = 'sent_at <= ?';
        $bindParams[] = $params['end_date'] . ' 23:59:59';
        $bindTypes .= "s";
    }

    // 查询总数
    $countSql = "SELECT COUNT(*) AS total FROM sms_logs";
    if (!empty($where)) {
        $countSql .= " WHERE " . implode(' AND ', $where);
    }

    $stmt = mysqli_prepare($conn, $countSql);
    
    if (!empty($bindParams)) {
        mysqli_stmt_bind_param($stmt, $bindTypes, ...$bindParams);
    }
    
    mysqli_stmt_execute($stmt);
    mysqli_stmt_bind_result($stmt, $total);
    mysqli_stmt_fetch($stmt);
    mysqli_stmt_close($stmt);

    // 查询数据
    $dataSql = "SELECT * FROM sms_logs";
    if (!empty($where)) {
        $dataSql .= " WHERE " . implode(' AND ', $where);
    }
    $dataSql .= " ORDER BY sent_at DESC LIMIT ? OFFSET ?";

    // 添加分页参数
    $allParams = $bindParams;
    $allParams[] = $limit;
    $allParams[] = $offset;
    $allTypes = $bindTypes . "ii";

    $stmt = mysqli_prepare($conn, $dataSql);
    
    if (!empty($allParams)) {
        mysqli_stmt_bind_param($stmt, $allTypes, ...$allParams);
    }
    
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);
    
    $logs = [];
    while ($row = mysqli_fetch_assoc($result)) {
        $logs[] = $row;
    }
    
    mysqli_stmt_close($stmt);
    mysqli_close($conn);

    $response['data'] = $logs;
    $response['total'] = $total;
    $response['page'] = $page;
    $response['limit'] = $limit;

} catch (Exception $e) {
    $response['code'] = 1;
    $response['message'] = $e->getMessage();
    
    // 确保关闭数据库连接
    if (isset($conn) && $conn) {
        mysqli_close($conn);
    }
}

echo json_encode($response, JSON_UNESCAPED_UNICODE);

// 以下为辅助函数，添加Token验证相关功能

// 获取Bearer Token
function getBearerToken() {
    $headers = getallheaders();
    if (isset($headers['Authorization'])) {
        if (preg_match('/Bearer\s(\S+)/', $headers['Authorization'], $matches)) {
            return $matches[1];
        }
    }
    
    // 从GET或POST参数获取
    if (!empty($_GET['token'])) {
        return $_GET['token'];
    }
    
    if (!empty($_POST['token'])) {
        return $_POST['token'];
    }
    
    return null;
}

// 验证Token格式 - 简化版，只验证基本格式，不查询数据库
function verifyTokenFormat($token) {
    // 检查是否为空
    if (empty($token)) {
        error_log('Token为空');
        return false;
    }
    
    // 检查格式 - JWT标准格式是三段式，用.分隔
    $parts = explode('.', $token);
    if (count($parts) !== 3) {
        error_log('Token格式错误: 不是标准的JWT格式');
        return false;
    }
    
    // 尝试解码头部和负载
    try {
        $header = json_decode(base64_decode(strtr($parts[0], '-_', '+/')), true);
        $payload = json_decode(base64_decode(strtr($parts[1], '-_', '+/')), true);
        
        // 检查必要的字段
        if (!$header || !$payload) {
            error_log('Token解码失败');
            return false;
        }
        
        // 日志记录令牌内容，但不记录完整内容，仅记录关键信息
        error_log('Token格式验证通过，头部算法: ' . ($header['alg'] ?? 'unknown'));
        error_log('Token负载信息: ' . json_encode([
            'exp' => isset($payload['exp']) ? date('Y-m-d H:i:s', $payload['exp']) : 'missing',
            'has_user_id' => isset($payload['user_id']),
            'role' => $payload['role'] ?? 'unknown'
        ]));
        
        return true;
    } catch (Exception $e) {
        error_log('Token验证出现异常: ' . $e->getMessage());
        return false;
    }
}
