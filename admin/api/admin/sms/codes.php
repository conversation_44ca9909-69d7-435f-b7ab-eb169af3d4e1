<?php
// 设置响应头
header('Content-Type: application/json');

// 允许跨域请求
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 如果是OPTIONS请求，直接返回200
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 获取请求参数
$page = isset($_GET['page']) ? intval($_GET['page']) : 1;
$limit = isset($_GET['limit']) ? intval($_GET['limit']) : 20;
$phone = isset($_GET['phone']) ? $_GET['phone'] : '';
$status = isset($_GET['status']) ? $_GET['status'] : '';
$start_date = isset($_GET['start_date']) ? $_GET['start_date'] : '';
$end_date = isset($_GET['end_date']) ? $_GET['end_date'] : '';

// 模拟数据
$mockData = [
    [
        'id' => 1,
        'phone' => '13800138000',
        'code' => '123456',
        'type' => 'login',
        'status' => 'unused',
        'created_at' => '2025-04-01 10:00:00',
        'expired_at' => '2025-04-01 10:05:00',
        'used_at' => null
    ],
    [
        'id' => 2,
        'phone' => '13900139000',
        'code' => '654321',
        'type' => 'register',
        'status' => 'used',
        'created_at' => '2025-04-01 11:00:00',
        'expired_at' => '2025-04-01 11:05:00',
        'used_at' => '2025-04-01 11:02:30'
    ],
    [
        'id' => 3,
        'phone' => '13700137000',
        'code' => '111222',
        'type' => 'reset',
        'status' => 'expired',
        'created_at' => '2025-04-01 12:00:00',
        'expired_at' => '2025-04-01 12:05:00',
        'used_at' => null
    ],
    [
        'id' => 4,
        'phone' => '13600136000',
        'code' => '333444',
        'type' => 'bind',
        'status' => 'used',
        'created_at' => '2025-04-01 13:00:00',
        'expired_at' => '2025-04-01 13:05:00',
        'used_at' => '2025-04-01 13:01:45'
    ],
    [
        'id' => 5,
        'phone' => '13500135000',
        'code' => '555666',
        'type' => 'verify',
        'status' => 'unused',
        'created_at' => '2025-04-01 14:00:00',
        'expired_at' => '2025-04-01 14:05:00',
        'used_at' => null
    ]
];

// 过滤数据
$filteredData = $mockData;

// 按手机号过滤
if (!empty($phone)) {
    $filteredData = array_filter($filteredData, function($item) use ($phone) {
        return strpos($item['phone'], $phone) !== false;
    });
}

// 按状态过滤
if (!empty($status) && $status !== 'all') {
    $filteredData = array_filter($filteredData, function($item) use ($status) {
        return $item['status'] === $status;
    });
}

// 按日期范围过滤
if (!empty($start_date) && !empty($end_date)) {
    $filteredData = array_filter($filteredData, function($item) use ($start_date, $end_date) {
        $itemDate = substr($item['created_at'], 0, 10);
        return $itemDate >= $start_date && $itemDate <= $end_date;
    });
}

// 重新索引数组
$filteredData = array_values($filteredData);

// 计算分页
$total = count($filteredData);
$offset = ($page - 1) * $limit;
$paginatedData = array_slice($filteredData, $offset, $limit);

// 构建响应
$response = [
    'code' => 0,
    'message' => 'success',
    'data' => $paginatedData,
    'total' => $total,
    'page' => $page,
    'limit' => $limit
];

// 返回JSON响应
echo json_encode($response);
