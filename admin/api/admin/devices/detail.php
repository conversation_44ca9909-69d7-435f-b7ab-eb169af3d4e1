<?php
/**
 * 获取设备详情API
 * 此API提供给前端获取单个设备的详细信息
 */

require_once '../../config.php';
require_once '../../functions/auth.php';
require_once '../../functions/utils.php';
require_once '../../functions/db.php';

// 设置响应头
header('Content-Type: application/json');

// 检查认证
$token = '';
if (isset($_SERVER['HTTP_AUTHORIZATION'])) {
    $token = str_replace('Bearer ', '', $_SERVER['HTTP_AUTHORIZATION']);
} elseif (isset($_GET['token'])) {
    $token = $_GET['token'];
} elseif (isset($_POST['token'])) {
    $token = $_POST['token'];
}

$auth = verify_auth($token);
if (!$auth) {
    echo json_encode([
        'code' => 401,
        'message' => '未认证或认证已过期',
        'data' => null
    ]);
    exit;
}

// 获取设备ID
$deviceId = isset($_GET['id']) ? intval($_GET['id']) : 0;
if ($deviceId <= 0) {
    echo json_encode([
        'code' => 400,
        'message' => '设备ID不能为空',
        'data' => null
    ]);
    exit;
}

try {
    // 连接净水器数据库
    global $WATER_DB_CONFIG;
    $conn = new mysqli(
        $WATER_DB_CONFIG['HOST'],
        $WATER_DB_CONFIG['USER'],
        $WATER_DB_CONFIG['PASSWORD'],
        $WATER_DB_CONFIG['DATABASE'],
        $WATER_DB_CONFIG['PORT']
    );
    
    if ($conn->connect_error) {
        throw new Exception("连接净水器数据库失败: " . $conn->connect_error);
    }
    
    // 设置字符集
    $conn->set_charset($WATER_DB_CONFIG['CHARSET']);
    
    // 准备查询语句
    $sql = "SELECT 
                id, 
                device_number, 
                device_type,
                device_status,
                raw_water_value,
                purification_water_value,
                billing_mode,
                surplus_flow,
                remaining_days,
                cumulative_filtration_flow,
                water_quality_grade,
                network_status,
                product_id,
                dealer_id,
                dealer_id_sale,
                cash_pledge,
                client_id,
                activate_date,
                iccid,
                imei,
                bind_status,
                status,
                remark,
                address,
                longitude,
                latitude,
                cod_after,
                cod_before,
                toc_after,
                toc_before,
                service_end_time,
                filter_date,
                create_date,
                update_date
            FROM wb_device 
            WHERE id = ?";
    
    $stmt = $conn->prepare($sql);
    $stmt->bind_param('s', $deviceId); // 使用字符串类型，因为ID是varchar(36)
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows === 0) {
        echo json_encode([
            'code' => 404,
            'message' => '设备不存在',
            'data' => null
        ]);
        $stmt->close();
        $conn->close();
        exit;
    }
    
    $device = $result->fetch_assoc();
    $stmt->close();
    
    // 查询设备关联的客户信息
    $clientInfo = null;
    if (!empty($device['client_id'])) {
        $clientSql = "SELECT 
                    id, 
                        name, 
                    phone, 
                        province,
                        city,
                        area,
                    address,
                        status,
                        wx_nickname,
                        wx_head_img,
                        client_dealer_name
                    FROM wb_client
                    WHERE id = ?";
    
        $stmtClient = $conn->prepare($clientSql);
        $stmtClient->bind_param('s', $device['client_id']);
        $stmtClient->execute();
        $clientResult = $stmtClient->get_result();
    
        if ($clientResult->num_rows > 0) {
            $clientInfo = $clientResult->fetch_assoc();
    }
        $stmtClient->close();
    }
    
    // 格式化状态文本
    $statusText = $device['status'] === 'E' ? '启用' : '禁用';
    
    // 格式化设备类型文本
    $deviceTypeText = '';
    switch ($device['device_type']) {
        case 'purifier':
            $deviceTypeText = '净水器';
            break;
        case 'dispenser':
            $deviceTypeText = '饮水机';
            break;
        default:
            $deviceTypeText = $device['device_type'] ?: '未知类型';
    }
    
    // 格式化网络状态
    $networkStatusText = '';
    switch ($device['network_status']) {
        case '1':
        case 'online':
            $networkStatusText = '在线';
            break;
        case '0':
        case 'offline':
            $networkStatusText = '离线';
            break;
        default:
            $networkStatusText = '未知';
    }
    
    // 格式化绑定状态
    $bindStatusText = '';
    switch ($device['bind_status']) {
        case '1':
        case 'bound':
            $bindStatusText = '已绑定';
            break;
        case '0':
        case 'unbound':
            $bindStatusText = '未绑定';
            break;
        default:
            $bindStatusText = '未知';
    }
    
    // 构建客户地址
    $clientAddress = '';
    if ($clientInfo) {
        if (!empty($clientInfo['province'])) $clientAddress .= $clientInfo['province'];
        if (!empty($clientInfo['city'])) $clientAddress .= $clientInfo['city'];
        if (!empty($clientInfo['area'])) $clientAddress .= $clientInfo['area'];
        if (!empty($clientInfo['address'])) $clientAddress .= $clientInfo['address'];
    }
    
    // 构建响应数据
    $responseData = [
        'id' => $device['id'],
        'device_sn' => $device['device_number'],
        'device_name' => $device['device_number'], // 使用设备编号作为名称
        'device_type' => $device['device_type'],
        'device_type_text' => $deviceTypeText,
        'device_model' => $device['product_id'],
        'merchant_id' => $device['dealer_id'],
        'merchant_name' => $device['dealer_id_sale'],
        'install_time' => $device['activate_date'],
        'install_address' => $device['address'],
        'status' => $device['status'],
        'status_text' => $statusText,
        'device_status' => $device['device_status'],
        'network_status' => $device['network_status'],
        'network_status_text' => $networkStatusText,
        'bind_status' => $device['bind_status'],
        'bind_status_text' => $bindStatusText,
        'remark' => $device['remark'],
        'iccid' => $device['iccid'],
        'imei' => $device['imei'],
        'billing_mode' => $device['billing_mode'],
        'surplus_flow' => $device['surplus_flow'],
        'remaining_days' => $device['remaining_days'],
        'raw_water_value' => $device['raw_water_value'],
        'purification_water_value' => $device['purification_water_value'],
        'cumulative_filtration_flow' => $device['cumulative_filtration_flow'],
        'water_quality' => $device['water_quality_grade'],
        'cod_after' => $device['cod_after'],
        'cod_before' => $device['cod_before'],
        'toc_after' => $device['toc_after'],
        'toc_before' => $device['toc_before'],
        'service_end_time' => $device['service_end_time'],
        'filter_date' => $device['filter_date'],
        'longitude' => $device['longitude'],
        'latitude' => $device['latitude'],
        'cash_pledge' => $device['cash_pledge'],
        'client_id' => $device['client_id'],
        'client_name' => $clientInfo ? $clientInfo['name'] : '未关联客户',
        'client_phone' => $clientInfo ? $clientInfo['phone'] : '',
        'client_address' => $clientAddress,
        'client_dealer_name' => $clientInfo ? $clientInfo['client_dealer_name'] : '',
        'wx_nickname' => $clientInfo ? $clientInfo['wx_nickname'] : '',
        'wx_head_img' => $clientInfo ? $clientInfo['wx_head_img'] : '',
        'created_at' => $device['create_date'],
        'updated_at' => $device['update_date']
    ];
    
    // 返回结果
    echo json_encode([
        'code' => 0,
        'message' => '获取成功',
        'data' => $responseData
    ]);
    
} catch (Exception $e) {
    error_log("获取设备详情失败: " . $e->getMessage());
    echo json_encode([
        'code' => 500,
        'message' => "服务器内部错误: " . $e->getMessage(),
        'data' => null
    ]);
} finally {
    if (isset($conn) && $conn) {
        $conn->close();
    }
} 