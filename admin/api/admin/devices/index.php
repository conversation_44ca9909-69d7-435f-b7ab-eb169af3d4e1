<?php
/**
 * 获取设备列表API
 * 此API提供给前端获取设备列表
 */

require_once '../../config.php';
require_once '../../functions/auth.php';
require_once '../../functions/utils.php';
require_once '../../functions/db.php';

// 设置响应头
header('Content-Type: application/json');

// 检查认证
$token = '';
if (isset($_SERVER['HTTP_AUTHORIZATION'])) {
    $token = str_replace('Bearer ', '', $_SERVER['HTTP_AUTHORIZATION']);
} elseif (isset($_GET['token'])) {
    $token = $_GET['token'];
} elseif (isset($_POST['token'])) {
    $token = $_POST['token'];
}

$auth = verify_auth($token);
if (!$auth) {
    echo json_encode([
        'code' => 401,
        'message' => '未认证或认证已过期',
        'data' => null
    ]);
    exit;
}

// 获取请求参数
$page = isset($_GET['page']) ? intval($_GET['page']) : 1;
$limit = isset($_GET['limit']) ? intval($_GET['limit']) : 10;
$keyword = isset($_GET['keyword']) ? trim($_GET['keyword']) : '';
$status = isset($_GET['status']) ? trim($_GET['status']) : '';
$deviceType = isset($_GET['device_type']) ? trim($_GET['device_type']) : '';

// 计算偏移量
$offset = ($page - 1) * $limit;

try {
    // 连接净水器数据库
    global $WATER_DB_CONFIG;
    $conn = new mysqli(
        $WATER_DB_CONFIG['HOST'],
        $WATER_DB_CONFIG['USER'],
        $WATER_DB_CONFIG['PASSWORD'],
        $WATER_DB_CONFIG['DATABASE'],
        $WATER_DB_CONFIG['PORT']
    );
    
    if ($conn->connect_error) {
        throw new Exception("连接净水器数据库失败: " . $conn->connect_error);
    }
    
    // 设置字符集
    $conn->set_charset($WATER_DB_CONFIG['CHARSET']);
    
    // 构建SQL查询条件
    $where = [];
    $params = [];
    
    // 添加关键字搜索条件
    if (!empty($keyword)) {
        $where[] = "(device_number LIKE ? OR imei LIKE ? OR client_dealer_name LIKE ?)";
        $params[] = "%$keyword%";
        $params[] = "%$keyword%";
        $params[] = "%$keyword%";
    }
    
    // 添加状态过滤条件
    if (!empty($status)) {
        $where[] = "status = ?";
        $params[] = $status;
    }
    
    // 添加设备类型过滤条件
    if (!empty($deviceType)) {
        $where[] = "device_type = ?";
        $params[] = $deviceType;
    }
    
    // 组合WHERE条件
    $whereStr = !empty($where) ? "WHERE " . implode(" AND ", $where) : "";
    
    // 准备查询语句
    $countSql = "SELECT COUNT(*) as total FROM wb_device $whereStr";
    $querySql = "SELECT 
                    id, 
                    device_number, 
                    device_type,
                    device_status,
                    raw_water_value,
                    purification_water_value,
                    billing_mode,
                    surplus_flow,
                    remaining_days,
                    cumulative_filtration_flow,
                    water_quality_grade,
                    network_status,
                    dealer_id,
                    client_id,
                    activate_date,
                    iccid,
                    imei,
                    bind_status,
                    status,
                    remark,
                    address,
                    service_end_time,
                    filter_date,
                    create_date, 
                    update_date
                FROM wb_device 
                $whereStr
                ORDER BY create_date DESC
                LIMIT ?, ?";
    
    // 查询总数
    $stmt = $conn->prepare($countSql);
    if (!empty($params)) {
        $types = str_repeat('s', count($params));
        $stmt->bind_param($types, ...$params);
    }
    $stmt->execute();
    $totalResult = $stmt->get_result();
    $totalRow = $totalResult->fetch_assoc();
    $total = $totalRow['total'];
    $stmt->close();
    
    // 如果没有结果，直接返回空列表
    if ($total === 0) {
        echo json_encode([
            'code' => 0,
            'message' => '获取成功',
            'data' => [
                'list' => [],
                'total' => 0,
                'page' => $page,
                'limit' => $limit
            ]
        ]);
        $conn->close();
        exit;
    }
    
    // 查询分页数据
    $stmt = $conn->prepare($querySql);
    if (!empty($params)) {
        $params[] = $offset;
        $params[] = $limit;
        $types = str_repeat('s', count($params) - 2) . 'ii';
        $stmt->bind_param($types, ...$params);
    } else {
        $stmt->bind_param('ii', $offset, $limit);
    }
    $stmt->execute();
    $result = $stmt->get_result();
    
    $devices = [];
    while ($row = $result->fetch_assoc()) {
        // 格式化状态文本
        $statusText = $row['status'] === 'E' ? '启用' : '禁用';
        
        // 格式化设备类型文本
        $deviceTypeText = '';
        switch ($row['device_type']) {
            case 'purifier':
                $deviceTypeText = '净水器';
                break;
            case 'dispenser':
                $deviceTypeText = '饮水机';
                break;
            default:
                $deviceTypeText = $row['device_type'] ?: '未知类型';
        }
        
        // 格式化网络状态
        $networkStatusText = '';
        switch ($row['network_status']) {
            case '1':
            case 'online':
                $networkStatusText = '在线';
                break;
            case '0':
            case 'offline':
                $networkStatusText = '离线';
                break;
            default:
                $networkStatusText = '未知';
        }
        
        // 查询关联的客户信息
        $clientName = '未关联客户';
        $dealerName = '';
        if (!empty($row['client_id'])) {
            $clientSql = "SELECT name, client_dealer_name FROM wb_client WHERE id = ?";
            $clientStmt = $conn->prepare($clientSql);
            $clientStmt->bind_param('s', $row['client_id']);
            $clientStmt->execute();
            $clientResult = $clientStmt->get_result();
            if ($clientResult->num_rows > 0) {
                $clientData = $clientResult->fetch_assoc();
                $clientName = $clientData['name'] ?: '未命名客户';
                $dealerName = $clientData['client_dealer_name'] ?: '';
            }
            $clientStmt->close();
        }
        
        $devices[] = [
            'id' => $row['id'],
            'device_number' => $row['device_number'],
            'device_name' => $row['device_number'], // 使用设备编号作为名称
            'device_type' => $row['device_type'],
            'device_type_text' => $deviceTypeText,
            'device_status' => $row['device_status'],
            'raw_water_value' => $row['raw_water_value'],
            'purification_water_value' => $row['purification_water_value'],
            'billing_mode' => $row['billing_mode'],
            'surplus_flow' => $row['surplus_flow'],
            'remaining_days' => $row['remaining_days'],
            'cumulative_filtration_flow' => $row['cumulative_filtration_flow'],
            'water_quality_grade' => $row['water_quality_grade'],
            'network_status' => $row['network_status'],
            'network_status_text' => $networkStatusText,
            'dealer_id' => $row['dealer_id'],
            'dealer_name' => $dealerName,
            'client_id' => $row['client_id'],
            'client_name' => $clientName,
            'activate_date' => $row['activate_date'],
            'iccid' => $row['iccid'],
            'imei' => $row['imei'],
            'bind_status' => $row['bind_status'],
            'status' => $row['status'],
            'status_text' => $statusText,
            'remark' => $row['remark'],
            'address' => $row['address'],
            'service_end_time' => $row['service_end_time'],
            'filter_date' => $row['filter_date'],
            'create_date' => $row['create_date'],
            'update_date' => $row['update_date']
        ];
    }
    $stmt->close();
    
    // 返回结果
    echo json_encode([
        'code' => 0,
        'message' => '获取成功',
        'data' => [
            'list' => $devices,
            'total' => $total,
            'page' => $page,
            'limit' => $limit
        ]
    ]);
    
} catch (Exception $e) {
    error_log("获取设备列表失败: " . $e->getMessage());
    echo json_encode([
        'code' => 500,
        'message' => "服务器内部错误: " . $e->getMessage(),
        'data' => null
    ]);
} finally {
    if (isset($conn) && $conn) {
        $conn->close();
    }
} 