<?php
/**
 * 获取设备客户列表API
 * 此API提供给前端获取设备关联的客户列表
 */

require_once '../../config.php';
require_once '../../functions/auth.php';
require_once '../../functions/utils.php';
require_once '../../functions/db.php';

// 设置响应头
header('Content-Type: application/json');

// 检查认证
$token = '';
if (isset($_SERVER['HTTP_AUTHORIZATION'])) {
    $token = str_replace('Bearer ', '', $_SERVER['HTTP_AUTHORIZATION']);
} elseif (isset($_GET['token'])) {
    $token = $_GET['token'];
} elseif (isset($_POST['token'])) {
    $token = $_POST['token'];
}

$auth = verify_auth($token);
if (!$auth) {
    echo json_encode([
        'code' => 401,
        'message' => '未认证或认证已过期',
        'data' => null
    ]);
    exit;
}

// 获取请求参数
$page = isset($_GET['page']) ? intval($_GET['page']) : 1;
$limit = isset($_GET['limit']) ? intval($_GET['limit']) : 10;
$keyword = isset($_GET['keyword']) ? trim($_GET['keyword']) : '';
$status = isset($_GET['status']) ? trim($_GET['status']) : '';

// 计算偏移量
$offset = ($page - 1) * $limit;

try {
    // 连接净水器数据库
    global $WATER_DB_CONFIG;
    $conn = new mysqli(
        $WATER_DB_CONFIG['HOST'],
        $WATER_DB_CONFIG['USER'],
        $WATER_DB_CONFIG['PASSWORD'],
        $WATER_DB_CONFIG['DATABASE'],
        $WATER_DB_CONFIG['PORT']
    );
    
    if ($conn->connect_error) {
        throw new Exception("连接净水器数据库失败: " . $conn->connect_error);
    }
    
    // 设置字符集
    $conn->set_charset($WATER_DB_CONFIG['CHARSET']);
    
    // 构建SQL查询条件
    $where = [];
    $params = [];
    
    // 添加关键字搜索条件
    if (!empty($keyword)) {
        $where[] = "(name LIKE ? OR phone LIKE ?)";
        $params[] = "%$keyword%";
        $params[] = "%$keyword%";
    }
    
    // 添加状态过滤条件
    if (!empty($status)) {
        $where[] = "status = ?";
        $params[] = $status;
    }
    
    // 组合WHERE条件
    $whereStr = !empty($where) ? "WHERE " . implode(" AND ", $where) : "";
    
    // 准备查询语句
    $countSql = "SELECT COUNT(*) as total FROM wb_client $whereStr";
    $querySql = "SELECT 
                    id, 
                    name, 
                    phone, 
                    province, 
                    city, 
                    area, 
                    address, 
                    status, 
                    remark, 
                    wx_nickname, 
                    wx_head_img, 
                    client_device_id, 
                    client_product_name as client_device_name,
                    create_date, 
                    update_date
                FROM wb_client 
                $whereStr
                ORDER BY create_date DESC
                LIMIT ?, ?";
    
    // 查询总数
    $stmt = $conn->prepare($countSql);
    if (!empty($params)) {
        $types = str_repeat('s', count($params));
        $stmt->bind_param($types, ...$params);
    }
    $stmt->execute();
    $totalResult = $stmt->get_result();
    $totalRow = $totalResult->fetch_assoc();
    $total = $totalRow['total'];
    $stmt->close();
    
    // 如果没有结果，直接返回空列表
    if ($total === 0) {
        echo json_encode([
            'code' => 0,
            'message' => '获取成功',
            'data' => [
                'list' => [],
                'total' => 0,
                'page' => $page,
                'limit' => $limit
            ]
        ]);
        $conn->close();
        exit;
    }
    
    // 查询分页数据
    $stmt = $conn->prepare($querySql);
    if (!empty($params)) {
        $params[] = $offset;
        $params[] = $limit;
        $types = str_repeat('s', count($params) - 2) . 'ii';
        $stmt->bind_param($types, ...$params);
    } else {
        $stmt->bind_param('ii', $offset, $limit);
    }
    $stmt->execute();
    $result = $stmt->get_result();
    
    $clients = [];
    while ($row = $result->fetch_assoc()) {
        // 处理地址
        $address = '';
        if (!empty($row['province'])) $address .= $row['province'];
        if (!empty($row['city'])) $address .= $row['city'];
        if (!empty($row['area'])) $address .= $row['area'];
        if (!empty($row['address'])) $address .= $row['address'];
        
        // 格式化状态文本
        $statusText = $row['status'] === 'E' ? '启用' : '禁用';
        
        $clients[] = [
            'id' => $row['id'],
            'name' => $row['name'],
            'phone' => $row['phone'],
            'address' => $address,
            'status' => $row['status'],
            'status_text' => $statusText,
            'remark' => $row['remark'],
            'wx_nickname' => $row['wx_nickname'],
            'wx_head_img' => $row['wx_head_img'],
            'client_device_id' => $row['client_device_id'],
            'client_device_name' => $row['client_device_name'],
            'create_date' => $row['create_date'],
            'update_date' => $row['update_date']
        ];
    }
    $stmt->close();
    
    // 返回结果
    echo json_encode([
        'code' => 0,
        'message' => '获取成功',
        'data' => [
            'list' => $clients,
            'total' => $total,
            'page' => $page,
            'limit' => $limit
        ]
    ]);
    
} catch (Exception $e) {
    error_log("获取设备客户列表失败: " . $e->getMessage());
    echo json_encode([
        'code' => 500,
        'message' => "服务器内部错误: " . $e->getMessage(),
        'data' => null
    ]);
} finally {
    if (isset($conn) && $conn) {
        $conn->close();
    }
} 