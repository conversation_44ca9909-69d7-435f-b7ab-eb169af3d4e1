<?php
/**
 * 管理员菜单API
 * 
 * 从数据库获取管理员菜单数据
 */

// 设置响应头
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: X-Requested-With, Content-Type, Authorization');

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(204);
    exit;
}

// 引入配置文件
require_once __DIR__ . '/../config.php';

try {
    // 连接数据库
    $conn = new PDO("mysql:host={$DB_CONFIG['HOST']};dbname={$DB_CONFIG['DATABASE']};charset=utf8mb4", $DB_CONFIG['USER'], $DB_CONFIG['PASSWORD']);
    $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // 检查admin_menu表是否存在
    $stmt = $conn->prepare("SHOW TABLES LIKE 'admin_menu'");
    $stmt->execute();
    $tableExists = $stmt->rowCount() > 0;
    
    if (!$tableExists) {
        // 表不存在，返回默认菜单
        echo json_encode([
            'code' => 0,
            'message' => '成功获取菜单（默认）',
            'data' => getDefaultMenus()
        ]);
        exit;
    }
    
    // 从数据库获取菜单数据
    $stmt = $conn->prepare("SELECT id, parent_id, title, icon, path, sort_order, is_enabled FROM admin_menu WHERE is_enabled = 1 ORDER BY sort_order ASC");
    $stmt->execute();
    $menus = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // 如果菜单表为空，返回默认菜单结构
    if (empty($menus)) {
        echo json_encode([
            'code' => 0,
            'message' => '成功获取菜单（默认）',
            'data' => getDefaultMenus()
        ]);
        exit;
    }
    
    // 构建菜单树
    $menuTree = buildMenuTree($menus);
    
    echo json_encode([
        'code' => 0,
        'message' => '成功获取菜单',
        'data' => $menuTree
    ]);
    
} catch (PDOException $e) {
    // 数据库错误，返回默认菜单
    error_log("菜单API错误: " . $e->getMessage());
    
    echo json_encode([
        'code' => 0,
        'message' => '成功获取菜单（默认）',
        'data' => getDefaultMenus()
    ]);
}

/**
 * 构建菜单树
 *
 * @param array $menus
 * @return array
 */
function buildMenuTree($menus)
{
    // 将菜单项按照parent_id分组
    $menuMap = [];
    foreach ($menus as $menu) {
        $menuMap[$menu['parent_id']][] = $menu;
    }
    
    // 递归构建菜单树
    return buildMenuTreeRecursive($menuMap, 0);
}

/**
 * 递归构建菜单树
 *
 * @param array $menuMap
 * @param int $parentId
 * @return array
 */
function buildMenuTreeRecursive($menuMap, $parentId)
{
    $result = [];
    
    if (!isset($menuMap[$parentId])) {
        return $result;
    }
    
    foreach ($menuMap[$parentId] as $menu) {
        $menuItem = [
            'id' => (string)$menu['id'],
            'path' => $menu['path'],
            'meta' => [
                'title' => $menu['title'],
                'icon' => $menu['icon']
            ]
        ];
        
        // 如果有子菜单，递归构建子菜单
        if (isset($menuMap[$menu['id']])) {
            $menuItem['children'] = buildMenuTreeRecursive($menuMap, $menu['id']);
        } else {
            $menuItem['children'] = [];
        }
        
        $result[] = $menuItem;
    }
    
    return $result;
}

/**
 * 获取默认菜单
 *
 * @return array
 */
function getDefaultMenus()
{
    return [
        [
            'id' => '1',
            'path' => 'dashboard',
            'meta' => ['title' => '面板', 'icon' => 'Monitor'],
            'children' => [
                ['id' => '1-1', 'path' => 'dashboard', 'meta' => ['title' => '控制面板', 'icon' => 'Monitor']],
                ['id' => '1-2', 'path' => 'dashboard/purifier', 'meta' => ['title' => '净水器', 'icon' => 'Tools']],
                ['id' => '1-3', 'path' => 'dashboard/shengfutong', 'meta' => ['title' => '盛付通', 'icon' => 'CreditCard']],
                ['id' => '1-4', 'path' => 'dashboard/xinsheng', 'meta' => ['title' => '新生', 'icon' => 'Star']],
                ['id' => '1-5', 'path' => 'dashboard/guotong', 'meta' => ['title' => '国通星驿', 'icon' => 'Connection']]
            ]
        ],
        [
            'id' => '2',
            'path' => 'users',
            'meta' => ['title' => '用户管理', 'icon' => 'User'],
            'children' => [
                ['id' => '2-1', 'path' => 'users/app-users', 'meta' => ['title' => 'APP用户', 'icon' => 'Avatar']],
                ['id' => '2-2', 'path' => 'users/admins', 'meta' => ['title' => '后台管理员', 'icon' => 'UserFilled']],
                ['id' => '2-3', 'path' => 'users/salesmen', 'meta' => ['title' => '业务员管理', 'icon' => 'User']],
                ['id' => '2-4', 'path' => 'users/salesman-stats', 'meta' => ['title' => '业务员统计', 'icon' => 'DataAnalysis']]
            ]
        ],
        [
            'id' => '3',
            'path' => 'mall',
            'meta' => ['title' => '商城管理', 'icon' => 'ShoppingCart'],
            'children' => [
                ['id' => '3-1', 'path' => 'mall/categories', 'meta' => ['title' => '商品分类', 'icon' => 'List']],
                ['id' => '3-2', 'path' => 'mall/products', 'meta' => ['title' => '商品管理', 'icon' => 'Goods']],
                ['id' => '3-3', 'path' => 'mall/orders', 'meta' => ['title' => '订单管理', 'icon' => 'Document']]
            ]
        ],
        [
            'id' => '4',
            'path' => 'devices',
            'meta' => ['title' => '设备管理', 'icon' => 'Cpu'],
            'children' => [
                ['id' => '4-1', 'path' => 'devices/list', 'meta' => ['title' => '设备列表', 'icon' => 'Grid']],
                ['id' => '4-2', 'path' => 'devices/stats', 'meta' => ['title' => '设备统计', 'icon' => 'DataAnalysis']],
                ['id' => '4-3', 'path' => 'tapp-devices', 'meta' => ['title' => '点点够设备', 'icon' => 'Grid']]
            ]
        ],
        [
            'id' => '5',
            'path' => 'vip',
            'meta' => ['title' => 'VIP管理', 'icon' => 'Star'],
            'children' => [
                ['id' => '5-1', 'path' => 'vip-dividends', 'meta' => ['title' => 'VIP分红', 'icon' => 'Money']],
                ['id' => '5-2', 'path' => 'users/vip-dividends', 'meta' => ['title' => 'VIP用户', 'icon' => 'User']]
            ]
        ],
        [
            'id' => '6',
            'path' => 'installation',
            'meta' => ['title' => '安装管理', 'icon' => 'Tools'],
            'children' => [
                ['id' => '6-1', 'path' => 'installation/booking', 'meta' => ['title' => '安装预约', 'icon' => 'Calendar']],
                ['id' => '6-2', 'path' => 'installation/statistics', 'meta' => ['title' => '安装统计', 'icon' => 'DataAnalysis']]
            ]
        ],
        [
            'id' => '7',
            'path' => 'finance',
            'meta' => ['title' => '财务', 'icon' => 'Money'],
            'children' => [
                ['id' => '7-1', 'path' => 'finance/shengfutong', 'meta' => ['title' => '盛付通提现', 'icon' => 'CreditCard']],
                ['id' => '7-2', 'path' => 'finance/xinsheng', 'meta' => ['title' => '新生提现', 'icon' => 'Star']],
                ['id' => '7-3', 'path' => 'finance/purifier', 'meta' => ['title' => '净水器提现', 'icon' => 'Tools']]
            ]
        ],
        [
            'id' => '8',
            'path' => 'system',
            'meta' => ['title' => '系统管理', 'icon' => 'Setting'],
            'children' => [
                ['id' => '8-1', 'path' => 'system/menu', 'meta' => ['title' => '菜单管理', 'icon' => 'Menu']],
                ['id' => '8-2', 'path' => 'system/nav', 'meta' => ['title' => '导航管理', 'icon' => 'Operation']],
                ['id' => '8-3', 'path' => 'system/sms/logs', 'meta' => ['title' => '短信日志', 'icon' => 'ChatDotRound']],
                ['id' => '8-4', 'path' => 'system/sms/stats', 'meta' => ['title' => '短信统计', 'icon' => 'DataLine']]
            ]
        ]
    ];
}
