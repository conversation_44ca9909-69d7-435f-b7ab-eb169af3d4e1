<?php
// 允许跨域请求
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    header('HTTP/1.1 204 No Content');
    exit();
}

// 确保请求是GET
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    echo json_encode(['code' => 1, 'message' => '方法不允许']);
    exit();
}

// 检查认证token
$headers = getallheaders();
$token = null;

if (isset($headers['Authorization'])) {
    $auth_header = $headers['Authorization'];
    if (strpos($auth_header, 'Bearer ') === 0) {
        $token = substr($auth_header, 7);
    }
}

// 简单模拟token验证
if ($token) {
    // 返回用户信息
    header('Content-Type: application/json');
    echo json_encode([
        'code' => 0,
        'message' => '获取成功',
        'data' => [
            'id' => 1,
            'username' => 'admin',
            'name' => '管理员',
            'avatar' => '/admin/images/avatar.png',
            'roles' => ['admin'],
            'permissions' => ['*']
        ]
    ]);
} else {
    // 返回认证失败响应
    http_response_code(401);
    echo json_encode(['code' => 1, 'message' => '未授权或token已过期']);
} 