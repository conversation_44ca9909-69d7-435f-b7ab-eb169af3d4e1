<?php
// 允许跨域请求
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    header('HTTP/1.1 204 No Content');
    exit();
}

// 确保请求是POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['code' => 1, 'message' => '方法不允许']);
    exit();
}

// 获取POST数据
$json_data = file_get_contents('php://input');
$data = json_decode($json_data, true);

// 验证输入
if (!isset($data['username']) || !isset($data['password'])) {
    http_response_code(400);
    echo json_encode(['code' => 1, 'message' => '用户名和密码必须提供']);
    exit();
}

$username = $data['username'];
$password = $data['password'];

// 在这里应该有数据库验证，但我们简化为固定账号密码
if ($username === 'admin' && $password === 'admin123') {
    // 生成一个简单的token
    $token = bin2hex(random_bytes(32));
    
    // 返回成功响应
    header('Content-Type: application/json');
    echo json_encode([
        'code' => 0,
        'message' => '登录成功',
        'data' => [
            'token' => $token,
            'user' => [
                'id' => 1,
                'username' => 'admin',
                'name' => '管理员',
                'avatar' => '/admin/images/avatar.png'
            ]
        ]
    ]);
} else {
    // 返回失败响应
    http_response_code(401);
    echo json_encode(['code' => 1, 'message' => '用户名或密码错误']);
} 