<?php
/**
 * 管理后台 - 获取净水器安装预约详情
 * API路径: /api/admin/installation/get_booking_details.php
 * 请求方式: GET
 * 
 * 参数:
 * - id: 预约ID
 * 
 * 返回:
 * - code: 0表示成功，非0表示失败
 * - message: 提示信息
 * - data: 预约详情对象
 */

// // 使用统一的数据库连接文件
require_once __DIR__ . '/../../db.php';

require_once __DIR__ . '/../../../api/functions.php'; /* 已注释 */

// 验证管理员权限
$admin = validateAdminToken(['is_admin' => true]);
if (!$admin) {
    responseError(401, '未登录或权限不足');
}

// 验证必填参数
if (!isset($_GET['id']) || empty($_GET['id'])) {
    responseError(400, '缺少必要参数: id');
}

$bookingId = intval($_GET['id']);

// 查询预约详情
$db = getDbConnection();
$sql = "SELECT 
            b.*,
            u.username as user_username,
            u.wechat_nickname as user_nickname,
            u.wechat_avatar as user_avatar,
            u.phone as user_phone,
            r.username as referrer_username,
            r.wechat_nickname as referrer_nickname,
            r.wechat_avatar as referrer_avatar,
            e.username as engineer_username,
            e.wechat_nickname as engineer_nickname,
            e.phone as engineer_phone,
            e.wechat_avatar as engineer_avatar
        FROM 
            install_bookings b
        LEFT JOIN 
            app_users u ON b.user_id = u.id
        LEFT JOIN 
            app_users r ON b.referrer_id = r.id
        LEFT JOIN 
            app_users e ON b.engineer_id = e.id
        WHERE 
            b.id = :booking_id
        LIMIT 1";

$stmt = $db->prepare($sql);
$stmt->bindParam(':booking_id', $bookingId);
$stmt->execute();

$booking = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$booking) {
    responseError(404, '预约记录不存在');
}

// 格式化套餐类型
$packageTypeMap = [
    'personal' => '个人套餐',
    'unlimited' => '无限续用套餐',
    'business_year' => '商业年费套餐',
    'business_flow' => '商业流量套餐'
];
$booking['package_type_text'] = $packageTypeMap[$booking['package_type']] ?? $booking['package_type'];

// 格式化状态
$statusMap = [
    'pending' => '待处理',
    'confirmed' => '已确认',
    'assigned' => '已分配工程师',
    'completed' => '已完成',
    'cancelled' => '已取消'
];
$booking['status_text'] = $statusMap[$booking['status']] ?? $booking['status'];

// 格式化支付状态
$paymentStatusMap = [
    'unpaid' => '未支付',
    'paid' => '已支付',
    'refunded' => '已退款'
];
$booking['payment_status_text'] = $paymentStatusMap[$booking['payment_status']] ?? $booking['payment_status'];

// 设置用户显示名称
$booking['user_display_name'] = $booking['user_nickname'] ?: $booking['user_username'] ?: '未知用户';

// 设置推荐人显示名称
if ($booking['referrer_id']) {
    $booking['referrer_display_name'] = $booking['referrer_nickname'] ?: $booking['referrer_username'] ?: '未知推荐人';
}

// 设置工程师显示名称
if ($booking['engineer_id']) {
    $booking['engineer_display_name'] = $booking['engineer_nickname'] ?: $booking['engineer_username'] ?: '未知工程师';
}

// 获取工程师列表(用于分配)
$sql = "SELECT 
            id, username, wechat_nickname, wechat_avatar, phone
        FROM 
            app_users
        WHERE 
            is_engineer = 1
        ORDER BY 
            id ASC";

$stmt = $db->prepare($sql);
$stmt->execute();
$engineers = $stmt->fetchAll(PDO::FETCH_ASSOC);

foreach ($engineers as &$engineer) {
    $engineer['display_name'] = $engineer['wechat_nickname'] ?: $engineer['username'] ?: 'ID:' . $engineer['id'];
}

// 返回成功响应
responseSuccess([
    'booking' => $booking,
    'engineers' => $engineers
]); 