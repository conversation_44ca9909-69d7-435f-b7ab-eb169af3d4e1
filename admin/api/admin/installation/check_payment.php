<?php
/**
 * 管理后台 - 检查安装预约支付状态
 * API路径: /api/admin/installation/check_payment.php
 * 请求方式: GET
 * 
 * 参数:
 * - booking_id: 预约ID
 * - order_no: 订单号
 * 
 * 返回:
 * - code: 0表示成功，非0表示失败
 * - message: 提示信息
 * - data: 支付状态信息
 */

// // 使用统一的数据库连接文件
require_once __DIR__ . '/../../db.php';

require_once __DIR__ . '/../../../api/functions.php'; /* 已注释 */

// 验证管理员权限
$admin = validateAdminToken(['is_admin' => true]);
if (!$admin) {
    responseError(401, '未登录或权限不足');
}

// 验证必填参数
if ((!isset($_GET['booking_id']) || empty($_GET['booking_id'])) && 
    (!isset($_GET['order_no']) || empty($_GET['order_no']))) {
    responseError(400, '缺少必要参数: booking_id 或 order_no');
}

try {
    // 连接数据库
    $conn = getConnection();
    
    // 构建查询条件
    $whereClause = '';
    $params = [];
    $types = '';
    
    if (isset($_GET['booking_id']) && !empty($_GET['booking_id'])) {
        $bookingId = $_GET['booking_id'];
        $whereClause = 'id = ?';
        $params[] = $bookingId;
        $types .= 'i';
    } else {
        $orderNo = $_GET['order_no'];
        $whereClause = 'booking_no = ?';
        $params[] = $orderNo;
        $types .= 's';
    }
    
    // 查询预约信息
    $stmt = $conn->prepare("SELECT id, booking_no, payment_status, payment_time, payment_method, transaction_id, status, total_amount FROM install_bookings WHERE {$whereClause}");
    $stmt->bind_param($types, ...$params);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows === 0) {
        $conn->close();
        responseError(404, '预约记录不存在');
    }
    
    $booking = $result->fetch_assoc();
    
    // 关闭数据库连接
    $conn->close();
    
    // 构建响应数据
    $responseData = [
        'booking_id' => $booking['id'],
        'order_no' => $booking['booking_no'],
        'payment_status' => $booking['payment_status'],
        'payment_time' => $booking['payment_time'],
        'payment_method' => $booking['payment_method'],
        'transaction_id' => $booking['transaction_id'],
        'status' => $booking['status'],
        'total_amount' => $booking['total_amount'],
        'is_paid' => $booking['payment_status'] === 'paid'
    ];
    
    // 返回支付状态
    responseSuccess('获取支付状态成功', $responseData);
} catch (Exception $e) {
    responseError(500, '获取支付状态失败: ' . $e->getMessage());
}
