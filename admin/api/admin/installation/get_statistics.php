<?php
/**
 * 管理后台 - 获取净水器安装预约统计数据
 * API路径: /api/admin/installation/get_statistics.php
 * 请求方式: GET
 * 
 * 参数:
 * - type: 统计类型 (daily, weekly, monthly)，默认daily
 * - start_date: 开始日期筛选 (可选)
 * - end_date: 结束日期筛选 (可选)
 * - engineer_id: 工程师ID筛选 (可选)
 * 
 * 返回:
 * - code: 0表示成功，非0表示失败
 * - message: 提示信息
 * - data: 统计数据对象
 */

// // 使用统一的数据库连接文件
require_once __DIR__ . '/../../db.php';

require_once __DIR__ . '/../../../api/functions.php'; /* 已注释 */
require_once __DIR__ . '/../../../api/functions/logger.php';

// 验证管理员权限
$admin = validateAdminToken(['is_admin' => true]);
if (!$admin) {
    responseError(401, '未登录或权限不足');
}

// 获取查询参数
$type = isset($_GET['type']) ? $_GET['type'] : 'daily';
$startDate = isset($_GET['start_date']) ? $_GET['start_date'] : null;
$endDate = isset($_GET['end_date']) ? $_GET['end_date'] : null;
$engineerId = isset($_GET['engineer_id']) ? intval($_GET['engineer_id']) : null;

// 如果没有指定日期范围，根据统计类型设置默认范围
if (!$startDate || !$endDate) {
    switch ($type) {
        case 'daily':
            $startDate = date('Y-m-d', strtotime('-7 days'));
            $endDate = date('Y-m-d');
            break;
        case 'weekly':
            $startDate = date('Y-m-d', strtotime('-4 weeks'));
            $endDate = date('Y-m-d');
            break;
        case 'monthly':
            $startDate = date('Y-m-d', strtotime('-6 months'));
            $endDate = date('Y-m-d');
            break;
        default:
            $startDate = date('Y-m-d', strtotime('-7 days'));
            $endDate = date('Y-m-d');
    }
}

try {
    // 连接数据库
    $db = getDbConnection();
    
    // 构建基础查询条件
    $params = [];
    $whereClause = "WHERE created_at BETWEEN :start_date AND :end_date_end";
    $params[':start_date'] = $startDate . ' 00:00:00';
    $params[':end_date_end'] = $endDate . ' 23:59:59';
    
    // 添加工程师筛选
    if ($engineerId) {
        $whereClause .= " AND engineer_id = :engineer_id";
        $params[':engineer_id'] = $engineerId;
    }
    
    // 1. 获取概览数据
    $overviewSql = "SELECT 
                        COUNT(*) as total_bookings,
                        SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending_bookings,
                        SUM(CASE WHEN status = 'confirmed' THEN 1 ELSE 0 END) as confirmed_bookings,
                        SUM(CASE WHEN status = 'assigned' THEN 1 ELSE 0 END) as assigned_bookings,
                        SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed_bookings,
                        SUM(CASE WHEN status = 'cancelled' THEN 1 ELSE 0 END) as cancelled_bookings,
                        SUM(CASE WHEN payment_status = 'paid' THEN 1 ELSE 0 END) as paid_bookings,
                        SUM(CASE WHEN payment_status = 'unpaid' THEN 1 ELSE 0 END) as unpaid_bookings,
                        SUM(CASE WHEN payment_status = 'paid' THEN total_amount ELSE 0 END) as total_revenue
                    FROM 
                        install_bookings
                    $whereClause";
    
    $stmt = $db->prepare($overviewSql);
    foreach ($params as $key => $value) {
        $stmt->bindValue($key, $value);
    }
    $stmt->execute();
    $overview = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // 2. 获取状态分布数据
    $statusSql = "SELECT 
                    status,
                    COUNT(*) as count
                FROM 
                    install_bookings
                $whereClause
                GROUP BY 
                    status";
    
    $stmt = $db->prepare($statusSql);
    foreach ($params as $key => $value) {
        $stmt->bindValue($key, $value);
    }
    $stmt->execute();
    $statusDistribution = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // 3. 获取时间趋势数据
    $trendsSql = "";
    $groupBy = "";
    
    switch ($type) {
        case 'daily':
            $groupBy = "DATE(created_at)";
            break;
        case 'weekly':
            $groupBy = "YEARWEEK(created_at)";
            break;
        case 'monthly':
            $groupBy = "DATE_FORMAT(created_at, '%Y-%m')";
            break;
        default:
            $groupBy = "DATE(created_at)";
    }
    
    $trendsSql = "SELECT 
                    $groupBy as time_period,
                    COUNT(*) as total,
                    SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed,
                    SUM(CASE WHEN payment_status = 'paid' THEN 1 ELSE 0 END) as paid
                FROM 
                    install_bookings
                $whereClause
                GROUP BY 
                    $groupBy
                ORDER BY 
                    time_period ASC";
    
    $stmt = $db->prepare($trendsSql);
    foreach ($params as $key => $value) {
        $stmt->bindValue($key, $value);
    }
    $stmt->execute();
    $trends = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // 4. 获取工程师排名数据
    $engineerSql = "SELECT 
                        b.engineer_id,
                        COUNT(*) as total_assigned,
                        SUM(CASE WHEN b.status = 'completed' THEN 1 ELSE 0 END) as completed_count,
                        u.username as engineer_username,
                        u.wechat_nickname as engineer_nickname
                    FROM 
                        install_bookings b
                    LEFT JOIN 
                        app_users u ON b.engineer_id = u.id
                    $whereClause
                    AND 
                        b.engineer_id IS NOT NULL
                    GROUP BY 
                        b.engineer_id
                    ORDER BY 
                        completed_count DESC
                    LIMIT 10";
    
    $stmt = $db->prepare($engineerSql);
    foreach ($params as $key => $value) {
        $stmt->bindValue($key, $value);
    }
    $stmt->execute();
    $engineerRanking = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // 处理工程师排名数据
    foreach ($engineerRanking as &$engineer) {
        $engineer['name'] = $engineer['engineer_nickname'] ?: $engineer['engineer_username'] ?: 'ID:' . $engineer['engineer_id'];
        $engineer['value'] = intval($engineer['completed_count']);
    }
    
    // 5. 获取套餐类型分布
    $packageSql = "SELECT 
                    package_type,
                    COUNT(*) as count
                FROM 
                    install_bookings
                $whereClause
                GROUP BY 
                    package_type";
    
    $stmt = $db->prepare($packageSql);
    foreach ($params as $key => $value) {
        $stmt->bindValue($key, $value);
    }
    $stmt->execute();
    $packageDistribution = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // 6. 获取最近预约列表
    $recentSql = "SELECT 
                    b.*,
                    u.username as user_username,
                    u.wechat_nickname as user_nickname,
                    e.username as engineer_username,
                    e.wechat_nickname as engineer_nickname
                FROM 
                    install_bookings b
                LEFT JOIN 
                    app_users u ON b.user_id = u.id
                LEFT JOIN 
                    app_users e ON b.engineer_id = e.id
                $whereClause
                ORDER BY 
                    b.created_at DESC
                LIMIT 10";
    
    $stmt = $db->prepare($recentSql);
    foreach ($params as $key => $value) {
        $stmt->bindValue($key, $value);
    }
    $stmt->execute();
    $recentBookings = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // 处理最近预约数据
    $packageTypeMap = [
        'personal' => '个人套餐',
        'unlimited' => '无限续用套餐',
        'business_year' => '商业年费套餐',
        'business_flow' => '商业流量套餐'
    ];
    
    $statusMap = [
        'pending' => '待处理',
        'confirmed' => '已确认',
        'assigned' => '已分配工程师',
        'completed' => '已完成',
        'cancelled' => '已取消'
    ];
    
    foreach ($recentBookings as &$booking) {
        $booking['package_type_text'] = $packageTypeMap[$booking['package_type']] ?? $booking['package_type'];
        $booking['status_text'] = $statusMap[$booking['status']] ?? $booking['status'];
        $booking['user_display_name'] = $booking['user_nickname'] ?: $booking['user_username'] ?: '未知用户';
        
        if ($booking['engineer_id']) {
            $booking['engineer_display_name'] = $booking['engineer_nickname'] ?: $booking['engineer_username'] ?: '未知工程师';
        }
    }
    
    // 构建返回数据
    $data = [
        'overview' => $overview,
        'status_distribution' => $statusDistribution,
        'trends' => $trends,
        'engineer_ranking' => $engineerRanking,
        'package_distribution' => $packageDistribution,
        'recent_bookings' => $recentBookings,
        'date_range' => [
            'start_date' => $startDate,
            'end_date' => $endDate
        ]
    ];
    
    // 记录日志
    log_message('获取安装预约统计数据成功', LOG_INFO, 'admin_api');
    
    // 返回成功响应
    responseSuccess($data);
} catch (Exception $e) {
    // 记录错误日志
    log_message('获取安装预约统计数据失败: ' . $e->getMessage(), LOG_ERROR, 'admin_api');
    
    // 返回错误响应
    responseError(500, '获取统计数据失败: ' . $e->getMessage());
}
