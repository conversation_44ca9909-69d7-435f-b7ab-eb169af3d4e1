<?php
/**
 * 管理后台 - 微信地址选择API
 * API路径: /api/admin/installation/wechat_address.php
 * 请求方式: POST
 * 
 * 参数:
 * - address_data: 微信地址数据 (JSON格式)
 * 
 * 返回:
 * - code: 0表示成功，非0表示失败
 * - message: 提示信息
 * - data: 处理后的地址数据
 */

// // 使用统一的数据库连接文件
require_once __DIR__ . '/../../db.php';

require_once __DIR__ . '/../../../api/functions.php'; /* 已注释 */
require_once __DIR__ . '/../../../api/functions/logger.php';

// 设置响应头
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 只允许POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    responseError(405, 'Method Not Allowed');
}

// 验证管理员权限
$admin = validateAdminToken(['is_admin' => true]);
if (!$admin) {
    responseError(401, '未登录或权限不足');
}

// 获取请求体
$requestBody = file_get_contents('php://input');
$data = json_decode($requestBody, true);

// 验证请求数据
if (!$data || !isset($data['address_data'])) {
    responseError(400, '无效的请求数据');
}

// 提取地址数据
$addressData = $data['address_data'];

// 记录日志
log_message('微信地址选择: ' . json_encode($addressData, JSON_UNESCAPED_UNICODE), LOG_INFO, 'wechat_address');

try {
    // 处理地址数据
    $processedAddress = processWechatAddress($addressData);
    
    // 返回处理后的地址
    responseSuccess('地址处理成功', $processedAddress);
} catch (Exception $e) {
    log_message('处理微信地址失败: ' . $e->getMessage(), LOG_ERROR, 'wechat_address');
    responseError(500, '处理地址失败: ' . $e->getMessage());
}

/**
 * 处理微信地址数据
 * 
 * @param array $addressData 微信地址数据
 * @return array 处理后的地址数据
 */
function processWechatAddress($addressData) {
    // 验证必要字段
    $requiredFields = ['userName', 'telNumber', 'provinceName', 'cityName', 'countryName', 'detailInfo'];
    foreach ($requiredFields as $field) {
        if (!isset($addressData[$field]) || empty($addressData[$field])) {
            throw new Exception("缺少必要字段: {$field}");
        }
    }
    
    // 构建完整地址
    $fullAddress = $addressData['provinceName'] . $addressData['cityName'] . $addressData['countryName'] . $addressData['detailInfo'];
    
    // 返回处理后的数据
    return [
        'contact_name' => $addressData['userName'],
        'contact_phone' => $addressData['telNumber'],
        'install_address' => $fullAddress,
        'province' => $addressData['provinceName'],
        'city' => $addressData['cityName'],
        'district' => $addressData['countryName'],
        'address_detail' => $addressData['detailInfo'],
        'postal_code' => $addressData['postalCode'] ?? '',
        'original_data' => $addressData
    ];
}
