<?php
/**
 * 管理后台 - 创建安装预约微信支付订单
 * API路径: /api/admin/installation/create_payment.php
 * 请求方式: POST
 *
 * 参数:
 * - booking_id: 预约ID
 * - openid: 用户微信openid
 *
 * 返回:
 * - code: 0表示成功，非0表示失败
 * - message: 提示信息
 * - data: 支付参数
 */

// // 使用统一的数据库连接文件
require_once __DIR__ . '/../../db.php';

require_once __DIR__ . '/../../../api/functions.php'; /* 已注释 */
require_once __DIR__ . '/../../../api/functions/logger.php';
require_once __DIR__ . '/../../../api/functions/logger_installation.php';

// 验证管理员权限
$admin = validateAdminToken(['is_admin' => true]);
if (!$admin) {
    responseError(401, '未登录或权限不足');
}

// 验证必填参数
if (!isset($_POST['booking_id']) || empty($_POST['booking_id'])) {
    responseError(400, '缺少必要参数: booking_id');
}

if (!isset($_POST['openid']) || empty($_POST['openid'])) {
    responseError(400, '缺少必要参数: openid');
}

$bookingId = $_POST['booking_id'];
$openid = $_POST['openid'];

// 记录API调用日志
log_installation_api('create_payment', [
    'booking_id' => $bookingId,
    'openid' => $openid,
    'admin_id' => $admin['id']
]);

try {
    // 连接数据库
    $conn = getConnection();

    // 查询预约信息
    $stmt = $conn->prepare("SELECT * FROM install_bookings WHERE id = ?");
    $stmt->bind_param("i", $bookingId);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows === 0) {
        $conn->close();
        log_installation_payment('预约记录不存在', LOG_ERROR, ['booking_id' => $bookingId]);
        responseError(404, '预约记录不存在');
    }

    $booking = $result->fetch_assoc();
    log_installation_payment('找到预约信息', LOG_INFO, [
        'booking_id' => $bookingId,
        'booking_no' => $booking['booking_no'],
        'total_amount' => $booking['total_amount']
    ]);

    // 检查支付状态
    if ($booking['payment_status'] === 'paid') {
        $conn->close();
        log_installation_payment('预约已支付，无需重复支付', LOG_WARNING, [
            'booking_id' => $bookingId,
            'booking_no' => $booking['booking_no']
        ]);
        responseError(400, '该预约已支付，无需重复支付');
    }

    // 生成订单号
    $orderNo = 'INST' . date('YmdHis') . rand(1000, 9999);
    log_installation_payment('生成订单号', LOG_INFO, [
        'booking_id' => $bookingId,
        'order_no' => $orderNo
    ]);

    // 更新预约订单号
    $stmt = $conn->prepare("UPDATE install_bookings SET booking_no = ? WHERE id = ?");
    $stmt->bind_param("si", $orderNo, $bookingId);
    $stmt->execute();

    // 关闭数据库连接
    $conn->close();

    // 调用微信支付接口
    log_installation_payment('准备调用微信支付接口', LOG_INFO, [
        'booking_id' => $bookingId,
        'order_no' => $orderNo,
        'openid' => $openid
    ]);
    $paymentParams = createWechatPayment($orderNo, $booking, $openid);

    // 记录日志
    log_message('创建微信支付订单成功: ' . $orderNo, LOG_INFO, 'wechat_pay');
    log_installation_payment('创建微信支付订单成功', LOG_INFO, [
        'booking_id' => $bookingId,
        'order_no' => $orderNo,
        'total_amount' => $booking['total_amount']
    ]);

    // 记录API响应日志
    log_installation_api('create_payment', [
        'booking_id' => $bookingId,
        'openid' => $openid,
        'admin_id' => $admin['id']
    ], [
        'code' => 0,
        'order_no' => $orderNo
    ]);

    // 返回支付参数
    responseSuccess('创建支付订单成功', [
        'order_no' => $orderNo,
        'payment_params' => $paymentParams
    ]);
} catch (Exception $e) {
    log_message('创建微信支付订单失败: ' . $e->getMessage(), LOG_ERROR, 'wechat_pay');
    log_installation_payment('创建微信支付订单失败', LOG_ERROR, [
        'booking_id' => $bookingId,
        'error' => $e->getMessage(),
        'trace' => $e->getTraceAsString()
    ]);

    // 记录API错误日志
    log_installation_api('create_payment', [
        'booking_id' => $bookingId,
        'openid' => $openid,
        'admin_id' => $admin['id']
    ], [
        'code' => 500,
        'message' => $e->getMessage()
    ], LOG_ERROR);

    responseError(500, '创建支付订单失败: ' . $e->getMessage());
}

/**
 * 创建微信支付订单
 *
 * @param string $orderNo 订单号
 * @param array $booking 预约信息
 * @param string $openid 用户微信openid
 * @return array 支付参数
 */
function createWechatPayment($orderNo, $booking, $openid) {
    global $WECHAT_CONFIG;

    // 微信支付参数
    $appid = $WECHAT_CONFIG['APP_ID'];
    $mchId = $WECHAT_CONFIG['PAYMENT']['MCH_ID'];
    $mchKey = $WECHAT_CONFIG['PAYMENT']['KEY'];
    $notifyUrl = $WECHAT_CONFIG['PAYMENT']['NOTIFY_URL'];

    // 订单金额（单位：分）
    $totalFee = intval($booking['total_amount'] * 100);

    // 订单描述
    $body = '净水器安装预约-' . $booking['package_type'];

    // 附加数据
    $attach = json_encode([
        'booking_id' => $booking['id'],
        'type' => 'installation'
    ]);

    // 构建请求参数
    $params = [
        'appid' => $appid,
        'mch_id' => $mchId,
        'nonce_str' => generateNonceStr(),
        'body' => $body,
        'out_trade_no' => $orderNo,
        'total_fee' => $totalFee,
        'spbill_create_ip' => $_SERVER['REMOTE_ADDR'],
        'notify_url' => $notifyUrl,
        'trade_type' => 'JSAPI',
        'openid' => $openid,
        'attach' => $attach
    ];

    // 生成签名
    $params['sign'] = generateWxPaySign($params, $mchKey);

    // 将参数转为XML
    $xml = arrayToXml($params);

    // 发送请求到微信支付接口
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'https://api.mch.weixin.qq.com/pay/unifiedorder');
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $xml);

    $response = curl_exec($ch);

    if ($error = curl_error($ch)) {
        curl_close($ch);
        throw new Exception('微信支付请求失败: ' . $error);
    }

    curl_close($ch);

    // 解析返回的XML
    $result = xmlToArray($response);

    if ($result['return_code'] === 'SUCCESS' && $result['result_code'] === 'SUCCESS') {
        // 构建JSAPI支付参数
        $timeStamp = (string)time();
        $nonceStr = generateNonceStr();
        $package = 'prepay_id=' . $result['prepay_id'];
        $signType = 'MD5';

        $payParams = [
            'appId' => $appid,
            'timeStamp' => $timeStamp,
            'nonceStr' => $nonceStr,
            'package' => $package,
            'signType' => $signType
        ];

        $payParams['paySign'] = generateWxPaySign($payParams, $mchKey);

        return $payParams;
    } else {
        throw new Exception($result['return_msg'] ?? '微信支付统一下单失败');
    }
}

/**
 * 生成随机字符串
 *
 * @param int $length 字符串长度
 * @return string 随机字符串
 */
function generateNonceStr($length = 32) {
    $chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    $str = '';
    for ($i = 0; $i < $length; $i++) {
        $str .= substr($chars, mt_rand(0, strlen($chars) - 1), 1);
    }
    return $str;
}

/**
 * 生成微信支付签名
 *
 * @param array $params 参数数组
 * @param string $key 商户密钥
 * @return string 签名
 */
function generateWxPaySign($params, $key) {
    // 按照键名对参数进行排序
    ksort($params);

    // 构建签名字符串
    $stringA = '';
    foreach ($params as $k => $v) {
        if ($k != 'sign' && $v !== '' && !is_null($v)) {
            $stringA .= "{$k}={$v}&";
        }
    }
    $stringA .= "key={$key}";

    // MD5加密并转为大写
    return strtoupper(md5($stringA));
}

/**
 * 将数组转为XML
 *
 * @param array $arr 数组
 * @return string XML字符串
 */
function arrayToXml($arr) {
    $xml = "<xml>";
    foreach ($arr as $key => $val) {
        if (is_numeric($val)) {
            $xml .= "<{$key}>{$val}</{$key}>";
        } else {
            $xml .= "<{$key}><![CDATA[{$val}]]></{$key}>";
        }
    }
    $xml .= "</xml>";
    return $xml;
}

/**
 * 将XML转为数组
 *
 * @param string $xml XML字符串
 * @return array 数组
 */
function xmlToArray($xml) {
    // 禁用libxml错误显示
    libxml_use_internal_errors(true);

    // 解析XML
    $obj = simplexml_load_string($xml, 'SimpleXMLElement', LIBXML_NOCDATA);

    // 转为JSON再转为数组
    $json = json_encode($obj);
    return json_decode($json, true);
}
