<?php
/**
 * 管理后台 - 微信支付回调通知处理
 * API路径: /api/admin/installation/payment_notify.php
 * 请求方式: POST (由微信服务器调用)
 * 
 * 返回:
 * - XML格式的处理结果
 */

// // 使用统一的数据库连接文件
require_once __DIR__ . '/../../db.php';

require_once __DIR__ . '/../../../api/functions.php'; /* 已注释 */
require_once __DIR__ . '/../../../api/functions/logger.php';

// 接收微信支付通知
$xml = file_get_contents('php://input');
if (empty($xml)) {
    log_message('微信支付回调通知: 未接收到数据', LOG_ERROR, 'wechat_pay');
    echo returnWxpayNotifyFail('未接收到数据');
    exit;
}

// 记录原始通知数据
log_message('微信支付回调通知: 接收到数据', LOG_INFO, 'wechat_pay');
log_message('微信支付回调通知原始数据: ' . $xml, LOG_INFO, 'wechat_pay');

try {
    // 解析XML数据
    $data = xmlToArray($xml);
    
    // 验证返回状态
    if ($data['return_code'] !== 'SUCCESS') {
        log_message('微信支付回调通知: 返回状态失败 - ' . ($data['return_msg'] ?? '未知错误'), LOG_ERROR, 'wechat_pay');
        echo returnWxpayNotifyFail($data['return_msg'] ?? '返回状态失败');
        exit;
    }
    
    // 验证业务结果
    if ($data['result_code'] !== 'SUCCESS') {
        log_message('微信支付回调通知: 业务结果失败 - ' . ($data['err_code_des'] ?? '未知错误'), LOG_ERROR, 'wechat_pay');
        echo returnWxpayNotifyFail($data['err_code_des'] ?? '业务结果失败');
        exit;
    }
    
    // 验证签名
    if (!verifyWxpaySign($data)) {
        log_message('微信支付回调通知: 签名验证失败', LOG_ERROR, 'wechat_pay');
        echo returnWxpayNotifyFail('签名验证失败');
        exit;
    }
    
    // 解析基本信息
    $orderNo = $data['out_trade_no'];
    $transactionId = $data['transaction_id'];
    $totalFee = $data['total_fee'] / 100; // 微信支付金额单位为分，转为元
    $attach = $data['attach'] ?? '';
    
    // 处理附加数据
    $attachData = json_decode($attach, true);
    $bookingId = $attachData['booking_id'] ?? null;
    $type = $attachData['type'] ?? '';
    
    // 验证是否为安装预约支付
    if ($type !== 'installation' || !$bookingId) {
        log_message('微信支付回调通知: 非安装预约支付或缺少预约ID', LOG_ERROR, 'wechat_pay');
        echo returnWxpayNotifyFail('非安装预约支付或缺少预约ID');
        exit;
    }
    
    // 连接数据库
    $conn = getConnection();
    
    // 查询预约信息
    $stmt = $conn->prepare("SELECT * FROM install_bookings WHERE id = ? AND booking_no = ?");
    $stmt->bind_param("is", $bookingId, $orderNo);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows === 0) {
        $conn->close();
        log_message('微信支付回调通知: 预约记录不存在', LOG_ERROR, 'wechat_pay');
        echo returnWxpayNotifyFail('预约记录不存在');
        exit;
    }
    
    $booking = $result->fetch_assoc();
    
    // 验证金额
    if (abs($booking['total_amount'] - $totalFee) > 0.01) {
        $conn->close();
        log_message("微信支付回调通知: 金额不匹配 - 订单金额{$booking['total_amount']}, 支付金额{$totalFee}", LOG_ERROR, 'wechat_pay');
        echo returnWxpayNotifyFail('金额不匹配');
        exit;
    }
    
    // 检查是否已处理过该支付
    if ($booking['payment_status'] === 'paid' && !empty($booking['transaction_id'])) {
        $conn->close();
        log_message('微信支付回调通知: 该订单已处理过支付', LOG_INFO, 'wechat_pay');
        echo returnWxpayNotifySuccess();
        exit;
    }
    
    // 更新预约支付状态
    $now = date('Y-m-d H:i:s');
    $paymentStatus = 'paid';
    $paymentMethod = 'wechat';
    
    $stmt = $conn->prepare("UPDATE install_bookings SET 
        payment_status = ?, 
        payment_time = ?, 
        payment_method = ?, 
        transaction_id = ?, 
        status = CASE WHEN status = 'pending' THEN 'confirmed' ELSE status END,
        updated_at = ?
        WHERE id = ?");
    
    $stmt->bind_param("sssssi", 
        $paymentStatus, 
        $now, 
        $paymentMethod, 
        $transactionId, 
        $now, 
        $bookingId
    );
    
    if (!$stmt->execute()) {
        $conn->close();
        log_message('微信支付回调通知: 更新预约支付状态失败 - ' . $stmt->error, LOG_ERROR, 'wechat_pay');
        echo returnWxpayNotifyFail('更新预约支付状态失败');
        exit;
    }
    
    // 关闭数据库连接
    $conn->close();
    
    // 记录支付成功日志
    log_message("微信支付回调通知: 支付成功处理完成 - 订单号{$orderNo}, 交易号{$transactionId}", LOG_INFO, 'wechat_pay');
    
    // 返回成功
    echo returnWxpayNotifySuccess();
} catch (Exception $e) {
    log_message('微信支付回调通知处理异常: ' . $e->getMessage(), LOG_ERROR, 'wechat_pay');
    echo returnWxpayNotifyFail('处理异常: ' . $e->getMessage());
}

/**
 * 验证微信支付签名
 * 
 * @param array $data 微信支付回调数据
 * @return bool 是否验证通过
 */
function verifyWxpaySign($data) {
    global $WECHAT_CONFIG;
    
    // 如果没有签名，直接返回失败
    if (!isset($data['sign'])) {
        return false;
    }
    
    // 获取微信支付密钥
    $key = $WECHAT_CONFIG['PAYMENT']['KEY'];
    
    // 获取原始签名
    $originalSign = $data['sign'];
    
    // 移除签名字段，重新生成签名
    $data2 = $data;
    unset($data2['sign']);
    
    // 生成新签名
    $newSign = generateWxPaySign($data2, $key);
    
    // 比较签名
    return $originalSign === $newSign;
}

/**
 * 返回微信支付通知成功响应
 * 
 * @return string XML响应
 */
function returnWxpayNotifySuccess() {
    return "<xml><return_code><![CDATA[SUCCESS]]></return_code><return_msg><![CDATA[OK]]></return_msg></xml>";
}

/**
 * 返回微信支付通知失败响应
 * 
 * @param string $message 错误信息
 * @return string XML响应
 */
function returnWxpayNotifyFail($message) {
    return "<xml><return_code><![CDATA[FAIL]]></return_code><return_msg><![CDATA[{$message}]]></return_msg></xml>";
}

/**
 * 生成微信支付签名
 * 
 * @param array $params 参数数组
 * @param string $key 商户密钥
 * @return string 签名
 */
function generateWxPaySign($params, $key) {
    // 按照键名对参数进行排序
    ksort($params);
    
    // 构建签名字符串
    $stringA = '';
    foreach ($params as $k => $v) {
        if ($k != 'sign' && $v !== '' && !is_null($v)) {
            $stringA .= "{$k}={$v}&";
        }
    }
    $stringA .= "key={$key}";
    
    // MD5加密并转为大写
    return strtoupper(md5($stringA));
}

/**
 * 将XML转为数组
 * 
 * @param string $xml XML字符串
 * @return array 数组
 */
function xmlToArray($xml) {
    // 禁用libxml错误显示
    libxml_use_internal_errors(true);
    
    // 解析XML
    $obj = simplexml_load_string($xml, 'SimpleXMLElement', LIBXML_NOCDATA);
    
    // 转为JSON再转为数组
    $json = json_encode($obj);
    return json_decode($json, true);
}
