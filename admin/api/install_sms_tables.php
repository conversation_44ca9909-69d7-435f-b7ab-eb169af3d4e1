<?php
// 设置HTTP响应头
header('Content-Type: application/json');
require_once __DIR__ . '/functions/db.php';
require_once __DIR__ . '/config.php';

// 检查权限 - 仅允许在DEBUG模式下运行
if (!DEBUG_MODE) {
    echo json_encode([
        'code' => 403,
        'message' => '禁止访问',
        'data' => null
    ]);
    exit;
}

// 连接数据库
$conn = get_db_connection();
if (!$conn) {
    echo json_encode([
        'code' => 500,
        'message' => '数据库连接失败',
        'data' => null
    ]);
    exit;
}

// 创建短信验证码表
$sql_create_sms_codes = "
CREATE TABLE IF NOT EXISTS `sms_codes` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `phone` varchar(20) NOT NULL COMMENT '手机号',
  `code` varchar(10) NOT NULL COMMENT '验证码',
  `type` varchar(20) NOT NULL COMMENT '验证码类型(login/register/reset/bind)',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  `is_used` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否已使用',
  `used_at` datetime DEFAULT NULL COMMENT '使用时间',
  PRIMARY KEY (`id`),
  KEY `idx_phone_code_type` (`phone`, `code`, `type`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='短信验证码表';
";

// 创建短信发送日志表
$sql_create_sms_logs = "
CREATE TABLE IF NOT EXISTS `sms_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `phone` varchar(20) NOT NULL COMMENT '手机号',
  `code` varchar(10) NOT NULL COMMENT '验证码',
  `type` varchar(20) NOT NULL COMMENT '短信类型',
  `channel` varchar(20) DEFAULT 'aliyun' COMMENT '发送渠道',
  `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '发送状态 0-失败 1-成功',
  `request_ip` varchar(50) DEFAULT NULL COMMENT '请求IP',
  `request_data` text DEFAULT NULL COMMENT '请求参数',
  `response_data` text DEFAULT NULL COMMENT '响应数据',
  `error_message` text DEFAULT NULL COMMENT '错误信息',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_phone` (`phone`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='短信发送日志表';
";

// 检查sms_logs表结构，如果已存在且没有channel列，则添加
$sql_check_sms_logs = "
SELECT COUNT(*) as table_exists FROM information_schema.tables 
WHERE table_schema = DATABASE() AND table_name = 'sms_logs'
";

$sql_check_channel_column = "
SELECT COUNT(*) as column_exists FROM information_schema.columns 
WHERE table_schema = DATABASE() AND table_name = 'sms_logs' 
AND column_name = 'channel'
";

$table_exists = false;
$column_exists = false;

$result_check_table = $conn->query($sql_check_sms_logs);
if ($result_check_table && $row = $result_check_table->fetch_assoc()) {
    $table_exists = (int)$row['table_exists'] > 0;
}

if ($table_exists) {
    $result_check_column = $conn->query($sql_check_channel_column);
    if ($result_check_column && $row = $result_check_column->fetch_assoc()) {
        $column_exists = (int)$row['column_exists'] > 0;
    }
    
    if (!$column_exists) {
        // 如果表存在但没有channel列，添加该列
        $sql_add_channel = "ALTER TABLE `sms_logs` ADD COLUMN `channel` varchar(20) DEFAULT 'aliyun' COMMENT '发送渠道' AFTER `type`";
        $conn->query($sql_add_channel);
        
        // 检查其他必要列是否存在，如果不存在则添加
        $required_columns = [
            'code' => "ADD COLUMN `code` varchar(10) NOT NULL COMMENT '验证码' AFTER `phone`",
            'request_ip' => "ADD COLUMN `request_ip` varchar(50) DEFAULT NULL COMMENT '请求IP' AFTER `status`",
            'request_data' => "ADD COLUMN `request_data` text DEFAULT NULL COMMENT '请求参数' AFTER `request_ip`",
            'response_data' => "ADD COLUMN `response_data` text DEFAULT NULL COMMENT '响应数据' AFTER `request_data`",
            'error_message' => "ADD COLUMN `error_message` text DEFAULT NULL COMMENT '错误信息' AFTER `response_data`"
        ];
        
        foreach ($required_columns as $column => $sql_add) {
            $sql_check_column = "SELECT COUNT(*) as column_exists FROM information_schema.columns 
                WHERE table_schema = DATABASE() AND table_name = 'sms_logs' AND column_name = '$column'";
            
            $result_check = $conn->query($sql_check_column);
            if ($result_check && $row = $result_check->fetch_assoc() && (int)$row['column_exists'] == 0) {
                $conn->query("ALTER TABLE `sms_logs` $sql_add");
            }
        }
    }
}

// 执行SQL创建表
$result_codes = $conn->query($sql_create_sms_codes);
$result_logs = $conn->query($sql_create_sms_logs);

// 检查结果
$success = $result_codes && $result_logs;
$messages = [];

if (!$result_codes) {
    $messages[] = '创建短信验证码表失败: ' . $conn->error;
}

if (!$result_logs) {
    $messages[] = '创建短信发送日志表失败: ' . $conn->error;
}

$conn->close();

// 返回结果
echo json_encode([
    'code' => $success ? 0 : 500,
    'message' => $success ? '短信相关数据表创建成功' : implode('; ', $messages),
    'data' => null
]);
?> 