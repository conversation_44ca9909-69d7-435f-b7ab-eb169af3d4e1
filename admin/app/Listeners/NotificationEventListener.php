<?php

namespace App\Listeners;

use App\Events\NewUserLoginEvent;
use App\Events\NewInstallationBookingEvent;
use App\Events\VipUpgradePaymentEvent;
use App\Services\NotificationService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class NotificationEventListener implements ShouldQueue
{
    use InteractsWithQueue;

    protected $notificationService;

    /**
     * Create the event listener.
     *
     * @param NotificationService $notificationService
     */
    public function __construct(NotificationService $notificationService)
    {
        $this->notificationService = $notificationService;
    }

    /**
     * Handle new user login events.
     *
     * @param NewUserLoginEvent $event
     * @return void
     */
    public function handleNewUserLogin(NewUserLoginEvent $event)
    {
        try {
            $this->notificationService->notifyNewUserLogin($event->user);
            Log::info('新用户登录通知已发送', ['user_id' => $event->user->id]);
        } catch (\Exception $e) {
            Log::error('发送新用户登录通知失败: ' . $e->getMessage(), ['user_id' => $event->user->id]);
        }
    }

    /**
     * Handle new installation booking events.
     *
     * @param NewInstallationBookingEvent $event
     * @return void
     */
    public function handleNewInstallationBooking(NewInstallationBookingEvent $event)
    {
        try {
            $this->notificationService->notifyNewInstallationBooking($event->booking);
            Log::info('新安装预约通知已发送', ['booking_id' => $event->booking->id]);
        } catch (\Exception $e) {
            Log::error('发送新安装预约通知失败: ' . $e->getMessage(), ['booking_id' => $event->booking->id]);
        }
    }

    /**
     * Handle VIP upgrade payment events.
     *
     * @param VipUpgradePaymentEvent $event
     * @return void
     */
    public function handleVipUpgradePayment(VipUpgradePaymentEvent $event)
    {
        try {
            $this->notificationService->notifyVipUpgradePayment($event->order, $event->user);
            Log::info('VIP升级支付通知已发送', [
                'order_id' => $event->order->id,
                'user_id' => $event->user->id
            ]);
        } catch (\Exception $e) {
            Log::error('发送VIP升级支付通知失败: ' . $e->getMessage(), [
                'order_id' => $event->order->id,
                'user_id' => $event->user->id
            ]);
        }
    }
} 