<?php

namespace App\Listeners;

use App\Events\AppUserUpdated;
use App\Services\VipDividendService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class UpdateVipDividends implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * VIP分红服务
     *
     * @var \App\Services\VipDividendService
     */
    protected $dividendService;

    /**
     * 创建事件监听器
     *
     * @param  \App\Services\VipDividendService  $dividendService
     * @return void
     */
    public function __construct(VipDividendService $dividendService)
    {
        $this->dividendService = $dividendService;
    }

    /**
     * 处理事件
     *
     * @param  \App\Events\AppUserUpdated  $event
     * @return void
     */
    public function handle(AppUserUpdated $event)
    {
        // 检查 is_vip 是否发生变化
        if ($event->originalIsVip != $event->user->is_vip) {
            Log::info('检测到用户VIP状态变化', [
                'user_id' => $event->user->id,
                'name' => $event->user->name,
                'old_is_vip' => $event->originalIsVip,
                'new_is_vip' => $event->user->is_vip
            ]);

            // 调用服务方法更新分红数据
            if ($event->user->is_vip == 1) {
                // 用户成为VIP
                $this->dividendService->handleUserBecomeVip($event->user);
            } else {
                // 用户不再是VIP
                $this->dividendService->handleUserLoseVip($event->user);
            }
        }
    }
}
