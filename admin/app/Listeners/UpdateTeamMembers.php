<?php

namespace App\Listeners;

use App\Events\AppUserUpdated;
use App\Services\TeamMemberService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class UpdateTeamMembers implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * 团队成员服务
     *
     * @var \App\Services\TeamMemberService
     */
    protected $teamMemberService;

    /**
     * 创建事件监听器
     *
     * @param  \App\Services\TeamMemberService  $teamMemberService
     * @return void
     */
    public function __construct(TeamMemberService $teamMemberService)
    {
        $this->teamMemberService = $teamMemberService;
    }

    /**
     * 处理事件
     *
     * @param  \App\Events\AppUserUpdated  $event
     * @return void
     */
    public function handle(AppUserUpdated $event)
    {
        // 检查 is_vip_paid 是否发生变化
        $originalIsVipPaid = $event->user->getOriginal('is_vip_paid');
        
        if ($originalIsVipPaid != $event->user->is_vip_paid) {
            Log::info('检测到用户VIP支付状态变化', [
                'user_id' => $event->user->id,
                'name' => $event->user->name,
                'original_is_vip_paid' => $originalIsVipPaid,
                'new_is_vip_paid' => $event->user->is_vip_paid
            ]);

            // 更新团队成员关系
            $this->teamMemberService->updateTeamMembers($event->user);
        }
    }
} 