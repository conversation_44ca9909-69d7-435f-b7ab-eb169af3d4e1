<?php

namespace App\Providers;

// use Illuminate\Support\Facades\Gate;
use Illuminate\Foundation\Support\Providers\AuthServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\DB;
use Laravel\Sanctum\Sanctum;
use Lara<PERSON>\Sanctum\PersonalAccessToken;
use App\Models\AppUser;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

class AuthServiceProvider extends ServiceProvider
{
    /**
     * The model to policy mappings for the application.
     *
     * @var array<class-string, class-string>
     */
    protected $policies = [
        //
    ];

    /**
     * Register any authentication / authorization services.
     */
    public function boot(): void
    {
        // 自定义Sanctum的token查找逻辑
        Sanctum::authenticateAccessTokensUsing(function (PersonalAccessToken $accessToken, bool $is_valid) {
            // 如果token已经有效，直接返回true
            if ($is_valid) {
                return true;
            }

            // 检查token是否过期
            if ($accessToken->expires_at && $accessToken->expires_at < Carbon::now()) {
                return false;
            }

            return true;
        });

        // 自定义Sanctum的token查找逻辑，支持旧的token格式
        Sanctum::getAccessTokenFromRequestUsing(function ($request) {
            // 尝试从Authorization头获取令牌
            $bearerToken = $request->bearerToken();

            if ($bearerToken) {
                // 检查是否是旧的token格式（未经过hash处理）
                $oldToken = DB::table('auth_tokens')
                    ->where('token', $bearerToken)
                    ->where(function ($query) {
                        $query->whereNull('expires_at')
                            ->orWhere('expires_at', '>', Carbon::now());
                    })
                    ->first();

                if ($oldToken) {
                    // 查找用户
                    $user = AppUser::find($oldToken->user_id);

                    if ($user) {
                        // 检查是否已经在Sanctum中创建了对应的token
                        $sanctumToken = PersonalAccessToken::where('token', hash('sha256', $bearerToken))->first();

                        if (!$sanctumToken) {
                            // 创建新的Sanctum令牌
                            $token = new PersonalAccessToken();
                            $token->tokenable_type = get_class($user);
                            $token->tokenable_id = $user->id;
                            $token->name = 'auth_token';
                            $token->token = hash('sha256', $bearerToken);
                            $token->abilities = '["*"]'; // 所有权限
                            $token->last_used_at = $oldToken->last_used_at;
                            $token->expires_at = $oldToken->expires_at;
                            $token->created_at = $oldToken->created_at;
                            $token->updated_at = $oldToken->updated_at;
                            $token->save();

                            Log::info('已将旧令牌迁移到Sanctum', [
                                'user_id' => $user->id,
                                'token' => substr($bearerToken, 0, 10) . '...'
                            ]);
                        } else {
                            // 更新最后使用时间
                            $sanctumToken->last_used_at = Carbon::now();
                            $sanctumToken->save();
                        }

                        // 更新旧token的最后使用时间
                        DB::table('auth_tokens')
                            ->where('id', $oldToken->id)
                            ->update(['last_used_at' => Carbon::now()]);
                    }
                }
            }

            // 返回原始token，让Sanctum继续处理
            return $bearerToken;
        });
    }
}
