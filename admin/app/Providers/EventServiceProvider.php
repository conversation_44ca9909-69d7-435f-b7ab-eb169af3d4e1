<?php

namespace App\Providers;

use Illuminate\Auth\Events\Registered;
use Illuminate\Auth\Listeners\SendEmailVerificationNotification;
use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Event;

class EventServiceProvider extends ServiceProvider
{
    /**
     * The event to listener mappings for the application.
     *
     * @var array<class-string, array<int, class-string>>
     */
    protected $listen = [
        Registered::class => [
            SendEmailVerificationNotification::class,
        ],
        \App\Events\AppUserUpdated::class => [
            \App\Listeners\UpdateVipDividends::class,
            \App\Listeners\UpdateTeamMembers::class,
        ],
        \App\Events\NewUserLoginEvent::class => [
            \App\Listeners\NotificationEventListener::class . '@handleNewUserLogin',
        ],
        \App\Events\NewInstallationBookingEvent::class => [
            \App\Listeners\NotificationEventListener::class . '@handleNewInstallationBooking',
        ],
        \App\Events\VipUpgradePaymentEvent::class => [
            \App\Listeners\NotificationEventListener::class . '@handleVipUpgradePayment',
        ],
    ];

    /**
     * Register any events for your application.
     */
    public function boot(): void
    {
        //
    }

    /**
     * Determine if events and listeners should be automatically discovered.
     */
    public function shouldDiscoverEvents(): bool
    {
        return false;
    }
}
