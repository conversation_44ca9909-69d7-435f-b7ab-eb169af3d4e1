<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        // 注册视图服务
        $this->app->singleton('view', function ($app) {
            // 创建一个视图工厂实例
            $factory = new \Illuminate\View\Factory(
                new \Illuminate\View\Engines\EngineResolver(),
                new \Illuminate\View\FileViewFinder(
                    new \Illuminate\Filesystem\Filesystem(),
                    ['/www/wwwroot/pay.itapgo.com/Tapp/admin/resources/views']
                ),
                new \Illuminate\Events\Dispatcher()
            );

            // 设置容器实例
            $factory->setContainer($app);

            // 注册视图命名空间
            $factory->addNamespace('app', '/www/wwwroot/pay.itapgo.com/Tapp/admin/resources/views/app');
            $factory->addNamespace('admin', '/www/wwwroot/pay.itapgo.com/Tapp/admin/resources/views/admin');

            return $factory;
        });
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        //
    }
}
