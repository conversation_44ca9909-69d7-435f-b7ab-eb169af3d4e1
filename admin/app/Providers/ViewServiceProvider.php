<?php

namespace App\Providers;

use Illuminate\View\ViewServiceProvider as BaseViewServiceProvider;

class ViewServiceProvider extends BaseViewServiceProvider
{
    /**
     * Register the Blade engine implementation.
     *
     * @param  \Illuminate\View\Engines\EngineResolver  $resolver
     * @return void
     */
    public function registerBladeEngine($resolver)
    {
        // First we'll register the Blade compiler implementation, which is used to compile
        // all of the views for this application. We'll pass the compiler to the engine
        // so it can process the views properly.
        $this->app->singleton('blade.compiler', function () {
            return new \Illuminate\View\Compilers\BladeCompiler(
                $this->app['files'],
                $this->app['config']['view.compiled']
            );
        });

        $resolver->register('blade', function () {
            return new \Illuminate\View\Engines\CompilerEngine(
                $this->app['blade.compiler'],
                $this->app['files']
            );
        });
    }
}
