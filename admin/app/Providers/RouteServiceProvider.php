<?php

namespace App\Providers;

use Illuminate\Cache\RateLimiting\Limit;
use Illuminate\Foundation\Support\Providers\RouteServiceProvider as ServiceProvider;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\URL;

class RouteServiceProvider extends ServiceProvider
{
    /**
     * The path to your application's "home" route.
     *
     * Typically, users are redirected here after authentication.
     *
     * @var string
     */
    public const HOME = '/home';

    /**
     * Define your route model bindings, pattern filters, and other route configuration.
     */
    public function boot(): void
    {
        $this->configureRateLimiting();

        $this->routes(function () {
            Route::prefix('api')
                ->middleware('api')
                ->namespace($this->namespace)
                ->group(base_path('routes/api.php'));

            Route::middleware('web')
                ->namespace($this->namespace)
                ->group(base_path('routes/web.php'));

            // 加载管理后台API路由
            if (file_exists(base_path('routes/admin_api.php'))) {
                Route::middleware('api')
                    ->prefix('api')
                    ->group(base_path('routes/admin_api.php'));
            }
            
            // 加载V1测试路由 - 临时修复，不使用api中间件
            if (file_exists(base_path('routes/v1_test.php'))) {
                Route::prefix('api')
                    ->group(base_path('routes/v1_test.php'));
            }

            // 加载V1测试路由 - 临时修复，不使用api中间件
            if (file_exists(base_path('routes/v1_test.php'))) {
                Route::prefix('api')
                    ->group(base_path('routes/v1_test.php'));
            }

            // 加载管理后台API V1路由 - 标准化RESTful API，移除认证以解决前端认证问题
            if (file_exists(base_path('routes/admin_api_v1.php'))) {
                Route::prefix('api/admin/v1')
                    ->group(base_path('routes/admin_api_v1.php'));
            }

            // 加载安装预约API路由 - 移除web中间件以修复500错误
            if (file_exists(base_path('routes/api/admin/installation-bookings.php'))) {
                Route::group([], base_path('routes/api/admin/installation-bookings.php'));
            }

            // 加载安装统计API路由 - 移除web中间件以修复500错误
            if (file_exists(base_path('routes/api/admin/installation-statistics.php'))) {
                Route::group([], base_path('routes/api/admin/installation-statistics.php'));
            }

            // 加载简化版API路由 - 解决PHP版本兼容性问题
            if (file_exists(base_path('routes/simple_api.php'))) {
                Route::middleware('api')
                     ->namespace($this->namespace)
                     ->group(base_path('routes/simple_api.php'));
            }

            // 设置全局URL前缀
            if (env('APP_URL')) {
                $parsed = parse_url(env('APP_URL'));
                if (isset($parsed['path'])) {
                    URL::forceRootUrl(env('APP_URL'));
                    URL::forceScheme($parsed['scheme'] ?? 'https');
                }
            }
        });
    }

    /**
     * Configure the rate limiters for the application.
     */
    protected function configureRateLimiting()
    {
        RateLimiter::for('api', function (Request $request) {
            return Limit::perMinute(60)->by($request->user()?->id ?: $request->ip());
        });
    }
}
