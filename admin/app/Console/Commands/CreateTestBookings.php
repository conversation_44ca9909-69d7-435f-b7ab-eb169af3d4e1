<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

class CreateTestBookings extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'booking:create-test';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Create test installation bookings with completed status';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Creating test installation bookings...');

        // 获取所有工程师
        $engineers = \DB::table('installation_engineers')->get();

        if ($engineers->isEmpty()) {
            $this->error('No engineers found in database!');
            return 1;
        }

        $this->info('Found ' . $engineers->count() . ' engineers');

        // 创建测试数据
        $count = 0;
        $bookingsPerEngineer = 3; // 每个工程师创建的预约数量

        foreach ($engineers as $engineer) {
            // 只为活跃状态的工程师创建预约
            if ($engineer->status !== 'active') {
                continue;
            }

            $this->info("Creating bookings for engineer: {$engineer->name} (ID: {$engineer->id})");

            for ($i = 0; $i < $bookingsPerEngineer; $i++) {
                $bookingNo = 'TEST-' . date('YmdHis') . '-' . $engineer->id . '-' . $i;

                $bookingId = \DB::table('install_bookings')->insertGetId([
                    'booking_no' => $bookingNo,
                    'user_id' => 1, // 测试用户ID
                    'contact_name' => '测试用户' . $i,
                    'contact_phone' => '1388888' . str_pad($i, 4, '0', STR_PAD_LEFT),
                    'address' => '测试地址' . $i,
                    'booking_date' => date('Y-m-d', strtotime('-' . ($i + 1) . ' days')),
                    'booking_time' => '09:00-12:00',
                    'package_type' => 'basic',
                    'status' => 'completed',
                    'engineer_id' => $engineer->id,
                    'completed_at' => date('Y-m-d H:i:s', strtotime('-' . $i . ' days')),
                    'created_at' => date('Y-m-d H:i:s', strtotime('-' . ($i + 7) . ' days')),
                    'updated_at' => date('Y-m-d H:i:s', strtotime('-' . $i . ' days'))
                ]);

                $this->line(" - Created booking ID: {$bookingId}, No: {$bookingNo}");
                $count++;
            }

            // 更新工程师的完成安装数量
            \DB::table('installation_engineers')
                ->where('id', $engineer->id)
                ->update(['completed_installations' => $bookingsPerEngineer]);
        }

        $this->info("\nCreated {$count} test bookings successfully!");

        // 运行更新工程师安装数量的命令
        $this->call('engineer:update-installation-count');

        return 0;
    }
}
