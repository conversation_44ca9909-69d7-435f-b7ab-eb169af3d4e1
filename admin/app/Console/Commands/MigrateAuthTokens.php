<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use App\Models\AppUser;
use Laravel\Sanctum\PersonalAccessToken;
use Carbon\Carbon;

class MigrateAuthTokens extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'auth:migrate-tokens';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '将旧的auth_tokens表中的令牌迁移到Sanctum的personal_access_tokens表';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('开始迁移认证令牌...');

        // 获取所有旧的令牌
        $oldTokens = DB::table('auth_tokens')
            ->whereNull('expires_at')
            ->orWhere('expires_at', '>', Carbon::now())
            ->get();

        $this->info("找到 {$oldTokens->count()} 个有效的旧令牌");

        $migrated = 0;
        $errors = 0;

        foreach ($oldTokens as $oldToken) {
            try {
                // 查找用户
                $user = AppUser::find($oldToken->user_id);

                if (!$user) {
                    $this->warn("用户ID {$oldToken->user_id} 不存在，跳过令牌 {$oldToken->id}");
                    continue;
                }

                // 检查令牌是否已经存在
                $existingToken = PersonalAccessToken::where('token', hash('sha256', $oldToken->token))->first();

                if ($existingToken) {
                    $this->info("令牌 {$oldToken->id} 已经存在于Sanctum中，跳过");
                    continue;
                }

                // 创建新的Sanctum令牌
                $token = new PersonalAccessToken();
                $token->tokenable_type = get_class($user);
                $token->tokenable_id = $user->id;
                $token->name = 'auth_token';
                $token->token = hash('sha256', $oldToken->token);
                $token->abilities = '["*"]'; // 所有权限
                $token->last_used_at = $oldToken->last_used_at;
                $token->expires_at = $oldToken->expires_at;
                $token->created_at = $oldToken->created_at;
                $token->updated_at = $oldToken->updated_at;
                $token->save();

                $this->info("成功迁移令牌 {$oldToken->id} 到Sanctum");
                $migrated++;
            } catch (\Exception $e) {
                $this->error("迁移令牌 {$oldToken->id} 时出错: " . $e->getMessage());
                $errors++;
            }
        }

        $this->info("迁移完成: 成功 {$migrated}, 失败 {$errors}");
        return 0;
    }
}
