<?php

namespace App\Console\Commands;

use App\Services\VipDividendService;
use Carbon\Carbon;
use Illuminate\Console\Command;

class CalculateVipDividends extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'vip:calculate-dividends {period? : 分红周期，格式YYYY-MM，默认上个月} {--force : 强制重新计算，即使已经计算过}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '计算VIP会员分红';

    /**
     * VIP分红服务
     *
     * @var VipDividendService
     */
    protected $dividendService;

    /**
     * Create a new command instance.
     *
     * @param VipDividendService $dividendService
     * @return void
     */
    public function __construct(VipDividendService $dividendService)
    {
        parent::__construct();
        $this->dividendService = $dividendService;
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $period = $this->argument('period');
        if (!$period) {
            $period = Carbon::now()->subMonth()->format('Y-m');
            $this->info("未指定周期，默认计算 {$period} 的分红");
        }

        // 验证周期格式
        if (!preg_match('/^\d{4}-\d{2}$/', $period)) {
            $this->error("周期格式不正确，应为YYYY-MM，如2023-12");
            return 1;
        }

        // 检查周期是否为未来时间
        $periodDate = Carbon::createFromFormat('Y-m', $period)->startOfMonth();
        $currentDate = Carbon::now()->startOfMonth();
        
        if ($periodDate->gt($currentDate)) {
            $this->warn("警告：您正在尝试计算未来周期 {$period} 的分红，这可能导致数据不准确。");
            if (!$this->confirm('是否继续?', false)) {
                $this->info('已取消计算');
                return 0;
            }
        }

        $this->info("开始计算 {$period} 周期的VIP分红...");

        try {
            // 检查是否已计算过
            $existingDividends = \App\Models\VipDividend::where('period', $period)
                ->where('amount', '>', 0)
                ->exists();
                
            if ($existingDividends && !$this->option('force')) {
                $this->warn("{$period} 周期已经计算过分红记录。");
                if (!$this->confirm('是否重新计算?', false)) {
                    $this->info('已取消计算');
                    return 0;
                }
            }
            
            $result = $this->dividendService->calculateMonthlyDividends($period);

            if ($result['status'] === 'skipped') {
                if ($this->option('force')) {
                    $this->warn("{$period} 周期已经计算过分红，正在尝试强制重新计算...");
                    
                    // 删除已有记录
                    $count = \App\Models\VipDividend::where('period', $period)->delete();
                    $this->info("已删除 {$count} 条已有分红记录，准备重新计算。");
                    
                    // 重新计算
                    $result = $this->dividendService->calculateMonthlyDividends($period);
                } else {
                    $this->warn("{$period} 周期已经计算过分红，跳过计算。使用 --force 参数可以强制重新计算。");
                    return 0;
                }
            }

            if ($result['status'] === 'success') {
                $this->info("分红计算成功!");
                $this->table(
                    ['统计项', '数值'],
                    [
                        ['周期', $period],
                        ['新增VIP人数', $result['statistics']['new_vip_count'] ?? 0],
                        ['VIP奖金池', $result['statistics']['vip_pool_amount'] ?? 0],
                        ['新增充值数', $result['statistics']['new_recharge_count'] ?? 0],
                        ['充值奖金池', $result['statistics']['recharge_pool_amount'] ?? 0],
                    ]
                );

                // 显示分红结果
                $this->info("VIP招募分红:");
                $this->table(
                    ['等级', '分红人数', '分红总额'],
                    [
                        ['初级分红', $result['vip_dividends']['primary']['count'] ?? 0, $result['vip_dividends']['primary']['amount'] ?? 0],
                        ['中级分红', $result['vip_dividends']['middle']['count'] ?? 0, $result['vip_dividends']['middle']['amount'] ?? 0],
                        ['高级分红', $result['vip_dividends']['high']['count'] ?? 0, $result['vip_dividends']['high']['amount'] ?? 0],
                    ]
                );

                $this->info("充值分红:");
                $this->table(
                    ['等级', '分红人数', '分红总额'],
                    [
                        ['初级分红', $result['recharge_dividends']['primary']['count'] ?? 0, $result['recharge_dividends']['primary']['amount'] ?? 0],
                        ['中级分红', $result['recharge_dividends']['middle']['count'] ?? 0, $result['recharge_dividends']['middle']['amount'] ?? 0],
                        ['高级分红', $result['recharge_dividends']['high']['count'] ?? 0, $result['recharge_dividends']['high']['amount'] ?? 0],
                    ]
                );

                return 0;
            } else {
                $this->error("分红计算失败: " . ($result['message'] ?? '未知错误'));
                if (isset($result['error_details'])) {
                    $this->error("错误详情: " . $result['error_details']);
                }
                return 1;
            }
        } catch (\Exception $e) {
            $this->error("计算分红过程中发生错误: " . $e->getMessage());
            $this->newLine();
            $this->error("错误堆栈跟踪:");
            $this->error($e->getTraceAsString());
            $this->newLine();
            $this->warn("请检查日志文件获取更多详细信息。");
            return 1;
        }
    }
} 