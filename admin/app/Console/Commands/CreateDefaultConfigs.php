<?php

namespace App\Console\Commands;

use App\Models\SystemConfig;
use Illuminate\Console\Command;

class CreateDefaultConfigs extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:create-default-configs';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '创建默认系统配置';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('开始创建默认配置...');
        
        // 创建微信配置
        $this->createWechatConfig();
        $this->info('微信配置创建完成');
        
        // 创建短信配置
        $this->createSmsConfig();
        $this->info('短信配置创建完成');
        
        // 创建导航配置
        $this->createNavConfig();
        $this->info('导航配置创建完成');
        
        $this->info('所有默认配置创建完成!');
        
        return Command::SUCCESS;
    }
    
    /**
     * 创建微信配置
     */
    private function createWechatConfig()
    {
        $defaultConfigs = [
            [
                'module' => 'wechat',
                'key' => 'enabled',
                'value' => '0',
                'title' => '启用微信登录',
                'description' => '是否启用微信登录功能',
                'type' => 'switch',
                'options' => null,
                'is_system' => true,
                'sort' => 10,
            ],
            [
                'module' => 'wechat',
                'key' => 'app_id',
                'value' => '',
                'title' => 'AppID',
                'description' => '微信公众平台AppID',
                'type' => 'text',
                'options' => null,
                'is_system' => true,
                'sort' => 20,
            ],
            [
                'module' => 'wechat',
                'key' => 'app_secret',
                'value' => '',
                'title' => 'AppSecret',
                'description' => '微信公众平台AppSecret',
                'type' => 'text',
                'options' => null,
                'is_system' => true,
                'sort' => 30,
            ],
            [
                'module' => 'wechat',
                'key' => 'oauth_callback_url',
                'value' => '/api/auth/wechat/callback',
                'title' => '授权回调地址',
                'description' => '微信授权回调地址',
                'type' => 'text',
                'options' => null,
                'is_system' => true,
                'sort' => 40,
            ]
        ];
        
        foreach ($defaultConfigs as $config) {
            SystemConfig::updateOrCreate(
                ['module' => $config['module'], 'key' => $config['key']],
                $config
            );
        }
    }
    
    /**
     * 创建短信配置
     */
    private function createSmsConfig()
    {
        $defaultConfigs = [
            [
                'module' => 'sms',
                'key' => 'provider',
                'value' => 'aliyun',
                'title' => '短信提供商',
                'description' => '短信服务提供商',
                'type' => 'select',
                'options' => json_encode(['aliyun' => '阿里云', 'tencent' => '腾讯云']),
                'is_system' => true,
                'sort' => 10,
            ],
            [
                'module' => 'sms',
                'key' => 'access_key_id',
                'value' => '',
                'title' => 'AccessKeyId',
                'description' => '短信服务AccessKeyId',
                'type' => 'text',
                'options' => null,
                'is_system' => true,
                'sort' => 20,
            ],
            [
                'module' => 'sms',
                'key' => 'access_key_secret',
                'value' => '',
                'title' => 'AccessKeySecret',
                'description' => '短信服务AccessKeySecret',
                'type' => 'text',
                'options' => null,
                'is_system' => true,
                'sort' => 30,
            ],
            [
                'module' => 'sms',
                'key' => 'sign_name',
                'value' => '',
                'title' => '短信签名',
                'description' => '短信签名名称',
                'type' => 'text',
                'options' => null,
                'is_system' => true,
                'sort' => 40,
            ],
            [
                'module' => 'sms',
                'key' => 'template_code',
                'value' => '',
                'title' => '验证码模板',
                'description' => '验证码短信模板代码',
                'type' => 'text',
                'options' => null,
                'is_system' => true,
                'sort' => 50,
            ],
            [
                'module' => 'sms',
                'key' => 'enable_test_code',
                'value' => '0',
                'title' => '启用测试验证码',
                'description' => '是否启用测试验证码(123456)',
                'type' => 'switch',
                'options' => null,
                'is_system' => true,
                'sort' => 60,
            ]
        ];
        
        foreach ($defaultConfigs as $config) {
            SystemConfig::updateOrCreate(
                ['module' => $config['module'], 'key' => $config['key']],
                $config
            );
        }
    }
    
    /**
     * 创建导航配置
     */
    private function createNavConfig()
    {
        $defaultConfigs = [
            [
                'module' => 'nav',
                'key' => 'show_sidebar',
                'value' => '1',
                'title' => '显示侧边栏',
                'description' => '控制是否显示侧边导航栏',
                'type' => 'switch',
                'options' => null,
                'is_system' => true,
                'sort' => 10,
            ],
            [
                'module' => 'nav',
                'key' => 'collapse_sidebar',
                'value' => '0',
                'title' => '折叠侧边栏',
                'description' => '控制侧边栏默认是否折叠',
                'type' => 'switch',
                'options' => null,
                'is_system' => true,
                'sort' => 20,
            ],
            [
                'module' => 'nav',
                'key' => 'show_breadcrumb',
                'value' => '1',
                'title' => '显示面包屑',
                'description' => '控制是否显示页面顶部面包屑导航',
                'type' => 'switch',
                'options' => null,
                'is_system' => true,
                'sort' => 30,
            ],
            [
                'module' => 'nav',
                'key' => 'fixed_header',
                'value' => '1',
                'title' => '固定头部',
                'description' => '控制页面头部是否固定',
                'type' => 'switch',
                'options' => null,
                'is_system' => true,
                'sort' => 40,
            ]
        ];
        
        foreach ($defaultConfigs as $config) {
            SystemConfig::updateOrCreate(
                ['module' => $config['module'], 'key' => $config['key']],
                $config
            );
        }
    }
}