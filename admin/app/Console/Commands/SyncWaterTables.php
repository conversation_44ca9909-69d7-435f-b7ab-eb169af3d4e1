<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Schema;

class SyncWaterTables extends Command
{
    /**
     * 命令名称
     *
     * @var string
     */
    protected $signature = 'water:sync {--force : 强制执行同步}';

    /**
     * 命令描述
     *
     * @var string
     */
    protected $description = '将水系统数据库的数据同步到主数据库中';

    /**
     * 执行命令
     */
    public function handle()
    {
        $this->info('水系统数据同步已禁用');
        $this->info('系统将直接从水系统数据库读取数据，无需同步');
        
        if ($this->option('force')) {
            $this->warn('警告：--force 参数已被忽略，同步功能已禁用');
        }
        
        $this->info('如需重新启用同步，请联系开发人员修改代码');
        return 0;
    }
    
    /**
     * 同步表数据
     * 
     * @param string $sourceTable 源表名称
     * @param string $targetTable 目标表名称
     * @return void
     */
    protected function syncTable($sourceTable, $targetTable)
    {
        try {
            $this->info("开始同步表 {$sourceTable} 到 {$targetTable}...");
            
            // 检查源表是否存在
            if (!Schema::connection('water_db')->hasTable($sourceTable)) {
                $this->warn("源表 {$sourceTable} 不存在，跳过同步");
                return;
            }
            
            // 检查目标表是否存在
            if (!Schema::connection('mysql')->hasTable($targetTable)) {
                $this->error("目标表 {$targetTable} 不存在，跳过同步");
                return;
            }
            
            // 在目标表中清除旧数据，如果--force参数未指定则提示确认
            if (!$this->option('force')) {
                if (!$this->confirm("将清空目标表 {$targetTable} 中的数据并重新插入，是否继续?")) {
                    $this->info("已跳过 {$targetTable} 表的同步");
                    return;
                }
            }
            
            // 清空目标表
            DB::connection('mysql')->table($targetTable)->truncate();
            $this->info("已清空目标表 {$targetTable}");
            
            // 分批获取源表数据，每次1000条
            $offset = 0;
            $limit = 1000;
            $total = 0;
            
            do {
                $records = DB::connection('water_db')
                    ->table($sourceTable)
                    ->offset($offset)
                    ->limit($limit)
                    ->get();
                
                $count = count($records);
                
                if ($count > 0) {
                    // 转换记录为数组
                    $data = json_decode(json_encode($records), true);
                    
                    // 插入数据到目标表
                    DB::connection('mysql')
                        ->table($targetTable)
                        ->insert($data);
                    
                    $total += $count;
                    $this->info("已同步 {$count} 条记录，总计: {$total}");
                }
                
                $offset += $limit;
            } while ($count > 0);
            
            $this->info("表 {$sourceTable} 同步完成，共同步 {$total} 条记录");
            
        } catch (\Exception $e) {
            $this->error("同步表 {$sourceTable} 到 {$targetTable} 失败: " . $e->getMessage());
            Log::error("同步表 {$sourceTable} 到 {$targetTable} 失败", [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }
} 