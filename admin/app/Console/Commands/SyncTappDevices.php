<?php

namespace App\Console\Commands;

use App\Models\TappDevice;
use App\Models\WaterDevice;
use App\Models\WaterClient;
use App\Models\WaterDealer;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class SyncTappDevices extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'tapp:sync-devices {--force : 强制同步所有数据}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '同步点点够设备数据从jzq_water_plat到本地数据库';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('开始同步点点够设备数据...');
        
        $startTime = microtime(true);
        $forceSync = $this->option('force');
        
        try {
            // 获取最后同步时间
            $lastSyncTime = null;
            if (!$forceSync) {
                $lastSyncTime = TappDevice::max('last_sync_time');
                if ($lastSyncTime) {
                    $this->info("使用最后同步时间: {$lastSyncTime}");
                } else {
                    $this->info("未找到最后同步时间，将同步所有数据");
                }
            } else {
                $this->info("强制同步所有数据");
            }
            
            // 构建查询条件
            $query = WaterDevice::with(['client', 'saleDealer'])
                ->whereHas('saleDealer', function($q) {
                    $q->where('dealer_number', '00000019')
                      ->orWhere('dealer_name', '陈来意');
                });
            
            // 如果有最后同步时间且不是强制同步，则只同步新的数据
            if ($lastSyncTime && !$forceSync) {
                $query->where(function($q) use ($lastSyncTime) {
                    $q->where('update_date', '>', $lastSyncTime)
                      ->orWhere('create_date', '>', $lastSyncTime);
                });
            }
            
            // 获取需要同步的设备数量
            $totalDevices = $query->count();
            $this->info("找到 {$totalDevices} 条需要同步的设备数据");
            
            if ($totalDevices == 0) {
                $this->info("没有需要同步的新数据");
                return Command::SUCCESS;
            }
            
            // 设置进度条
            $bar = $this->output->createProgressBar($totalDevices);
            $bar->start();
            
            // 分批处理数据，避免内存溢出
            $processedCount = 0;
            $successCount = 0;
            $errorCount = 0;
            $chunkSize = 100;
            
            $query->chunk($chunkSize, function($devices) use (&$processedCount, &$successCount, &$errorCount, $bar) {
                foreach ($devices as $device) {
                    try {
                        $this->syncDevice($device);
                        $successCount++;
                    } catch (\Exception $e) {
                        $errorCount++;
                        Log::error("同步设备 {$device->device_number} 失败: " . $e->getMessage());
                        $this->error("同步设备 {$device->device_number} 失败: " . $e->getMessage());
                    }
                    
                    $processedCount++;
                    $bar->advance();
                }
            });
            
            $bar->finish();
            $this->newLine();
            
            // 更新同步时间
            $currentTime = Carbon::now();
            
            $endTime = microtime(true);
            $executionTime = round($endTime - $startTime, 2);
            
            $this->info("同步完成! 耗时: {$executionTime} 秒");
            $this->info("成功: {$successCount}, 失败: {$errorCount}, 总计: {$processedCount}");
            
            return Command::SUCCESS;
        } catch (\Exception $e) {
            $this->error("同步过程中发生错误: " . $e->getMessage());
            Log::error("同步点点够设备数据失败: " . $e->getMessage());
            
            return Command::FAILURE;
        }
    }
    
    /**
     * 同步单个设备数据
     */
    private function syncDevice(WaterDevice $waterDevice)
    {
        // 提取客户信息
        $clientName = null;
        $clientPhone = null;
        $clientAddress = null;
        
        if ($waterDevice->client) {
            $clientName = $waterDevice->client->name;
            $clientPhone = $waterDevice->client->phone;
            $clientAddress = $waterDevice->client->full_address ?? $waterDevice->client->address;
        }
        
        // 提取渠道商信息
        $dealerNumber = null;
        $dealerName = null;
        
        if ($waterDevice->saleDealer) {
            $dealerNumber = $waterDevice->saleDealer->dealer_number;
            $dealerName = $waterDevice->saleDealer->dealer_name;
        }
        
        // 查找现有设备记录，以保留特定字段的值
        $existingDevice = TappDevice::where('device_id', $waterDevice->id)->first();
        
        // 准备数据
        $deviceData = [
            'device_id' => $waterDevice->id,
            'device_number' => $waterDevice->device_number,
            'device_type' => $waterDevice->device_type,
            'device_status' => $waterDevice->device_status,
            'raw_water_value' => $waterDevice->raw_water_value,
            'purification_water_value' => $waterDevice->purification_water_value,
            'billing_mode' => $waterDevice->billing_mode,
            'surplus_flow' => $waterDevice->surplus_flow,
            'remaining_days' => $waterDevice->remaining_days,
            'cumulative_filtration_flow' => $waterDevice->cumulative_filtration_flow,
            'water_quality_grade' => $waterDevice->water_quality_grade,
            'network_status' => $waterDevice->network_status,
            'dealer_id' => $waterDevice->dealer_id,
            'dealer_id_sale' => $waterDevice->dealer_id_sale,
            'client_id' => $waterDevice->client_id,
            'activate_date' => $waterDevice->activate_date,
            'iccid' => $waterDevice->iccid,
            'imei' => $waterDevice->imei,
            'status' => $waterDevice->status,
            'remark' => $waterDevice->remark,
            'create_date' => $waterDevice->create_date,
            'update_date' => $waterDevice->update_date,
            'longitude' => $waterDevice->longitude,
            'latitude' => $waterDevice->latitude,
            'address' => $waterDevice->address,
            'f1_flux' => $waterDevice->f1_flux,
            'f1_flux_max' => $waterDevice->f1_flux_max,
            'f2_flux' => $waterDevice->f2_flux,
            'f2_flux_max' => $waterDevice->f2_flux_max,
            'f3_flux' => $waterDevice->f3_flux,
            'f3_flux_max' => $waterDevice->f3_flux_max,
            'f4_flux' => $waterDevice->f4_flux,
            'f4_flux_max' => $waterDevice->f4_flux_max,
            'f5_flux' => $waterDevice->f5_flux,
            'f5_flux_max' => $waterDevice->f5_flux_max,
            'filter_date' => $waterDevice->filter_date,
            'signal_intensity' => $waterDevice->signal_intensity,
            'service_end_time' => $waterDevice->service_end_time,
            'cod_after' => $waterDevice->cod_after,
            'cod_before' => $waterDevice->cod_before,
            'toc_after' => $waterDevice->toc_after,
            'toc_before' => $waterDevice->toc_before,
            'client_name' => $clientName,
            'client_phone' => $clientPhone,
            'client_address' => $clientAddress,
            'dealer_number' => $dealerNumber,
            'dealer_name' => $dealerName,
            'last_sync_time' => now(),
            'is_sync_success' => true,
            'is_self_use' => $existingDevice ? $existingDevice->is_self_use : 0,
        ];
        
        // 对于已存在的设备，保留app_user关联关系
        if ($existingDevice) {
            // 保留app_user关联
            if ($existingDevice->app_user_id) {
                $deviceData['app_user_id'] = $existingDevice->app_user_id;
                $deviceData['app_user_name'] = $existingDevice->app_user_name;
            }
        }
        
        // 更新或创建记录
        TappDevice::updateOrCreate(
            ['device_id' => $waterDevice->id],
            $deviceData
        );
    }
}
