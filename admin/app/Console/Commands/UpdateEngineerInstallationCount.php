<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

class UpdateEngineerInstallationCount extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'engineer:update-installation-count';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update installation count for all engineers';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Updating installation count for all engineers...');

        // 获取所有工程师
        $engineers = \DB::table('installation_engineers')->get();

        $updateCount = 0;
        $errorCount = 0;

        foreach ($engineers as $engineer) {
            try {
                // 获取工程师完成的安装数量
                $completedInstallations = \DB::table('install_bookings')
                    ->where('engineer_id', $engineer->id)
                    ->where('status', 'completed')
                    ->count();

                // 输出计算结果
                $this->line("Engineer ID {$engineer->id} ({$engineer->name}): {$completedInstallations} completed installations");

                // 更新工程师安装数量
                \DB::table('installation_engineers')
                    ->where('id', $engineer->id)
                    ->update(['completed_installations' => $completedInstallations]);

                $updateCount++;
            } catch (\Exception $e) {
                $this->error("Failed to update engineer ID {$engineer->id}: {$e->getMessage()}");
                $errorCount++;
            }
        }

        // 输出统计信息
        $this->info("\nUpdate completed!");
        $this->info("Total engineers: " . count($engineers));
        $this->info("Successfully updated: {$updateCount}");
        $this->info("Failed: {$errorCount}");

        // 检查安装预约表中的完成数量
        $totalCompletedBookings = \DB::table('install_bookings')
            ->where('status', 'completed')
            ->count();

        $this->info("Total completed bookings in database: {$totalCompletedBookings}");

        return 0;
    }
}
