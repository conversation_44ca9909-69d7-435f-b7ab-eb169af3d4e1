<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Schema;

class InitRolesPermissions extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'admin:init-roles-permissions';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '初始化管理员角色和权限系统';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('开始初始化管理员角色和权限系统...');

        // 检查表是否存在
        $tablesExist = Schema::hasTable('admin_roles') && 
                       Schema::hasTable('admin_permissions') && 
                       Schema::hasTable('admin_role_permissions') && 
                       Schema::hasTable('admin_user_roles') && 
                       Schema::hasTable('admin_user_permissions');

        if (!$tablesExist) {
            $this->info('表不存在，运行迁移创建表...');
            Artisan::call('migrate', ['--path' => 'database/migrations/2025_03_19_000001_create_admin_roles_and_permissions_tables.php']);
            $this->info(Artisan::output());
        } else {
            $this->info('表已存在，跳过迁移步骤');
        }

        // 运行填充器
        $this->info('填充角色和权限数据...');
        Artisan::call('db:seed', ['--class' => 'Database\\Seeders\\AdminRolesPermissionsSeeder']);
        $this->info(Artisan::output());

        $this->info('管理员角色和权限系统初始化完成！');

        return Command::SUCCESS;
    }
}
