<?php

namespace App\Console\Commands;

use App\Services\VipDividendService;
use Carbon\Carbon;
use Illuminate\Console\Command;

class SettleVipDividends extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'vip:settle-dividends {period? : 分红周期，格式YYYY-MM，默认上个月} {--dry-run : 仅显示将要结算的分红，不实际结算}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '结算VIP会员分红到用户余额';

    /**
     * VIP分红服务
     *
     * @var VipDividendService
     */
    protected $dividendService;

    /**
     * Create a new command instance.
     *
     * @param VipDividendService $dividendService
     * @return void
     */
    public function __construct(VipDividendService $dividendService)
    {
        parent::__construct();
        $this->dividendService = $dividendService;
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $period = $this->argument('period');
        if (!$period) {
            $period = Carbon::now()->subMonth()->format('Y-m');
            $this->info("未指定周期，默认结算 {$period} 的分红");
        }

        // 验证周期格式
        if (!preg_match('/^\d{4}-\d{2}$/', $period)) {
            $this->error("周期格式不正确，应为YYYY-MM，如2023-12");
            return 1;
        }

        $this->info("开始结算 {$period} 周期的VIP分红...");

        try {
            // 如果是dry-run模式，显示将要结算的分红
            if ($this->option('dry-run')) {
                $this->warn("干运行模式：以下分红将被结算，但实际上不会执行结算操作");
                
                // 查询待结算的分红记录
                $pendingDividends = \App\Models\VipDividend::where('period', $period)
                    ->where('status', 'pending')
                    ->get();
                
                if ($pendingDividends->isEmpty()) {
                    $this->warn("周期 {$period} 没有待结算的分红记录");
                    return 0;
                }
                
                $rows = [];
                $totalAmount = 0;
                
                foreach ($pendingDividends as $dividend) {
                    $user = \App\Models\AppUser::find($dividend->user_id);
                    $userName = $user ? ($user->name ?: $user->phone) : '未知用户';
                    
                    $rows[] = [
                        $dividend->id,
                        $dividend->user_id,
                        $userName,
                        $dividend->type === 'vip' ? 'VIP招募分红' : '充值分红',
                        $dividend->getLevelTextAttribute(),
                        $dividend->amount
                    ];
                    
                    $totalAmount += $dividend->amount;
                }
                
                $this->table(
                    ['ID', '用户ID', '用户名', '分红类型', '分红等级', '金额'],
                    $rows
                );
                
                $this->info("总计：{$pendingDividends->count()}条记录，总金额：{$totalAmount}元");
                $this->warn("以上分红记录将被结算到用户余额，使用命令 php artisan vip:settle-dividends {$period} 执行真实结算");
                
                return 0;
            }
            
            // 执行实际结算
            $result = $this->dividendService->settleDividends($period);
            
            if ($result['status'] === 'skipped') {
                $this->warn("周期 {$period} 没有待结算的分红记录");
                return 0;
            }
            
            if ($result['status'] === 'success') {
                $this->info("分红结算成功!");
                $this->table(
                    ['统计项', '数值'],
                    [
                        ['周期', $period],
                        ['成功结算记录数', $result['success_count']],
                        ['失败记录数', $result['error_count']],
                        ['总结算金额', $result['total_amount']],
                    ]
                );
                
                return 0;
            } else {
                $this->error("分红结算失败: " . $result['message']);
                return 1;
            }
        } catch (\Exception $e) {
            $this->error("结算分红过程中发生错误: " . $e->getMessage());
            $this->error($e->getTraceAsString());
            return 1;
        }
    }
} 