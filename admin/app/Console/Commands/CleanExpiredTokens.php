<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class CleanExpiredTokens extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'tokens:clean-expired {--dry-run : Show what would be deleted without actually deleting}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clean up expired personal access tokens';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('开始清理过期的访问令牌...');

        // 查询过期的token
        $expiredTokens = DB::table('personal_access_tokens')
            ->where('expires_at', '<', Carbon::now())
            ->whereNotNull('expires_at')
            ->get();

        $count = $expiredTokens->count();

        if ($count === 0) {
            $this->info('没有找到过期的令牌。');
            return Command::SUCCESS;
        }

        $this->info("找到 {$count} 个过期的令牌:");

        // 显示过期token的详细信息
        $this->table(
            ['ID', 'Name', 'Tokenable Type', 'Tokenable ID', 'Expires At', 'Days Expired'],
            $expiredTokens->map(function ($token) {
                $expiresAt = Carbon::parse($token->expires_at);
                $daysExpired = $expiresAt->diffInDays(Carbon::now());
                return [
                    $token->id,
                    $token->name,
                    $token->tokenable_type,
                    $token->tokenable_id,
                    $expiresAt->format('Y-m-d H:i:s'),
                    $daysExpired . ' 天前'
                ];
            })->toArray()
        );

        if ($this->option('dry-run')) {
            $this->warn('这是预览模式，没有实际删除任何令牌。');
            $this->info('要实际删除这些令牌，请运行: php artisan tokens:clean-expired');
            return Command::SUCCESS;
        }

        if ($this->confirm('确定要删除这些过期的令牌吗？')) {
            $deletedCount = DB::table('personal_access_tokens')
                ->where('expires_at', '<', Carbon::now())
                ->whereNotNull('expires_at')
                ->delete();

            $this->info("成功删除了 {$deletedCount} 个过期的令牌。");
        } else {
            $this->info('操作已取消。');
        }

        return Command::SUCCESS;
    }
}