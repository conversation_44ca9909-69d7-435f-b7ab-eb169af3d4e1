<?php

namespace App\Console\Commands;

use App\Models\AppUser;
use App\Models\Salesman;
use App\Services\UserRoleSyncService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class SyncAppUsersToSalesmen extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:sync-users-to-salesmen {--user_id= : 指定同步单个用户} {--force : 强制同步所有用户，包括已有业务员记录的用户}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '将app_users表中的用户同步为业务员';

    /**
     * 同步服务类实例
     */
    protected $syncService;

    /**
     * 构造函数
     */
    public function __construct(UserRoleSyncService $syncService)
    {
        parent::__construct();
        $this->syncService = $syncService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('开始同步APP用户到业务员...');
        
        $startTime = microtime(true);
        $userId = $this->option('user_id');
        $force = $this->option('force');
        
        if ($userId) {
            // 同步单个用户
            $this->syncSingleUser($userId, $force);
        } else {
            // 同步所有用户
            $this->syncAllUsers($force);
        }
        
        $endTime = microtime(true);
        $executionTime = round($endTime - $startTime, 2);
        
        $this->info("同步完成! 耗时: {$executionTime} 秒");
        
        return Command::SUCCESS;
    }
    
    /**
     * 同步单个用户
     *
     * @param int $userId 用户ID
     * @param bool $force 是否强制同步
     */
    protected function syncSingleUser($userId, $force = false)
    {
        $user = AppUser::find($userId);
        
        if (!$user) {
            $this->error("用户ID {$userId} 不存在");
            return;
        }
        
        $this->info("开始同步用户: ID:{$user->id}, 姓名:{$user->name}, 手机:{$user->phone}");
        
        // 检查用户是否已经有业务员记录
        $existingSalesman = Salesman::where('user_id', $user->id)->first();
        
        if ($existingSalesman && !$force) {
            $this->info("用户 ID: {$user->id}, 姓名: {$user->name} 已经是业务员，跳过");
            
            // 确保用户标记为业务员
            if (!$user->is_salesman) {
                $user->is_salesman = 1;
                $user->save();
                $this->info("已更新用户 ID: {$user->id} 的业务员标记");
            }
            return;
        }
        
        try {
            // 使用同步服务创建业务员
            $result = $this->syncService->syncSalesman($user);
            
            if ($result['success']) {
                $this->info("同步结果: " . ($result['action'] === 'created' ? '创建成功' : '更新成功'));
            } else {
                $this->warn("同步失败: " . ($result['message'] ?? '未知错误'));
            }
        } catch (\Exception $e) {
            $this->error("处理用户 ID: {$user->id} 时出错 - " . $e->getMessage());
            Log::error("同步用户 {$user->id} 到业务员时出错", [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }
    
    /**
     * 同步所有用户
     *
     * @param bool $force 是否强制同步
     */
    protected function syncAllUsers($force = false)
    {
        // 获取所有APP用户，只处理is_salesman=1的用户
        $query = AppUser::query()->where('status', 'active');
        $query->where('is_salesman', 1);
        
        $totalUsers = $query->count();
        $this->info("找到 {$totalUsers} 个活跃APP用户需要同步");
        
        if ($totalUsers === 0) {
            $this->warn("没有找到需要同步的用户");
            return;
        }
        
        $newSalesmen = 0;
        $existingSalesmen = 0;
        $errors = 0;
        $processed = 0;
        
        // 使用分批处理以减少内存使用
        $query->chunk(100, function ($users) use (&$newSalesmen, &$existingSalesmen, &$errors, &$processed, $force, $totalUsers) {
            foreach ($users as $user) {
                $processed++;
                $this->output->write("\r处理进度: {$processed}/{$totalUsers} (" . round(($processed / $totalUsers) * 100, 2) . "%)");
                
                try {
                    // 检查该用户是否已经有业务员记录
                    $existingSalesman = Salesman::where('user_id', $user->id)->first();
                    
                    if ($existingSalesman && !$force) {
                        $existingSalesmen++;
                        
                        // 确保用户标记为业务员
                        if (!$user->is_salesman) {
                            $user->is_salesman = 1;
                            $user->save();
                        }
                        continue;
                    }
                    
                    // 使用同步服务创建业务员
                    $result = $this->syncService->syncSalesman($user);
                    
                    if ($result['success']) {
                        if ($result['action'] === 'created') {
                            $newSalesmen++;
                        } else {
                            $existingSalesmen++;
                        }
                    } else {
                        $errors++;
                        Log::warning("同步用户 {$user->id} 到业务员时失败", [
                            'error' => $result['message'] ?? '未知错误'
                        ]);
                    }
                } catch (\Exception $e) {
                    $errors++;
                    Log::error("同步用户 {$user->id} 到业务员时出错", [
                        'error' => $e->getMessage(),
                        'trace' => $e->getTraceAsString()
                    ]);
                }
            }
        });
        
        $this->output->write("\n");
        $this->info("同步统计");
        $this->info("新创建业务员: {$newSalesmen}");
        $this->info("已存在业务员: {$existingSalesmen}");
        $this->info("处理错误: {$errors}");
        $this->info("总用户数: {$totalUsers}");
        
        // 记录到日志
        Log::info('APP用户同步到业务员完成', [
            'total' => $totalUsers,
            'new' => $newSalesmen,
            'existing' => $existingSalesmen,
            'errors' => $errors
        ]);
    }
} 