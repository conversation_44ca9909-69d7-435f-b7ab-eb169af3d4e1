<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class CreateDefaultConfig extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'config:create-default {module?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '创建默认配置数据';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $module = $this->argument('module');
        
        if ($module === 'sms' || $module === null) {
            $this->createSmsConfig();
        }
        
        if ($module === 'wechat' || $module === null) {
            $this->createWechatConfig();
        }
        
        $this->info('默认配置创建完成！');
        
        return 0;
    }
    
    /**
     * 创建短信配置
     */
    protected function createSmsConfig()
    {
        $configs = [
            [
                'module' => 'sms',
                'key' => 'provider',
                'value' => 'aliyun',
                'title' => '短信服务提供商',
                'description' => '选择短信服务提供商',
                'type' => 'select',
                'options' => json_encode(['aliyun' => '阿里云短信']),
                'is_system' => true,
                'sort' => 10
            ],
            [
                'module' => 'sms',
                'key' => 'access_key_id',
                'value' => '',
                'title' => 'AccessKey ID',
                'description' => '阿里云AccessKey ID',
                'type' => 'text',
                'options' => null,
                'is_system' => true,
                'sort' => 20
            ],
            [
                'module' => 'sms',
                'key' => 'access_key_secret',
                'value' => '',
                'title' => 'AccessKey Secret',
                'description' => '阿里云AccessKey Secret',
                'type' => 'text',
                'options' => null,
                'is_system' => true,
                'sort' => 30
            ],
            [
                'module' => 'sms',
                'key' => 'sign_name',
                'value' => '',
                'title' => '短信签名',
                'description' => '短信签名名称',
                'type' => 'text',
                'options' => null,
                'is_system' => true,
                'sort' => 40
            ],
            [
                'module' => 'sms',
                'key' => 'template_code',
                'value' => '',
                'title' => '验证码模板ID',
                'description' => '验证码短信模板ID',
                'type' => 'text',
                'options' => null,
                'is_system' => true,
                'sort' => 50
            ],
            [
                'module' => 'sms',
                'key' => 'template_param',
                'value' => 'code',
                'title' => '模板参数名',
                'description' => '验证码在模板中的参数名',
                'type' => 'text',
                'options' => null,
                'is_system' => true,
                'sort' => 60
            ],
            [
                'module' => 'sms',
                'key' => 'enable_test_code',
                'value' => '0',
                'title' => '启用测试模式',
                'description' => '启用后不会真实发送短信，仅用于测试',
                'type' => 'switch',
                'options' => null,
                'is_system' => true,
                'sort' => 5
            ]
        ];
        
        foreach ($configs as $config) {
            DB::table('system_configs')->updateOrInsert(
                ['module' => $config['module'], 'key' => $config['key']],
                array_merge($config, [
                    'created_at' => now(),
                    'updated_at' => now()
                ])
            );
            $this->info("SMS配置已添加/更新: {$config['key']}");
        }
    }
    
    /**
     * 创建微信配置
     */
    protected function createWechatConfig()
    {
        $configs = [
            [
                'module' => 'wechat',
                'key' => 'enabled',
                'value' => '0',
                'title' => '启用微信登录',
                'description' => '是否启用微信登录功能',
                'type' => 'switch',
                'options' => null,
                'is_system' => true,
                'sort' => 10
            ],
            [
                'module' => 'wechat',
                'key' => 'app_id',
                'value' => '',
                'title' => 'AppID',
                'description' => '微信公众平台AppID',
                'type' => 'text',
                'options' => null,
                'is_system' => true,
                'sort' => 20
            ],
            [
                'module' => 'wechat',
                'key' => 'app_secret',
                'value' => '',
                'title' => 'AppSecret',
                'description' => '微信公众平台AppSecret',
                'type' => 'text',
                'options' => null,
                'is_system' => true,
                'sort' => 30
            ],
            [
                'module' => 'wechat',
                'key' => 'oauth_callback_url',
                'value' => '/api/auth/wechat/callback',
                'title' => '授权回调地址',
                'description' => '微信授权回调地址',
                'type' => 'text',
                'options' => null,
                'is_system' => true,
                'sort' => 40
            ]
        ];
        
        foreach ($configs as $config) {
            DB::table('system_configs')->updateOrInsert(
                ['module' => $config['module'], 'key' => $config['key']],
                array_merge($config, [
                    'created_at' => now(),
                    'updated_at' => now()
                ])
            );
            $this->info("微信配置已添加/更新: {$config['key']}");
        }
    }
} 