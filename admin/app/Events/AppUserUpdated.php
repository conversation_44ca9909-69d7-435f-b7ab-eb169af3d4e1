<?php

namespace App\Events;

use App\Models\AppUser;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class AppUserUpdated
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    /**
     * 更新后的用户实例
     *
     * @var \App\Models\AppUser
     */
    public $user;

    /**
     * 原始的 is_vip 值
     *
     * @var int
     */
    public $originalIsVip;

    /**
     * 原始的 is_vip_paid 值
     *
     * @var int
     */
    public $originalIsVipPaid;

    /**
     * 创建一个新的事件实例
     *
     * @param  \App\Models\AppUser  $user
     * @return void
     */
    public function __construct(AppUser $user)
    {
        $this->user = $user;
        $this->originalIsVip = $user->getOriginal('is_vip');
        $this->originalIsVipPaid = $user->getOriginal('is_vip_paid');
    }
}
