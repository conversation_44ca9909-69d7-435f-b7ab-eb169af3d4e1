<?php

namespace App\Jobs;

use App\Models\AppUser;
use App\Services\UserRoleSyncService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class SyncUserRolesJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * 重试次数
     */
    public $tries = 5;

    /**
     * 重试间隔（秒）
     */
    public $backoff = 30;

    /**
     * 任务超时时间（秒）
     */
    public $timeout = 600;

    /**
     * 队列名称
     */
    public $queue = 'sync-roles';

    /**
     * 用户ID
     */
    protected $userId;

    /**
     * 是否批量同步
     */
    protected $isBatchSync;

    /**
     * 批次ID，用于断点续传
     */
    protected $batchId;

    /**
     * 批次总数
     */
    protected $batchTotal;

    /**
     * 创建新的任务实例
     *
     * @param int $userId 用户ID，如果为null则表示同步所有用户
     * @param bool $isBatchSync 是否为批量同步任务
     * @param string|null $batchId 批次ID
     * @param int|null $batchTotal 批次总数
     * @return void
     */
    public function __construct($userId = null, $isBatchSync = false, $batchId = null, $batchTotal = null)
    {
        $this->userId = $userId;
        $this->isBatchSync = $isBatchSync;
        $this->batchId = $batchId;
        $this->batchTotal = $batchTotal;
    }

    /**
     * 执行任务
     *
     * @param UserRoleSyncService $syncService
     * @return void
     */
    public function handle(UserRoleSyncService $syncService)
    {
        if ($this->userId) {
            // 同步单个用户
            $this->syncSingleUser($syncService);
        } else {
            // 同步所有用户
            $this->syncAllUsers($syncService);
        }
    }

    /**
     * 同步单个用户
     *
     * @param UserRoleSyncService $syncService
     * @return void
     */
    protected function syncSingleUser(UserRoleSyncService $syncService)
    {
        $user = AppUser::find($this->userId);

        if (!$user) {
            Log::error('用户同步失败：用户不存在', ['user_id' => $this->userId]);
            return;
        }

        if (empty($user->phone)) {
            Log::info('用户同步跳过：手机号为空', ['user_id' => $this->userId]);
            return;
        }

        try {
            $result = $syncService->syncUserRoles($user);
            Log::info('用户角色同步完成', [
                'user_id' => $this->userId,
                'success' => $result['success'] ?? false,
                'message' => $result['message'] ?? '成功',
                'is_batch' => $this->isBatchSync,
                'batch_id' => $this->batchId,
            ]);
        } catch (\Exception $e) {
            Log::error('用户角色同步异常', [
                'user_id' => $this->userId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // 如果是批量同步的一部分，抛出异常让队列系统重试
            if ($this->isBatchSync) {
                throw $e;
            }
        }
    }

    /**
     * 同步所有用户（分批处理）
     *
     * @param UserRoleSyncService $syncService
     * @return void
     */
    protected function syncAllUsers(UserRoleSyncService $syncService)
    {
        try {
            // 获取所有需要同步的用户
            $query = AppUser::whereNotNull('phone');

            // 如果有批次ID，则从上次中断的地方继续
            if ($this->batchId) {
                // 从缓存中获取上次同步到的用户ID
                $lastSyncedId = cache("sync_user_roles_batch_{$this->batchId}_last_id", 0);
                if ($lastSyncedId > 0) {
                    $query->where('id', '>', $lastSyncedId);
                }
            }

            // 分批处理，每批100个用户
            $batchSize = 100;
            $users = $query->orderBy('id')->limit($batchSize)->get();

            if ($users->isEmpty()) {
                // 更新最终进度
                if ($this->batchId) {
                    $progress = [
                        'processed' => $this->batchTotal,
                        'total' => $this->batchTotal,
                        'success' => cache("sync_user_roles_batch_{$this->batchId}_success", 0),
                        'failed' => cache("sync_user_roles_batch_{$this->batchId}_failed", 0),
                        'percentage' => 100,
                        'last_updated' => now()->toDateTimeString(),
                        'completed' => true,
                        'duration' => time() - cache("sync_user_roles_batch_{$this->batchId}_start_time", time())
                    ];
                    cache()->put("sync_user_roles_batch_{$this->batchId}_progress", $progress, now()->addHours(24));
                }

                Log::info('批量同步完成：没有更多用户需要同步', [
                    'batch_id' => $this->batchId,
                    'progress' => $progress ?? []
                ]);

                return;
            }

            $batchId = $this->batchId ?: uniqid('batch_');

            // 记录开始时间
            if (!cache()->has("sync_user_roles_batch_{$batchId}_start_time")) {
                cache()->put("sync_user_roles_batch_{$batchId}_start_time", time(), now()->addHours(24));
            }

            // 获取已处理的统计数据
            $totalProcessed = cache("sync_user_roles_batch_{$batchId}_processed", 0);
            $totalSuccess = cache("sync_user_roles_batch_{$batchId}_success", 0);
            $totalFailed = cache("sync_user_roles_batch_{$batchId}_failed", 0);

            $processedCount = 0;
            $successCount = 0;
            $failedCount = 0;
            $lastSyncedId = 0;

            // 处理当前批次中的用户
            foreach ($users as $user) {
                try {
                    // 调用同步服务进行同步
                    $result = $syncService->syncUserRoles($user);
                    $processedCount++;

                    if ($result['success'] ?? false) {
                        $successCount++;
                    } else {
                        $failedCount++;
                    }

                    $lastSyncedId = $user->id;

                    // 保存进度，用于断点续传
                    cache(["sync_user_roles_batch_{$batchId}_last_id" => $lastSyncedId], now()->addHours(24));
                } catch (\Exception $e) {
                    $failedCount++;
                    Log::error('批量同步用户角色异常', [
                        'user_id' => $user->id,
                        'batch_id' => $batchId,
                        'error' => $e->getMessage(),
                        'trace' => $e->getTraceAsString()
                    ]);
                }

                // 每处理 10 个用户更新一次进度
                if ($processedCount % 10 === 0) {
                    $this->updateProgress($batchId, $totalProcessed + $processedCount, $totalSuccess + $successCount, $totalFailed + $failedCount);
                }
            }

            // 更新总计数
            $totalProcessed += $processedCount;
            $totalSuccess += $successCount;
            $totalFailed += $failedCount;

            // 保存统计数据
            cache()->put("sync_user_roles_batch_{$batchId}_processed", $totalProcessed, now()->addHours(24));
            cache()->put("sync_user_roles_batch_{$batchId}_success", $totalSuccess, now()->addHours(24));
            cache()->put("sync_user_roles_batch_{$batchId}_failed", $totalFailed, now()->addHours(24));

            // 更新进度
            $this->updateProgress($batchId, $totalProcessed, $totalSuccess, $totalFailed);

            Log::info('批量同步进度', [
                'batch_id' => $batchId,
                'processed' => $totalProcessed,
                'success' => $totalSuccess,
                'failed' => $totalFailed,
                'last_synced_id' => $lastSyncedId,
                'batch_size' => $users->count(),
                'has_more' => $users->count() == $batchSize
            ]);

            // 递归处理下一批
            if ($users->count() == $batchSize) {
                SyncUserRolesJob::dispatch(null, true, $batchId, $this->batchTotal)
                    ->onQueue('sync-roles')
                    ->delay(now()->addSeconds(5));
            } else {
                // 更新最终进度
                $progress = [
                    'processed' => $totalProcessed,
                    'total' => $this->batchTotal,
                    'success' => $totalSuccess,
                    'failed' => $totalFailed,
                    'percentage' => $this->batchTotal > 0 ? round(($totalProcessed / $this->batchTotal) * 100, 2) : 100,
                    'last_updated' => now()->toDateTimeString(),
                    'completed' => true,
                    'duration' => time() - cache("sync_user_roles_batch_{$batchId}_start_time", time())
                ];
                cache()->put("sync_user_roles_batch_{$batchId}_progress", $progress, now()->addHours(24));

                Log::info('批量同步完成', [
                    'batch_id' => $batchId,
                    'total_processed' => $totalProcessed,
                    'total_success' => $totalSuccess,
                    'total_failed' => $totalFailed,
                    'duration' => $progress['duration']
                ]);
            }
        } catch (\Exception $e) {
            Log::error('批量同步处理异常', [
                'batch_id' => $this->batchId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // 如果是批量同步，则重试
            if ($this->batchId) {
                throw $e; // 抛出异常以触发队列重试
            }
        }
    }

    /**
     * 更新同步进度
     *
     * @param string $batchId 批次ID
     * @param int $processed 已处理数量
     * @param int $success 成功数量
     * @param int $failed 失败数量
     * @return void
     */
    protected function updateProgress($batchId, $processed, $success, $failed)
    {
        $progress = [
            'processed' => $processed,
            'total' => $this->batchTotal,
            'success' => $success,
            'failed' => $failed,
            'percentage' => $this->batchTotal > 0 ? round(($processed / $this->batchTotal) * 100, 2) : 0,
            'last_updated' => now()->toDateTimeString()
        ];

        cache()->put("sync_user_roles_batch_{$batchId}_progress", $progress, now()->addHours(24));
    }
}