<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class OrderRefund extends Model
{
    use HasFactory;
    
    /**
     * 关联的数据表
     *
     * @var string
     */
    protected $table = 'order_refunds';

    /**
     * 主键
     *
     * @var string
     */
    protected $primaryKey = 'id';
    
    /**
     * 指示模型是否自动维护时间戳
     *
     * @var bool
     */
    public $timestamps = true;

    /**
     * 可以被批量赋值的属性
     *
     * @var array
     */
    protected $fillable = [
        'refund_no', 'order_id', 'user_id', 'order_item_id', 'type',
        'status', 'refund_amount', 'reason', 'description', 'images',
        'reject_reason', 'processed_at', 'processed_by', 'admin_remark',
        'express_company', 'express_no', 'refunded_at', 'payment_refund_no'
    ];

    /**
     * 属性类型转换
     *
     * @var array
     */
    protected $casts = [
        'order_id' => 'integer',
        'user_id' => 'integer',
        'order_item_id' => 'integer',
        'type' => 'integer',
        'status' => 'integer',
        'refund_amount' => 'float',
        'images' => 'json',
        'processed_at' => 'datetime',
        'refunded_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    /**
     * 获取订单
     */
    public function order()
    {
        return $this->belongsTo(Order::class, 'order_id', 'id');
    }

    /**
     * 获取订单项
     */
    public function orderItem()
    {
        return $this->belongsTo(OrderItem::class, 'order_item_id', 'id');
    }

    /**
     * 获取用户
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

    /**
     * 获取审核状态文本
     */
    public function getVerifyStatusTextAttribute()
    {
        $statusMap = [
            0 => '待审核',
            1 => '通过审核',
            2 => '未通过审核',
            3 => '退款中',
            4 => '退款完成'
        ];

        return $statusMap[$this->verify_status] ?? '未知状态';
    }

    /**
     * 获取申请来源文本
     */
    public function getApplyFromTextAttribute()
    {
        $sourceMap = [
            0 => '平台',
            1 => '会员',
            2 => '团长'
        ];

        return $sourceMap[$this->apply_from] ?? '未知来源';
    }

    /**
     * 获取售后类型文本
     */
    public function getAftermarketTypeTextAttribute()
    {
        $typeMap = [
            0 => '仅退款',
            1 => '退货退款',
            2 => '退货换货'
        ];

        return $typeMap[$this->aftermarket_type] ?? '未知类型';
    }
}
