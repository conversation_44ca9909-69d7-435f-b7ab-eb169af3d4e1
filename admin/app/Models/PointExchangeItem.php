<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PointExchangeItem extends Model
{
    use HasFactory;

    /**
     * 可批量赋值的属性
     */
    protected $fillable = [
        'title',
        'description',
        'image',
        'type',
        'points',
        'stock',
        'sort',
        'status',
        'extra_data',
    ];
    
    /**
     * 类型常量
     */
    const TYPE_VOUCHER = 'voucher';
    const TYPE_GOODS = 'goods';
    const TYPE_VIRTUAL = 'virtual';
    
    /**
     * 状态常量
     */
    const STATUS_ACTIVE = 'active';
    const STATUS_INACTIVE = 'inactive';
    
    /**
     * 额外的属性类型转换
     */
    protected $casts = [
        'extra_data' => 'array',
    ];
    
    /**
     * 获取此商品相关的兑换记录
     */
    public function exchangeRecords()
    {
        return $this->hasMany(PointExchangeRecord::class, 'item_id');
    }
} 