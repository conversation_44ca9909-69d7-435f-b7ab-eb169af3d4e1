<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PointRecord extends Model
{
    use HasFactory;

    /**
     * 可批量赋值的属性
     */
    protected $fillable = [
        'user_id',
        'rule_id',
        'type',
        'points',
        'description',
        'before_points',
        'after_points',
    ];

    /**
     * 类型常量
     */
    const TYPE_REWARD = 'reward';
    const TYPE_CONSUME = 'consume';

    /**
     * 获取此记录关联的用户
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * 获取此记录关联的规则
     */
    public function rule()
    {
        return $this->belongsTo(PointRule::class, 'rule_id');
    }
} 