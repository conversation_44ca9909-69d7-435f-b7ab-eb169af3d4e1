<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class SalesmanCustomer extends Model
{
    use HasFactory;
    
    /**
     * 表名
     *
     * @var string
     */
    protected $table = 'salesman_customers';
    
    /**
     * 可批量赋值的属性
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'salesman_id',
        'customer_name',
        'customer_phone',
        'customer_address',
        'source',
        'status',
        'deal_count',
        'total_amount',
        'last_purchase_date',
        'remarks',
    ];
    
    /**
     * 应该被转换成原生类型的属性
     *
     * @var array
     */
    protected $casts = [
        'deal_count' => 'integer',
        'total_amount' => 'decimal:2',
        'last_purchase_date' => 'date',
    ];
    
    /**
     * 获取关联的业务员
     */
    public function salesman(): BelongsTo
    {
        return $this->belongsTo(Salesman::class, 'salesman_id');
    }
    
    /**
     * 增加成交次数和金额
     *
     * @param int $customerId
     * @param float $amount
     * @return bool
     */
    public static function addDeal($customerId, $amount)
    {
        $customer = self::find($customerId);
        
        if (!$customer) {
            return false;
        }
        
        $customer->deal_count += 1;
        $customer->total_amount += $amount;
        $customer->last_purchase_date = now();
        
        // 更新状态为活跃
        $customer->status = 'active';
        
        return $customer->save();
    }
    
    /**
     * 获取特定业务员的客户数量
     *
     * @param int $salesmanId
     * @return int
     */
    public static function getCustomerCount($salesmanId)
    {
        return self::where('salesman_id', $salesmanId)->count();
    }
    
    /**
     * 获取特定业务员的活跃客户数量
     *
     * @param int $salesmanId
     * @return int
     */
    public static function getActiveCustomerCount($salesmanId)
    {
        return self::where('salesman_id', $salesmanId)
            ->where('status', 'active')
            ->count();
    }
}
