<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Banner extends Model
{
    use HasFactory;

    protected $table = 'banners';

    protected $fillable = [
        'title',
        'image_url', 
        'link_url',
        'link_type',
        'position',
        'sort',
        'status',
        'start_time',
        'end_time'
    ];
    
    protected $casts = [
        'start_time' => 'datetime',
        'end_time' => 'datetime',
    ];
    
    /**
     * 状态值常量
     */
    const STATUS_ACTIVE = 'active';
    const STATUS_INACTIVE = 'inactive';
    
    /**
     * 链接类型常量
     */
    const LINK_TYPE_URL = 'url';
    const LINK_TYPE_PRODUCT = 'product';
    const LINK_TYPE_CATEGORY = 'category';
    const LINK_TYPE_WATERPOINT = 'waterpoint';
    const LINK_TYPE_NONE = 'none';
    
    /**
     * 展示位置常量
     */
    const POSITION_HOME = 'home';
    const POSITION_MALL = 'mall';
    const POSITION_WATER = 'water';
    const POSITION_PRODUCT = 'product';
    
    /**
     * 获取激活的轮播图
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeActive($query)
    {
        return $query->where('status', self::STATUS_ACTIVE);
    }
    
    /**
     * 获取指定位置的轮播图
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param string $position
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopePosition($query, $position)
    {
        return $query->where('position', $position);
    }
}
