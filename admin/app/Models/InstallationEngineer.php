<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class InstallationEngineer extends Model
{
    use HasFactory;

    protected $table = 'installation_engineers';

    protected $fillable = [
        'name',
        'phone',
        'password',
        'region',
        'address',
        'id_card',
        'avatar',
        'status',
        'remark',
    ];

    protected $hidden = [
        'password',
    ];

    protected $casts = [
        'status' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * 获取工程师的安装任务
     */
    public function installations()
    {
        return $this->hasMany(Installation::class, 'engineer_id');
    }

    /**
     * 状态标签
     */
    public function getStatusLabelAttribute()
    {
        $statusMap = [
            0 => '禁用',
            1 => '在职',
        ];

        return $statusMap[$this->status] ?? '未知';
    }

    /**
     * 获取完成的安装数量
     */
    public function getCompletedInstallationsCountAttribute()
    {
        return $this->installations()->where('status', 'completed')->count();
    }
} 