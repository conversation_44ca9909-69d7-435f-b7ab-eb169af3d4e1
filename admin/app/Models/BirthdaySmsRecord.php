<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class BirthdaySmsRecord extends Model
{
    use HasFactory;

    protected $table = 'sft_bsms_record';

    protected $fillable = [
        'institution_id',
        'name',
        'phone',
        'sms_content',
        'birthday',
        'gender',
        'age',
        'send_time',
        'status'
    ];

    protected $casts = [
        'birthday' => 'date',
        'send_time' => 'datetime',
        'age' => 'integer'
    ];

    /**
     * 获取机构信息
     */
    public function institution()
    {
        return $this->belongsTo(Institution::class, 'institution_id');
    }
} 