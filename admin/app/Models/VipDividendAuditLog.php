<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class VipDividendAuditLog extends Model
{
    use HasFactory;

    /**
     * 表名
     */
    protected $table = 'vip_dividend_audit_logs';

    /**
     * 可批量赋值的属性
     */
    protected $fillable = [
        'batch_id',
        'action',
        'status_from',
        'status_to',
        'operator_id',
        'operator_name',
        'operation_reason',
        'operation_data',
        'ip_address',
        'user_agent'
    ];

    /**
     * 属性转换
     */
    protected $casts = [
        'operation_data' => 'json',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * 操作类型常量
     */
    const ACTION_CREATE = 'create';
    const ACTION_CALCULATE = 'calculate';
    const ACTION_REVIEW = 'review';
    const ACTION_APPROVE = 'approve';
    const ACTION_REJECT = 'reject';
    const ACTION_SETTLE = 'settle';
    const ACTION_CANCEL = 'cancel';

    /**
     * 操作类型映射
     */
    public static function getActionMap()
    {
        return [
            self::ACTION_CREATE => '创建',
            self::ACTION_CALCULATE => '计算',
            self::ACTION_REVIEW => '审核',
            self::ACTION_APPROVE => '审批',
            self::ACTION_REJECT => '拒绝',
            self::ACTION_SETTLE => '结算',
            self::ACTION_CANCEL => '取消',
        ];
    }

    /**
     * 获取操作类型文本
     */
    public function getActionTextAttribute()
    {
        return self::getActionMap()[$this->action] ?? '未知操作';
    }

    /**
     * 获取分红批次
     */
    public function batch(): BelongsTo
    {
        return $this->belongsTo(VipDividendBatch::class, 'batch_id');
    }

    /**
     * 获取操作人
     */
    public function operator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'operator_id');
    }

    /**
     * 范围查询：按批次
     */
    public function scopeByBatch($query, $batchId)
    {
        return $query->where('batch_id', $batchId);
    }

    /**
     * 范围查询：按操作类型
     */
    public function scopeByAction($query, $action)
    {
        return $query->where('action', $action);
    }

    /**
     * 范围查询：按操作人
     */
    public function scopeByOperator($query, $operatorId)
    {
        return $query->where('operator_id', $operatorId);
    }
} 