<?php

namespace App\Models\Shengfutong;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class SftResellerSum extends Model
{
    use HasFactory;

    /**
     * 指定数据库连接
     */
    protected $connection = 'payment_db';

    /**
     * 表名
     */
    protected $table = 'ddg_sft_reseller_sum';

    /**
     * 禁用时间戳
     */
    public $timestamps = false;

    /**
     * 可批量赋值的属性
     */
    protected $fillable = [
        'summary_period',
        'reseller_id',
        'reseller_name',
        'payment_channel',
        'success_amount',
        'success_count',
        'refund_amount',
        'refund_count',
        'actual_amount',
        'agent_commission',
        'reseller_commission',
        'should_receive_commission',
        'commission_type'
    ];

    /**
     * 属性转换
     */
    protected $casts = [
        'success_amount' => 'decimal:2',
        'success_count' => 'integer',
        'refund_amount' => 'decimal:2',
        'refund_count' => 'integer',
        'actual_amount' => 'decimal:2',
        'agent_commission' => 'decimal:2',
        'reseller_commission' => 'decimal:2',
        'should_receive_commission' => 'decimal:2',
    ];

    /**
     * 作用域：按汇总周期筛选
     */
    public function scopeBySummaryPeriod($query, $period)
    {
        return $query->where('summary_period', $period);
    }

    /**
     * 作用域：按渠道商ID筛选
     */
    public function scopeByResellerId($query, $resellerId)
    {
        return $query->where('reseller_id', $resellerId);
    }

    /**
     * 作用域：按支付渠道筛选
     */
    public function scopeByPaymentChannel($query, $channel)
    {
        return $query->where('payment_channel', $channel);
    }
} 