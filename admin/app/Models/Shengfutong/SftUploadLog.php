<?php

namespace App\Models\Shengfutong;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use App\Models\Admin;

class SftUploadLog extends Model
{
    use HasFactory;

    /**
     * 指定数据库连接
     */
    protected $connection = 'payment_db';

    /**
     * 表名
     */
    protected $table = 'sft_upload_log';

    /**
     * 启用时间戳
     */
    public $timestamps = true;

    /**
     * 可批量赋值的属性
     */
    protected $fillable = [
        'filename',
        'original_filename',
        'data_time',
        'table_name',
        'file_path',
        'file_size',
        'status',
        'message',
        'uploader_id',
        'upload_type',
        'processed_rows',
        'upload_time',
        'insert_success_time',
        'insert_success'
    ];

    /**
     * 属性转换
     */
    protected $casts = [
        'file_size' => 'integer',
        'processed_rows' => 'integer',
        'uploader_id' => 'integer',
        'upload_time' => 'datetime',
        'insert_success_time' => 'datetime',
        'insert_success' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * 状态常量
     */
    const STATUS_PROCESSING = 'processing';
    const STATUS_SUCCESS = 'success';
    const STATUS_FAILED = 'failed';

    /**
     * 上传类型常量
     */
    const TYPE_DEFAULT = 'default';
    const TYPE_MANUAL = 'manual';
    const TYPE_AUTO = 'auto';

    /**
     * 表名常量
     */
    const TABLE_AGENT_MCH_SUM = 'ddg_sft_agent_mch_sum';
    const TABLE_DETAIL = 'ddg_sft_detail';
    const TABLE_RESELLER_SUM = 'ddg_sft_reseller_sum';

    /**
     * 关联上传者
     * 注意：Admin模型在默认数据库中，需要跨数据库关联
     */
    public function uploader()
    {
        // 由于跨数据库关联的复杂性，这里返回null关联
        // 在实际使用中通过手动查询获取上传者信息
        return null;
    }

    /**
     * 获取上传者信息
     */
    public function getUploaderAttribute()
    {
        if (!$this->uploader_id) {
            return null;
        }

        try {
            // 手动查询默认数据库中的admin_users表
            return \DB::table('admin_users')->where('id', $this->uploader_id)->first();
        } catch (\Exception $e) {
            \Log::warning('获取上传者信息失败', ['uploader_id' => $this->uploader_id, 'error' => $e->getMessage()]);
            return null;
        }
    }

    /**
     * 获取状态文本
     */
    public function getStatusTextAttribute()
    {
        $statusMap = [
            self::STATUS_PROCESSING => '处理中',
            self::STATUS_SUCCESS => '成功',
            self::STATUS_FAILED => '失败',
        ];

        return $statusMap[$this->status] ?? '未知';
    }

    /**
     * 获取表名文本
     */
    public function getTableNameTextAttribute()
    {
        $tableMap = [
            self::TABLE_AGENT_MCH_SUM => '代理商户汇总',
            self::TABLE_DETAIL => '交易明细',
            self::TABLE_RESELLER_SUM => '渠道商汇总',
        ];

        return $tableMap[$this->table_name] ?? $this->table_name;
    }

    /**
     * 获取文件大小格式化文本
     */
    public function getFileSizeFormattedAttribute()
    {
        if (!$this->file_size) {
            return '0 B';
        }

        $units = ['B', 'KB', 'MB', 'GB'];
        $size = $this->file_size;
        $unitIndex = 0;

        while ($size >= 1024 && $unitIndex < count($units) - 1) {
            $size /= 1024;
            $unitIndex++;
        }

        return round($size, 2) . ' ' . $units[$unitIndex];
    }

    /**
     * 作用域：按状态筛选
     */
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * 作用域：按表名筛选
     */
    public function scopeByTableName($query, $tableName)
    {
        return $query->where('table_name', $tableName);
    }

    /**
     * 作用域：按日期范围筛选
     */
    public function scopeByDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('created_at', [$startDate, $endDate]);
    }

    /**
     * 作用域：按数据时间筛选
     */
    public function scopeByDataTime($query, $dataTime)
    {
        return $query->where('data_time', $dataTime);
    }
} 