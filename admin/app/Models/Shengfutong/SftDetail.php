<?php

namespace App\Models\Shengfutong;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class SftDetail extends Model
{
    use HasFactory;

    /**
     * 指定数据库连接
     */
    protected $connection = 'payment_db';

    /**
     * 表名
     */
    protected $table = 'ddg_sft_detail';

    /**
     * 禁用时间戳
     */
    public $timestamps = false;

    /**
     * 可批量赋值的属性
     */
    protected $fillable = [
        'transaction_time',
        'merchant_id',
        'merchant_name',
        'reseller_id',
        'reseller_name',
        'order_type',
        'payment_method',
        'transaction_amount',
        'handling_fee',
        'agent_commission',
        'reseller_commission',
        'should_receive_commission',
        'order_id',
        'merchant_order_id',
        'operator',
        'commission_type'
    ];

    /**
     * 属性转换
     */
    protected $casts = [
        'transaction_time' => 'datetime',
        'transaction_amount' => 'decimal:2',
        'handling_fee' => 'decimal:2',
        'agent_commission' => 'decimal:2',
        'reseller_commission' => 'decimal:2',
        'should_receive_commission' => 'decimal:2',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * 作用域：按交易时间范围筛选
     */
    public function scopeByTransactionTimeRange($query, $startTime, $endTime)
    {
        return $query->whereBetween('transaction_time', [$startTime, $endTime]);
    }

    /**
     * 作用域：按商户ID筛选
     */
    public function scopeByMerchantId($query, $merchantId)
    {
        return $query->where('merchant_id', $merchantId);
    }

    /**
     * 作用域：按渠道商ID筛选
     */
    public function scopeByResellerId($query, $resellerId)
    {
        return $query->where('reseller_id', $resellerId);
    }

    /**
     * 作用域：按支付方式筛选
     */
    public function scopeByPaymentMethod($query, $method)
    {
        return $query->where('payment_method', $method);
    }

    /**
     * 作用域：按订单类型筛选
     */
    public function scopeByOrderType($query, $type)
    {
        return $query->where('order_type', $type);
    }

    /**
     * 作用域：按盛付通订单号筛选
     */
    public function scopeByOrderId($query, $orderId)
    {
        return $query->where('order_id', $orderId);
    }
} 