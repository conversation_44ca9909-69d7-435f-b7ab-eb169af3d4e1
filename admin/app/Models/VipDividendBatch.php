<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class VipDividendBatch extends Model
{
    use HasFactory;

    /**
     * 表名
     */
    protected $table = 'vip_dividend_batches';

    /**
     * 可批量赋值的属性
     */
    protected $fillable = [
        'batch_no',
        'settlement_month',
        'status',
        'total_pool',
        'vip_pool',
        'recharge_pool',
        'new_vip_count',
        'new_device_count',
        'qualified_users_count',
        'total_dividend_amount',
        'calculation_params',
        'pool_distribution',
        'created_by',
        'calculated_by',
        'approved_by',
        'settled_by',
        'calculated_at',
        'approved_at',
        'settled_at',
        'remark'
    ];

    /**
     * 属性转换
     */
    protected $casts = [
        'total_pool' => 'decimal:2',
        'vip_pool' => 'decimal:2',
        'recharge_pool' => 'decimal:2',
        'total_dividend_amount' => 'decimal:2',
        'calculation_params' => 'json',
        'pool_distribution' => 'json',
        'calculated_at' => 'datetime',
        'approved_at' => 'datetime',
        'settled_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * 状态常量
     */
    const STATUS_DRAFT = 'draft';
    const STATUS_CALCULATING = 'calculating';
    const STATUS_CALCULATED = 'calculated';
    const STATUS_REVIEWING = 'reviewing';
    const STATUS_APPROVED = 'approved';
    const STATUS_SETTLED = 'settled';
    const STATUS_CANCELLED = 'cancelled';

    /**
     * 状态映射
     */
    public static function getStatusMap()
    {
        return [
            self::STATUS_DRAFT => '草稿',
            self::STATUS_CALCULATING => '计算中',
            self::STATUS_CALCULATED => '已计算',
            self::STATUS_REVIEWING => '审核中',
            self::STATUS_APPROVED => '已审批',
            self::STATUS_SETTLED => '已结算',
            self::STATUS_CANCELLED => '已取消',
        ];
    }

    /**
     * 获取状态文本
     */
    public function getStatusTextAttribute()
    {
        return self::getStatusMap()[$this->status] ?? '未知状态';
    }

    /**
     * 获取状态颜色
     */
    public function getStatusColorAttribute()
    {
        $colors = [
            self::STATUS_DRAFT => 'info',
            self::STATUS_CALCULATING => 'warning',
            self::STATUS_CALCULATED => 'primary',
            self::STATUS_REVIEWING => 'warning',
            self::STATUS_APPROVED => 'success',
            self::STATUS_SETTLED => 'success',
            self::STATUS_CANCELLED => 'danger',
        ];

        return $colors[$this->status] ?? 'secondary';
    }

    /**
     * 获取分红记录
     */
    public function dividendRecords(): HasMany
    {
        return $this->hasMany(VipDividendRecord::class, 'batch_id');
    }

    /**
     * 获取审核日志
     */
    public function auditLogs(): HasMany
    {
        return $this->hasMany(VipDividendAuditLog::class, 'batch_id');
    }

    /**
     * 获取创建人
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * 获取计算人
     */
    public function calculator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'calculated_by');
    }

    /**
     * 获取审批人
     */
    public function approver(): BelongsTo
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    /**
     * 获取结算人
     */
    public function settler(): BelongsTo
    {
        return $this->belongsTo(User::class, 'settled_by');
    }

    /**
     * 生成批次号
     */
    public static function generateBatchNo($month)
    {
        $prefix = str_replace('-', '', $month);
        $lastBatch = self::where('settlement_month', $month)
            ->orderBy('batch_no', 'desc')
            ->first();

        if ($lastBatch) {
            $lastNumber = intval(substr($lastBatch->batch_no, -3));
            $newNumber = $lastNumber + 1;
        } else {
            $newNumber = 1;
        }

        return $prefix . '-' . str_pad($newNumber, 3, '0', STR_PAD_LEFT);
    }

    /**
     * 检查是否可以编辑
     */
    public function canEdit()
    {
        return in_array($this->status, [self::STATUS_DRAFT, self::STATUS_CALCULATED]);
    }

    /**
     * 检查是否可以计算
     */
    public function canCalculate()
    {
        return in_array($this->status, [self::STATUS_DRAFT, self::STATUS_CALCULATED]);
    }

    /**
     * 检查是否可以审核
     */
    public function canReview()
    {
        return $this->status === self::STATUS_CALCULATED;
    }

    /**
     * 检查是否可以审批
     */
    public function canApprove()
    {
        return $this->status === self::STATUS_REVIEWING;
    }

    /**
     * 检查是否可以结算
     */
    public function canSettle()
    {
        return $this->status === self::STATUS_APPROVED;
    }

    /**
     * 检查是否可以取消
     */
    public function canCancel()
    {
        return !in_array($this->status, [self::STATUS_SETTLED, self::STATUS_CANCELLED]);
    }

    /**
     * 获取月份显示文本
     */
    public function getMonthTextAttribute()
    {
        if (!$this->settlement_month) return '';
        
        $parts = explode('-', $this->settlement_month);
        if (count($parts) !== 2) return $this->settlement_month;
        
        return $parts[0] . '年' . intval($parts[1]) . '月';
    }

    /**
     * 获取进度百分比
     */
    public function getProgressPercentAttribute()
    {
        $statusProgress = [
            self::STATUS_DRAFT => 10,
            self::STATUS_CALCULATING => 30,
            self::STATUS_CALCULATED => 50,
            self::STATUS_REVIEWING => 70,
            self::STATUS_APPROVED => 90,
            self::STATUS_SETTLED => 100,
            self::STATUS_CANCELLED => 0,
        ];

        return $statusProgress[$this->status] ?? 0;
    }

    /**
     * 获取VIP分红池分配详情
     */
    public function getVipPoolDistributionAttribute()
    {
        if (!$this->pool_distribution || !$this->vip_pool) {
            return null;
        }

        $distribution = $this->pool_distribution;
        return [
            'junior' => [
                'ratio' => $distribution['junior'] ?? 0.4,
                'amount' => $this->vip_pool * ($distribution['junior'] ?? 0.4)
            ],
            'middle' => [
                'ratio' => $distribution['middle'] ?? 0.3,
                'amount' => $this->vip_pool * ($distribution['middle'] ?? 0.3)
            ],
            'senior' => [
                'ratio' => $distribution['senior'] ?? 0.3,
                'amount' => $this->vip_pool * ($distribution['senior'] ?? 0.3)
            ]
        ];
    }

    /**
     * 获取充值分红池分配详情
     */
    public function getRechargePoolDistributionAttribute()
    {
        if (!$this->pool_distribution || !$this->recharge_pool) {
            return null;
        }

        $distribution = $this->pool_distribution;
        return [
            'junior' => [
                'ratio' => $distribution['junior'] ?? 0.4,
                'amount' => $this->recharge_pool * ($distribution['junior'] ?? 0.4)
            ],
            'middle' => [
                'ratio' => $distribution['middle'] ?? 0.3,
                'amount' => $this->recharge_pool * ($distribution['middle'] ?? 0.3)
            ],
            'senior' => [
                'ratio' => $distribution['senior'] ?? 0.3,
                'amount' => $this->recharge_pool * ($distribution['senior'] ?? 0.3)
            ]
        ];
    }

    /**
     * 获取统计摘要
     */
    public function getSummaryAttribute()
    {
        $records = $this->dividendRecords;
        
        return [
            'total_records' => $records->count(),
            'settled_records' => $records->where('status', 'settled')->count(),
            'pending_records' => $records->where('status', 'pending')->count(),
            'settled_amount' => $records->where('status', 'settled')->sum('amount'),
            'pending_amount' => $records->where('status', 'pending')->sum('amount'),
            'vip_records' => $records->where('dividend_type', 'vip_recruitment')->count(),
            'recharge_records' => $records->where('dividend_type', 'device_recharge')->count(),
        ];
    }

    /**
     * 范围查询：按月份
     */
    public function scopeByMonth($query, $month)
    {
        return $query->where('settlement_month', $month);
    }

    /**
     * 范围查询：按状态
     */
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * 范围查询：已结算
     */
    public function scopeSettled($query)
    {
        return $query->where('status', self::STATUS_SETTLED);
    }

    /**
     * 范围查询：待处理
     */
    public function scopePending($query)
    {
        return $query->whereIn('status', [
            self::STATUS_DRAFT,
            self::STATUS_CALCULATING,
            self::STATUS_CALCULATED,
            self::STATUS_REVIEWING,
            self::STATUS_APPROVED
        ]);
    }

    /**
     * 范围查询：最近的批次
     */
    public function scopeRecent($query, $limit = 10)
    {
        return $query->orderBy('created_at', 'desc')->limit($limit);
    }
} 