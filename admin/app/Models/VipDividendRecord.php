<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class VipDividendRecord extends Model
{
    use HasFactory;

    /**
     * 表名
     */
    protected $table = 'vip_dividend_records';

    /**
     * 可批量赋值的属性
     */
    protected $fillable = [
        'batch_id',
        'user_id',
        'settlement_month',
        'dividend_type',
        'dividend_level',
        'amount',
        'status',
        'team_vip_count',
        'direct_vip_count',
        'month_direct_vip_count',
        'month_team_vip_count',
        'team_device_count',
        'direct_device_count',
        'month_direct_device_count',
        'month_team_device_count',
        'direct_ratio',
        'pool_amount',
        'qualified_users_count',
        'calculation_detail',
        'qualification_reason',
        'settled_at',
        'settlement_transaction_id',
        'settlement_remark'
    ];

    /**
     * 属性转换
     */
    protected $casts = [
        'amount' => 'decimal:2',
        'direct_ratio' => 'decimal:6',
        'pool_amount' => 'decimal:2',
        'calculation_detail' => 'json',
        'settled_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * 分红类型常量
     */
    const TYPE_VIP_RECRUITMENT = 'vip_recruitment';
    const TYPE_DEVICE_RECHARGE = 'device_recharge';

    /**
     * 分红等级常量
     */
    const LEVEL_JUNIOR = 'junior';
    const LEVEL_MIDDLE = 'middle';
    const LEVEL_SENIOR = 'senior';

    /**
     * 状态常量
     */
    const STATUS_PENDING = 'pending';
    const STATUS_APPROVED = 'approved';
    const STATUS_SETTLED = 'settled';
    const STATUS_CANCELLED = 'cancelled';

    /**
     * 分红类型映射
     */
    public static function getTypeMap()
    {
        return [
            self::TYPE_VIP_RECRUITMENT => 'VIP招募分红',
            self::TYPE_DEVICE_RECHARGE => '充值套餐分红',
        ];
    }

    /**
     * 分红等级映射
     */
    public static function getLevelMap()
    {
        return [
            self::LEVEL_JUNIOR => '初级分红',
            self::LEVEL_MIDDLE => '中级分红',
            self::LEVEL_SENIOR => '高级分红',
        ];
    }

    /**
     * 状态映射
     */
    public static function getStatusMap()
    {
        return [
            self::STATUS_PENDING => '待结算',
            self::STATUS_APPROVED => '已审批',
            self::STATUS_SETTLED => '已结算',
            self::STATUS_CANCELLED => '已取消',
        ];
    }

    /**
     * 获取分红类型文本
     */
    public function getTypeTextAttribute()
    {
        return self::getTypeMap()[$this->dividend_type] ?? '未知类型';
    }

    /**
     * 获取分红等级文本
     */
    public function getLevelTextAttribute()
    {
        return self::getLevelMap()[$this->dividend_level] ?? '未知等级';
    }

    /**
     * 获取状态文本
     */
    public function getStatusTextAttribute()
    {
        return self::getStatusMap()[$this->status] ?? '未知状态';
    }

    /**
     * 获取状态颜色
     */
    public function getStatusColorAttribute()
    {
        $colors = [
            self::STATUS_PENDING => 'warning',
            self::STATUS_APPROVED => 'primary',
            self::STATUS_SETTLED => 'success',
            self::STATUS_CANCELLED => 'danger',
        ];

        return $colors[$this->status] ?? 'secondary';
    }

    /**
     * 获取等级颜色
     */
    public function getLevelColorAttribute()
    {
        $colors = [
            self::LEVEL_JUNIOR => 'info',
            self::LEVEL_MIDDLE => 'warning',
            self::LEVEL_SENIOR => 'success',
        ];

        return $colors[$this->dividend_level] ?? 'secondary';
    }

    /**
     * 获取分红批次
     */
    public function batch(): BelongsTo
    {
        return $this->belongsTo(VipDividendBatch::class, 'batch_id');
    }

    /**
     * 获取用户
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * 获取月份显示文本
     */
    public function getMonthTextAttribute()
    {
        if (!$this->settlement_month) return '';
        
        $parts = explode('-', $this->settlement_month);
        if (count($parts) !== 2) return $this->settlement_month;
        
        return $parts[0] . '年' . intval($parts[1]) . '月';
    }

    /**
     * 获取直推占比百分比
     */
    public function getDirectRatioPercentAttribute()
    {
        return $this->direct_ratio ? round($this->direct_ratio * 100, 2) : 0;
    }

    /**
     * 获取计算公式
     */
    public function getCalculationFormulaAttribute()
    {
        if (!$this->calculation_detail) {
            return '无计算详情';
        }

        $detail = $this->calculation_detail;
        
        if ($this->dividend_level === self::LEVEL_SENIOR && $this->direct_ratio) {
            // 高级分红按直推占比计算
            return "奖金池 {$this->pool_amount} × 直推占比 {$this->direct_ratio_percent}% = {$this->amount}";
        } else {
            // 初级和中级分红均分
            return "奖金池 {$this->pool_amount} ÷ 达标人数 {$this->qualified_users_count} = {$this->amount}";
        }
    }

    /**
     * 获取达标条件说明
     */
    public function getQualificationDescriptionAttribute()
    {
        $descriptions = [];
        
        if ($this->dividend_type === self::TYPE_VIP_RECRUITMENT) {
            switch ($this->dividend_level) {
                case self::LEVEL_JUNIOR:
                    $descriptions[] = "团队VIP满3人（当前：{$this->team_vip_count}人）";
                    break;
                case self::LEVEL_MIDDLE:
                    $descriptions[] = "团队VIP满10人（当前：{$this->team_vip_count}人）";
                    break;
                case self::LEVEL_SENIOR:
                    $descriptions[] = "团队VIP满30人（当前：{$this->team_vip_count}人）";
                    $descriptions[] = "本月直推≠0（当前：{$this->month_direct_vip_count}人）";
                    break;
            }
        } else {
            switch ($this->dividend_level) {
                case self::LEVEL_JUNIOR:
                    $descriptions[] = "团队充值满10台（当前：{$this->team_device_count}台）";
                    break;
                case self::LEVEL_MIDDLE:
                    $descriptions[] = "团队充值满30台（当前：{$this->team_device_count}台）";
                    break;
                case self::LEVEL_SENIOR:
                    $descriptions[] = "团队充值满80台（当前：{$this->team_device_count}台）";
                    $descriptions[] = "本月直推≠0（当前：{$this->month_direct_device_count}台）";
                    break;
            }
        }
        
        return implode('，', $descriptions);
    }

    /**
     * 检查是否可以结算
     */
    public function canSettle()
    {
        return in_array($this->status, [self::STATUS_PENDING, self::STATUS_APPROVED]);
    }

    /**
     * 检查是否可以取消
     */
    public function canCancel()
    {
        return $this->status !== self::STATUS_SETTLED;
    }

    /**
     * 范围查询：按批次
     */
    public function scopeByBatch($query, $batchId)
    {
        return $query->where('batch_id', $batchId);
    }

    /**
     * 范围查询：按用户
     */
    public function scopeByUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * 范围查询：按月份
     */
    public function scopeByMonth($query, $month)
    {
        return $query->where('settlement_month', $month);
    }

    /**
     * 范围查询：按分红类型
     */
    public function scopeByType($query, $type)
    {
        return $query->where('dividend_type', $type);
    }

    /**
     * 范围查询：按分红等级
     */
    public function scopeByLevel($query, $level)
    {
        return $query->where('dividend_level', $level);
    }

    /**
     * 范围查询：按状态
     */
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * 范围查询：VIP招募分红
     */
    public function scopeVipRecruitment($query)
    {
        return $query->where('dividend_type', self::TYPE_VIP_RECRUITMENT);
    }

    /**
     * 范围查询：充值套餐分红
     */
    public function scopeDeviceRecharge($query)
    {
        return $query->where('dividend_type', self::TYPE_DEVICE_RECHARGE);
    }

    /**
     * 范围查询：已结算
     */
    public function scopeSettled($query)
    {
        return $query->where('status', self::STATUS_SETTLED);
    }

    /**
     * 范围查询：待结算
     */
    public function scopePending($query)
    {
        return $query->where('status', self::STATUS_PENDING);
    }

    /**
     * 范围查询：初级分红
     */
    public function scopeJunior($query)
    {
        return $query->where('dividend_level', self::LEVEL_JUNIOR);
    }

    /**
     * 范围查询：中级分红
     */
    public function scopeMiddle($query)
    {
        return $query->where('dividend_level', self::LEVEL_MIDDLE);
    }

    /**
     * 范围查询：高级分红
     */
    public function scopeSenior($query)
    {
        return $query->where('dividend_level', self::LEVEL_SENIOR);
    }

    /**
     * 范围查询：按金额排序
     */
    public function scopeOrderByAmount($query, $direction = 'desc')
    {
        return $query->orderBy('amount', $direction);
    }

    /**
     * 范围查询：按创建时间排序
     */
    public function scopeOrderByCreated($query, $direction = 'desc')
    {
        return $query->orderBy('created_at', $direction);
    }
} 