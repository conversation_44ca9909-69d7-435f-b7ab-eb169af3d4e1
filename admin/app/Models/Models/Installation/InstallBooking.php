<?php

namespace App\Models\Models\Installation;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Models\User;

class InstallBooking extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'install_bookings';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'booking_no',
        'user_id',
        'referrer_id',
        'package_type',
        'package_price',
        'installation_fee',
        'total_amount',
        'contact_name',
        'contact_phone',
        'install_address',
        'install_time',
        'remarks',
        'payment_status',
        'payment_time',
        'payment_method',
        'transaction_id',
        'status',
        'engineer_id',
        'completion_time',
        'cancellation_reason',
        'device_id',
        'device_model',
        'device_sn',
        'rating',
        'review',
        'review_time',
        'meta_data',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'install_time' => 'datetime',
        'payment_time' => 'datetime',
        'completion_time' => 'datetime',
        'review_time' => 'datetime',
        'meta_data' => 'array',
        'package_price' => 'decimal:2',
        'installation_fee' => 'decimal:2',
        'total_amount' => 'decimal:2',
    ];

    /**
     * Get the user that owns the booking.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the engineer assigned to the booking.
     */
    public function engineer()
    {
        return $this->belongsTo(User::class, 'engineer_id');
    }

    /**
     * Get the referrer user for the booking.
     */
    public function referrer()
    {
        return $this->belongsTo(User::class, 'referrer_id');
    }
}
