<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class LoginLog extends Model
{
    use HasFactory;

    protected $table = 'login_logs';

    protected $fillable = [
        'user_id',
        'user_type',
        'login_method',
        'ip_address',
        'user_agent',
        'status',
        'message',
    ];

    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * 关联到APP用户
     */
    public function appUser(): BelongsTo
    {
        return $this->belongsTo(AppUser::class, 'user_id')->where('user_type', 'app_user');
    }

    /**
     * 关联到管理员用户
     */
    public function adminUser(): BelongsTo
    {
        return $this->belongsTo(AdminUser::class, 'user_id')->where('user_type', 'admin_user');
    }

    /**
     * 按用户类型筛选
     */
    public function scopeByUserType($query, $userType)
    {
        return $query->where('user_type', $userType);
    }

    /**
     * 按登录方式筛选
     */
    public function scopeByLoginMethod($query, $loginMethod)
    {
        return $query->where('login_method', $loginMethod);
    }

    /**
     * 按状态筛选
     */
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * 按日期范围筛选
     */
    public function scopeByDateRange($query, $startDate, $endDate)
    {
        if ($startDate) {
            $query->where('created_at', '>=', $startDate . ' 00:00:00');
        }
        if ($endDate) {
            $query->where('created_at', '<=', $endDate . ' 23:59:59');
        }
        return $query;
    }

    /**
     * 搜索
     */
    public function scopeSearch($query, $keyword)
    {
        if (!$keyword) {
            return $query;
        }

        return $query->where(function ($q) use ($keyword) {
            $q->where('ip_address', 'like', "%{$keyword}%")
              ->orWhere('message', 'like', "%{$keyword}%")
              ->orWhereHas('appUser', function ($subQ) use ($keyword) {
                  $subQ->where('name', 'like', "%{$keyword}%")
                       ->orWhere('phone', 'like', "%{$keyword}%")
                       ->orWhere('wechat_nickname', 'like', "%{$keyword}%");
              })
              ->orWhereHas('adminUser', function ($subQ) use ($keyword) {
                  $subQ->where('name', 'like', "%{$keyword}%")
                       ->orWhere('phone', 'like', "%{$keyword}%");
              });
        });
    }

    /**
     * 记录登录日志
     */
    public static function createLog($userId, $userType, $loginMethod, $status, $message = '', $ipAddress = null, $userAgent = null)
    {
        return self::create([
            'user_id' => $userId,
            'user_type' => $userType,
            'login_method' => $loginMethod,
            'ip_address' => $ipAddress ?: request()->ip(),
            'user_agent' => $userAgent ?: request()->userAgent(),
            'status' => $status,
            'message' => $message,
        ]);
    }

    /**
     * 获取登录方式显示名称
     */
    public function getLoginMethodNameAttribute()
    {
        $methods = [
            'password' => '密码登录',
            'wechat' => '微信登录',
            'code' => '验证码登录',
            'bind_phone' => '绑定手机号',
            'auto_bind' => '自动绑定',
        ];

        return $methods[$this->login_method] ?? $this->login_method;
    }

    /**
     * 获取状态显示名称
     */
    public function getStatusNameAttribute()
    {
        return $this->status === 'success' ? '成功' : '失败';
    }

    /**
     * 获取用户显示名称
     */
    public function getUserDisplayNameAttribute()
    {
        if ($this->user_type === 'app_user' && $this->appUser) {
            return $this->appUser->name ?: $this->appUser->wechat_nickname ?: $this->appUser->phone ?: "用户{$this->user_id}";
        } elseif ($this->user_type === 'admin_user' && $this->adminUser) {
            return $this->adminUser->name ?: "管理员{$this->user_id}";
        } elseif ($this->user_id) {
            return "用户{$this->user_id}";
        } else {
            return '未知用户';
        }
    }
} 