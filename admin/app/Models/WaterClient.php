<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class WaterClient extends Model
{
    // 自动检测是否存在water_db中的原始表，否则使用主数据库中的镜像表
    public function __construct(array $attributes = [])
    {
        // 强制使用水系统数据库
        $this->connection = 'water_db';
        $this->table = 'wb_client';
        
        parent::__construct($attributes);
    }
    
    // 主键类型
    protected $keyType = 'string';
    
    // 不自增
    public $incrementing = false;
    
    // 关闭时间戳自动维护
    public $timestamps = false;
    
    // 定义可批量赋值的字段
    protected $fillable = [
        'id',
        'name',
        'phone',
        'province',
        'city',
        'area',
        'address',
        'status',
        'remark',
        'wx_id',
        'wx_nickname',
        'wx_head_img',
        'client_device_id',
        'client_device_name',
        'client_product_id',
        'client_product_name',
        'client_dealer_id',
        'client_dealer_name',
        'create_name',
        'create_by',
        'create_date',
        'update_name',
        'update_by',
        'update_date'
    ];

    protected $dates = [
        'create_date',
        'update_date',
    ];

    // 状态文本映射
    public function getStatusTextAttribute()
    {
        return $this->status == 'E' ? '启用' : '禁用';
    }

    // 关联设备
    public function device()
    {
        return $this->hasOne(WaterDevice::class, 'id', 'client_device_id');
    }
    
    // 格式化创建时间
    public function getCreatedAtFormattedAttribute()
    {
        return $this->create_date ? date('Y-m-d H:i:s', strtotime($this->create_date)) : '';
    }
    
    // 格式化更新时间
    public function getUpdatedAtFormattedAttribute()
    {
        return $this->update_date ? date('Y-m-d H:i:s', strtotime($this->update_date)) : '';
    }
    
    // 获取完整地址
    public function getFullAddressAttribute()
    {
        $address = '';
        if (!empty($this->province)) $address .= $this->province;
        if (!empty($this->city)) $address .= $this->city;
        if (!empty($this->area)) $address .= $this->area;
        if (!empty($this->address)) $address .= $this->address;
        
        return $address ?: '未设置地址';
    }

    /**
     * 按设备ID查找客户
     */
    public function scopeByDeviceId($query, $deviceId)
    {
        return $query->where('client_device_id', $deviceId);
    }

    /**
     * 按设备编号查找客户
     */
    public function scopeByDeviceNumber($query, $deviceNumber)
    {
        return $query->where('client_device_name', $deviceNumber);
    }

    /**
     * 按客户姓名查找
     */
    public function scopeByName($query, $name)
    {
        return $query->where('name', 'like', "%{$name}%");
    }

    /**
     * 按客户手机号查找
     */
    public function scopeByPhone($query, $phone)
    {
        return $query->where('phone', 'like', "%{$phone}%");
    }

    /**
     * 按微信ID查找客户
     */
    public function scopeByWxId($query, $wxId)
    {
        return $query->where('wx_id', $wxId);
    }

    /**
     * 获取设备编号（8800开头的编号）
     */
    public function getDeviceNumberAttribute()
    {
        return $this->client_device_name;
    }

    /**
     * 获取设备内部ID（UUID格式）
     */
    public function getDeviceInternalIdAttribute()
    {
        return $this->client_device_id;
    }

    /**
     * 获取设备产品名称
     */
    public function getProductNameAttribute()
    {
        return $this->client_product_name ?: '标准净水器';
    }
}
