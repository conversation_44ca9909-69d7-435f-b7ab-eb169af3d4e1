<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class SalesmanTarget extends Model
{
    use HasFactory;
    
    /**
     * 表名
     *
     * @var string
     */
    protected $table = 'salesman_targets';
    
    /**
     * 可批量赋值的属性
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'salesman_id',
        'target_amount',
        'target_quantity',
        'period_type',
        'period',
        'start_date',
        'end_date',
        'achievement',
        'status',
        'remarks',
    ];
    
    /**
     * 应该被转换成原生类型的属性
     *
     * @var array
     */
    protected $casts = [
        'target_amount' => 'decimal:2',
        'target_quantity' => 'integer',
        'achievement' => 'decimal:2',
        'start_date' => 'date',
        'end_date' => 'date',
    ];
    
    /**
     * 应该被转换为日期的属性
     *
     * @var array
     */
    protected $dates = [
        'start_date',
        'end_date',
        'created_at',
        'updated_at',
    ];
    
    /**
     * 获取关联的业务员
     */
    public function salesman(): BelongsTo
    {
        return $this->belongsTo(Salesman::class, 'salesman_id');
    }
    
    /**
     * 获取完成百分比
     *
     * @return float
     */
    public function getCompletionRateAttribute()
    {
        if ($this->target_quantity <= 0) {
            return 0;
        }
        
        $rate = ($this->achievement / $this->target_quantity) * 100;
        return round($rate, 2);
    }
    
    /**
     * 获取周期类型的显示名称
     *
     * @return string
     */
    public function getPeriodTypeDisplayAttribute()
    {
        $types = [
            'month' => '月度',
            'year' => '年度',
            'quarter' => '季度',
        ];

        return $types[$this->period_type] ?? $this->period_type;
    }
    
    /**
     * 获取状态的显示名称
     *
     * @return string
     */
    public function getStatusDisplayAttribute()
    {
        $statuses = [
            'in_progress' => '进行中',
            'completed' => '已完成',
            'failed' => '未达标',
        ];

        return $statuses[$this->status] ?? $this->status;
    }
    
    /**
     * 获取格式化的周期显示
     *
     * @return string
     */
    public function getFormattedPeriodAttribute()
    {
        switch ($this->period_type) {
            case 'month':
                // 格式化月度: 2023年05月
                if (preg_match('/^(\d{4})-(\d{2})$/', $this->period, $matches)) {
                    return $matches[1] . '年' . $matches[2] . '月';
                }
                break;
            case 'year':
                // 格式化年度: 2023年
                if (preg_match('/^(\d{4})$/', $this->period)) {
                    return $this->period . '年';
                }
                break;
            case 'quarter':
                // 格式化季度: 2023年Q1季度
                if (preg_match('/^(\d{4})-Q([1-4])$/', $this->period, $matches)) {
                    return $matches[1] . '年第' . $matches[2] . '季度';
                }
                break;
        }

        return $this->period;
    }
    
    /**
     * 获取当前月度目标
     *
     * @param int $salesmanId
     * @return SalesmanTarget|null
     */
    public static function getCurrentMonthTarget($salesmanId)
    {
        $currentMonth = Carbon::now()->format('Y-m');
        
        return self::where('salesman_id', $salesmanId)
            ->where('period_type', 'month')
            ->where('period', $currentMonth)
            ->first();
    }
    
    /**
     * 获取当前年度目标
     *
     * @param int $salesmanId
     * @return SalesmanTarget|null
     */
    public static function getCurrentYearTarget($salesmanId)
    {
        $currentYear = Carbon::now()->format('Y');
        
        return self::where('salesman_id', $salesmanId)
            ->where('period_type', 'year')
            ->where('period', $currentYear)
            ->first();
    }
    
    /**
     * 获取当前季度目标
     *
     * @param int $salesmanId
     * @return SalesmanTarget|null
     */
    public static function getCurrentQuarterTarget($salesmanId)
    {
        $currentYear = Carbon::now()->format('Y');
        $currentQuarter = 'Q' . Carbon::now()->quarter;
        
        return self::where('salesman_id', $salesmanId)
            ->where('period_type', 'quarter')
            ->where('period', $currentYear . '-' . $currentQuarter)
            ->first();
    }
    
    /**
     * 更新目标完成率
     *
     * @param int $targetId
     * @param float $achievement
     * @return bool
     */
    public static function updateAchievement($targetId, $achievement)
    {
        $target = self::find($targetId);
        
        if (!$target) {
            return false;
        }
        
        $target->achievement = $achievement;
        
        // 更新状态
        if ($achievement >= 100) {
            $target->status = 'completed';
        } else if ($target->end_date < now() && $achievement < 100) {
            $target->status = 'failed';
        } else {
            $target->status = 'in_progress';
        }
        
        return $target->save();
    }
}
