<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class WaterDealer extends Model
{
    // 自动检测是否存在water_db中的原始表，否则使用主数据库中的镜像表
    public function __construct(array $attributes = [])
    {
        // 强制使用水系统数据库
        $this->connection = 'water_db';
        $this->table = 'wb_dealer';
        
        parent::__construct($attributes);
    }
    
    // 主键类型
    protected $keyType = 'string';
    
    // 不自增
    public $incrementing = false;
    
    // 关闭时间戳自动维护
    public $timestamps = false;
    
    // 定义可批量赋值的字段
    protected $fillable = [
        'id',
        'dealer_number',
        'dealer_name',
        'dealer_type',
        'parent_id',
        'card',
        'phone',
        'email',
        'province',
        'city',
        'area',
        'address',
        'status',
        'remark',
        'create_name',
        'create_by',
        'create_date',
        'update_name',
        'update_by',
        'update_date'
    ];

    protected $dates = [
        'create_date',
        'update_date',
    ];

    /**
     * 获取渠道商状态文本
     */
    public function getStatusTextAttribute()
    {
        return $this->status == 'E' ? '启用' : '禁用';
    }

    /**
     * 关联上级渠道商
     */
    public function parent()
    {
        return $this->belongsTo(self::class, 'parent_id', 'id');
    }

    /**
     * 关联下级渠道商
     */
    public function children()
    {
        return $this->hasMany(self::class, 'parent_id', 'id');
    }

    /**
     * 关联销售的设备
     */
    public function salesDevices()
    {
        return $this->hasMany(WaterDevice::class, 'dealer_id_sale', 'id');
    }

    /**
     * 关联管理的设备
     */
    public function managedDevices()
    {
        return $this->hasMany(WaterDevice::class, 'dealer_id', 'id');
    }

    /**
     * 关联客户
     */
    public function clients()
    {
        return $this->hasMany(WaterClient::class, 'client_dealer_id', 'id');
    }

    /**
     * 获取完整地址
     */
    public function getFullAddressAttribute()
    {
        $address = '';
        if (!empty($this->province)) $address .= $this->province;
        if (!empty($this->city)) $address .= $this->city;
        if (!empty($this->area)) $address .= $this->area;
        if (!empty($this->address)) $address .= $this->address;
        
        return $address ?: '未设置地址';
    }

    /**
     * 范围查询：按渠道商编号搜索
     */
    public function scopeByDealerNumber($query, $dealerNumber)
    {
        return $query->where('dealer_number', 'like', "%{$dealerNumber}%");
    }

    /**
     * 范围查询：按渠道商名称搜索
     */
    public function scopeByName($query, $name)
    {
        return $query->where('dealer_name', 'like', "%{$name}%");
    }

    /**
     * 范围查询：按上级渠道商ID筛选
     */
    public function scopeByParentId($query, $parentId)
    {
        return $query->where('parent_id', $parentId);
    }
} 