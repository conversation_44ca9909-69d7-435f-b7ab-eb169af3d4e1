<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class OrderItem extends Model
{
    use HasFactory;
    
    /**
     * 关联的数据表
     *
     * @var string
     */
    protected $table = 'order_items';

    /**
     * 主键
     *
     * @var string
     */
    protected $primaryKey = 'id';
    
    /**
     * 指示模型是否自动维护时间戳
     *
     * @var bool
     */
    public $timestamps = true;

    /**
     * 可以被批量赋值的属性
     *
     * @var array
     */
    protected $fillable = [
        'order_id', 'product_id', 'product_name', 'product_thumbnail',
        'sku', 'product_specs', 'price', 'quantity', 'total_amount',
        'remark', 'is_reviewed', 'refund_status'
    ];

    /**
     * 属性类型转换
     *
     * @var array
     */
    protected $casts = [
        'order_id' => 'integer',
        'product_id' => 'integer',
        'price' => 'float',
        'quantity' => 'integer',
        'total_amount' => 'float',
        'is_reviewed' => 'boolean',
        'refund_status' => 'integer',
        'product_specs' => 'json',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    /**
     * 获取订单
     */
    public function order()
    {
        return $this->belongsTo(Order::class, 'order_id', 'id');
    }

    /**
     * 获取商品
     */
    public function product()
    {
        return $this->belongsTo(Product::class, 'product_id', 'id');
    }

    /**
     * 获取订单项状态文本
     */
    public function getStatusTextAttribute()
    {
        $statusMap = [
            1 => '待付款',
            2 => '待发货',
            3 => '已发货',
            4 => '已完成',
            5 => '申请退款',
            6 => '退款完成',
            7 => '已取消',
            8 => '拒绝退款'
        ];

        return $statusMap[$this->status] ?? '未知状态';
    }
}
