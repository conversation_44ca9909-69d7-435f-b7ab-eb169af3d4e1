<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class AppUserDevice extends Model
{
    use HasFactory;
    
    /**
     * 与模型关联的表名
     */
    protected $table = 'app_user_devices';
    
    /**
     * 可批量赋值的属性
     */
    protected $fillable = [
        'user_id',
        'device_name',
        'device_id',
        'bind_time',
        'status',
        'remark'
    ];
    
    /**
     * 应该被转换成日期的属性
     */
    protected $dates = [
        'bind_time',
        'created_at',
        'updated_at'
    ];
    
    /**
     * 获取此设备关联的用户
     */
    public function user()
    {
        return $this->belongsTo(AppUser::class, 'user_id');
    }
    
    /**
     * 设备状态是否正常
     */
    public function isActive()
    {
        return $this->status === 'active';
    }
}
