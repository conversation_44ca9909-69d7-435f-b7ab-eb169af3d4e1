<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class SalesmanSale extends Model
{
    use HasFactory;
    
    /**
     * 表名
     *
     * @var string
     */
    protected $table = 'salesman_sales';
    
    /**
     * 可批量赋值的属性
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'salesman_id',
        'order_id',
        'amount',
        'commission_rate',
        'commission_amount',
        'product_name',
        'product_id',
        'quantity',
        'customer_name',
        'customer_phone',
        'status',
        'sale_date',
        'remarks',
    ];
    
    /**
     * 应该被转换成原生类型的属性
     *
     * @var array
     */
    protected $casts = [
        'amount' => 'decimal:2',
        'commission_rate' => 'decimal:2',
        'commission_amount' => 'decimal:2',
        'quantity' => 'integer',
        'sale_date' => 'date',
    ];
    
    /**
     * 获取关联的业务员
     */
    public function salesman(): BelongsTo
    {
        return $this->belongsTo(Salesman::class, 'salesman_id');
    }
    
    /**
     * 根据给定的业务员ID和日期范围获取销售数据
     *
     * @param int $salesmanId
     * @param string $startDate
     * @param string $endDate
     * @return array
     */
    public static function getSalesStatsByDateRange($salesmanId, $startDate, $endDate)
    {
        $sales = self::where('salesman_id', $salesmanId)
            ->where('status', 'completed')
            ->whereBetween('sale_date', [$startDate, $endDate])
            ->get();
            
        return [
            'count' => $sales->sum('quantity'),
            'amount' => $sales->sum('amount'),
            'commission' => $sales->sum('commission_amount'),
        ];
    }
    
    /**
     * 获取今日销售数据
     *
     * @param int $salesmanId
     * @return array
     */
    public static function getTodaySales($salesmanId)
    {
        $today = date('Y-m-d');
        return self::getSalesStatsByDateRange($salesmanId, $today, $today);
    }
    
    /**
     * 获取本月销售数据
     *
     * @param int $salesmanId
     * @return array
     */
    public static function getMonthSales($salesmanId)
    {
        $startOfMonth = date('Y-m-01');
        $endOfMonth = date('Y-m-t');
        return self::getSalesStatsByDateRange($salesmanId, $startOfMonth, $endOfMonth);
    }
    
    /**
     * 获取本年销售数据
     *
     * @param int $salesmanId
     * @return array
     */
    public static function getYearSales($salesmanId)
    {
        $startOfYear = date('Y-01-01');
        $endOfYear = date('Y-12-31');
        return self::getSalesStatsByDateRange($salesmanId, $startOfYear, $endOfYear);
    }
}
