<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class AdminNotification extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'content',
        'type',
        'priority',
        'admin_id',
        'sender_id',
        'is_read',
        'read_at',
        'extra_data',
        'action_url',
        'action_text',
        'is_system',
        'expires_at'
    ];

    protected $casts = [
        'is_read' => 'boolean',
        'is_system' => 'boolean',
        'read_at' => 'datetime',
        'expires_at' => 'datetime',
        'extra_data' => 'array'
    ];

    /**
     * 通知类型常量
     */
    const TYPE_INFO = 'info';
    const TYPE_SUCCESS = 'success';
    const TYPE_WARNING = 'warning';
    const TYPE_ERROR = 'error';

    /**
     * 优先级常量
     */
    const PRIORITY_LOW = 'low';
    const PRIORITY_NORMAL = 'normal';
    const PRIORITY_HIGH = 'high';
    const PRIORITY_URGENT = 'urgent';

    /**
     * 接收者关联
     */
    public function admin(): BelongsTo
    {
        return $this->belongsTo(Admin::class, 'admin_id');
    }

    /**
     * 发送者关联
     */
    public function sender(): BelongsTo
    {
        return $this->belongsTo(Admin::class, 'sender_id');
    }

    /**
     * 标记为已读
     */
    public function markAsRead(): bool
    {
        if (!$this->is_read) {
            return $this->update([
                'is_read' => true,
                'read_at' => Carbon::now()
            ]);
        }
        return true;
    }

    /**
     * 标记为未读
     */
    public function markAsUnread(): bool
    {
        return $this->update([
            'is_read' => false,
            'read_at' => null
        ]);
    }

    /**
     * 检查是否已过期
     */
    public function isExpired(): bool
    {
        return $this->expires_at && Carbon::now()->isAfter($this->expires_at);
    }

    /**
     * 获取未读通知数量
     */
    public static function getUnreadCount($adminId = null): int
    {
        $query = static::where('is_read', false);
        
        if ($adminId) {
            $query->where(function ($q) use ($adminId) {
                $q->where('admin_id', $adminId)
                  ->orWhereNull('admin_id'); // 全体通知
            });
        }
        
        // 排除已过期的通知
        $query->where(function ($q) {
            $q->whereNull('expires_at')
              ->orWhere('expires_at', '>', Carbon::now());
        });
        
        return $query->count();
    }

    /**
     * 获取管理员的通知列表
     */
    public static function getAdminNotifications($adminId, $limit = 10, $onlyUnread = false)
    {
        $query = static::where(function ($q) use ($adminId) {
            $q->where('admin_id', $adminId)
              ->orWhereNull('admin_id'); // 全体通知
        });

        if ($onlyUnread) {
            $query->where('is_read', false);
        }

        // 排除已过期的通知
        $query->where(function ($q) {
            $q->whereNull('expires_at')
              ->orWhere('expires_at', '>', Carbon::now());
        });

        return $query->with(['sender'])
                    ->orderBy('priority', 'desc')
                    ->orderBy('created_at', 'desc')
                    ->limit($limit)
                    ->get();
    }

    /**
     * 创建系统通知
     */
    public static function createSystemNotification($title, $content, $type = self::TYPE_INFO, $priority = self::PRIORITY_NORMAL, $adminId = null, $extraData = null)
    {
        return static::create([
            'title' => $title,
            'content' => $content,
            'type' => $type,
            'priority' => $priority,
            'admin_id' => $adminId,
            'is_system' => true,
            'extra_data' => $extraData
        ]);
    }

    /**
     * 批量标记为已读
     */
    public static function markAllAsRead($adminId)
    {
        return static::where(function ($q) use ($adminId) {
            $q->where('admin_id', $adminId)
              ->orWhereNull('admin_id');
        })->where('is_read', false)
          ->update([
              'is_read' => true,
              'read_at' => Carbon::now()
          ]);
    }

    /**
     * 清理过期通知
     */
    public static function cleanExpiredNotifications()
    {
        return static::where('expires_at', '<', Carbon::now())->delete();
    }
}
