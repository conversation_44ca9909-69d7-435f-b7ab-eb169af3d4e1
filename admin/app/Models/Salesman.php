<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Salesman extends Model
{
    use HasFactory;
    
    /**
     * 表名
     *
     * @var string
     */
    protected $table = 'salesmen';
    
    /**
     * 可批量赋值的属性
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'employee_id',
        'title',
        'department',
        'region',
        'manager',
        'manager_id',
        'status',
        'extra_info',
        'name',
        'phone',
        'code',
        'parent_id',
        'commission_rate',
        'remark'
    ];
    
    /**
     * 应该被转换成原生类型的属性
     *
     * @var array
     */
    protected $casts = [
        'status' => 'integer',
        'commission_rate' => 'float',
    ];
    
    /**
     * 获取关联的用户
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(AppUser::class, 'user_id');
    }
    
    /**
     * 获取关联的上级业务员
     */
    public function parent(): BelongsTo
    {
        return $this->belongsTo(Salesman::class, 'parent_id');
    }
    
    /**
     * 获取下级业务员
     */
    public function children(): HasMany
    {
        return $this->hasMany(Salesman::class, 'parent_id');
    }
    
    /**
     * 获取销售记录
     */
    public function sales(): HasMany
    {
        return $this->hasMany(SalesmanSale::class, 'salesman_id');
    }
    
    /**
     * 获取提成记录
     */
    public function commissions(): HasMany
    {
        return $this->hasMany(SalesmanCommission::class, 'salesman_id');
    }
    
    /**
     * 获取业务员的销售目标
     */
    public function targets()
    {
        return $this->hasMany(SalesmanTarget::class);
    }
    
    /**
     * 获取业务员当前月份的销售目标
     */
    public function getCurrentMonthTarget()
    {
        return SalesmanTarget::getCurrentMonthTarget($this->id);
    }
    
    /**
     * 获取业务员当前年份的销售目标
     */
    public function getCurrentYearTarget()
    {
        return SalesmanTarget::getCurrentYearTarget($this->id);
    }
    
    /**
     * 获取业务员当前季度的销售目标
     */
    public function getCurrentQuarterTarget()
    {
        return SalesmanTarget::getCurrentQuarterTarget($this->id);
    }
    
    /**
     * 获取客户
     */
    public function customers(): HasMany
    {
        return $this->hasMany(SalesmanCustomer::class, 'salesman_id');
    }
    
    /**
     * 获取今日销售统计
     */
    public function getTodaySalesAttribute()
    {
        return $this->sales()
            ->where('sale_date', date('Y-m-d'))
            ->sum('quantity');
    }
    
    /**
     * 获取当月销售统计
     */
    public function getMonthlySalesAttribute()
    {
        $startOfMonth = date('Y-m-01');
        $endOfMonth = date('Y-m-t');
        
        return $this->sales()
            ->whereBetween('sale_date', [$startOfMonth, $endOfMonth])
            ->sum('quantity');
    }
    
    /**
     * 获取累计销售统计
     */
    public function getTotalSalesAttribute()
    {
        return $this->sales()->sum('quantity');
    }
}
