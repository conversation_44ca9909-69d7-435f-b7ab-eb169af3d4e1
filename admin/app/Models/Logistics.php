<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Logistics extends Model
{
    use HasFactory;
    
    /**
     * 关联的数据表
     *
     * @var string
     */
    protected $table = 'ddg_order_delivery';

    /**
     * 主键
     *
     * @var string
     */
    protected $primaryKey = 'id';
    
    /**
     * 指示模型是否自动维护时间戳
     *
     * @var bool
     */
    public $timestamps = false;

    /**
     * 可以被批量赋值的属性
     *
     * @var array
     */
    protected $fillable = [
        'number', 'list', 'expName', 'takeTime', 'deliverystatus'
    ];

    /**
     * 属性类型转换
     *
     * @var array
     */
    protected $casts = [
        'deliverystatus' => 'integer',
    ];

    /**
     * 获取配送状态文本
     */
    public function getDeliveryStatusTextAttribute()
    {
        $statusMap = [
            0 => '快递收件(揽件)',
            1 => '在途中',
            2 => '正在派件',
            3 => '已签收',
            4 => '派送失败',
            5 => '疑难件',
            6 => '退件签收'
        ];

        return $statusMap[$this->deliverystatus] ?? '未知状态';
    }
    
    /**
     * 获取相关订单项
     */
    public function orderItems()
    {
        return $this->hasMany(OrderItem::class, 'ship_area_id', 'number');
    }
}
