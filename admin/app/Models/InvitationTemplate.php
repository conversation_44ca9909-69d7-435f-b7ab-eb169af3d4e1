<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class InvitationTemplate extends Model
{
    use HasFactory;

    /**
     * 可批量赋值的属性
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'title',
        'content',
        'background_image',
        'background_music',
        'map_location',
        'map_address',
        'map_longitude',
        'map_latitude',
        'event_time',
        'extra_info',
        'is_active',
    ];

    /**
     * 应该转换的属性
     *
     * @var array<string, string>
     */
    protected $casts = [
        'is_active' => 'boolean',
        'event_time' => 'datetime',
        'extra_info' => 'array',
    ];

    /**
     * 获取与此模板关联的所有访客
     */
    public function guests()
    {
        return $this->hasMany(InvitationGuest::class, 'template_id');
    }

    /**
     * 获取已注册的访客
     */
    public function registeredGuests()
    {
        return $this->guests()->where('status', 'registered');
    }

    /**
     * 获取参与的访客（包含未注册信息的）
     */
    public function joinedGuests()
    {
        return $this->guests()->where('status', 'joined');
    }
} 