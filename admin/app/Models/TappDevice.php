<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;

class TappDevice extends Model
{
    use HasFactory;
    
    protected $table = 'tapp_devices';
    
    protected $fillable = [
        'device_id',
        'device_number',
        'device_type',
        'device_status',
        'raw_water_value',
        'purification_water_value',
        'billing_mode',
        'surplus_flow',
        'remaining_days',
        'cumulative_filtration_flow',
        'water_quality_grade',
        'network_status',
        'dealer_id',
        'dealer_id_sale',
        'client_id',
        'app_user_id',
        'app_user_name',
        'activate_date',
        'iccid',
        'imei',
        'status',
        'remark',
        'create_date',
        'update_date',
        'longitude', 
        'latitude',
        'address',
        'f1_flux',
        'f1_flux_max',
        'f2_flux',
        'f2_flux_max',
        'f3_flux',
        'f3_flux_max',
        'f4_flux',
        'f4_flux_max',
        'f5_flux',
        'f5_flux_max',
        'filter_date',
        'signal_intensity',
        'service_end_time',
        'cod_after',
        'cod_before',
        'toc_after',
        'toc_before',
        'client_name',
        'client_phone',
        'client_address',
        'dealer_number',
        'dealer_name',
        'last_sync_time',
        'is_sync_success',
        'is_self_use',
        'is_water_point',
    ];
    
    protected $dates = [
        'activate_date',
        'create_date',
        'update_date',
        'filter_date',
        'service_end_time',
        'last_sync_time',
        'created_at',
        'updated_at',
    ];
    
    /**
     * 获取设备状态文本描述
     */
    public function getStatusTextAttribute()
    {
        $statusMap = [
            'E' => '启用',
            'D' => '禁用',
            'maintenance' => '维护中',
        ];
        
        return $statusMap[$this->status] ?? '未知';
    }
    
    /**
     * 获取网络状态文本描述
     */
    public function getNetworkStatusTextAttribute()
    {
        return $this->network_status == '1' ? '在线' : '离线';
    }
    
    /**
     * 计算设备当前水量百分比
     */
    public function getCurrentWaterLevelAttribute()
    {
        // 原水值和净水值
        $rawWater = floatval($this->raw_water_value);
        $purifiedWater = floatval($this->purification_water_value);
        
        if ($rawWater <= 0) {
            return 100; // 默认满水
        }
        
        $level = ($purifiedWater / $rawWater) * 100;
        
        // 确保返回值在0-100范围内
        return max(0, min(100, intval($level)));
    }
    
    /**
     * 计算PP棉滤芯剩余寿命百分比
     */
    public function getF1LifePercentAttribute()
    {
        if (empty($this->f1_flux_max) || $this->f1_flux_max <= 0) {
            return 100; // 如果没有设置最大值或最大值无效，默认为100%
        }
        
        $percent = ($this->f1_flux / $this->f1_flux_max) * 100;
        
        // 确保返回值在0-100范围内
        return max(0, min(100, intval($percent)));
    }
    
    /**
     * 计算活性炭滤芯剩余寿命百分比
     */
    public function getF2LifePercentAttribute()
    {
        if (empty($this->f2_flux_max) || $this->f2_flux_max <= 0) {
            return 100; // 如果没有设置最大值或最大值无效，默认为100%
        }
        
        $percent = ($this->f2_flux / $this->f2_flux_max) * 100;
        
        // 确保返回值在0-100范围内
        return max(0, min(100, intval($percent)));
    }
    
    /**
     * 计算RO反渗透滤芯剩余寿命百分比
     */
    public function getF3LifePercentAttribute()
    {
        if (empty($this->f3_flux_max) || $this->f3_flux_max <= 0) {
            return 100; // 如果没有设置最大值或最大值无效，默认为100%
        }
        
        $percent = ($this->f3_flux / $this->f3_flux_max) * 100;
        
        // 确保返回值在0-100范围内
        return max(0, min(100, intval($percent)));
    }
    
    /**
     * 范围查询：按网络状态筛选
     */
    public function scopeByNetworkStatus($query, $status)
    {
        return $query->where('network_status', $status);
    }
    
    /**
     * 范围查询：按激活状态筛选
     */
    public function scopeActivated($query)
    {
        return $query->whereNotNull('activate_date');
    }
    
    /**
     * 范围查询：按渠道商编号筛选
     */
    public function scopeByDealerNumber($query, $dealerNumber)
    {
        return $query->where('dealer_number', $dealerNumber);
    }
    
    /**
     * 获取设备所属的APP用户（VIP用户）
     */
    public function appUser()
    {
        return $this->belongsTo(AppUser::class, 'app_user_id');
    }

    /**
     * 获取设备的充值订单信息
     * 从净水器数据库的wb_order表查询设备的充值续费订单
     */
    public function getRechargeOrders()
    {
        // 如果设备号为空，则返回空数组
        if (empty($this->device_number)) {
            return [];
        }
        
        try {
            // 使用 water_db 连接查询净水器数据库
            $orders = DB::connection('water_db')
                ->table('wb_order')
                ->where('device_number', $this->device_number)
                ->where('order_type', '01') // 01表示充值订单
                ->where('order_status', '103') // 103表示支付成功
                ->orderBy('create_date', 'desc')
                ->get();
                
            return $orders;
        } catch (\Exception $e) {
            \Log::error('获取设备充值订单失败: ' . $e->getMessage(), [
                'device_number' => $this->device_number,
                'trace' => $e->getTraceAsString()
            ]);
            
            return [];
        }
    }

    /**
     * 获取设备最近一次充值信息
     */
    public function getLastRechargeInfo()
    {
        $orders = $this->getRechargeOrders();
        
        if (count($orders) > 0) {
            return $orders[0];
        }
        
        return null;
    }

    /**
     * 计算设备的提成金额
     * 根据订单金额和提成比例计算
     * 
     * @param float $commissionRate 提成比例，默认0.3（30%）
     * @return float 提成金额
     */
    public function calculateCommissionAmount($commissionRate = 0.3)
    {
        $lastRecharge = $this->getLastRechargeInfo();
        
        if (!$lastRecharge) {
            return 0;
        }
        
        // 如果有订单金额，则按提成比例计算
        $orderAmount = floatval($lastRecharge->money);
        
        // 计算提成金额
        return round($orderAmount * $commissionRate, 2);
    }
}
