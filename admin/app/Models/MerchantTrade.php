<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class MerchantTrade extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'trade_no',
        'merchant_id',
        'merchant_name',
        'payment_method',
        'amount',
        'fee',
        'actual_amount',
        'trade_type',
        'status',
        'pay_time',
        'payer_account',
        'payer_name',
        'transaction_id',
        'device_info',
        'location',
        'remark',
        'operator',
        'extra_data',
        'refund_trade_no',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'amount' => 'decimal:2',
        'fee' => 'decimal:2',
        'actual_amount' => 'decimal:2',
        'pay_time' => 'datetime',
        'extra_data' => 'json',
    ];

    /**
     * 获取关联的商户
     */
    public function merchant()
    {
        return $this->belongsTo(Merchant::class, 'merchant_id', 'merchant_id');
    }

    /**
     * 创建新的交易记录
     */
    public static function createTrade($data)
    {
        // 设置实际收款金额
        if (!isset($data['actual_amount'])) {
            $data['actual_amount'] = $data['amount'] - ($data['fee'] ?? 0);
        }

        return self::create($data);
    }

    /**
     * 获取退款订单
     */
    public function refundOrder()
    {
        if ($this->trade_type === 'payment') {
            return $this->hasOne(self::class, 'refund_trade_no', 'trade_no');
        }
        return null;
    }

    /**
     * 获取原始订单（如果这是退款订单）
     */
    public function originalOrder()
    {
        if ($this->trade_type === 'refund') {
            return $this->belongsTo(self::class, 'refund_trade_no', 'trade_no');
        }
        return null;
    }

    /**
     * 获取交易状态文本
     */
    public function getStatusTextAttribute()
    {
        $statusMap = [
            'pending' => '处理中',
            'success' => '成功',
            'failed' => '失败',
            'cancelled' => '已取消'
        ];

        return $statusMap[$this->status] ?? '未知状态';
    }

    /**
     * 获取支付方式文本
     */
    public function getPaymentMethodTextAttribute()
    {
        $methodMap = [
            'alipay' => '支付宝',
            'wechat' => '微信支付',
            'card' => '银行卡',
            'cash' => '现金'
        ];

        return $methodMap[$this->payment_method] ?? $this->payment_method;
    }

    /**
     * 获取交易类型文本
     */
    public function getTradeTypeTextAttribute()
    {
        $typeMap = [
            'payment' => '收款',
            'refund' => '退款'
        ];

        return $typeMap[$this->trade_type] ?? $this->trade_type;
    }

    /**
     * 同步交易数据
     * 从支付系统同步交易数据到本地
     */
    public static function syncFromPaymentSystem($merchantId = null, $startDate = null, $endDate = null)
    {
        // 这个方法将在数据同步脚本中实现
    }
} 