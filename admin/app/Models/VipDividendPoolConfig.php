<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class VipDividendPoolConfig extends Model
{
    use HasFactory;

    /**
     * 表名
     */
    protected $table = 'vip_dividend_pool_configs';

    /**
     * 可批量赋值的属性
     */
    protected $fillable = [
        'config_name',
        'config_key',
        'config_type',
        'unit_contribution',
        'contribution_rounds',
        'level_distribution',
        'qualification_rules',
        'is_active',
        'effective_from',
        'effective_to',
        'created_by',
        'remark'
    ];

    /**
     * 属性转换
     */
    protected $casts = [
        'unit_contribution' => 'decimal:2',
        'level_distribution' => 'json',
        'qualification_rules' => 'json',
        'is_active' => 'boolean',
        'effective_from' => 'date',
        'effective_to' => 'date',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * 配置类型常量
     */
    const TYPE_VIP_RECRUITMENT = 'vip_recruitment';
    const TYPE_DEVICE_RECHARGE = 'device_recharge';
    const TYPE_GLOBAL = 'global';

    /**
     * 配置类型映射
     */
    public static function getTypeMap()
    {
        return [
            self::TYPE_VIP_RECRUITMENT => 'VIP招募分红',
            self::TYPE_DEVICE_RECHARGE => '充值套餐分红',
            self::TYPE_GLOBAL => '全局配置',
        ];
    }

    /**
     * 获取配置类型文本
     */
    public function getTypeTextAttribute()
    {
        return self::getTypeMap()[$this->config_type] ?? '未知类型';
    }

    /**
     * 获取状态文本
     */
    public function getStatusTextAttribute()
    {
        return $this->is_active ? '启用' : '禁用';
    }

    /**
     * 获取状态颜色
     */
    public function getStatusColorAttribute()
    {
        return $this->is_active ? 'success' : 'danger';
    }

    /**
     * 获取创建人
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * 检查配置是否在有效期内
     */
    public function isEffective($date = null)
    {
        $checkDate = $date ? Carbon::parse($date) : now();
        
        if ($checkDate->lt($this->effective_from)) {
            return false;
        }
        
        if ($this->effective_to && $checkDate->gt($this->effective_to)) {
            return false;
        }
        
        return $this->is_active;
    }

    /**
     * 获取等级分配详情
     */
    public function getLevelDistributionDetailAttribute()
    {
        if (!$this->level_distribution) {
            return null;
        }

        $distribution = $this->level_distribution;
        return [
            'junior' => [
                'ratio' => $distribution['junior'] ?? 0.4,
                'percent' => round(($distribution['junior'] ?? 0.4) * 100, 1) . '%'
            ],
            'middle' => [
                'ratio' => $distribution['middle'] ?? 0.3,
                'percent' => round(($distribution['middle'] ?? 0.3) * 100, 1) . '%'
            ],
            'senior' => [
                'ratio' => $distribution['senior'] ?? 0.3,
                'percent' => round(($distribution['senior'] ?? 0.3) * 100, 1) . '%'
            ]
        ];
    }

    /**
     * 获取达标条件说明
     */
    public function getQualificationRulesTextAttribute()
    {
        if (!$this->qualification_rules) {
            return '无达标条件';
        }

        $rules = $this->qualification_rules;
        $descriptions = [];

        if ($this->config_type === self::TYPE_VIP_RECRUITMENT) {
            if (isset($rules['junior']['team_vip_count'])) {
                $descriptions[] = "初级：团队VIP满{$rules['junior']['team_vip_count']}人";
            }
            if (isset($rules['middle']['team_vip_count'])) {
                $descriptions[] = "中级：团队VIP满{$rules['middle']['team_vip_count']}人";
            }
            if (isset($rules['senior']['team_vip_count'])) {
                $senior = "高级：团队VIP满{$rules['senior']['team_vip_count']}人";
                if (isset($rules['senior']['month_direct_vip_required']) && $rules['senior']['month_direct_vip_required']) {
                    $senior .= "且本月直推≠0";
                }
                $descriptions[] = $senior;
            }
        } elseif ($this->config_type === self::TYPE_DEVICE_RECHARGE) {
            if (isset($rules['junior']['team_device_count'])) {
                $descriptions[] = "初级：团队充值满{$rules['junior']['team_device_count']}台";
            }
            if (isset($rules['middle']['team_device_count'])) {
                $descriptions[] = "中级：团队充值满{$rules['middle']['team_device_count']}台";
            }
            if (isset($rules['senior']['team_device_count'])) {
                $senior = "高级：团队充值满{$rules['senior']['team_device_count']}台";
                if (isset($rules['senior']['month_direct_device_required']) && $rules['senior']['month_direct_device_required']) {
                    $senior .= "且本月直推≠0";
                }
                $descriptions[] = $senior;
            }
        }

        return implode('；', $descriptions);
    }

    /**
     * 范围查询：按配置类型
     */
    public function scopeByType($query, $type)
    {
        return $query->where('config_type', $type);
    }

    /**
     * 范围查询：启用的配置
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * 范围查询：有效期内的配置
     */
    public function scopeEffective($query, $date = null)
    {
        $checkDate = $date ? Carbon::parse($date) : now();
        
        return $query->where('is_active', true)
                    ->where('effective_from', '<=', $checkDate)
                    ->where(function($q) use ($checkDate) {
                        $q->whereNull('effective_to')
                          ->orWhere('effective_to', '>=', $checkDate);
                    });
    }

    /**
     * 范围查询：VIP招募配置
     */
    public function scopeVipRecruitment($query)
    {
        return $query->where('config_type', self::TYPE_VIP_RECRUITMENT);
    }

    /**
     * 范围查询：充值套餐配置
     */
    public function scopeDeviceRecharge($query)
    {
        return $query->where('config_type', self::TYPE_DEVICE_RECHARGE);
    }
} 