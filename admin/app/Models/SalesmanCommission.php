<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class SalesmanCommission extends Model
{
    use HasFactory;
    
    /**
     * 表名
     *
     * @var string
     */
    protected $table = 'salesman_commissions';
    
    /**
     * 可批量赋值的属性
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'salesman_id',
        'amount',
        'period',
        'start_date',
        'end_date',
        'status',
        'payment_date',
        'remarks',
    ];
    
    /**
     * 应该被转换成原生类型的属性
     *
     * @var array
     */
    protected $casts = [
        'amount' => 'decimal:2',
        'start_date' => 'date',
        'end_date' => 'date',
        'payment_date' => 'date',
    ];
    
    /**
     * 获取关联的业务员
     */
    public function salesman(): BelongsTo
    {
        return $this->belongsTo(Salesman::class, 'salesman_id');
    }
    
    /**
     * 获取待结算提成总额
     *
     * @param int $salesmanId
     * @return float
     */
    public static function getPendingCommissionAmount($salesmanId)
    {
        return self::where('salesman_id', $salesmanId)
            ->where('status', 'pending')
            ->sum('amount');
    }
    
    /**
     * 获取本月提成总额
     *
     * @param int $salesmanId
     * @return float
     */
    public static function getMonthCommissionAmount($salesmanId)
    {
        $currentMonth = date('Y-m');
        
        return self::where('salesman_id', $salesmanId)
            ->where('period', $currentMonth)
            ->sum('amount');
    }
    
    /**
     * 获取累计提成总额
     *
     * @param int $salesmanId
     * @return float
     */
    public static function getTotalCommissionAmount($salesmanId)
    {
        return self::where('salesman_id', $salesmanId)
            ->sum('amount');
    }
}
