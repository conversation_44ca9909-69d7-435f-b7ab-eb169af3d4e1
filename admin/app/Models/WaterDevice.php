<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class WaterDevice extends Model
{
    // 自动检测是否存在water_db中的原始表，否则使用主数据库中的镜像表
    public function __construct(array $attributes = [])
    {
        // 强制使用水系统数据库
        $this->connection = 'water_db';
        $this->table = 'wb_device';
        
        parent::__construct($attributes);
    }
    
    // 主键类型
    protected $keyType = 'string';
    
    // 不自增
    public $incrementing = false;
    
    // 关闭时间戳自动维护
    public $timestamps = false;
    
    // 定义可批量赋值的字段
    protected $fillable = [
        'id',
        'device_number',
        'device_type',
        'device_status',
        'raw_water_value',
        'purification_water_value',
        'billing_mode',
        'surplus_flow',
        'remaining_days',
        'cumulative_filtration_flow',
        'water_quality_grade',
        'network_status',
        'product_id',
        'dealer_id',
        'dealer_id_sale',
        'cash_pledge',
        'client_id',
        'activate_date',
        'iccid',
        'imei',
        'bind_status',
        'status',
        'remark',
        'create_name',
        'create_by',
        'create_date',
        'update_name',
        'update_by',
        'update_date',
        'address',
        'longitude',
        'latitude',
        // 滤芯相关字段
        'f1_flux',
        'f1_flux_max',
        'f2_flux',
        'f2_flux_max',
        'f3_flux',
        'f3_flux_max',
        'f4_flux',
        'f4_flux_max',
        'f5_flux',
        'f5_flux_max',
        'filter_date'
    ];

    protected $dates = [
        'create_date',
        'update_date',
        'activate_date',
        'service_end_time',
        'filter_date'
    ];

    // 关联客户
    public function client()
    {
        return $this->belongsTo(WaterClient::class, 'client_id', 'id');
    }
    
    // 关联渠道商
    public function dealer()
    {
        return $this->belongsTo(WaterDealer::class, 'dealer_id', 'id');
    }
    
    // 关联销售渠道商
    public function saleDealer()
    {
        return $this->belongsTo(WaterDealer::class, 'dealer_id_sale', 'id');
    }
    
    // 关联设备制水数据
    public function waterData()
    {
        return $this->hasMany(WaterDeviceData::class, 'device_number', 'device_number');
    }
    
    // 获取最新的制水数据
    public function latestWaterData()
    {
        return $this->waterData()->orderBy('create_date', 'desc')->first();
    }
    
    /**
     * 获取设备实际显示编号（8800开头的编号）
     */
    public function getDisplayNumberAttribute()
    {
        return $this->device_number;
    }
    
    /**
     * 获取设备内部ID（UUID格式）
     */
    public function getInternalIdAttribute()
    {
        return $this->id;
    }
    
    /**
     * 获取IMEI号码
     */
    public function getImeiNumberAttribute()
    {
        return $this->imei;
    }
    
    /**
     * 获取SIM卡ICCID
     */
    public function getIccidNumberAttribute()
    {
        return $this->iccid;
    }
    
    /**
     * 获取设备网络状态
     */
    public function getNetworkStatusTextAttribute()
    {
        $statusMap = [
            'online' => '在线',
            'offline' => '离线',
            'abnormal' => '异常'
        ];
        
        return isset($statusMap[$this->network_status]) ? $statusMap[$this->network_status] : '未知';
    }
    
    /**
     * 获取设备状态文本
     */
    public function getStatusTextAttribute()
    {
        $statusMap = [
            'E' => '启用',
            'D' => '禁用',
            'online' => '在线',
            'offline' => '离线',
            'maintenance' => '维护中'
        ];
        
        return isset($statusMap[$this->status]) ? $statusMap[$this->status] : '未知';
    }
    
    /**
     * 获取设备绑定状态
     */
    public function getBindStatusTextAttribute()
    {
        $bindMap = [
            '0' => '未绑定',
            '1' => '已绑定',
            '' => '未知'
        ];
        
        return $bindMap[$this->bind_status] ?? '未知';
    }
    
    /**
     * 范围查询：按设备编号搜索
     */
    public function scopeByDeviceNumber($query, $deviceNumber)
    {
        return $query->where('device_number', 'like', "%{$deviceNumber}%");
    }
    
    /**
     * 范围查询：按设备ID搜索
     */
    public function scopeByDeviceId($query, $deviceId)
    {
        return $query->where('id', $deviceId);
    }
    
    /**
     * 范围查询：按激活状态筛选
     */
    public function scopeActivated($query)
    {
        return $query->whereNotNull('activate_date');
    }
    
    /**
     * 范围查询：按状态筛选
     */
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }
    
    /**
     * 范围查询：按IMEI号搜索
     */
    public function scopeByImei($query, $imei)
    {
        return $query->where('imei', 'like', "%{$imei}%");
    }
    
    /**
     * 范围查询：按网络状态筛选
     */
    public function scopeByNetworkStatus($query, $status)
    {
        return $query->where('network_status', $status);
    }
    
    /**
     * 范围查询：按客户ID筛选
     */
    public function scopeByClientId($query, $clientId)
    {
        return $query->where('client_id', $clientId);
    }
    
    /**
     * 范围查询：按渠道商ID筛选
     */
    public function scopeByDealerId($query, $dealerId)
    {
        return $query->where('dealer_id', $dealerId)
                     ->orWhere('dealer_id_sale', $dealerId);
    }
    
    /**
     * 计算设备当前水量百分比
     */
    public function getCurrentWaterLevelAttribute()
    {
        // 原水值和净水值
        $rawWater = floatval($this->raw_water_value);
        $purifiedWater = floatval($this->purification_water_value);
        
        if ($rawWater <= 0) {
            return 100; // 默认满水
        }
        
        $level = ($purifiedWater / $rawWater) * 100;
        
        // 确保返回值在0-100范围内
        return max(0, min(100, intval($level)));
    }
    
    /**
     * 获取设备过去7天的制水统计
     */
    public function getWaterProductionTrendAttribute()
    {
        // 尝试获取制水统计，如果失败返回空数组
        try {
            return WaterDeviceData::getDailyWaterProduction($this->device_number);
        } catch (\Exception $e) {
            return [];
        }
    }
    
    /**
     * 获取设备当天制水量
     */
    public function getTodayWaterProductionAttribute()
    {
        // 尝试获取当日制水量，如果失败返回0
        try {
            return WaterDeviceData::getTodayWaterProduction($this->device_number);
        } catch (\Exception $e) {
            return 0;
        }
    }
    
    /**
     * 计算设备的提成金额
     * 根据订单金额和提成比例计算
     * 
     * @param float $commissionRate 提成比例，默认0.3（30%）
     * @return float 提成金额
     */
    public function calculateCommissionAmount($commissionRate = 0.3)
    {
        $lastRecharge = $this->getLastRechargeInfo();
        
        if (!$lastRecharge) {
            return 0;
        }
        
        // 如果有订单金额，则按提成比例计算
        $orderAmount = floatval($lastRecharge->money);
        
        // 计算提成金额
        return round($orderAmount * $commissionRate, 2);
    }
    
    /**
     * 计算PP棉滤芯剩余寿命百分比
     */
    public function getF1LifePercentAttribute()
    {
        $flux = floatval($this->f1_flux ?? 0);
        $fluxMax = floatval($this->f1_flux_max ?? 0);
        
        if ($fluxMax <= 0) {
            return 100; // 如果没有设置最大值，默认为100%
        }
        
        $percent = ($flux / $fluxMax) * 100;
        
        // 确保返回值在0-100范围内
        return max(0, min(100, round($percent, 1)));
    }
    
    /**
     * 计算活性炭滤芯剩余寿命百分比
     */
    public function getF2LifePercentAttribute()
    {
        $flux = floatval($this->f2_flux ?? 0);
        $fluxMax = floatval($this->f2_flux_max ?? 0);
        
        if ($fluxMax <= 0) {
            return 100; // 如果没有设置最大值，默认为100%
        }
        
        $percent = ($flux / $fluxMax) * 100;
        
        // 确保返回值在0-100范围内
        return max(0, min(100, round($percent, 1)));
    }
    
    /**
     * 计算RO反渗透滤芯剩余寿命百分比
     */
    public function getF3LifePercentAttribute()
    {
        $flux = floatval($this->f3_flux ?? 0);
        $fluxMax = floatval($this->f3_flux_max ?? 0);
        
        if ($fluxMax <= 0) {
            return 100; // 如果没有设置最大值，默认为100%
        }
        
        $percent = ($flux / $fluxMax) * 100;
        
        // 确保返回值在0-100范围内
        return max(0, min(100, round($percent, 1)));
    }
    
    /**
     * 计算第四级滤芯剩余寿命百分比
     */
    public function getF4LifePercentAttribute()
    {
        $flux = floatval($this->f4_flux ?? 0);
        $fluxMax = floatval($this->f4_flux_max ?? 0);
        
        if ($fluxMax <= 0) {
            return 100; // 如果没有设置最大值，默认为100%
        }
        
        $percent = ($flux / $fluxMax) * 100;
        
        // 确保返回值在0-100范围内
        return max(0, min(100, round($percent, 1)));
    }
    
    /**
     * 计算第五级滤芯剩余寿命百分比
     */
    public function getF5LifePercentAttribute()
    {
        $flux = floatval($this->f5_flux ?? 0);
        $fluxMax = floatval($this->f5_flux_max ?? 0);
        
        if ($fluxMax <= 0) {
            return 100; // 如果没有设置最大值，默认为100%
        }
        
        $percent = ($flux / $fluxMax) * 100;
        
        // 确保返回值在0-100范围内
        return max(0, min(100, round($percent, 1)));
    }
    
    /**
     * 检查是否有滤芯需要更换预警
     * 当滤芯使用量超过90%时发出预警
     */
    public function getFilterAlertAttribute()
    {
        $alertFilters = [];
        
        // 检查各级滤芯
        if ($this->f1_life_percent >= 90) {
            $alertFilters[] = [
                'level' => 1,
                'name' => 'PP棉滤芯',
                'percent' => $this->f1_life_percent,
                'status' => 'warning'
            ];
        }
        
        if ($this->f2_life_percent >= 90) {
            $alertFilters[] = [
                'level' => 2,
                'name' => '活性炭滤芯',
                'percent' => $this->f2_life_percent,
                'status' => 'warning'
            ];
        }
        
        if ($this->f3_life_percent >= 90) {
            $alertFilters[] = [
                'level' => 3,
                'name' => 'RO反渗透滤芯',
                'percent' => $this->f3_life_percent,
                'status' => 'warning'
            ];
        }
        
        if ($this->f4_life_percent >= 90) {
            $alertFilters[] = [
                'level' => 4,
                'name' => '第四级滤芯',
                'percent' => $this->f4_life_percent,
                'status' => 'warning'
            ];
        }
        
        if ($this->f5_life_percent >= 90) {
            $alertFilters[] = [
                'level' => 5,
                'name' => '第五级滤芯',
                'percent' => $this->f5_life_percent,
                'status' => 'warning'
            ];
        }
        
        return $alertFilters;
    }
    
    /**
     * 检查是否有滤芯预警
     */
    public function getHasFilterAlertAttribute()
    {
        return count($this->filter_alert) > 0;
    }
}
