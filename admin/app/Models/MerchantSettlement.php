<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class MerchantSettlement extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'settlement_no',
        'merchant_id',
        'merchant_name',
        'amount',
        'fee',
        'actual_amount',
        'status',
        'request_time',
        'finish_time',
        'bank_name',
        'bank_account',
        'bank_holder',
        'bank_branch',
        'settlement_period',
        'remark',
        'fail_reason',
        'operator',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'amount' => 'decimal:2',
        'fee' => 'decimal:2',
        'actual_amount' => 'decimal:2',
        'request_time' => 'datetime',
        'finish_time' => 'datetime',
    ];

    /**
     * 获取关联的商户
     */
    public function merchant()
    {
        return $this->belongsTo(Merchant::class, 'merchant_id', 'merchant_id');
    }

    /**
     * 同步结算记录
     * 从支付系统同步结算记录数据到本地
     */
    public static function syncFromPaymentSystem($merchantId = null, $startDate = null, $endDate = null)
    {
        // 这个方法将在数据同步脚本中实现
        // 从支付系统同步结算记录，仅做数据展示，不处理实际资金
    }

    /**
     * 获取结算状态文本
     */
    public function getStatusTextAttribute()
    {
        $statusMap = [
            'pending' => '待处理',
            'processing' => '处理中',
            'success' => '成功',
            'failed' => '失败'
        ];

        return $statusMap[$this->status] ?? '未知状态';
    }
} 