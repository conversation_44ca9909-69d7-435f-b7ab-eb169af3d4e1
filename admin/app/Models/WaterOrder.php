<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class WaterOrder extends Model
{
    // 指定连接配置
    protected $connection = 'water_db';
    
    // 指定表名
    protected $table = 'wb_order';
    
    // 主键类型
    protected $keyType = 'string';
    
    // 不自增
    public $incrementing = false;
    
    // 关闭时间戳自动维护
    public $timestamps = false;
    
    // 定义可批量赋值的字段
    protected $fillable = [
        'id',
        'order_number',
        'order_type',
        'client_id',
        'dealer_id',
        'set_meal_id',
        'device_id',
        'product_id',
        'order_status',
        'billing_mode',
        'time_gross',
        'money',
        'flow',
        'device_number',
        'surrogate_type',
        'create_date',
        'update_date'
    ];

    protected $dates = [
        'create_date',
        'update_date'
    ];
    
    /**
     * 关联设备
     */
    public function device()
    {
        return $this->belongsTo(WaterDevice::class, 'device_id', 'id');
    }
    
    /**
     * 关联客户
     */
    public function client()
    {
        return $this->belongsTo(WaterClient::class, 'client_id', 'id');
    }
    
    /**
     * 关联经销商
     */
    public function dealer()
    {
        return $this->belongsTo(WaterDealer::class, 'dealer_id', 'id');
    }
    
    /**
     * 获取充值类型文本
     */
    public function getOrderTypeTextAttribute()
    {
        $types = [
            '01' => '充值',
            '02' => '滤芯'
        ];
        
        return $types[$this->order_type] ?? '未知';
    }
    
    /**
     * 获取计费模式文本
     */
    public function getBillingModeTextAttribute()
    {
        $modes = [
            '0' => '包年计费',
            '1' => '流量计费'
        ];
        
        return $modes[$this->billing_mode] ?? '未知';
    }
    
    /**
     * 获取订单状态文本
     */
    public function getOrderStatusTextAttribute()
    {
        $statuses = [
            '101' => '未支付',
            '102' => '支付中',
            '103' => '支付成功',
            '104' => '支付失败',
            '105' => '已取消'
        ];
        
        return $statuses[$this->order_status] ?? '未知';
    }
    
    /**
     * 获取代充状态文本
     */
    public function getSurrogateTypeTextAttribute()
    {
        $types = [
            '0' => '自充',
            '1' => '代充'
        ];
        
        return $types[$this->surrogate_type] ?? '未知';
    }
    
    /**
     * 计算订单提成金额
     * 
     * @param float $rate 提成比例，默认0.3（30%）
     * @return float 提成金额
     */
    public function calculateCommission($rate = 0.3)
    {
        // 只有支付成功的订单才计算提成
        if ($this->order_status != '103') {
            return 0;
        }
        
        // 计算提成金额
        return round(floatval($this->money) * $rate, 2);
    }
    
    /**
     * 按设备编号查询订单
     */
    public function scopeByDeviceNumber($query, $deviceNumber)
    {
        return $query->where('device_number', $deviceNumber);
    }
    
    /**
     * 查询充值订单
     */
    public function scopeRechargeOrders($query)
    {
        return $query->where('order_type', '01');
    }
    
    /**
     * 查询支付成功的订单
     */
    public function scopePaid($query)
    {
        return $query->where('order_status', '103');
    }
} 