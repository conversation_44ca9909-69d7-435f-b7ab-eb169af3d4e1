<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class WaterMaster extends Model
{
    // 指定连接配置
    protected $connection = 'water_db';
    
    // 指定表名
    protected $table = 'wb_master';
    
    // 主键类型
    protected $keyType = 'string';
    
    // 不自增
    public $incrementing = false;
    
    // 关闭时间戳自动维护
    public $timestamps = false;
    
    // 定义可批量赋值的字段
    protected $fillable = [
        'id',
        'master_name',
        'phone',
        'register_date',
        'province',
        'city',
        'area',
        'address',
        'status',
        'remark',
        'create_name',
        'create_by',
        'create_date',
        'update_name',
        'update_by',
        'update_date',
        'sys_org_code',
        'sys_company_code',
        'bpm_status',
        'bank_code',
        'bank_number',
        'email',
        'dealer_id'
    ];

    protected $dates = [
        'register_date',
        'create_date',
        'update_date',
    ];

    /**
     * 获取状态文本
     */
    public function getStatusTextAttribute()
    {
        return $this->status == 'E' ? '启用' : '禁用';
    }
}
