<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Institution extends Model
{
    use HasFactory;

    protected $table = 'ddg_institution';

    protected $fillable = [
        'name',
        'number',
        'phone',
        'xs_number',
        'lv',
        'sfz',
        'institution_id'
    ];

    /**
     * 获取生日短信记录
     */
    public function birthdaySmsRecords()
    {
        return $this->hasMany(BirthdaySmsRecord::class, 'institution_id');
    }

    /**
     * 解析身份证获取生日信息
     */
    public function getBirthdayInfoAttribute()
    {
        if (!$this->sfz || strlen($this->sfz) !== 18) {
            return null;
        }

        try {
            $birthDate = \Carbon\Carbon::createFromFormat('Ymd', substr($this->sfz, 6, 8));
            $age = $birthDate->diffInYears(\Carbon\Carbon::now());
            $gender = (int)substr($this->sfz, -2, 1) % 2 === 0 ? '女' : '男';

            return [
                'birth_date' => $birthDate->format('Y-m-d'),
                'age' => $age,
                'gender' => $gender
            ];
        } catch (\Exception $e) {
            return null;
        }
    }
} 