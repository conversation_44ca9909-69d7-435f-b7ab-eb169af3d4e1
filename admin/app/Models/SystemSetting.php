<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Cache;

class SystemSetting extends Model
{
    use HasFactory;
    
    /**
     * 批量赋值白名单
     */
    protected $fillable = [
        'key', 'value', 'group', 'title', 'description', 'type', 'options', 'is_system', 'sort'
    ];
    
    /**
     * 类型转换
     */
    protected $casts = [
        'is_system' => 'boolean',
        'sort' => 'integer',
    ];
    
    /**
     * 获取单个设置值
     */
    public static function getSetting(string $key, $default = null)
    {
        // 优先从缓存获取
        return Cache::remember('system_setting:' . $key, 3600, function () use ($key, $default) {
            $setting = static::where('key', $key)->first();
            return $setting ? $setting->value : $default;
        });
    }
    
    /**
     * 获取指定分组的所有设置
     */
    public static function getSettingsByGroup(string $group = 'basic')
    {
        return Cache::remember('system_settings:' . $group, 3600, function () use ($group) {
            $settings = static::where('group', $group)
                ->orderBy('sort')
                ->get();
                
            if ($settings->isEmpty()) {
                // 如果没有设置，返回默认值
                return [
                    'site_name' => '点点够管理系统',
                    'site_logo' => '/images/logo.png',
                    'admin_email' => '<EMAIL>',
                    'system_version' => '1.0.0'
                ];
            }
            
            return $settings->keyBy('key')
                ->map(function ($item) {
                    return $item->value;
                })
                ->toArray();
        });
    }
    
    /**
     * 更新设置值
     */
    public static function updateSetting(string $key, $value)
    {
        $setting = static::where('key', $key)->first();
        
        if ($setting) {
            $setting->update(['value' => $value]);
            
            // 清除缓存
            Cache::forget('system_setting:' . $key);
            Cache::forget('system_settings:' . $setting->group);
            
            return true;
        }
        
        return false;
    }
    
    /**
     * 批量更新设置
     */
    public static function updateSettings(array $settings)
    {
        $updatedGroups = [];
        
        foreach ($settings as $key => $value) {
            $setting = static::where('key', $key)->first();
            
            if ($setting) {
                $setting->update(['value' => $value]);
                Cache::forget('system_setting:' . $key);
                $updatedGroups[$setting->group] = true;
            }
        }
        
        // 清除分组缓存
        foreach (array_keys($updatedGroups) as $group) {
            Cache::forget('system_settings:' . $group);
        }
        
        return true;
    }
}
