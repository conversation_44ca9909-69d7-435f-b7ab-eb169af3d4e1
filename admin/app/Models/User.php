<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Lara<PERSON>\Sanctum\HasApiTokens;
use App\Models\Models\Installation\InstallBooking;

class User extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable;

    /**
     * 数据表名称
     *
     * @var string
     */
    protected $table = 'app_users';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'phone',
        'status',
        'avatar',
        'gender',
        'birthday',
        'address',
        'remark',
        'wechat_openid',
        'wechat_nickname',
        'wechat_avatar',
        'wechat_gender',
        'wechat_country',
        'wechat_province',
        'wechat_city',
        'referrer_id',
        'referrer_name',
        'is_admin',
        'is_salesman',
        'is_vip',
        'is_pay_institution',
        'is_water_purifier_user',
        'is_engineer',
        'is_water_purifier_agent',
        'is_pay_merchant',
        'institution_id',
        'institution_name',
        'institution_number',
        'institution_xs_number',
        'institution_lv',
        'institution_core_type',
        'institution_sfz',
        'institution_account',
        'institution_card_name',
        'institution_card_number',
        'purifier_client_device_name',
        'purifier_client_device_id',
        'engineer_id',
        'balance',
        'points',
        'total_commission',
        'total_withdraw',
        'bank_name',
        'bank_account',
        'bank_branch',
        'account_name',
        'vip_at',
        'is_vip_paid',
        'vip_paid_at',
        'last_login_time',
        'last_login_ip',
        'last_active_time'
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'password' => 'hashed',
    ];
    
    /**
     * 获取用户的安装预约
     */
    public function installBookings()
    {
        return $this->hasMany(InstallBooking::class);
    }
    
    /**
     * 获取用户作为工程师的安装预约
     */
    public function engineerBookings()
    {
        return $this->hasMany(InstallBooking::class, 'engineer_id');
    }
    
    /**
     * 获取用户作为推荐人的安装预约
     */
    public function referredBookings()
    {
        return $this->hasMany(InstallBooking::class, 'referrer_id');
    }
}
