<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PointExchangeRecord extends Model
{
    use HasFactory;

    /**
     * 可批量赋值的属性
     */
    protected $fillable = [
        'user_id',
        'item_id',
        'item_title',
        'item_type',
        'points',
        'status',
        'shipping_info',
        'remark',
    ];
    
    /**
     * 状态常量
     */
    const STATUS_PENDING = 'pending';
    const STATUS_COMPLETED = 'completed';
    const STATUS_CANCELLED = 'cancelled';
    
    /**
     * 获取此记录关联的用户
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }
    
    /**
     * 获取此记录关联的兑换商品
     */
    public function item()
    {
        return $this->belongsTo(PointExchangeItem::class, 'item_id');
    }
} 