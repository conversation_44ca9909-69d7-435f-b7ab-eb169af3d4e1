<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\Log;

class WaterDeviceData extends Model
{
    // 自动检测是否存在water_db中的原始表，否则使用主数据库中的镜像表
    public function __construct(array $attributes = [])
    {
        // 强制使用水系统数据库
        $this->connection = 'water_db';
        $this->table = 'wb_device_water';
        
        parent::__construct($attributes);
    }
    
    // 关闭时间戳自动维护
    public $timestamps = false;
    
    // 定义可批量赋值的字段
    protected $fillable = [
        'id',
        'device_number',
        'water_production',
        'temperature',
        'purification_water_value',
        'raw_water_value',
        'create_date',
        'update_date'
    ];

    protected $dates = [
        'create_date',
        'update_date'
    ];

    /**
     * 关联设备
     */
    public function device()
    {
        return $this->belongsTo(WaterDevice::class, 'device_number', 'device_number');
    }

    /**
     * 获取设备每日制水量统计
     * 
     * @param string $deviceNumber 设备编号
     * @param int $days 统计天数
     * @return array
     */
    public static function getDailyWaterProduction($deviceNumber, $days = 7)
    {
        try {
            $data = [];
            $now = now();
            
            // 始终使用water_db连接
            $connection = 'water_db';
            $tableName = 'wb_device_water';
            
            // 获取过去7天的制水量统计
            for ($i = 0; $i < $days; $i++) {
                $date = $now->copy()->subDays($i)->format('Y-m-d');
                $nextDate = $now->copy()->subDays($i - 1)->format('Y-m-d');
                
                $production = DB::connection($connection)
                    ->table($tableName)
                    ->where('device_number', $deviceNumber)
                    ->where('create_date', '>=', $date . ' 00:00:00')
                    ->where('create_date', '<', $nextDate . ' 00:00:00')
                    ->sum('water_production');
                
                $data[] = [
                    'date' => $date,
                    'production' => round(floatval($production), 2)
                ];
            }
            
            return array_reverse($data);
        } catch (\Exception $e) {
            Log::error('获取制水量统计失败：' . $e->getMessage());
            return [];
        }
    }
    
    /**
     * 获取设备制水历史记录
     * 
     * @param string $deviceNumber 设备编号
     * @param int $limit 记录条数限制
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public static function getWaterHistory($deviceNumber, $limit = 10)
    {
        return self::where('device_number', $deviceNumber)
            ->orderBy('create_date', 'desc')
            ->limit($limit)
            ->get();
    }
    
    /**
     * 获取设备当天制水量
     * 
     * @param string $deviceNumber 设备编号
     * @return float 当天制水量
     */
    public static function getTodayWaterProduction($deviceNumber)
    {
        try {
            $today = now()->format('Y-m-d');
            
            // 始终使用water_db连接
            $connection = 'water_db';
            $tableName = 'wb_device_water';
            
            $production = DB::connection($connection)
                ->table($tableName)
                ->where('device_number', $deviceNumber)
                ->where('create_date', '>=', $today . ' 00:00:00')
                ->where('create_date', '<=', $today . ' 23:59:59')
                ->sum('water_production');
            
            return round(floatval($production), 2);
        } catch (\Exception $e) {
            Log::error('获取当天制水量失败：' . $e->getMessage());
            return 0.0;
        }
    }
} 