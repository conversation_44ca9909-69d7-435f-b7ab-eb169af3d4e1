<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SystemConfig extends Model
{
    use HasFactory;

    /**
     * 与模型关联的表名
     *
     * @var string
     */
    protected $table = 'system_configs';

    /**
     * 可批量赋值的属性
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'module',
        'key',
        'value',
        'title',
        'description',
        'type',
        'options',
        'is_system',
        'sort',
    ];

    /**
     * 应该转换的属性
     *
     * @var array<string, string>
     */
    protected $casts = [
        'options' => 'array',
        'is_system' => 'boolean',
        'sort' => 'integer',
    ];

    /**
     * 获取指定模块的所有配置
     *
     * @param string $module
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public static function getConfigsByModule($module)
    {
        try {
            \Log::info('获取模块配置: ' . $module);
            return self::where('module', $module)->orderBy('sort')->get();
        } catch (\Exception $e) {
            \Log::error('获取' . $module . '配置失败: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString()
            ]);
            return collect(); // 返回空集合
        }
    }

    /**
     * 获取指定模块和键的配置值
     *
     * @param string $module
     * @param string $key
     * @param mixed $default
     * @return mixed
     */
    public static function getConfigValue($module, $key, $default = null)
    {
        try {
            $config = self::where('module', $module)
                ->where('key', $key)
                ->first();

            return $config ? $config->value : $default;
        } catch (\Exception $e) {
            \Log::error('获取配置值失败: ' . $e->getMessage(), [
                'module' => $module,
                'key' => $key,
                'trace' => $e->getTraceAsString()
            ]);
            return $default;
        }
    }

    /**
     * 检查配置是否启用
     *
     * @param string $module
     * @param string $key
     * @return bool
     */
    public static function isEnabled($module, $key)
    {
        return (bool) self::getConfigValue($module, $key, false);
    }

    /**
     * 设置配置值
     *
     * @param string $module
     * @param string $key
     * @param mixed $value
     * @return bool
     */
    public static function setConfigValue($module, $key, $value)
    {
        try {
            // 使用updateOrCreate方法，如果不存在则创建，存在则更新
            $config = self::updateOrCreate(
                ['module' => $module, 'key' => $key],
                [
                    'value' => $value,
                    'title' => ucfirst(str_replace('_', ' ', $key)),
                    'description' => ucfirst(str_replace('_', ' ', $key)) . ' 配置',
                    'type' => 'text',
                    'is_system' => true,
                    'sort' => 0
                ]
            );

            \Log::info('更新配置成功', [
                'module' => $module,
                'key' => $key,
                'value' => $value,
                'created' => $config->wasRecentlyCreated
            ]);

            return true;
        } catch (\Exception $e) {
            \Log::error('设置配置值失败: ' . $e->getMessage(), [
                'module' => $module,
                'key' => $key,
                'value' => $value,
                'trace' => $e->getTraceAsString()
            ]);
            return false;
        }
    }

    /**
     * 获取微信配置
     *
     * @return array
     */
    public static function getWechatConfig()
    {
        try {
            \Log::info('开始获取微信配置');

            // 首先尝试从数据库获取
            $configs = self::where('module', 'wechat')->pluck('value', 'key')->toArray();

            // 如果配置为空, 创建默认配置
            if (empty($configs)) {
                \Log::info('微信配置为空，创建默认配置');
                self::createDefaultWechatConfig();
                $configs = self::where('module', 'wechat')->pluck('value', 'key')->toArray();
            }

            // 如果app_id为空，尝试从环境变量获取
            if (empty($configs['app_id'])) {
                $configs['app_id'] = env('WECHAT_APP_ID', 'wx501332efbaae387c');
                \Log::info('从环境变量获取app_id', ['app_id' => $configs['app_id']]);
            }

            // 如果app_secret为空，尝试从环境变量获取
            if (empty($configs['app_secret'])) {
                $configs['app_secret'] = env('WECHAT_APP_SECRET', 'f70ad4faefb54e68e3a5e7b5885a7c28');
                \Log::info('从环境变量获取app_secret');
            }

            // 如果oauth_callback_url为空，设置默认值
            if (empty($configs['oauth_callback_url'])) {
                $configs['oauth_callback_url'] = '/app/wechat-callback.html';
                \Log::info('使用默认的oauth_callback_url', ['url' => $configs['oauth_callback_url']]);
            }

            // 如果enabled为空，设置默认值
            if (!isset($configs['enabled'])) {
                $configs['enabled'] = '1'; // 默认启用
                \Log::info('使用默认的enabled值', ['enabled' => $configs['enabled']]);
            }

            \Log::info('获取微信配置成功', [
                'app_id' => substr($configs['app_id'] ?? '', 0, 6) . '...',
                'oauth_callback_url' => $configs['oauth_callback_url'] ?? '',
                'enabled' => $configs['enabled'] ?? ''
            ]);

            return $configs;
        } catch (\Exception $e) {
            \Log::error('获取微信配置失败: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString()
            ]);

            // 返回默认配置
            $defaultConfig = [
                'enabled' => '1',
                'app_id' => env('WECHAT_APP_ID', 'wx501332efbaae387c'),
                'app_secret' => env('WECHAT_APP_SECRET', 'f70ad4faefb54e68e3a5e7b5885a7c28'),
                'oauth_callback_url' => '/app/wechat-callback.html'
            ];

            \Log::info('使用默认微信配置', [
                'app_id' => substr($defaultConfig['app_id'], 0, 6) . '...',
                'oauth_callback_url' => $defaultConfig['oauth_callback_url'],
                'enabled' => $defaultConfig['enabled']
            ]);

            return $defaultConfig;
        }
    }

    /**
     * 创建默认微信配置
     */
    public static function createDefaultWechatConfig()
    {
        // 从环境变量获取默认值
        $appId = env('WECHAT_APP_ID', 'wx501332efbaae387c');
        $appSecret = env('WECHAT_APP_SECRET', 'f70ad4faefb54e68e3a5e7b5885a7c28');

        \Log::info('创建默认微信配置', [
            'app_id' => substr($appId, 0, 6) . '...',
            'app_secret_length' => strlen($appSecret)
        ]);

        $defaultConfigs = [
            [
                'module' => 'wechat',
                'key' => 'enabled',
                'value' => '1', // 默认启用
                'title' => '启用微信登录',
                'description' => '是否启用微信登录功能',
                'type' => 'switch',
                'options' => null,
                'is_system' => true,
                'sort' => 10,
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'module' => 'wechat',
                'key' => 'app_id',
                'value' => $appId,
                'title' => 'AppID',
                'description' => '微信公众平台AppID',
                'type' => 'text',
                'options' => null,
                'is_system' => true,
                'sort' => 20,
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'module' => 'wechat',
                'key' => 'app_secret',
                'value' => $appSecret,
                'title' => 'AppSecret',
                'description' => '微信公众平台AppSecret',
                'type' => 'text',
                'options' => null,
                'is_system' => true,
                'sort' => 30,
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'module' => 'wechat',
                'key' => 'oauth_callback_url',
                'value' => '/app/wechat-callback.html',
                'title' => '授权回调地址',
                'description' => '微信授权回调地址',
                'type' => 'text',
                'options' => null,
                'is_system' => true,
                'sort' => 40,
                'created_at' => now(),
                'updated_at' => now()
            ]
        ];

        foreach ($defaultConfigs as $config) {
            self::updateOrCreate(
                ['module' => $config['module'], 'key' => $config['key']],
                $config
            );
        }
    }

    /**
     * 微信登录是否启用
     *
     * @return bool
     */
    public static function isWechatLoginEnabled()
    {
        return self::isEnabled('wechat', 'enabled');
    }
}