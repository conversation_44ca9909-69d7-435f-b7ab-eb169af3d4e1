<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PointRule extends Model
{
    use HasFactory;

    /**
     * 可批量赋值的属性
     */
    protected $fillable = [
        'name',
        'description',
        'type',
        'points',
        'event',
        'limit_cycle',
        'limit_times',
        'status',
    ];

    /**
     * 类型常量
     */
    const TYPE_REWARD = 'reward';
    const TYPE_CONSUME = 'consume';

    /**
     * 状态常量
     */
    const STATUS_ACTIVE = 'active';
    const STATUS_INACTIVE = 'inactive';

    /**
     * 获取此规则相关的积分记录
     */
    public function pointRecords()
    {
        return $this->hasMany(PointRecord::class, 'rule_id');
    }
} 