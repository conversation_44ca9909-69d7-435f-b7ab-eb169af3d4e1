<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class VipDividend extends Model
{
    use HasFactory;

    /**
     * 表名
     *
     * @var string
     */
    protected $table = 'vip_dividends';

    /**
     * 可批量赋值的属性
     *
     * @var array
     */
    protected $fillable = [
        'user_id',
        'amount',
        'period',
        'type',
        'level',
        'status',
        'direct_vip_count',
        'team_vip_count',
        'month_direct_vip_count',
        'month_team_vip_count',
        'direct_recharge_count',
        'team_recharge_count',
        'month_direct_recharge_count',
        'month_team_recharge_count',
        'calculation_data',
        'settled_at',
        'remark',
    ];

    /**
     * 应该被转换成原生类型的属性
     *
     * @var array
     */
    protected $casts = [
        'amount' => 'float',
        'direct_vip_count' => 'integer',
        'team_vip_count' => 'integer',
        'month_direct_vip_count' => 'integer',
        'month_team_vip_count' => 'integer',
        'direct_recharge_count' => 'integer',
        'team_recharge_count' => 'integer',
        'month_direct_recharge_count' => 'integer',
        'month_team_recharge_count' => 'integer',
        'calculation_data' => 'json',
        'settled_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * 获取所属用户
     */
    public function user()
    {
        return $this->belongsTo(AppUser::class, 'user_id');
    }

    /**
     * 获取计算公式
     *
     * @return string|null
     */
    public function getFormulaAttribute()
    {
        if (is_array($this->calculation_data) && isset($this->calculation_data['formula'])) {
            return $this->calculation_data['formula'];
        }
        
        if (is_string($this->calculation_data)) {
            $data = json_decode($this->calculation_data, true);
            return $data['formula'] ?? null;
        }
        
        return null;
    }

    /**
     * 获取分红类型显示文本
     *
     * @return string
     */
    public function getTypeTextAttribute()
    {
        return $this->type === 'vip' ? 'VIP招募分红' : '充值分红';
    }

    /**
     * 获取分红等级显示文本
     *
     * @return string
     */
    public function getLevelTextAttribute()
    {
        switch ($this->level) {
            case 'primary':
                return '初级分红';
            case 'middle':
                return '中级分红';
            case 'high':
                return '高级分红';
            default:
                return '未知等级';
        }
    }

    /**
     * 获取状态显示文本
     *
     * @return string
     */
    public function getStatusTextAttribute()
    {
        return $this->status === 'pending' ? '待结算' : '已结算';
    }

    /**
     * 获取创建时间格式化文本
     *
     * @return string
     */
    public function getCreatedTimeAttribute()
    {
        return $this->created_at ? Carbon::parse($this->created_at)->format('Y-m-d H:i:s') : '-';
    }

    /**
     * 获取结算时间格式化文本
     *
     * @return string
     */
    public function getSettledTimeAttribute()
    {
        return $this->settled_at ? Carbon::parse($this->settled_at)->format('Y-m-d H:i:s') : '-';
    }

    /**
     * 范围查询：VIP招募分红
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeVipRecruitment($query)
    {
        return $query->where('type', 'vip');
    }

    /**
     * 范围查询：充值分红
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeRecharge($query)
    {
        return $query->where('type', 'recharge');
    }

    /**
     * 范围查询：待结算
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * 范围查询：已结算
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeSettled($query)
    {
        return $query->where('status', 'settled');
    }

    /**
     * 范围查询：按周期
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param string $period
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByPeriod($query, $period)
    {
        return $query->where('period', $period);
    }

    /**
     * 范围查询：按用户
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param int $userId
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * 范围查询：按等级
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param string $level
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByLevel($query, $level)
    {
        return $query->where('level', $level);
    }
} 