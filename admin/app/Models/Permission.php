<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class Permission extends Model
{
    use HasFactory;

    /**
     * 与模型关联的表名
     *
     * @var string
     */
    protected $table = 'admin_permissions';

    /**
     * 可批量赋值的属性
     *
     * @var array
     */
    protected $fillable = [
        'name',
        'display_name',
        'description',
        'module'
    ];

    /**
     * 获取拥有此权限的所有角色
     */
    public function roles(): BelongsToMany
    {
        return $this->belongsToMany(Role::class, 'admin_role_permissions', 'permission_id', 'role_id')
            ->withTimestamps();
    }

    /**
     * 获取拥有此权限的所有管理员
     */
    public function admins(): BelongsToMany
    {
        return $this->belongsToMany(Admin::class, 'admin_user_permissions', 'permission_id', 'user_id')
            ->withTimestamps();
    }

    /**
     * 按模块分组获取所有权限
     *
     * @return \Illuminate\Support\Collection
     */
    public static function getGroupedPermissions()
    {
        return self::all()->groupBy('module');
    }
}
