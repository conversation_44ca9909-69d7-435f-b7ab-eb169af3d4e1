<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class InvitationGuest extends Model
{
    use HasFactory;

    /**
     * 可批量赋值的属性
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'template_id',
        'app_user_id',
        'referrer_id',
        'name',
        'phone',
        'avatar',
        'nickname',
        'open_id',
        'joined_at',
        'registered_at',
        'status',
        'extra_data',
    ];

    /**
     * 应该转换的属性
     *
     * @var array<string, string>
     */
    protected $casts = [
        'joined_at' => 'datetime',
        'registered_at' => 'datetime',
        'extra_data' => 'array',
    ];

    /**
     * 获取关联的邀请函模板
     */
    public function template()
    {
        return $this->belongsTo(InvitationTemplate::class, 'template_id');
    }

    /**
     * 获取关联的APP用户
     */
    public function appUser()
    {
        return $this->belongsTo(AppUser::class, 'app_user_id');
    }

    /**
     * 获取关联的推荐人
     */
    public function referrer()
    {
        return $this->belongsTo(AppUser::class, 'referrer_id');
    }

    /**
     * 将访客状态更新为已注册
     */
    public function markAsRegistered()
    {
        $this->update([
            'status' => 'registered',
            'registered_at' => now(),
        ]);

        // 更新APP用户的phone和name字段
        if ($this->app_user_id && $this->name && $this->phone) {
            $appUser = $this->appUser;
            if ($appUser) {
                $appUser->update([
                    'name' => $this->name,
                    'phone' => $this->phone,
                ]);
            }
        }

        return $this;
    }
} 