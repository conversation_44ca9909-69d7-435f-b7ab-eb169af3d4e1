<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class Role extends Model
{
    use HasFactory;

    /**
     * 与模型关联的表名
     *
     * @var string
     */
    protected $table = 'admin_roles';

    /**
     * 可批量赋值的属性
     *
     * @var array
     */
    protected $fillable = [
        'name',
        'display_name',
        'description',
        'is_system'
    ];

    /**
     * 应该被转换成原生类型的属性
     *
     * @var array
     */
    protected $casts = [
        'is_system' => 'boolean',
    ];

    /**
     * 获取拥有此角色的所有管理员
     */
    public function admins(): BelongsToMany
    {
        return $this->belongsToMany(Admin::class, 'admin_user_roles', 'role_id', 'user_id')
            ->withTimestamps();
    }

    /**
     * 获取此角色拥有的所有权限
     */
    public function permissions(): BelongsToMany
    {
        return $this->belongsToMany(Permission::class, 'admin_role_permissions', 'role_id', 'permission_id')
            ->withTimestamps();
    }

    /**
     * 检查角色是否拥有指定权限
     *
     * @param string|array $permission 权限名称或权限名称数组
     * @return bool
     */
    public function hasPermission($permission): bool
    {
        if (is_array($permission)) {
            return $this->permissions->whereIn('name', $permission)->count() == count($permission);
        }
        
        return $this->permissions->contains('name', $permission);
    }

    /**
     * 给角色分配权限
     *
     * @param array|Permission $permissions
     * @return $this
     */
    public function givePermissionTo($permissions)
    {
        $permissions = collect($permissions)->map(function ($permission) {
            if ($permission instanceof Permission) {
                return $permission->id;
            }
            
            return $permission;
        });
        
        $this->permissions()->sync($permissions, false);
        
        return $this;
    }

    /**
     * 移除角色的权限
     *
     * @param array|Permission $permissions
     * @return $this
     */
    public function revokePermissionTo($permissions)
    {
        $permissions = collect($permissions)->map(function ($permission) {
            if ($permission instanceof Permission) {
                return $permission->id;
            }
            
            return $permission;
        });
        
        $this->permissions()->detach($permissions);
        
        return $this;
    }

    /**
     * 同步角色的权限
     *
     * @param array|Permission $permissions
     * @return $this
     */
    public function syncPermissions($permissions)
    {
        $permissions = collect($permissions)->map(function ($permission) {
            if ($permission instanceof Permission) {
                return $permission->id;
            }
            
            return $permission;
        });
        
        $this->permissions()->sync($permissions);
        
        return $this;
    }
}
