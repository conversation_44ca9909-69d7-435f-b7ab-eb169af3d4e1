<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SmsLog extends Model
{
    use HasFactory;
    
    /**
     * 与模型关联的数据表
     *
     * @var string
     */
    protected $table = 'sms_logs';
    
    /**
     * 可批量赋值的属性
     *
     * @var array
     */
    protected $fillable = [
        'phone',
        'code',
        'type',
        'status',
        'message',
        'provider',
        'request_ip',
        'request_data',
        'response_data',
    ];
    
    /**
     * 应该被转换成原生类型的属性
     *
     * @var array
     */
    protected $casts = [
        'status' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];
    
    /**
     * 获取状态文本
     *
     * @return string
     */
    public function getStatusTextAttribute()
    {
        return $this->status ? '成功' : '失败';
    }
    
    /**
     * 获取请求数据（解析JSON）
     *
     * @return array
     */
    public function getRequestDataArrayAttribute()
    {
        return json_decode($this->request_data, true) ?: [];
    }
    
    /**
     * 获取响应数据（解析JSON）
     *
     * @return array
     */
    public function getResponseDataArrayAttribute()
    {
        return json_decode($this->response_data, true) ?: [];
    }
    
    /**
     * 获取掩码后的手机号
     *
     * @return string
     */
    public function getMaskedPhoneAttribute()
    {
        return substr_replace($this->phone, '****', 3, 4);
    }
} 