<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class AppUserPurifierDevice extends Model
{
    use HasFactory;
    
    /**
     * 与模型关联的表名
     *
     * @var string
     */
    protected $table = 'app_user_purifier_devices';
    
    /**
     * 可批量赋值的属性
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'device_name',
        'device_id',
        'device_model',
        'install_location',
        'is_primary',
        'device_info',
        'bind_time',
    ];
    
    /**
     * 应该转换的属性
     *
     * @var array<string, string>
     */
    protected $casts = [
        'device_info' => 'array',
        'is_primary' => 'integer',
        'bind_time' => 'datetime',
    ];
    
    /**
     * 获取关联的用户
     */
    public function user()
    {
        return $this->belongsTo(AppUser::class, 'user_id');
    }
    
    /**
     * 设置为主设备
     */
    public function setPrimary()
    {
        // 先将该用户所有设备都设为非主设备
        self::where('user_id', $this->user_id)
            ->where('id', '!=', $this->id)
            ->update(['is_primary' => 0]);
            
        // 将当前设备设为主设备
        $this->is_primary = 1;
        $this->save();
        
        return $this;
    }
}
