<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class HomeNavItem extends Model
{
    use HasFactory;
    
    protected $table = 'home_nav_items';
    
    protected $fillable = [
        'icon',
        'title',
        'link_url',
        'link_type',
        'status',
        'sort_order',
    ];
    
    protected $casts = [
        'status' => 'boolean',
    ];
}
