<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SmsCode extends Model
{
    use HasFactory;
    
    /**
     * 与模型关联的数据表
     *
     * @var string
     */
    protected $table = 'sms_codes';
    
    /**
     * 可批量赋值的属性
     *
     * @var array
     */
    protected $fillable = [
        'phone',
        'code',
        'type',
        'is_used',
        'created_at',
        'used_at',
    ];
    
    /**
     * 应该被转换成原生类型的属性
     *
     * @var array
     */
    protected $casts = [
        'is_used' => 'boolean',
        'created_at' => 'datetime',
        'used_at' => 'datetime',
    ];
    
    /**
     * 表明模型是否应该被打上时间戳
     *
     * @var bool
     */
    public $timestamps = false;
    
    /**
     * 获取使用状态文本
     *
     * @return string
     */
    public function getStatusTextAttribute()
    {
        return $this->is_used ? '已使用' : '未使用';
    }
    
    /**
     * 获取验证码类型的描述
     *
     * @return string
     */
    public function getTypeTextAttribute()
    {
        $types = [
            'login' => '登录验证',
            'register' => '注册验证',
            'reset' => '重置密码',
            'bind' => '绑定手机',
            'verify' => '身份验证',
        ];
        
        return $types[$this->type] ?? $this->type;
    }
    
    /**
     * 获取掩码后的手机号
     *
     * @return string
     */
    public function getMaskedPhoneAttribute()
    {
        return substr_replace($this->phone, '****', 3, 4);
    }
    
    /**
     * 获取有效期剩余时间（秒）
     *
     * @return int
     */
    public function getRemainingTimeAttribute()
    {
        if ($this->is_used) {
            return 0;
        }
        
        // 验证码30分钟有效期
        $expireTime = $this->created_at->addMinutes(30);
        $now = now();
        
        if ($now->gt($expireTime)) {
            return 0;
        }
        
        return $expireTime->diffInSeconds($now);
    }
    
    /**
     * 获取是否已过期
     *
     * @return bool
     */
    public function getIsExpiredAttribute()
    {
        if ($this->is_used) {
            return false;
        }
        
        return $this->created_at->addMinutes(30)->lt(now());
    }
} 