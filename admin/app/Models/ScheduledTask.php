<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class ScheduledTask extends Model
{
    use HasFactory;
    
    /**
     * 与模型关联的表名
     *
     * @var string
     */
    protected $table = 'scheduled_tasks';
    
    /**
     * 可以批量赋值的属性
     *
     * @var array
     */
    protected $fillable = [
        'name',
        'command',
        'description',
        'schedule_expression',
        'schedule_description',
        'is_enabled',
        'without_overlapping',
        'log_file',
        'last_run_at',
        'next_run_at',
        'last_run_status',
        'last_run_output',
        'run_count',
        'success_count',
        'failure_count'
    ];
    
    /**
     * 应该被转换为日期的属性
     *
     * @var array
     */
    protected $dates = [
        'last_run_at',
        'next_run_at',
        'created_at',
        'updated_at'
    ];
    
    /**
     * 应该被转换为原生类型的属性
     *
     * @var array
     */
    protected $casts = [
        'is_enabled' => 'boolean',
        'without_overlapping' => 'boolean',
        'run_count' => 'integer',
        'success_count' => 'integer',
        'failure_count' => 'integer',
        'last_run_at' => 'datetime',
        'next_run_at' => 'datetime'
    ];
    
    /**
     * 获取任务状态文本
     *
     * @return string
     */
    public function getStatusTextAttribute()
    {
        if (!$this->is_enabled) {
            return '已禁用';
        }
        
        if (!$this->last_run_at) {
            return '未执行';
        }
        
        switch ($this->last_run_status) {
            case 'success':
                return '成功';
            case 'failed':
                return '失败';
            case 'running':
                return '运行中';
            default:
                return '未知';
        }
    }
    
    /**
     * 获取成功率
     *
     * @return float
     */
    public function getSuccessRateAttribute()
    {
        if ($this->run_count == 0) {
            return 0;
        }
        
        return round(($this->success_count / $this->run_count) * 100, 2);
    }
    
    /**
     * 获取下次执行时间的人性化显示
     *
     * @return string
     */
    public function getNextRunHumanAttribute()
    {
        if (!$this->next_run_at) {
            return '未设置';
        }
        
        $now = Carbon::now();
        $nextRun = Carbon::parse($this->next_run_at);
        
        if ($nextRun->isPast()) {
            return '已过期';
        }
        
        return $nextRun->diffForHumans($now);
    }
    
    /**
     * 获取最后执行时间的人性化显示
     *
     * @return string
     */
    public function getLastRunHumanAttribute()
    {
        if (!$this->last_run_at) {
            return '从未执行';
        }
        
        return Carbon::parse($this->last_run_at)->diffForHumans();
    }
    
    /**
     * 更新任务执行统计
     *
     * @param string $status
     * @param string|null $output
     * @return void
     */
    public function updateRunStats($status, $output = null)
    {
        $this->increment('run_count');
        
        if ($status === 'success') {
            $this->increment('success_count');
        } else {
            $this->increment('failure_count');
        }
        
        $this->update([
            'last_run_at' => now(),
            'last_run_status' => $status,
            'last_run_output' => $output
        ]);
    }
    
    /**
     * 计算下次执行时间
     *
     * @return Carbon|null
     */
    public function calculateNextRunTime()
    {
        $expression = $this->schedule_expression;
        $now = Carbon::now();
        
        try {
            switch (true) {
                case $expression === 'hourly':
                    return $now->copy()->addHour()->startOfHour();
                    
                case $expression === 'daily':
                    return $now->copy()->addDay()->startOfDay();
                    
                case str_starts_with($expression, 'dailyAt:'):
                    $time = str_replace('dailyAt:', '', $expression);
                    [$hour, $minute] = explode(':', $time);
                    $next = $now->copy()->setTime((int)$hour, (int)$minute, 0);
                    if ($next->isPast()) {
                        $next->addDay();
                    }
                    return $next;
                    
                case str_starts_with($expression, 'monthlyOn:'):
                    $parts = explode(':', str_replace('monthlyOn:', '', $expression));
                    $day = (int)$parts[0];
                    $hour = (int)$parts[1];
                    $minute = (int)$parts[2];
                    
                    $next = $now->copy()->day($day)->setTime($hour, $minute, 0);
                    if ($next->isPast()) {
                        $next->addMonth();
                    }
                    return $next;
                    
                default:
                    return null;
            }
        } catch (\Exception $e) {
            return null;
        }
    }
    
    /**
     * 启用任务
     *
     * @return void
     */
    public function enable()
    {
        $this->update([
            'is_enabled' => true,
            'next_run_at' => $this->calculateNextRunTime()
        ]);
    }
    
    /**
     * 禁用任务
     *
     * @return void
     */
    public function disable()
    {
        $this->update([
            'is_enabled' => false,
            'next_run_at' => null
        ]);
    }
}
