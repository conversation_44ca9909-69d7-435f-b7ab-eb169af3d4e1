<?php

namespace App\Models\App;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class NavConfig extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'nav_name',
        'icon',
        'path',
        'status',
        'highlight',
        'sort_order',
        'type'
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'status' => 'integer',
        'highlight' => 'integer',
        'sort_order' => 'integer',
    ];

    /**
     * 获取可用的Vant图标列表
     *
     * @return array
     */
    public static function getVantIcons()
    {
        return [
            'home-o', 'home', 'search', 'setting-o', 'setting',
            'friends-o', 'friends', 'comment-o', 'comment',
            'like-o', 'like', 'star-o', 'star',
            'phone-o', 'phone', 'shopping-cart-o', 'shopping-cart',
            'orders-o', 'orders', 'goods-collect-o', 'goods-collect',
            'user-o', 'user', 'manager-o', 'manager',
            'apps-o', 'apps', 'gift-o', 'gift',
            'shop-o', 'shop', 'location-o', 'location',
            'cart-o', 'cart', 'coupon-o', 'coupon',
            'points', 'gold-coin-o', 'gold-coin',
            'circle', 'cluster-o', 'smile-o', 'smile-comment-o'
        ];
    }
}
