<?php

namespace App\Models\App;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class HomeNavItem extends Model
{
    use HasFactory;

    protected $table = 'home_nav_items';

    protected $fillable = [
        'icon',
        'title',
        'path',
        'status',
        'sort_order',
    ];

    protected $casts = [
        'status' => 'boolean',
    ];
}
