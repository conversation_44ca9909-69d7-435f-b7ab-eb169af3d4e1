<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

class RedirectController extends Controller
{
    /**
     * 重定向旧的API请求到新的API路径
     *
     * @param Request $request
     * @return \Illuminate\Http\Response
     */
    public function redirectApi(Request $request)
    {
        // 获取请求的路径
        $path = $request->path();
        
        // 记录重定向信息
        \Log::info('API重定向', [
            'from' => $path,
            'to' => '/Tapp/admin/api/' . $path,
            'method' => $request->method(),
            'params' => $request->all()
        ]);
        
        // 重定向到新的API路径
        return redirect('/Tapp/admin/api/' . $path);
    }
}
