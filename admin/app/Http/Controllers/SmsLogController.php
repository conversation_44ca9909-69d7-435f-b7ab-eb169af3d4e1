<?php

namespace App\Http\Controllers;

use App\Models\SmsLog;
use Illuminate\Http\Request;

class SmsLogController extends Controller
{
    /**
     * 显示短信日志列表
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $query = SmsLog::query();
        
        // 搜索条件
        if ($request->has('phone') && !empty($request->phone)) {
            $query->where('phone', 'like', '%' . $request->phone . '%');
        }
        
        if ($request->has('status') && $request->status !== '') {
            $query->where('status', $request->status);
        }
        
        if ($request->has('type') && !empty($request->type)) {
            $query->where('type', $request->type);
        }
        
        if ($request->has('date_start') && !empty($request->date_start)) {
            $query->whereDate('created_at', '>=', $request->date_start);
        }
        
        if ($request->has('date_end') && !empty($request->date_end)) {
            $query->whereDate('created_at', '<=', $request->date_end);
        }
        
        // 排序
        $query->orderBy('id', 'desc');
        
        // 分页
        $logs = $query->paginate(15);
        
        // 统计
        $totalCount = SmsLog::count();
        $successCount = SmsLog::where('status', 1)->count();
        $failCount = SmsLog::where('status', 0)->count();
        
        // 类型列表
        $types = SmsLog::select('type')->distinct()->pluck('type');
        
        return view('sms.logs.index', compact('logs', 'totalCount', 'successCount', 'failCount', 'types'));
    }
    
    /**
     * 显示短信日志详情
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $log = SmsLog::findOrFail($id);
        
        return view('sms.logs.show', compact('log'));
    }
} 