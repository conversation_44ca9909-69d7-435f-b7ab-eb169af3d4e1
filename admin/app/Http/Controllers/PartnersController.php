<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\User;

class PartnersController extends Controller
{
    public function index()
    {
        $partners = User::where('is_pay_institution', 1)
            ->orWhere('is_water_purifier_agent', 1)
            ->select(['id', 'name', 'mobile', 'is_pay_institution', 'is_water_purifier_agent'])
            ->paginate(15);

        return view('partners.index', compact('partners'));
    }

}
