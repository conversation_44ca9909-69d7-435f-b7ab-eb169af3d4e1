<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class TabbarNavController extends Controller
{
    /**
     * 获取底部导航列表
     */
    public function index()
    {
        return response()->json([
            'code' => 0,
            'message' => '获取成功',
            'data' => [
                [
                    'id' => 1,
                    'title' => '首页',
                    'icon' => 'home-o',
                    'active_icon' => 'home',
                    'url' => '/home',
                    'sort' => 1,
                    'is_active' => 1
                ],
                [
                    'id' => 2,
                    'title' => '商城',
                    'icon' => 'shop-o',
                    'active_icon' => 'shop',
                    'url' => '/mall',
                    'sort' => 2,
                    'is_active' => 1
                ],
                [
                    'id' => 3,
                    'title' => '我的',
                    'icon' => 'user-o',
                    'active_icon' => 'user',
                    'url' => '/user',
                    'sort' => 3,
                    'is_active' => 1
                ]
            ]
        ]);
    }

    /**
     * 创建底部导航项
     */
    public function store(Request $request)
    {
        try {
            $data = $request->validate([
                'title' => 'required|string|max:50',
                'icon' => 'required|string|max:50',
                'active_icon' => 'required|string|max:50',
                'url' => 'required|string|max:255',
                'sort' => 'required|integer|min:1',
                'is_active' => 'required|boolean'
            ]);

            // 这里可以实现保存底部导航项的逻辑
            // 例如保存到数据库

            return response()->json([
                'code' => 0,
                'message' => '创建成功',
                'data' => array_merge(['id' => rand(100, 999)], $data)
            ]);
        } catch (\Exception $e) {
            Log::error('创建底部导航项失败', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'code' => 500,
                'message' => '创建失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取单个底部导航项
     */
    public function show($id)
    {
        return response()->json([
            'code' => 0,
            'message' => '获取成功',
            'data' => [
                'id' => (int)$id,
                'title' => '导航项' . $id,
                'icon' => 'home-o',
                'active_icon' => 'home',
                'url' => '/page/' . $id,
                'sort' => (int)$id,
                'is_active' => 1
            ]
        ]);
    }

    /**
     * 更新底部导航项
     */
    public function update(Request $request, $id)
    {
        try {
            $data = $request->validate([
                'title' => 'required|string|max:50',
                'icon' => 'required|string|max:50',
                'active_icon' => 'required|string|max:50',
                'url' => 'required|string|max:255',
                'sort' => 'required|integer|min:1',
                'is_active' => 'required|boolean'
            ]);

            // 这里可以实现更新底部导航项的逻辑
            // 例如更新数据库中的记录

            return response()->json([
                'code' => 0,
                'message' => '更新成功',
                'data' => array_merge(['id' => (int)$id], $data)
            ]);
        } catch (\Exception $e) {
            Log::error('更新底部导航项失败', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'code' => 500,
                'message' => '更新失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 删除底部导航项
     */
    public function destroy($id)
    {
        // 这里可以实现删除底部导航项的逻辑
        // 例如从数据库中删除记录

        return response()->json([
            'code' => 0,
            'message' => '删除成功'
        ]);
    }

    /**
     * 获取激活的底部导航项
     */
    public function getActiveNavItems()
    {
        return response()->json([
            'code' => 0,
            'message' => '获取成功',
            'data' => [
                [
                    'id' => 1,
                    'title' => '首页',
                    'icon' => 'home-o',
                    'active_icon' => 'home',
                    'url' => '/home',
                    'sort' => 1
                ],
                [
                    'id' => 2,
                    'title' => '商城',
                    'icon' => 'shop-o',
                    'active_icon' => 'shop',
                    'url' => '/mall',
                    'sort' => 2
                ],
                [
                    'id' => 3,
                    'title' => '我的',
                    'icon' => 'user-o',
                    'active_icon' => 'user',
                    'url' => '/user',
                    'sort' => 3
                ]
            ]
        ]);
    }

    /**
     * 获取Vant图标列表
     */
    public function getVantIcons()
    {
        return response()->json([
            'code' => 0,
            'message' => '获取成功',
            'data' => [
                'home-o', 'home', 'search', 'search', 'friends-o', 'friends',
                'setting-o', 'setting', 'clock-o', 'clock', 'gold-coin-o',
                'gold-coin', 'chat-o', 'chat', 'smile-o', 'smile', 'music-o',
                'music', 'star-o', 'star', 'phone-o', 'phone', 'point-gift-o',
                'point-gift', 'service-o', 'service', 'goods-collect-o',
                'goods-collect', 'shop-o', 'shop', 'cart-o', 'cart',
                'user-o', 'user', 'orders-o', 'orders', 'coupon-o', 'coupon'
            ]
        ]);
    }
}
