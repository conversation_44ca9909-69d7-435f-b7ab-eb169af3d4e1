<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class AppUserDeviceController extends Controller
{
    /**
     * 获取用户设备列表
     *
     * @param Request $request
     * @param int $userId 用户ID
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request, $userId = null)
    {
        try {
            // 记录请求信息
            Log::info('获取用户设备列表请求', [
                'userId' => $userId,
                'params' => $request->all(),
                'ip' => $request->ip(),
                'user_agent' => $request->header('User-Agent')
            ]);

            // 如果没有指定用户ID，则尝试从请求参数中获取
            if (!$userId) {
                $userId = $request->input('user_id');
            }

            // 如果仍然没有用户ID，则返回错误
            if (!$userId) {
                return response()->json([
                    'code' => 400,
                    'message' => '缺少用户ID参数',
                    'data' => []
                ]);
            }

            // 查询用户设备
            $query = DB::table('tapp_devices')
                ->where('app_user_id', $userId)
                ->select([
                    'id',
                    'device_id',
                    'device_name',
                    'app_user_id',
                    'status',
                    'remark',
                    'created_at',
                    'updated_at'
                ]);

            // 分页参数
            $page = $request->input('page', 1);
            $limit = $request->input('limit', 10);

            // 查询总数
            $total = $query->count();

            // 查询数据
            $devices = $query->orderBy('created_at', 'desc')
                ->offset(($page - 1) * $limit)
                ->limit($limit)
                ->get();

            // 记录响应信息
            Log::info('获取用户设备列表成功', [
                'userId' => $userId,
                'total' => $total,
                'count' => count($devices)
            ]);

            return response()->json([
                'code' => 0,
                'message' => '获取成功',
                'data' => $devices,
                'total' => $total
            ]);
        } catch (\Exception $e) {
            Log::error('获取用户设备列表失败: ' . $e->getMessage(), [
                'userId' => $userId,
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'code' => 500,
                'message' => '获取用户设备列表失败: ' . $e->getMessage(),
                'data' => []
            ]);
        }
    }

    /**
     * 添加用户设备
     *
     * @param Request $request
     * @param int $userId 用户ID
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request, $userId)
    {
        try {
            // 验证请求参数
            $request->validate([
                'device_id' => 'required|string|max:100',
                'device_name' => 'required|string|max:100',
                'remark' => 'nullable|string|max:255'
            ]);

            // 检查设备是否已经绑定
            $existDevice = DB::table('tapp_devices')
                ->where('device_id', $request->input('device_id'))
                ->first();

            if ($existDevice) {
                return response()->json([
                    'code' => 400,
                    'message' => '设备已经被绑定',
                    'data' => null
                ]);
            }

            // 添加设备
            $deviceId = DB::table('tapp_devices')->insertGetId([
                'device_id' => $request->input('device_id'),
                'device_name' => $request->input('device_name'),
                'app_user_id' => $userId,
                'status' => 'active',
                'remark' => $request->input('remark'),
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now()
            ]);

            return response()->json([
                'code' => 0,
                'message' => '添加成功',
                'data' => [
                    'id' => $deviceId,
                    'device_id' => $request->input('device_id'),
                    'device_name' => $request->input('device_name'),
                    'app_user_id' => $userId,
                    'status' => 'active',
                    'remark' => $request->input('remark'),
                    'created_at' => Carbon::now(),
                    'updated_at' => Carbon::now()
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('添加用户设备失败: ' . $e->getMessage());
            return response()->json([
                'code' => 500,
                'message' => '添加用户设备失败: ' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

    /**
     * 删除/解绑用户设备
     *
     * @param int $id 设备ID
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy($id)
    {
        try {
            // 记录请求信息
            Log::info('解绑用户设备请求', [
                'device_id' => $id,
                'ip' => request()->ip(),
                'user_agent' => request()->header('User-Agent')
            ]);

            // 查找设备
            $device = DB::table('tapp_devices')->where('id', $id)->first();

            if (!$device) {
                return response()->json([
                    'code' => 404,
                    'message' => '设备不存在',
                    'data' => null
                ], 404);
            }

            // 删除设备
            DB::table('tapp_devices')->where('id', $id)->delete();

            // 记录响应信息
            Log::info('解绑用户设备成功', [
                'device_id' => $id,
                'device_name' => $device->device_name ?? '',
                'app_user_id' => $device->app_user_id ?? null
            ]);

            return response()->json([
                'code' => 0,
                'message' => '设备解绑成功',
                'data' => null
            ]);
        } catch (\Exception $e) {
            Log::error('解绑用户设备失败: ' . $e->getMessage(), [
                'device_id' => $id,
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'code' => 500,
                'message' => '解绑用户设备失败: ' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }
}
