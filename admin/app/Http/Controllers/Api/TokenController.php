<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;

class TokenController extends Controller
{
    /**
     * 刷新当前用户的访问令牌
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function refresh(Request $request)
    {
        try {
            $user = Auth::guard('sanctum')->user();
            
            if (!$user) {
                return response()->json([
                    'code' => 401,
                    'message' => '用户未认证',
                    'data' => null
                ], 401);
            }

            $currentToken = $user->currentAccessToken();
            
            if (!$currentToken) {
                return response()->json([
                    'code' => 401,
                    'message' => '无效的令牌',
                    'data' => null
                ], 401);
            }

            // 删除当前token
            $currentToken->delete();

            // 创建新的30天token
            $newToken = $user->createToken('auth_token', ['*'], now()->addDays(30))->plainTextToken;

            return response()->json([
                'code' => 0,
                'message' => '令牌刷新成功',
                'data' => [
                    'token' => $newToken,
                    'expires_at' => now()->addDays(30)->toISOString(),
                    'expires_in_days' => 30
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'code' => 500,
                'message' => '令牌刷新失败: ' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    /**
     * 获取当前令牌的状态信息
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function status(Request $request)
    {
        try {
            $user = Auth::guard('sanctum')->user();
            
            if (!$user) {
                return response()->json([
                    'code' => 401,
                    'message' => '用户未认证',
                    'data' => null
                ], 401);
            }

            $currentToken = $user->currentAccessToken();
            
            if (!$currentToken) {
                return response()->json([
                    'code' => 401,
                    'message' => '无效的令牌',
                    'data' => null
                ], 401);
            }

            $data = [
                'token_name' => $currentToken->name,
                'created_at' => $currentToken->created_at->toISOString(),
                'last_used_at' => $currentToken->last_used_at ? $currentToken->last_used_at->toISOString() : null,
            ];

            if ($currentToken->expires_at) {
                $expiresAt = Carbon::parse($currentToken->expires_at);
                $now = Carbon::now();
                $daysUntilExpiry = $now->diffInDays($expiresAt, false);
                
                $data['expires_at'] = $expiresAt->toISOString();
                $data['expires_in_days'] = $daysUntilExpiry;
                $data['is_expired'] = $daysUntilExpiry < 0;
                $data['expires_soon'] = $daysUntilExpiry <= 3 && $daysUntilExpiry > 0;
            } else {
                $data['expires_at'] = null;
                $data['expires_in_days'] = null;
                $data['is_expired'] = false;
                $data['expires_soon'] = false;
                $data['never_expires'] = true;
            }

            return response()->json([
                'code' => 0,
                'message' => '获取令牌状态成功',
                'data' => $data
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'code' => 500,
                'message' => '获取令牌状态失败: ' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }
}