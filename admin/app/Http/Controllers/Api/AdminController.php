<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class AdminController extends Controller
{
    /**
     * 获取管理员工作台数据
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getWorkspace(Request $request)
    {
        $user = Auth::user();
        
        if (!$user) {
            return response()->json([
                'code' => 1002,
                'message' => '无效的令牌或已过期',
                'data' => null
            ]);
        }
        
        try {
            // 验证用户是否为管理员
            $admin = DB::table('admins')
                ->where('user_id', $user->id)
                ->first();
                
            if (!$admin) {
                return response()->json([
                    'code' => 1,
                    'message' => '您不是管理员，无权访问',
                    'data' => null
                ]);
            }
            
            // 获取当前日期
            $today = Carbon::today()->format('Y-m-d');
            $yesterday = Carbon::yesterday()->format('Y-m-d');
            $currentMonth = Carbon::today()->format('Y-m');
            $lastMonth = Carbon::today()->subMonth()->format('Y-m');
            
            // 获取今日订单数据
            $todayOrders = DB::table('orders')
                ->whereDate('created_at', $today)
                ->count();
                
            // 获取昨日订单数据
            $yesterdayOrders = DB::table('orders')
                ->whereDate('created_at', $yesterday)
                ->count();
                
            // 获取本月订单数据
            $currentMonthOrders = DB::table('orders')
                ->whereRaw("DATE_FORMAT(created_at, '%Y-%m') = ?", [$currentMonth])
                ->count();
                
            // 获取上月订单数据
            $lastMonthOrders = DB::table('orders')
                ->whereRaw("DATE_FORMAT(created_at, '%Y-%m') = ?", [$lastMonth])
                ->count();
                
            // 获取总订单数据
            $totalOrders = DB::table('orders')
                ->count();
                
            // 获取今日销售金额
            $todayAmount = DB::table('orders')
                ->whereDate('created_at', $today)
                ->sum('total_amount');
                
            // 获取本月销售金额
            $currentMonthAmount = DB::table('orders')
                ->whereRaw("DATE_FORMAT(created_at, '%Y-%m') = ?", [$currentMonth])
                ->sum('total_amount');
                
            // 获取总销售金额
            $totalAmount = DB::table('orders')
                ->sum('total_amount');
                
            // 获取最近5条订单记录
            $recentOrders = DB::table('orders')
                ->orderBy('created_at', 'desc')
                ->limit(5)
                ->get();
                
            // 获取用户统计
            $userCount = DB::table('app_users')
                ->count();
                
            // 获取本月新增用户数
            $newUserCount = DB::table('app_users')
                ->whereRaw("DATE_FORMAT(created_at, '%Y-%m') = ?", [$currentMonth])
                ->count();
                
            // 获取设备统计
            $deviceCount = DB::table('tapp_devices')
                ->count();
                
            // 获取本月新增设备数
            $newDeviceCount = DB::table('tapp_devices')
                ->whereRaw("DATE_FORMAT(created_at, '%Y-%m') = ?", [$currentMonth])
                ->count();
                
            // 获取机构统计
            $institutionCount = DB::connection('payment_db')->table('institutions')
                ->count();
                
            // 获取代理商统计
            $agentCount = DB::table('agents')
                ->count();
                
            // 获取销售人员统计
            $salesmenCount = DB::table('salesmen')
                ->count();
                
            // 获取工程师统计
            $engineerCount = DB::table('installation_engineers')
                ->count();
                
            // 构建返回数据
            $data = [
                'admin' => [
                    'id' => $admin->id,
                    'name' => $admin->name,
                    'avatar' => $admin->avatar,
                    'role' => $admin->role,
                    'created_at' => $admin->created_at,
                ],
                'stats' => [
                    'today_orders' => $todayOrders,
                    'yesterday_orders' => $yesterdayOrders,
                    'current_month_orders' => $currentMonthOrders,
                    'last_month_orders' => $lastMonthOrders,
                    'total_orders' => $totalOrders,
                    'today_amount' => $todayAmount,
                    'current_month_amount' => $currentMonthAmount,
                    'total_amount' => $totalAmount,
                    'user_count' => $userCount,
                    'new_user_count' => $newUserCount,
                    'device_count' => $deviceCount,
                    'new_device_count' => $newDeviceCount,
                    'institution_count' => $institutionCount,
                    'agent_count' => $agentCount,
                    'salesmen_count' => $salesmenCount,
                    'engineer_count' => $engineerCount,
                ],
                'recent_orders' => $recentOrders,
                'order_trend' => $this->getOrderTrend(),
                'user_trend' => $this->getUserTrend(),
            ];
            
            return response()->json([
                'code' => 0,
                'message' => '获取管理员工作台数据成功',
                'data' => $data
            ]);
        } catch (\Exception $e) {
            Log::error('获取管理员工作台数据失败: ' . $e->getMessage());
            
            return response()->json([
                'code' => 1,
                'message' => '获取管理员工作台数据失败: ' . $e->getMessage(),
                'data' => null
            ]);
        }
    }
    
    /**
     * 获取订单趋势
     *
     * @return array
     */
    private function getOrderTrend()
    {
        $startDate = Carbon::today()->subDays(30);
        $endDate = Carbon::today();
        
        $trend = DB::table('orders')
            ->whereDate('created_at', '>=', $startDate)
            ->whereDate('created_at', '<=', $endDate)
            ->selectRaw('DATE(created_at) as date, COUNT(*) as count, SUM(total_amount) as amount')
            ->groupBy('date')
            ->orderBy('date')
            ->get();
            
        $result = [];
        $currentDate = $startDate->copy();
        
        while ($currentDate->lte($endDate)) {
            $dateStr = $currentDate->format('Y-m-d');
            $found = false;
            
            foreach ($trend as $item) {
                if ($item->date == $dateStr) {
                    $result[] = [
                        'date' => $dateStr,
                        'count' => $item->count,
                        'amount' => $item->amount,
                    ];
                    $found = true;
                    break;
                }
            }
            
            if (!$found) {
                $result[] = [
                    'date' => $dateStr,
                    'count' => 0,
                    'amount' => 0,
                ];
            }
            
            $currentDate->addDay();
        }
        
        return $result;
    }
    
    /**
     * 获取用户趋势
     *
     * @return array
     */
    private function getUserTrend()
    {
        $startDate = Carbon::today()->subDays(30);
        $endDate = Carbon::today();
        
        $trend = DB::table('app_users')
            ->whereDate('created_at', '>=', $startDate)
            ->whereDate('created_at', '<=', $endDate)
            ->selectRaw('DATE(created_at) as date, COUNT(*) as count')
            ->groupBy('date')
            ->orderBy('date')
            ->get();
            
        $result = [];
        $currentDate = $startDate->copy();
        
        while ($currentDate->lte($endDate)) {
            $dateStr = $currentDate->format('Y-m-d');
            $found = false;
            
            foreach ($trend as $item) {
                if ($item->date == $dateStr) {
                    $result[] = [
                        'date' => $dateStr,
                        'count' => $item->count,
                    ];
                    $found = true;
                    break;
                }
            }
            
            if (!$found) {
                $result[] = [
                    'date' => $dateStr,
                    'count' => 0,
                ];
            }
            
            $currentDate->addDay();
        }
        
        return $result;
    }
}
