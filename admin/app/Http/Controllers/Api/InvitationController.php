<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class InvitationController extends Controller
{
    /**
     * 获取邀请函信息
     */
    public function getInvitation($id): JsonResponse
    {
        // 临时返回空数据，避免路由错误
        return response()->json([
            'code' => 200,
            'message' => '邀请函功能开发中',
            'data' => [
                'id' => $id,
                'title' => '邀请函',
                'status' => 'developing'
            ]
        ]);
    }

    /**
     * 参加邀请函
     */
    public function join($id, Request $request): JsonResponse
    {
        return response()->json([
            'code' => 200,
            'message' => '邀请函参加功能开发中',
            'data' => null
        ]);
    }

    /**
     * 注册邀请函
     */
    public function register($id, Request $request): JsonResponse
    {
        return response()->json([
            'code' => 200,
            'message' => '邀请函注册功能开发中',
            'data' => null
        ]);
    }
} 