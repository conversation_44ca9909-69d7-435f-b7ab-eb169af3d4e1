<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class AgentController extends Controller
{
    /**
     * 获取代理商工作台数据
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getWorkspace(Request $request)
    {
        $user = Auth::user();
        
        if (!$user) {
            return response()->json([
                'code' => 1002,
                'message' => '无效的令牌或已过期',
                'data' => null
            ]);
        }
        
        try {
            // 验证用户是否为代理商
            $agent = DB::table('agents')
                ->where('user_id', $user->id)
                ->first();
                
            if (!$agent) {
                return response()->json([
                    'code' => 1,
                    'message' => '您不是代理商，无权访问',
                    'data' => null
                ]);
            }
            
            // 获取当前日期
            $today = Carbon::today()->format('Y-m-d');
            $yesterday = Carbon::yesterday()->format('Y-m-d');
            $currentMonth = Carbon::today()->format('Y-m');
            $lastMonth = Carbon::today()->subMonth()->format('Y-m');
            
            // 获取代理商下属销售人员
            $salesmenIds = DB::table('salesmen')
                ->where('agent_id', $agent->id)
                ->pluck('id')
                ->toArray();
                
            // 获取今日销售数据
            $todaySales = DB::table('orders')
                ->whereIn('salesman_id', $salesmenIds)
                ->whereDate('created_at', $today)
                ->count();
                
            // 获取昨日销售数据
            $yesterdaySales = DB::table('orders')
                ->whereIn('salesman_id', $salesmenIds)
                ->whereDate('created_at', $yesterday)
                ->count();
                
            // 获取本月销售数据
            $currentMonthSales = DB::table('orders')
                ->whereIn('salesman_id', $salesmenIds)
                ->whereRaw("DATE_FORMAT(created_at, '%Y-%m') = ?", [$currentMonth])
                ->count();
                
            // 获取上月销售数据
            $lastMonthSales = DB::table('orders')
                ->whereIn('salesman_id', $salesmenIds)
                ->whereRaw("DATE_FORMAT(created_at, '%Y-%m') = ?", [$lastMonth])
                ->count();
                
            // 获取总销售数据
            $totalSales = DB::table('orders')
                ->whereIn('salesman_id', $salesmenIds)
                ->count();
                
            // 获取今日销售金额
            $todayAmount = DB::table('orders')
                ->whereIn('salesman_id', $salesmenIds)
                ->whereDate('created_at', $today)
                ->sum('total_amount');
                
            // 获取本月销售金额
            $currentMonthAmount = DB::table('orders')
                ->whereIn('salesman_id', $salesmenIds)
                ->whereRaw("DATE_FORMAT(created_at, '%Y-%m') = ?", [$currentMonth])
                ->sum('total_amount');
                
            // 获取总销售金额
            $totalAmount = DB::table('orders')
                ->whereIn('salesman_id', $salesmenIds)
                ->sum('total_amount');
                
            // 获取最近5条销售记录
            $recentSales = DB::table('orders')
                ->whereIn('salesman_id', $salesmenIds)
                ->orderBy('created_at', 'desc')
                ->limit(5)
                ->get();
                
            // 获取销售人员统计
            $salesmenCount = count($salesmenIds);
            
            // 获取客户统计
            $customerCount = DB::table('customers')
                ->whereIn('salesman_id', $salesmenIds)
                ->count();
                
            // 获取本月新增客户数
            $newCustomerCount = DB::table('customers')
                ->whereIn('salesman_id', $salesmenIds)
                ->whereRaw("DATE_FORMAT(created_at, '%Y-%m') = ?", [$currentMonth])
                ->count();
                
            // 获取销售人员业绩排名
            $salesmenRanking = DB::table('orders')
                ->join('salesmen', 'orders.salesman_id', '=', 'salesmen.id')
                ->whereIn('orders.salesman_id', $salesmenIds)
                ->whereRaw("DATE_FORMAT(orders.created_at, '%Y-%m') = ?", [$currentMonth])
                ->select('orders.salesman_id', 'salesmen.name', DB::raw('COUNT(*) as sales_count'), DB::raw('SUM(orders.total_amount) as sales_amount'))
                ->groupBy('orders.salesman_id', 'salesmen.name')
                ->orderBy('sales_amount', 'desc')
                ->limit(10)
                ->get();
                
            // 构建返回数据
            $data = [
                'agent' => [
                    'id' => $agent->id,
                    'name' => $agent->name,
                    'avatar' => $agent->avatar,
                    'phone' => $agent->phone,
                    'status' => $agent->status,
                    'level' => $agent->level,
                    'created_at' => $agent->created_at,
                ],
                'stats' => [
                    'today_sales' => $todaySales,
                    'yesterday_sales' => $yesterdaySales,
                    'current_month_sales' => $currentMonthSales,
                    'last_month_sales' => $lastMonthSales,
                    'total_sales' => $totalSales,
                    'today_amount' => $todayAmount,
                    'current_month_amount' => $currentMonthAmount,
                    'total_amount' => $totalAmount,
                    'salesmen_count' => $salesmenCount,
                    'customer_count' => $customerCount,
                    'new_customer_count' => $newCustomerCount,
                ],
                'recent_sales' => $recentSales,
                'salesmen_ranking' => $salesmenRanking,
                'sales_trend' => $this->getSalesTrend($salesmenIds),
            ];
            
            return response()->json([
                'code' => 0,
                'message' => '获取代理商工作台数据成功',
                'data' => $data
            ]);
        } catch (\Exception $e) {
            Log::error('获取代理商工作台数据失败: ' . $e->getMessage());
            
            return response()->json([
                'code' => 1,
                'message' => '获取代理商工作台数据失败: ' . $e->getMessage(),
                'data' => null
            ]);
        }
    }
    
    /**
     * 获取销售趋势
     *
     * @param array $salesmenIds
     * @return array
     */
    private function getSalesTrend($salesmenIds)
    {
        $startDate = Carbon::today()->subDays(30);
        $endDate = Carbon::today();
        
        $trend = DB::table('orders')
            ->whereIn('salesman_id', $salesmenIds)
            ->whereDate('created_at', '>=', $startDate)
            ->whereDate('created_at', '<=', $endDate)
            ->selectRaw('DATE(created_at) as date, COUNT(*) as count, SUM(total_amount) as amount')
            ->groupBy('date')
            ->orderBy('date')
            ->get();
            
        $result = [];
        $currentDate = $startDate->copy();
        
        while ($currentDate->lte($endDate)) {
            $dateStr = $currentDate->format('Y-m-d');
            $found = false;
            
            foreach ($trend as $item) {
                if ($item->date == $dateStr) {
                    $result[] = [
                        'date' => $dateStr,
                        'count' => $item->count,
                        'amount' => $item->amount,
                    ];
                    $found = true;
                    break;
                }
            }
            
            if (!$found) {
                $result[] = [
                    'date' => $dateStr,
                    'count' => 0,
                    'amount' => 0,
                ];
            }
            
            $currentDate->addDay();
        }
        
        return $result;
    }
}
