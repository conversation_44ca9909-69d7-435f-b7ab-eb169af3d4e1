<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use App\Models\AppUser;

class UserLoginController extends Controller
{
    /**
     * 用户登录
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function login(Request $request)
    {
        // 验证请求数据
        $validator = Validator::make($request->all(), [
            'username' => 'required|string',
            'password' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'code' => 1,
                'message' => $validator->errors()->first(),
                'data' => null
            ]);
        }

        // 获取用户
        $user = AppUser::where('phone', $request->username)
            ->orWhere('email', $request->username)
            ->first();

        if (!$user || !Hash::check($request->password, $user->password)) {
            return response()->json([
                'code' => 1,
                'message' => '用户名或密码错误',
                'data' => null
            ]);
        }

        // 创建令牌（30天过期）
        $token = $user->createToken('auth_token', ['*'], now()->addDays(30))->plainTextToken;

        return response()->json([
            'code' => 0,
            'message' => '登录成功',
            'data' => [
                'user' => $user,
                'token' => $token
            ]
        ]);
    }

    /**
     * 短信验证码登录
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function loginBySms(Request $request)
    {
        // 验证请求数据
        $validator = Validator::make($request->all(), [
            'phone' => 'required|string',
            'code' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'code' => 1,
                'message' => $validator->errors()->first(),
                'data' => null
            ]);
        }

        // 验证短信验证码
        // 这里应该调用验证码验证服务
        // 为简化示例，假设验证通过

        // 获取用户
        $user = AppUser::where('phone', $request->phone)->first();

        if (!$user) {
            return response()->json([
                'code' => 1,
                'message' => '用户不存在',
                'data' => null
            ]);
        }

        // 创建令牌（30天过期）
        $token = $user->createToken('auth_token', ['*'], now()->addDays(30))->plainTextToken;

        return response()->json([
            'code' => 0,
            'message' => '登录成功',
            'data' => [
                'user' => $user,
                'token' => $token
            ]
        ]);
    }

    /**
     * 用户注册
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function register(Request $request)
    {
        // 验证请求数据
        $validator = Validator::make($request->all(), [
            'phone' => 'required|string|unique:app_users',
            'password' => 'required|string|min:6',
            'code' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'code' => 1,
                'message' => $validator->errors()->first(),
                'data' => null
            ]);
        }

        // 验证短信验证码
        // 这里应该调用验证码验证服务
        // 为简化示例，假设验证通过

        // 创建用户
        $user = new AppUser();
        $user->phone = $request->phone;
        $user->password = Hash::make($request->password);
        $user->nickname = $request->nickname ?? '用户'.substr($request->phone, -4);
        $user->save();

        // 创建令牌（30天过期）
        $token = $user->createToken('auth_token', ['*'], now()->addDays(30))->plainTextToken;

        return response()->json([
            'code' => 0,
            'message' => '注册成功',
            'data' => [
                'user' => $user,
                'token' => $token
            ]
        ]);
    }
}
