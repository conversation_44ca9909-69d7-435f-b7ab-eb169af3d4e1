<?php

namespace App\Http\Controllers\Api\App;

use App\Http\Controllers\Controller;
use App\Models\Banner;
use Illuminate\Http\Request;

class BannerController extends Controller
{
    /**
     * 获取轮播图列表
     */
    public function index(Request $request)
    {
        $query = Banner::query();

        // 只获取激活状态的轮播图
        $query->where('status', Banner::STATUS_ACTIVE);

        // 筛选指定位置的轮播图
        if ($request->has('position') && $request->position) {
            $query->where('position', $request->position);
        } else {
            // 默认获取首页轮播图
            $query->where('position', Banner::POSITION_HOME);
        }

        // 筛选当前时间有效的轮播图
        $now = now();
        $query->where(function($q) use ($now) {
            $q->whereNull('start_time')
              ->orWhere('start_time', '<=', $now);
        })->where(function($q) use ($now) {
            $q->whereNull('end_time')
              ->orWhere('end_time', '>=', $now);
        });

        // 排序
        $query->orderBy('sort', 'asc');

        $banners = $query->get();

        return response()->json([
            'code' => 0,
            'message' => 'success',
            'data' => $banners
        ]);
    }

    /**
     * 获取指定轮播图
     */
    public function show($id)
    {
        $banner = Banner::findOrFail($id);

        return response()->json([
            'code' => 0,
            'message' => 'success',
            'data' => $banner
        ]);
    }
}
