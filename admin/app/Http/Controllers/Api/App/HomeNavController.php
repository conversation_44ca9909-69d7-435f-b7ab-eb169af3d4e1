<?php

namespace App\Http\Controllers\Api\App;

use App\Http\Controllers\Controller;
use App\Models\App\HomeNavItem;
use Illuminate\Http\Request;

class HomeNavController extends Controller
{
    /**
     * 获取所有导航项
     */
    public function index(Request $request)
    {
        $query = HomeNavItem::query();

        // 筛选条件
        if ($request->has('status') && $request->status !== '') {
            $query->where('status', $request->status);
        }

        // 排序
        $query->orderBy('sort_order', 'asc');

        $navItems = $query->get();

        return response()->json([
            'code' => 0,
            'message' => 'success',
            'data' => $navItems
        ]);
    }

    /**
     * 获取激活的导航项
     */
    public function getActiveNavItems(Request $request)
    {
        $navItems = HomeNavItem::where('status', 1)
            ->orderBy('sort_order', 'asc')
            ->get();

        // 如果没有数据，返回默认导航项
        if ($navItems->isEmpty()) {
            $navItems = $this->getDefaultNavItems();
        }

        return response()->json([
            'code' => 0,
            'message' => 'success',
            'data' => $navItems
        ]);
    }

    /**
     * 存储新导航项
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'icon' => 'required|string|max:100',
            'title' => 'required|string|max:50',
            'path' => 'required|string|max:255',
            'status' => 'required|boolean',
            'sort_order' => 'nullable|integer',
        ]);

        // 设置默认值
        if (!isset($validated['sort_order'])) {
            $validated['sort_order'] = 0;
        }

        // 创建导航项
        $navItem = HomeNavItem::create($validated);

        return response()->json([
            'code' => 0,
            'message' => '导航项创建成功',
            'data' => $navItem
        ]);
    }

    /**
     * 获取指定导航项
     */
    public function show($id)
    {
        $navItem = HomeNavItem::findOrFail($id);

        return response()->json([
            'code' => 0,
            'message' => 'success',
            'data' => $navItem
        ]);
    }

    /**
     * 更新导航项
     */
    public function update(Request $request, $id)
    {
        $navItem = HomeNavItem::findOrFail($id);

        $validated = $request->validate([
            'icon' => 'required|string|max:100',
            'title' => 'required|string|max:50',
            'path' => 'required|string|max:255',
            'status' => 'required|boolean',
            'sort_order' => 'nullable|integer',
        ]);

        // 更新导航项
        $navItem->update($validated);

        return response()->json([
            'code' => 0,
            'message' => '导航项更新成功',
            'data' => $navItem
        ]);
    }

    /**
     * 删除导航项
     */
    public function destroy($id)
    {
        $navItem = HomeNavItem::findOrFail($id);
        $navItem->delete();

        return response()->json([
            'code' => 0,
            'message' => '导航项删除成功',
            'data' => null
        ]);
    }

    /**
     * 获取默认导航项
     */
    private function getDefaultNavItems()
    {
        return [
            [
                'id' => 1,
                'icon' => 'shop-o',
                'title' => '商品分类',
                'path' => '/category',
                'status' => 1,
                'sort_order' => 10
            ],
            [
                'id' => 2,
                'icon' => 'gift-o',
                'title' => '每日特惠',
                'path' => '/daily-special',
                'status' => 1,
                'sort_order' => 20
            ],
            [
                'id' => 3,
                'icon' => 'balance-o',
                'title' => '净水充值',
                'path' => '/water-recharge',
                'status' => 1,
                'sort_order' => 30
            ],
            [
                'id' => 4,
                'icon' => 'location-o',
                'title' => '取水点',
                'path' => '/water-point',
                'status' => 1,
                'sort_order' => 40
            ],
            [
                'id' => 5,
                'icon' => 'service-o',
                'title' => '客户服务',
                'path' => '/service',
                'status' => 1,
                'sort_order' => 50
            ]
        ];
    }
}
