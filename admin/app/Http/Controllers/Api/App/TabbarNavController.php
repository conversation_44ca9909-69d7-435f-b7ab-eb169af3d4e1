<?php

namespace App\Http\Controllers\Api\App;

use App\Http\Controllers\Controller;
use App\Models\App\NavConfig;
use Illuminate\Http\Request;

class TabbarNavController extends Controller
{
    /**
     * 获取所有底部导航项
     */
    public function index(Request $request)
    {
        $query = NavConfig::query();

        // 筛选底部导航
        $query->where('type', 'tabbar');

        // 筛选条件
        if ($request->has('status') && $request->status !== '') {
            $query->where('status', $request->status);
        }

        // 排序
        $query->orderBy('sort_order', 'asc');

        $navItems = $query->get();

        return response()->json([
            'code' => 0,
            'message' => 'success',
            'data' => $navItems
        ]);
    }

    /**
     * 获取激活的底部导航项
     */
    public function getActiveNavItems(Request $request)
    {
        $navItems = NavConfig::where('type', 'tabbar')
            ->where('status', 1)
            ->orderBy('sort_order', 'asc')
            ->get();

        // 如果没有数据，返回默认导航项
        if ($navItems->isEmpty()) {
            $navItems = $this->getDefaultNavItems();
        }

        return response()->json([
            'code' => 0,
            'message' => 'success',
            'data' => $navItems
        ]);
    }

    /**
     * 获取Vant图标列表
     */
    public function getVantIcons()
    {
        $icons = NavConfig::getVantIcons();

        return response()->json([
            'code' => 0,
            'message' => 'success',
            'data' => $icons
        ]);
    }

    /**
     * 存储新导航项
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'nav_name' => 'required|string|max:10',
            'icon' => 'required|string|max:100',
            'path' => 'required|string',
            'status' => 'required|integer|in:0,1',
            'highlight' => 'nullable|integer|in:0,1',
            'sort_order' => 'nullable|integer',
        ]);

        // 设置默认值
        $validated['type'] = 'tabbar';
        if (!isset($validated['highlight'])) {
            $validated['highlight'] = 0;
        }
        if (!isset($validated['sort_order'])) {
            $validated['sort_order'] = 0;
        }

        // 创建导航项
        $navItem = NavConfig::create($validated);

        return response()->json([
            'code' => 0,
            'message' => '导航项创建成功',
            'data' => $navItem
        ]);
    }

    /**
     * 获取指定导航项
     */
    public function show($id)
    {
        $navItem = NavConfig::findOrFail($id);

        return response()->json([
            'code' => 0,
            'message' => 'success',
            'data' => $navItem
        ]);
    }

    /**
     * 更新导航项
     */
    public function update(Request $request, $id)
    {
        $navItem = NavConfig::findOrFail($id);

        $validated = $request->validate([
            'nav_name' => 'required|string|max:10',
            'icon' => 'required|string|max:100',
            'path' => 'required|string',
            'status' => 'required|integer|in:0,1',
            'highlight' => 'nullable|integer|in:0,1',
            'sort_order' => 'nullable|integer',
        ]);

        // 更新导航项
        $navItem->update($validated);

        return response()->json([
            'code' => 0,
            'message' => '导航项更新成功',
            'data' => $navItem
        ]);
    }

    /**
     * 删除导航项
     */
    public function destroy($id)
    {
        $navItem = NavConfig::findOrFail($id);
        $navItem->delete();

        return response()->json([
            'code' => 0,
            'message' => '导航项删除成功',
            'data' => null
        ]);
    }

    /**
     * 获取默认导航项
     */
    private function getDefaultNavItems()
    {
        return [
            [
                'id' => 1,
                'nav_name' => '首页',
                'icon' => 'home-o',
                'path' => '/',
                'status' => 1,
                'highlight' => 1,
                'sort_order' => 10,
                'type' => 'tabbar'
            ],
            [
                'id' => 2,
                'nav_name' => '设备',
                'icon' => 'filter-o',
                'path' => '/device',
                'status' => 1,
                'highlight' => 0,
                'sort_order' => 20,
                'type' => 'tabbar'
            ],
            [
                'id' => 3,
                'nav_name' => '取水点',
                'icon' => 'location-o',
                'path' => '/water-point',
                'status' => 1,
                'highlight' => 0,
                'sort_order' => 30,
                'type' => 'tabbar'
            ],
            [
                'id' => 4,
                'nav_name' => '商家',
                'icon' => 'shop-o',
                'path' => '/merchant',
                'status' => 1,
                'highlight' => 0,
                'sort_order' => 40,
                'type' => 'tabbar'
            ],
            [
                'id' => 5,
                'nav_name' => '我的',
                'icon' => 'user-o',
                'path' => '/user',
                'status' => 1,
                'highlight' => 0,
                'sort_order' => 50,
                'type' => 'tabbar'
            ]
        ];
    }
}
