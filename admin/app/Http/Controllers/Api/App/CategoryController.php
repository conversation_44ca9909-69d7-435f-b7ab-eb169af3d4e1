<?php

namespace App\Http\Controllers\Api\App;

use App\Http\Controllers\Controller;
use App\Models\ProductCategory;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class CategoryController extends Controller
{
    /**
     * 获取商品分类列表
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        try {
            $query = ProductCategory::query();
            
            // 只获取激活状态的分类
            $query->where('status', 1);
            
            // 排序
            $query->orderBy('sort', 'asc');
            
            $categories = $query->get();
            
            // 构建树形结构
            $tree = $this->buildCategoryTree($categories);
            
            return response()->json([
                'code' => 0,
                'message' => 'success',
                'data' => $tree
            ]);
        } catch (\Exception $e) {
            Log::error('获取分类列表失败：' . $e->getMessage());
            return response()->json([
                'code' => 1,
                'message' => '获取分类列表失败',
                'data' => []
            ]);
        }
    }

    /**
     * 获取指定分类详情
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        try {
            $category = ProductCategory::find($id);
            
            if (!$category) {
                return response()->json([
                    'code' => 1,
                    'message' => '分类不存在',
                    'data' => null
                ]);
            }
            
            return response()->json([
                'code' => 0,
                'message' => 'success',
                'data' => $category
            ]);
        } catch (\Exception $e) {
            Log::error('获取分类详情失败：' . $e->getMessage());
            return response()->json([
                'code' => 1,
                'message' => '获取分类详情失败',
                'data' => null
            ]);
        }
    }

    /**
     * 构建分类树形结构
     *
     * @param \Illuminate\Database\Eloquent\Collection $categories
     * @param int $parentId
     * @return array
     */
    private function buildCategoryTree($categories, $parentId = 0)
    {
        $tree = [];
        
        foreach ($categories as $category) {
            if ($category->parent_id == $parentId) {
                $children = $this->buildCategoryTree($categories, $category->id);
                
                if ($children) {
                    $category->children = $children;
                }
                
                $tree[] = $category;
            }
        }
        
        return $tree;
    }
}
