<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\App\NavConfig;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class TabbarController extends Controller
{
    /**
     * 获取底部导航菜单 - 公共接口，不需要认证
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        try {
            // 记录请求信息，帮助调试
            Log::info('底部导航请求', [
                'ip' => $request->ip(),
                'user_agent' => $request->header('User-Agent'),
                'params' => $request->all()
            ]);

            $navItems = NavConfig::where('type', 'tabbar')
                ->where('status', 1)
                ->orderBy('sort_order', 'asc')
                ->get();

            // 如果没有数据，返回默认导航项
            if ($navItems->isEmpty()) {
                $navItems = $this->getDefaultNavItems();
            }

            return response()->json([
                'code' => 0,
                'message' => 'success',
                'data' => $navItems
            ]);
        } catch (\Exception $e) {
            Log::error('获取底部导航失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // 出错时返回默认导航
            return response()->json([
                'code' => 0,
                'message' => '返回默认导航',
                'data' => $this->getDefaultNavItems()
            ]);
        }
    }

    /**
     * 获取默认导航项
     *
     * @return array
     */
    private function getDefaultNavItems()
    {
        return [
            [
                'id' => 1,
                'nav_name' => '首页',
                'title' => '首页',
                'icon' => 'home-o',
                'vant_icon' => 'home-o',
                'path' => '/',
                'status' => 1,
                'highlight' => 1,
                'sort_order' => 10,
                'type' => 'tabbar'
            ],
            [
                'id' => 2,
                'nav_name' => '设备',
                'title' => '设备',
                'icon' => 'filter-o',
                'vant_icon' => 'filter-o',
                'path' => '/purifier/devices',
                'status' => 1,
                'highlight' => 0,
                'sort_order' => 20,
                'type' => 'tabbar'
            ],
            [
                'id' => 3,
                'nav_name' => '取水点',
                'title' => '取水点',
                'icon' => 'location-o',
                'vant_icon' => 'location-o',
                'path' => '/water-point',
                'status' => 1,
                'highlight' => 0,
                'sort_order' => 30,
                'type' => 'tabbar'
            ],
            [
                'id' => 4,
                'nav_name' => '商家',
                'title' => '商家',
                'icon' => 'shop-o',
                'vant_icon' => 'shop-o',
                'path' => '/merchant',
                'status' => 1,
                'highlight' => 0,
                'sort_order' => 40,
                'type' => 'tabbar'
            ],
            [
                'id' => 5,
                'nav_name' => '我的',
                'title' => '我的',
                'icon' => 'user-o',
                'vant_icon' => 'user-o',
                'path' => '/user',
                'status' => 1,
                'highlight' => 0,
                'sort_order' => 50,
                'type' => 'tabbar'
            ]
        ];
    }
}
