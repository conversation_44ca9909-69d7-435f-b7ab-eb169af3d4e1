<?php

namespace App\Http\Controllers\Api\Vip;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class PoolInfoController extends Controller
{
    /**
     * 获取团队VIP数量（非递归方式，避免MySQL 5.7递归限制）
     *
     * @param int $userId 用户ID
     * @param Carbon|null $endDate 截止日期，如果提供则只统计在此日期前成为VIP的用户
     * @return int 团队VIP数量
     */
    private function getTeamVipCount($userId, $endDate = null)
    {
        // 使用迭代方式代替递归，避免MySQL 5.7递归限制
        $processedIds = []; // 已处理的用户ID，防止循环引用
        $pendingIds = [$userId]; // 待处理的用户ID队列
        $teamVipCount = 0; // 团队VIP数量

        // 使用BFS（广度优先搜索）遍历团队成员
        while (!empty($pendingIds)) {
            // 每次最多处理100个ID，避免IN子句过长
            $batchIds = array_slice($pendingIds, 0, 100);
            $pendingIds = array_slice($pendingIds, 100);

            // 标记这批ID为已处理
            $processedIds = array_merge($processedIds, $batchIds);

            // 获取这批用户的直推用户
            $query = DB::table('app_users')
                ->whereIn('referrer_id', $batchIds)
                ->where('is_vip', 1)
                ->where('is_vip_paid', 1);

            // 如果提供了截止日期，则只统计在此日期前成为VIP的用户
            if ($endDate) {
                $query->where('vip_paid_at', '<=', $endDate);
            }

            $directUsers = $query->get(['id']);

            if ($directUsers->isEmpty()) {
                continue;
            }

            // 计算直推VIP用户数量
            $directVipCount = $directUsers->count();
            $teamVipCount += $directVipCount;

            // 将未处理过的直推用户添加到待处理队列
            foreach ($directUsers as $directUser) {
                if (!in_array($directUser->id, $processedIds) && !in_array($directUser->id, $pendingIds)) {
                    $pendingIds[] = $directUser->id;
                }
            }

            // 防止处理过多层级导致性能问题
            if (count($processedIds) > 1000) {
                \Log::warning('团队成员过多，已达到处理上限', [
                    'userId' => $userId,
                    'processedCount' => count($processedIds)
                ]);
                break;
            }
        }

        return $teamVipCount;
    }

    /**
     * 获取团队充值台数（非递归方式，避免MySQL 5.7递归限制）
     *
     * @param int $userId 用户ID
     * @param Carbon|null $startDate 开始日期
     * @param Carbon|null $endDate 结束日期
     * @return int 团队充值台数
     */
    private function getTeamRechargeCount($userId, $startDate = null, $endDate = null)
    {
        // 使用迭代方式代替递归，避免MySQL 5.7递归限制
        $processedIds = []; // 已处理的用户ID，防止循环引用
        $pendingIds = [$userId]; // 待处理的用户ID队列
        $teamRechargeCount = 0; // 团队充值台数

        // 使用BFS（广度优先搜索）遍历团队成员
        while (!empty($pendingIds)) {
            // 每次最多处理100个ID，避免IN子句过长
            $batchIds = array_slice($pendingIds, 0, 100);
            $pendingIds = array_slice($pendingIds, 100);

            // 标记这批ID为已处理
            $processedIds = array_merge($processedIds, $batchIds);

            // 获取这批用户的直推用户
            $directUsers = DB::table('app_users')
                ->whereIn('referrer_id', $batchIds)
                ->get(['id']);

            if ($directUsers->isEmpty()) {
                continue;
            }

            // 获取直推用户的充值台数
            $directUserIds = $directUsers->pluck('id')->toArray();
            $query = DB::table('tapp_devices')
                ->whereIn('app_user_id', $directUserIds)
                ->where('is_self_use', 0)
                ->where('status', 'E');

            // 如果提供了日期范围，则只统计在此日期范围内激活的设备
            if ($startDate && $endDate) {
                $query->whereBetween('created_at', [$startDate, $endDate]);
            }

            $directRechargeCount = $query->count();
            $teamRechargeCount += $directRechargeCount;

            // 将未处理过的直推用户添加到待处理队列
            foreach ($directUsers as $directUser) {
                if (!in_array($directUser->id, $processedIds) && !in_array($directUser->id, $pendingIds)) {
                    $pendingIds[] = $directUser->id;
                }
            }

            // 防止处理过多层级导致性能问题
            if (count($processedIds) > 1000) {
                \Log::warning('团队成员过多，已达到处理上限', [
                    'userId' => $userId,
                    'processedCount' => count($processedIds)
                ]);
                break;
            }
        }

        return $teamRechargeCount;
    }

    /**
     * 计算达标人数
     *
     * @param string $type 达标类型：vip_junior, vip_middle, vip_senior, recharge_junior, recharge_middle, recharge_senior
     * @param Carbon|null $endDate 截止日期
     * @return int 达标人数
     */
    private function getQualifiedUserCount($type, $endDate = null)
    {
        // 获取所有VIP用户
        $vipUsers = DB::table('app_users')
            ->where('is_vip', 1)
            ->where('is_vip_paid', 1)
            ->get(['id']);

        if ($vipUsers->isEmpty()) {
            return 0;
        }

        $qualifiedCount = 0;

        foreach ($vipUsers as $user) {
            $userId = $user->id;

            // 获取用户团队VIP数量（包括自己）
            $teamVipCount = $this->getTeamVipCount($userId, $endDate) + 1;

            // 获取用户团队充值台数
            $teamRechargeCount = $this->getTeamRechargeCount($userId, null, $endDate);

            // 获取用户本月直推VIP数量
            $directVipCount = 0;
            if ($type === 'vip_senior') {
                $query = DB::table('app_users')
                    ->where('referrer_id', $userId)
                    ->where('is_vip', 1)
                    ->where('is_vip_paid', 1);

                if ($endDate) {
                    $query->where('vip_paid_at', '<=', $endDate);
                }

                $directVipCount = $query->count();
            }

            // 根据达标类型判断是否达标
            $isQualified = false;
            switch ($type) {
                case 'vip_junior':
                    $isQualified = $teamVipCount >= 3;
                    break;
                case 'vip_middle':
                    $isQualified = $teamVipCount >= 10;
                    break;
                case 'vip_senior':
                    $isQualified = $teamVipCount >= 30 && $directVipCount > 0;
                    break;
                case 'recharge_junior':
                    $isQualified = $teamRechargeCount >= 10;
                    break;
                case 'recharge_middle':
                    $isQualified = $teamRechargeCount >= 30;
                    break;
                case 'recharge_senior':
                    $isQualified = $teamRechargeCount >= 80;
                    break;
            }

            if ($isQualified) {
                $qualifiedCount++;
            }
        }

        return $qualifiedCount;
    }
    /**
     * 递归获取团队VIP数量
     *
     * @param int $userId 用户ID
     * @param Carbon|null $endDate 截止日期，如果提供则只统计在此日期前成为VIP的用户
     * @param array $processedIds 已处理的用户ID数组，防止循环引用
     * @return int 团队VIP数量
     */
    private function getTeamVipCountRecursive($userId, $endDate = null, $processedIds = [])
    {
        // 防止循环引用
        if (in_array($userId, $processedIds)) {
            return 0;
        }

        // 添加当前用户ID到已处理数组
        $processedIds[] = $userId;

        // 获取直推VIP用户
        $query = DB::table('app_users')
            ->where('referrer_id', $userId)
            ->where('is_vip', 1)
            ->where('is_vip_paid', 1);

        // 如果提供了截止日期，则只统计在此日期前成为VIP的用户
        if ($endDate) {
            $query->where('vip_paid_at', '<=', $endDate);
        }

        $directVipUsers = $query->get(['id']);

        // 如果没有直推用户，返回0
        if ($directVipUsers->isEmpty()) {
            return 0;
        }

        // 计算直推VIP用户数量
        $directVipCount = $directVipUsers->count();

        // 递归获取每个直推用户的团队VIP数量
        $teamVipCount = $directVipCount;
        foreach ($directVipUsers as $directUser) {
            $teamVipCount += $this->getTeamVipCountRecursive($directUser->id, $endDate, $processedIds);
        }

        return $teamVipCount;
    }

    /**
     * 递归获取团队充值台数
     *
     * @param int $userId 用户ID
     * @param Carbon|null $startDate 开始日期
     * @param Carbon|null $endDate 结束日期
     * @param array $processedIds 已处理的用户ID数组，防止循环引用
     * @return int 团队充值台数
     */
    private function getTeamRechargeCountRecursive($userId, $startDate = null, $endDate = null, $processedIds = [])
    {
        // 防止循环引用
        if (in_array($userId, $processedIds)) {
            return 0;
        }

        // 添加当前用户ID到已处理数组
        $processedIds[] = $userId;

        // 获取直推用户
        $directUsers = DB::table('app_users')
            ->where('referrer_id', $userId)
            ->get(['id']);

        // 如果没有直推用户，返回0
        if ($directUsers->isEmpty()) {
            return 0;
        }

        // 获取直推用户的充值台数
        $directUserIds = $directUsers->pluck('id')->toArray();
        $query = DB::table('tapp_devices')
            ->whereIn('app_user_id', $directUserIds)
            ->where('is_self_use', 0)
            ->where('status', 'E');

        // 如果提供了日期范围，则只统计在此日期范围内激活的设备
        if ($startDate && $endDate) {
            $query->whereBetween('created_at', [$startDate, $endDate]);
        }

        $directRechargeCount = $query->count();

        // 递归获取每个直推用户的团队充值台数
        $teamRechargeCount = $directRechargeCount;
        foreach ($directUsers as $directUser) {
            $teamRechargeCount += $this->getTeamRechargeCountRecursive($directUser->id, $startDate, $endDate, $processedIds);
        }

        return $teamRechargeCount;
    }
    /**
     * 获取VIP奖金池信息
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        // 获取月份参数，默认为当前月
        $monthParam = $request->input('month', 'current');

        // 确定查询的月份
        $now = Carbon::now();
        if ($monthParam === 'last') {
            $currentMonth = $now->copy()->subMonth()->format('Y-m');
            $startDate = $now->copy()->subMonth()->startOfMonth();
            $endDate = $now->copy()->subMonth()->endOfMonth();
        } else {
            $currentMonth = $now->format('Y-m');
            $startDate = $now->copy()->startOfMonth();
            $endDate = $now->copy()->endOfMonth();
        }

        // 检查团队关系表是否存在
        $teamTableExists = false;
        try {
            $teamTableExistsQuery = "SHOW TABLES LIKE 'app_user_team_members'";
            $teamTableExistsResult = DB::select($teamTableExistsQuery);
            $teamTableExists = !empty($teamTableExistsResult);
        } catch (\Exception $tableEx) {
            // 表不存在，使用默认值
        }

        // 获取指定月份的VIP总人数
        $vipCount = 0;
        if ($monthParam === 'last') {
            // 上月VIP总人数 - 在上月月底前已经是VIP的用户
            $vipCount = DB::table('app_users')
                ->where('is_vip', 1)
                ->where('is_vip_paid', 1)
                ->where('vip_paid_at', '<=', $endDate)
                ->count();
        } else {
            // 本月VIP总人数 - 当前所有VIP用户
            $vipCount = DB::table('app_users')
                ->where('is_vip', 1)
                ->where('is_vip_paid', 1)
                ->count();
        }

        \Log::info('VIP总人数', [
            'month' => $monthParam,
            'vipCount' => $vipCount
        ]);

        // 获取指定月份的充值总台数
        $rechargeCount = 0;
        if ($monthParam === 'last') {
            // 上月充值总台数 - 在上月内激活的设备
            $rechargeCount = DB::table('tapp_devices')
                ->where('is_self_use', 0)
                ->where('status', 'E')
                ->whereBetween('created_at', [$startDate, $endDate])
                ->count();
        } else {
            // 本月充值总台数 - 在本月内激活的设备
            $rechargeCount = DB::table('tapp_devices')
                ->where('is_self_use', 0)
                ->where('status', 'E')
                ->whereBetween('created_at', [$startDate, $endDate])
                ->count();
        }

        \Log::info('充值总台数', [
            'month' => $monthParam,
            'rechargeCount' => $rechargeCount
        ]);

        // 使用辅助方法计算达标人数
        // 初级VIP分红（团队VIP总人数满3人，包括自己）
        $juniorVipTeams = $this->getQualifiedUserCount('vip_junior', $endDate);
        \Log::info('初级VIP分红达标人数', [
            'month' => $monthParam,
            'juniorVipTeams' => $juniorVipTeams
        ]);

        // 中级VIP分红（团队VIP总人数满10人，包括自己）
        $middleVipTeams = $this->getQualifiedUserCount('vip_middle', $endDate);
        \Log::info('中级VIP分红达标人数', [
            'month' => $monthParam,
            'middleVipTeams' => $middleVipTeams
        ]);

        // 高级VIP分红（团队VIP总人数满30人，包括自己，且本月直推不为0）
        $seniorVipTeams = $this->getQualifiedUserCount('vip_senior', $endDate);
        \Log::info('高级VIP分红达标人数', [
            'month' => $monthParam,
            'seniorVipTeams' => $seniorVipTeams
        ]);

        // 计算高级VIP分红符合条件用户的当月直推总数
        $totalSeniorDirectVips = 0;

        // 使用辅助方法计算达标人数
        // 初级充值分红（团队充值满10个套餐）
        $juniorRechargeTeams = $this->getQualifiedUserCount('recharge_junior', $endDate);
        \Log::info('初级充值分红达标人数', [
            'month' => $monthParam,
            'juniorRechargeTeams' => $juniorRechargeTeams
        ]);

        // 中级充值分红（团队充值满30个套餐）
        $middleRechargeTeams = $this->getQualifiedUserCount('recharge_middle', $endDate);
        \Log::info('中级充值分红达标人数', [
            'month' => $monthParam,
            'middleRechargeTeams' => $middleRechargeTeams
        ]);

        // 高级充值分红（团队充值满80个套餐）
        $seniorRechargeTeams = $this->getQualifiedUserCount('recharge_senior', $endDate);
        \Log::info('高级充值分红达标人数', [
            'month' => $monthParam,
            'seniorRechargeTeams' => $seniorRechargeTeams
        ]);

        // 计算总达标人数（不重复统计，满足VIP初级达标或充值初级达标任一条件即可）
        // 获取所有VIP用户
        $vipUsers = DB::table('app_users')
            ->where('is_vip', 1)
            ->where('is_vip_paid', 1)
            ->get(['id']);

        $totalQualifiedUsers = 0;

        foreach ($vipUsers as $user) {
            $userId = $user->id;

            // 获取用户团队VIP数量（包括自己）
            $teamVipCount = $this->getTeamVipCount($userId, $endDate) + 1;

            // 获取用户团队充值台数
            $teamRechargeCount = $this->getTeamRechargeCount($userId, null, $endDate);

            // 判断是否达标（满足VIP初级达标或充值初级达标任一条件即可）
            $isQualified = $teamVipCount >= 3 || $teamRechargeCount >= 10;

            if ($isQualified) {
                $totalQualifiedUsers++;
            }
        }

        \Log::info('总达标人数', [
            'month' => $monthParam,
            'totalQualifiedUsers' => $totalQualifiedUsers
        ]);

        // 构建返回数据
        $responseData = [
            'vipCount' => $vipCount,
            'rechargeCount' => $rechargeCount,
            'juniorVipTeams' => $juniorVipTeams,
            'middleVipTeams' => $middleVipTeams,
            'seniorVipTeams' => $seniorVipTeams,
            'juniorRechargeTeams' => $juniorRechargeTeams,
            'middleRechargeTeams' => $middleRechargeTeams,
            'seniorRechargeTeams' => $seniorRechargeTeams,
            'totalQualifiedUsers' => $totalQualifiedUsers,
            'totalSeniorDirectVips' => $totalSeniorDirectVips,
            'totalSeniorDirectRecharges' => 0,
            'month' => $monthParam === 'last' ? '上月' : '本月'
        ];

        return response()->json([
            'code' => 0,
            'message' => '获取VIP奖金池信息成功',
            'data' => $responseData
        ]);
    }
}
