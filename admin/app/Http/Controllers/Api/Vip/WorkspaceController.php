<?php

namespace App\Http\Controllers\Api\Vip;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class WorkspaceController extends Controller
{
    /**
     * 获取VIP工作区数据
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        // 记录请求信息
        Log::info('获取VIP工作区数据请求', [
            'headers' => $request->header(),
            'token' => $request->bearerToken(),
            'ip' => $request->ip(),
            'user_agent' => $request->userAgent()
        ]);

        // 尝试获取认证用户
        $user = Auth::user();
        $userId = $request->input('user_id');

        // 如果未登录且未提供user_id参数，返回默认数据
        if (!$user && !$userId) {
            Log::info('用户未登录且未提供user_id参数，返回默认数据');
            
            return response()->json([
                'code' => 0,
                'message' => '获取VIP工作区数据成功（默认数据）',
                'data' => [
                    'user' => [
                        'id' => '',
                        'nickname' => '用户',
                        'avatar' => '',
                        'phone' => '',
                        'vip_paid_at' => '',
                    ],
                    'referrer' => [
                        'id' => 0,
                        'nickname' => '点点够',
                        'phone' => '',
                    ],
                    'team' => [
                        'total' => 0,
                        'new' => 0,
                        'direct' => 0,
                        'new_direct' => 0,
                        'devices' => 0,
                        'new_devices' => 0,
                    ],
                    'dividend' => null,
                ]
            ]);
        }

        // 如果有user_id参数但没有认证用户，尝试获取指定用户
        if (!$user && $userId) {
            $user = DB::table('app_users')
                ->where('id', $userId)
                ->first();

            if (!$user) {
                return response()->json([
                    'code' => 0,
                    'message' => '获取VIP工作区数据成功（默认数据）',
                    'data' => [
                        'user' => [
                            'id' => '',
                            'nickname' => '用户',
                            'avatar' => '',
                            'phone' => '',
                            'vip_paid_at' => '',
                        ],
                        'referrer' => [
                            'id' => 0,
                            'nickname' => '点点够',
                            'phone' => '',
                        ],
                        'team' => [
                            'total' => 0,
                            'new' => 0,
                            'direct' => 0,
                            'new_direct' => 0,
                            'devices' => 0,
                            'new_devices' => 0,
                        ],
                        'dividend' => null,
                    ]
                ]);
            }
        }

        try {
            // 获取VIP信息
            $vipInfo = DB::table('app_users')
                ->select('id', 'nickname', 'avatar', 'phone', 'vip_paid_at', 'referrer_id', 'is_vip')
                ->where('id', $user->id)
                ->first();

            // 如果用户不是VIP，仍然返回基本信息，但是team和dividend为空
            $isVip = $vipInfo->is_vip ?? 0;

            // 获取推荐人信息
            $referrerInfo = null;
            if (isset($vipInfo->referrer_id) && $vipInfo->referrer_id) {
                $referrerInfo = DB::table('app_users')
                    ->select('id', 'nickname', 'phone')
                    ->where('id', $vipInfo->referrer_id)
                    ->first();
            }

            // 初始化团队和分红数据
            $teamData = [
                'total' => 0,
                'new' => 0,
                'direct' => 0,
                'new_direct' => 0,
                'devices' => 0,
                'new_devices' => 0,
            ];

            $dividendInfo = null;

            // 只有VIP用户才获取团队和分红信息
            if ($isVip == 1) {
                // 获取团队信息
                $teamCount = DB::table('app_users')
                    ->where('referrer_id', $user->id)
                    ->where('is_vip', 1)
                    ->count();

                // 获取本月新增团队成员数
                $now = Carbon::now();
                $startOfMonth = $now->copy()->startOfMonth();
                $endOfMonth = $now->copy()->endOfMonth();

                $newTeamCount = DB::table('app_users')
                    ->where('referrer_id', $user->id)
                    ->where('is_vip', 1)
                    ->whereBetween('vip_paid_at', [$startOfMonth, $endOfMonth])
                    ->count();

                // 获取直推人数
                $directCount = DB::table('app_users')
                    ->where('referrer_id', $user->id)
                    ->where('is_vip', 1)
                    ->count();

                // 获取本月直推人数
                $newDirectCount = DB::table('app_users')
                    ->where('referrer_id', $user->id)
                    ->where('is_vip', 1)
                    ->whereBetween('vip_paid_at', [$startOfMonth, $endOfMonth])
                    ->count();

                // 获取团队充值台数
                $teamDeviceCount = DB::table('tapp_devices')
                    ->join('app_users', 'tapp_devices.app_user_id', '=', 'app_users.id')
                    ->where('app_users.referrer_id', $user->id)
                    ->count();

                // 获取本月新增团队充值台数
                $newTeamDeviceCount = DB::table('tapp_devices')
                    ->join('app_users', 'tapp_devices.app_user_id', '=', 'app_users.id')
                    ->where('app_users.referrer_id', $user->id)
                    ->whereBetween('tapp_devices.created_at', [$startOfMonth, $endOfMonth])
                    ->count();

                // 更新团队数据
                $teamData = [
                    'total' => $teamCount,
                    'new' => $newTeamCount,
                    'direct' => $directCount,
                    'new_direct' => $newDirectCount,
                    'devices' => $teamDeviceCount,
                    'new_devices' => $newTeamDeviceCount,
                ];

                // 获取最新一条分红记录
                $dividendInfo = DB::table('vip_dividends')
                    ->where('user_id', $user->id)
                    ->orderBy('id', 'desc')
                    ->first();
            }

            // 准备返回数据
            $data = [
                'user' => [
                    'id' => $vipInfo->id ?? '',
                    'nickname' => $vipInfo->nickname ?? '用户',
                    'avatar' => $vipInfo->avatar ?? '',
                    'phone' => $vipInfo->phone ?? '',
                    'vip_paid_at' => $vipInfo->vip_paid_at ?? '',
                ],
                'referrer' => $referrerInfo ? [
                    'id' => $referrerInfo->id,
                    'nickname' => $referrerInfo->nickname,
                    'phone' => $referrerInfo->phone,
                ] : [
                    'id' => 0,
                    'nickname' => '点点够',
                    'phone' => '',
                ],
                'team' => $teamData,
                'dividend' => $dividendInfo ? [
                    'id' => $dividendInfo->id,
                    'amount' => $dividendInfo->amount,
                    'month' => $dividendInfo->month,
                    'status' => $dividendInfo->status,
                    'created_at' => $dividendInfo->created_at,
                ] : null,
            ];

            return response()->json([
                'code' => 0,
                'message' => '获取VIP工作区数据成功',
                'data' => $data
            ]);
            
        } catch (\Exception $e) {
            Log::error('获取VIP工作区数据失败', [
                'userId' => $user->id ?? null,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            // 返回明确的错误响应，而不是默认数据
            return response()->json([
                'code' => 500, // 或者其他表示服务器错误的code
                'message' => '获取VIP工作区数据时发生错误: ' . $e->getMessage(),
                'data' => null // 或者可以包含一些错误详情，但不包含默认的业务数据
            ], 500); // 同时返回HTTP 500状态码
        }
    }
} 