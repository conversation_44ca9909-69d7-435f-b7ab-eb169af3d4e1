<?php

namespace App\Http\Controllers\Api\Vip;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class TimeInfoController extends Controller
{
    /**
     * 获取VIP时间信息
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        $user = Auth::user();

        if (!$user) {
            return response()->json([
                'code' => 1002,
                'message' => '无效的令牌或已过期',
                'data' => null
            ]);
        }

        // 检查用户是否是VIP
        if ($user->is_vip != 1) {
            return response()->json([
                'code' => 1003,
                'message' => '您不是VIP会员',
                'data' => null
            ]);
        }

        // 获取VIP详细信息
        $vipInfo = DB::table('app_users')
            ->select('id', 'name', 'nickname', 'avatar', 'vip_paid_at', 'created_at', 'is_vip_paid', 'referrer_id')
            ->where('id', $user->id)
            ->first();
        
        // 获取推荐人信息
        $referrerInfo = null;
        if ($vipInfo->referrer_id) {
            $referrerInfo = DB::table('app_users')
                ->select('id', 'name', 'nickname')
                ->where('id', $vipInfo->referrer_id)
                ->first();
        }

        // 设置日期格式
        $joinDate = $vipInfo->created_at ? date('Y-m-d', strtotime($vipInfo->created_at)) : null;
        $vipJoinDate = $vipInfo->vip_paid_at ? date('Y-m-d', strtotime($vipInfo->vip_paid_at)) : $joinDate;
        
        // 设置完款状态
        $paymentStatus = $vipInfo->is_vip_paid == 1 ? '已完款' : '待支付';

        // 获取当前月份
        $currentMonth = date('Y-m');
        // 获取上个月
        $lastMonth = date('Y-m', strtotime('-1 month'));

        // 构建返回数据
        $data = [
            // 用户基本信息
            'name' => $vipInfo->name ?: $vipInfo->nickname ?: '尊贵会员',
            'avatar' => $vipInfo->avatar ?: '/app/images/profile/default-avatar.png',
            'user_id' => $user->id,
            
            // 时间信息
            'join_date' => $joinDate,
            'vip_join_date' => $vipJoinDate,
            'joinDate' => $joinDate,
            'vipJoinDate' => $vipJoinDate,
            'vip_expire_date' => null, // VIP不过期
            'vip_status' => '有效',
            'vip_payment_status' => $paymentStatus,
            'paymentStatus' => $paymentStatus,
            
            // 推荐人信息
            'referrer_id' => $vipInfo->referrer_id,
            'referrer_name' => $referrerInfo ? ($referrerInfo->name ?: $referrerInfo->nickname) : '点点够',
            
            // 月份信息
            'current_month' => $currentMonth,
            'last_month' => $lastMonth,
            
            // 调试信息
            'debug_info' => $request->has('debug') ? [
                'user_id' => $user->id,
                'name' => $vipInfo->name,
                'nickname' => $vipInfo->nickname,
                'avatar' => $vipInfo->avatar
            ] : null
        ];

        return response()->json([
            'code' => 0,
            'message' => '获取VIP时间信息成功',
            'data' => $data
        ]);
    }
} 