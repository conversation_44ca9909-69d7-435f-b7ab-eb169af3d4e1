<?php

namespace App\Http\Controllers\Api\Vip;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class ProfileController extends Controller
{
    /**
     * 获取VIP个人资料信息
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        // 获取用户ID参数，如果没有则使用认证用户
        $userId = $request->input('user_id');
        
        // 尝试获取认证用户
        $user = Auth::user();

        // 如果指定了user_id或没有认证用户，尝试从数据库获取用户
        if ($userId || !$user) {
            if ($userId) {
                $user = DB::table('app_users')
                    ->where('id', $userId)
                    ->first();
            } else {
                // 获取任意一个VIP用户作为示例
                $user = DB::table('app_users')
                    ->where('is_vip', 1)
                    ->first();
            }
        }

        // 如果仍然找不到用户，使用默认数据
        if (!$user) {
            return response()->json([
                'code' => 0,
                'message' => '获取VIP个人资料成功(默认数据)',
                'data' => [
                    'id' => 1,
                    'name' => '尊贵会员',
                    'avatar' => '/app/images/profile/default-avatar.png',
                    'join_date' => date('Y-m-d'),
                    'vip_join_date' => date('Y-m-d'),
                    'vip_payment_status' => '已完款',
                    'paymentStatus' => '已完款',
                    'joinDate' => date('Y-m-d'),
                    'vipJoinDate' => date('Y-m-d'),
                    'referrer_id' => null,
                    'referrerId' => null,
                    'referrer_name' => '点点够',
                    'referrerName' => '点点够'
                ]
            ]);
        }

        // 获取用户VIP完款时间作为VIP加入时间
        $vipJoinDate = isset($user->vip_paid_at) && $user->vip_paid_at ?
            Carbon::parse($user->vip_paid_at)->format('Y-m-d') : null;

        // 获取用户创建时间作为普通加入时间
        $joinDate = isset($user->created_at) && $user->created_at ?
            Carbon::parse($user->created_at)->format('Y-m-d') : date('Y-m-d');

        // 如果VIP加入时间为空，则使用普通加入时间
        if (!$vipJoinDate) {
            $vipJoinDate = $joinDate;
        }

        // VIP支付状态
        $vipPaymentStatus = isset($user->is_vip_paid) && $user->is_vip_paid ? '已完款' : '待支付';

        // 获取推荐人信息
        $referrerId = $user->referrer_id ?? null;
        $referrerName = '点点够'; // 默认值

        if ($referrerId) {
            $referrer = DB::table('app_users')
                ->select('id', 'name', 'nickname')
                ->where('id', $referrerId)
                ->first();

            if ($referrer) {
                $referrerName = $referrer->name ?? $referrer->nickname ?? '会员' . $referrerId;
            }
        }

        // 构建返回数据 - 同时使用下划线命名和驼峰命名，保证前端兼容性
        $data = [
            'id' => $user->id,
            'name' => $user->name ?? $user->nickname ?? '尊贵会员',
            'avatar' => $user->avatar ?? '/app/images/profile/default-avatar.png',
            'join_date' => $joinDate,
            'vip_join_date' => $vipJoinDate,
            'vip_payment_status' => $vipPaymentStatus,
            'paymentStatus' => $vipPaymentStatus, // 前端期望的驼峰命名
            'joinDate' => $joinDate, // 前端期望的驼峰命名
            'vipJoinDate' => $vipJoinDate, // 前端期望的驼峰命名
            'referrer_id' => $referrerId,
            'referrerId' => $referrerId, // 前端期望的驼峰命名
            'referrer_name' => $referrerName,
            'referrerName' => $referrerName // 前端期望的驼峰命名
        ];

        return response()->json([
            'code' => 0,
            'message' => '获取VIP个人资料成功',
            'data' => $data
        ]);
    }
}
