<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class EngineerController extends Controller
{
    /**
     * 获取工程师工作台数据
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getWorkspace(Request $request)
    {
        $user = Auth::user();
        
        if (!$user) {
            return response()->json([
                'code' => 1002,
                'message' => '无效的令牌或已过期',
                'data' => null
            ]);
        }
        
        try {
            // 验证用户是否为工程师
            $engineer = DB::table('installation_engineers')
                ->where('user_id', $user->id)
                ->first();
                
            if (!$engineer) {
                return response()->json([
                    'code' => 1,
                    'message' => '您不是工程师，无权访问',
                    'data' => null
                ]);
            }
            
            // 获取当前日期
            $today = Carbon::today()->format('Y-m-d');
            $yesterday = Carbon::yesterday()->format('Y-m-d');
            $currentMonth = Carbon::today()->format('Y-m');
            $lastMonth = Carbon::today()->subMonth()->format('Y-m');
            
            // 获取今日安装数据
            $todayInstalls = DB::table('install_bookings')
                ->where('engineer_id', $engineer->id)
                ->whereDate('install_date', $today)
                ->count();
                
            // 获取昨日安装数据
            $yesterdayInstalls = DB::table('install_bookings')
                ->where('engineer_id', $engineer->id)
                ->whereDate('install_date', $yesterday)
                ->count();
                
            // 获取本月安装数据
            $currentMonthInstalls = DB::table('install_bookings')
                ->where('engineer_id', $engineer->id)
                ->whereRaw("DATE_FORMAT(install_date, '%Y-%m') = ?", [$currentMonth])
                ->count();
                
            // 获取上月安装数据
            $lastMonthInstalls = DB::table('install_bookings')
                ->where('engineer_id', $engineer->id)
                ->whereRaw("DATE_FORMAT(install_date, '%Y-%m') = ?", [$lastMonth])
                ->count();
                
            // 获取总安装数据
            $totalInstalls = DB::table('install_bookings')
                ->where('engineer_id', $engineer->id)
                ->count();
                
            // 获取待处理安装预约
            $pendingInstalls = DB::table('install_bookings')
                ->where('engineer_id', $engineer->id)
                ->where('status', 'pending')
                ->count();
                
            // 获取最近5条安装记录
            $recentInstalls = DB::table('install_bookings')
                ->where('engineer_id', $engineer->id)
                ->orderBy('install_date', 'desc')
                ->limit(5)
                ->get();
                
            // 获取安装评价统计
            $ratings = DB::table('install_ratings')
                ->where('engineer_id', $engineer->id)
                ->selectRaw('AVG(rating) as avg_rating, COUNT(*) as total_ratings')
                ->first();
                
            // 构建返回数据
            $data = [
                'engineer' => [
                    'id' => $engineer->id,
                    'name' => $engineer->name,
                    'avatar' => $engineer->avatar,
                    'phone' => $engineer->phone,
                    'status' => $engineer->status,
                    'level' => $engineer->level,
                    'created_at' => $engineer->created_at,
                ],
                'stats' => [
                    'today_installs' => $todayInstalls,
                    'yesterday_installs' => $yesterdayInstalls,
                    'current_month_installs' => $currentMonthInstalls,
                    'last_month_installs' => $lastMonthInstalls,
                    'total_installs' => $totalInstalls,
                    'pending_installs' => $pendingInstalls,
                    'avg_rating' => round($ratings->avg_rating ?? 0, 1),
                    'total_ratings' => $ratings->total_ratings ?? 0,
                ],
                'recent_installs' => $recentInstalls,
                'today_schedule' => $this->getTodaySchedule($engineer->id),
            ];
            
            return response()->json([
                'code' => 0,
                'message' => '获取工程师工作台数据成功',
                'data' => $data
            ]);
        } catch (\Exception $e) {
            Log::error('获取工程师工作台数据失败: ' . $e->getMessage());
            
            return response()->json([
                'code' => 1,
                'message' => '获取工程师工作台数据失败: ' . $e->getMessage(),
                'data' => null
            ]);
        }
    }
    
    /**
     * 获取今日安装计划
     *
     * @param int $engineerId
     * @return array
     */
    private function getTodaySchedule($engineerId)
    {
        $today = Carbon::today()->format('Y-m-d');
        
        $schedule = DB::table('install_bookings')
            ->where('engineer_id', $engineerId)
            ->whereDate('install_date', $today)
            ->orderBy('install_time')
            ->get();
            
        $result = [];
        foreach ($schedule as $booking) {
            $result[] = [
                'id' => $booking->id,
                'customer_name' => $booking->customer_name,
                'customer_phone' => $booking->customer_phone,
                'address' => $booking->address,
                'device_type' => $booking->device_type,
                'install_time' => $booking->install_time,
                'status' => $booking->status,
                'remark' => $booking->remark,
            ];
        }
        
        return $result;
    }
}
