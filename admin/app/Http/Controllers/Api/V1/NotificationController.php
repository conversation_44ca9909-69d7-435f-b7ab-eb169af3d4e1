<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;

class NotificationController extends Controller
{
    /**
     * 获取通知列表
     */
    public function index(Request $request)
    {
        try {
            $user = Auth::user();
            if (!$user) {
                return response()->json([
                    'code' => 401,
                    'message' => '用户未登录'
                ]);
            }

            $page = $request->get('page', 1);
            $limit = $request->get('limit', 20);
            $type = $request->get('type', '');

            $query = DB::table('notifications')
                ->where('user_id', $user->id)
                ->where('user_type', 'merchant');

            // 按类型筛选
            if ($type && $type !== 'all') {
                $query->where('type', $type);
            }

            // 获取总数
            $total = $query->count();

            // 分页查询
            $notifications = $query
                ->orderBy('created_at', 'desc')
                ->offset(($page - 1) * $limit)
                ->limit($limit)
                ->get();

            return response()->json([
                'code' => 200,
                'message' => '获取成功',
                'data' => [
                    'list' => $notifications,
                    'total' => $total,
                    'page' => $page,
                    'limit' => $limit
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'code' => 500,
                'message' => '获取通知列表失败：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 获取未读通知数量
     */
    public function unreadCount()
    {
        try {
            $user = Auth::user();
            if (!$user) {
                return response()->json([
                    'code' => 401,
                    'message' => '用户未登录'
                ]);
            }

            $count = DB::table('notifications')
                ->where('user_id', $user->id)
                ->where('user_type', 'merchant')
                ->where('is_read', 0)
                ->count();

            return response()->json([
                'code' => 200,
                'message' => '获取成功',
                'data' => [
                    'count' => $count
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'code' => 500,
                'message' => '获取未读数量失败：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 标记通知为已读
     */
    public function markAsRead($id)
    {
        try {
            $user = Auth::user();
            if (!$user) {
                return response()->json([
                    'code' => 401,
                    'message' => '用户未登录'
                ]);
            }

            $affected = DB::table('notifications')
                ->where('id', $id)
                ->where('user_id', $user->id)
                ->where('user_type', 'merchant')
                ->update(['is_read' => 1, 'read_at' => now()]);

            if ($affected > 0) {
                return response()->json([
                    'code' => 200,
                    'message' => '标记成功'
                ]);
            } else {
                return response()->json([
                    'code' => 404,
                    'message' => '通知不存在'
                ]);
            }

        } catch (\Exception $e) {
            return response()->json([
                'code' => 500,
                'message' => '标记失败：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 标记所有通知为已读
     */
    public function markAllAsRead()
    {
        try {
            $user = Auth::user();
            if (!$user) {
                return response()->json([
                    'code' => 401,
                    'message' => '用户未登录'
                ]);
            }

            DB::table('notifications')
                ->where('user_id', $user->id)
                ->where('user_type', 'merchant')
                ->where('is_read', 0)
                ->update(['is_read' => 1, 'read_at' => now()]);

            return response()->json([
                'code' => 200,
                'message' => '全部标记成功'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'code' => 500,
                'message' => '标记失败：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 清空所有通知
     */
    public function clearAll()
    {
        try {
            $user = Auth::user();
            if (!$user) {
                return response()->json([
                    'code' => 401,
                    'message' => '用户未登录'
                ]);
            }

            DB::table('notifications')
                ->where('user_id', $user->id)
                ->where('user_type', 'merchant')
                ->delete();

            return response()->json([
                'code' => 200,
                'message' => '清空成功'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'code' => 500,
                'message' => '清空失败：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 获取通知设置
     */
    public function getSettings()
    {
        try {
            $user = Auth::user();
            if (!$user) {
                return response()->json([
                    'code' => 401,
                    'message' => '用户未登录'
                ]);
            }

            $settings = DB::table('notification_settings')
                ->where('user_id', $user->id)
                ->where('user_type', 'merchant')
                ->first();

            if (!$settings) {
                // 创建默认设置
                $defaultSettings = [
                    'user_id' => $user->id,
                    'user_type' => 'merchant',
                    'system_notification' => 1,
                    'trade_notification' => 1,
                    'order_notification' => 1,
                    'promotion_notification' => 1,
                    'created_at' => now(),
                    'updated_at' => now()
                ];
                
                DB::table('notification_settings')->insert($defaultSettings);
                $settings = (object) $defaultSettings;
            }

            return response()->json([
                'code' => 200,
                'message' => '获取成功',
                'data' => $settings
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'code' => 500,
                'message' => '获取设置失败：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 更新通知设置
     */
    public function updateSettings(Request $request)
    {
        try {
            $user = Auth::user();
            if (!$user) {
                return response()->json([
                    'code' => 401,
                    'message' => '用户未登录'
                ]);
            }

            $data = [
                'system_notification' => $request->get('system_notification', 1),
                'trade_notification' => $request->get('trade_notification', 1),
                'order_notification' => $request->get('order_notification', 1),
                'promotion_notification' => $request->get('promotion_notification', 1),
                'updated_at' => now()
            ];

            $affected = DB::table('notification_settings')
                ->where('user_id', $user->id)
                ->where('user_type', 'merchant')
                ->update($data);

            if ($affected === 0) {
                // 如果没有记录，则插入新记录
                $data['user_id'] = $user->id;
                $data['user_type'] = 'merchant';
                $data['created_at'] = now();
                DB::table('notification_settings')->insert($data);
            }

            return response()->json([
                'code' => 200,
                'message' => '设置更新成功'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'code' => 500,
                'message' => '更新设置失败：' . $e->getMessage()
            ]);
        }
    }
} 