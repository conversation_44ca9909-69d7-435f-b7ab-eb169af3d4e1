<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class MerchantController extends Controller
{
    /**
     * 获取商户工作台数据
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getWorkspace(Request $request)
    {
        $user = Auth::user();
        
        if (!$user) {
            return response()->json([
                'code' => 1002,
                'message' => '无效的令牌或已过期',
                'data' => null
            ]);
        }
        
        try {
            // 验证用户是否为商户
            $merchant = DB::table('merchants')
                ->where('user_id', $user->id)
                ->first();
                
            if (!$merchant) {
                return response()->json([
                    'code' => 1,
                    'message' => '您不是商户，无权访问',
                    'data' => null
                ]);
            }
            
            // 获取当前日期
            $today = Carbon::today()->format('Y-m-d');
            $yesterday = Carbon::yesterday()->format('Y-m-d');
            $currentMonth = Carbon::today()->format('Y-m');
            $lastMonth = Carbon::today()->subMonth()->format('Y-m');
            
            // 获取今日交易数据
            $todayStats = DB::table('merchant_transactions')
                ->where('merchant_id', $merchant->id)
                ->whereDate('created_at', $today)
                ->selectRaw('COUNT(*) as count, SUM(amount) as amount')
                ->first();
                
            // 获取昨日交易数据
            $yesterdayStats = DB::table('merchant_transactions')
                ->where('merchant_id', $merchant->id)
                ->whereDate('created_at', $yesterday)
                ->selectRaw('COUNT(*) as count, SUM(amount) as amount')
                ->first();
                
            // 获取本月交易数据
            $currentMonthStats = DB::table('merchant_transactions')
                ->where('merchant_id', $merchant->id)
                ->whereRaw("DATE_FORMAT(created_at, '%Y-%m') = ?", [$currentMonth])
                ->selectRaw('COUNT(*) as count, SUM(amount) as amount')
                ->first();
                
            // 获取上月交易数据
            $lastMonthStats = DB::table('merchant_transactions')
                ->where('merchant_id', $merchant->id)
                ->whereRaw("DATE_FORMAT(created_at, '%Y-%m') = ?", [$lastMonth])
                ->selectRaw('COUNT(*) as count, SUM(amount) as amount')
                ->first();
                
            // 获取总交易数据
            $totalStats = DB::table('merchant_transactions')
                ->where('merchant_id', $merchant->id)
                ->selectRaw('COUNT(*) as count, SUM(amount) as amount')
                ->first();
                
            // 获取最近5笔交易
            $recentTransactions = DB::table('merchant_transactions')
                ->where('merchant_id', $merchant->id)
                ->orderBy('created_at', 'desc')
                ->limit(5)
                ->get();
                
            // 构建返回数据
            $data = [
                'merchant' => [
                    'id' => $merchant->id,
                    'name' => $merchant->name,
                    'logo' => $merchant->logo,
                    'address' => $merchant->address,
                    'phone' => $merchant->phone,
                    'status' => $merchant->status,
                    'created_at' => $merchant->created_at,
                ],
                'stats' => [
                    'today' => [
                        'count' => $todayStats->count ?? 0,
                        'amount' => $todayStats->amount ?? 0,
                    ],
                    'yesterday' => [
                        'count' => $yesterdayStats->count ?? 0,
                        'amount' => $yesterdayStats->amount ?? 0,
                    ],
                    'current_month' => [
                        'count' => $currentMonthStats->count ?? 0,
                        'amount' => $currentMonthStats->amount ?? 0,
                    ],
                    'last_month' => [
                        'count' => $lastMonthStats->count ?? 0,
                        'amount' => $lastMonthStats->amount ?? 0,
                    ],
                    'total' => [
                        'count' => $totalStats->count ?? 0,
                        'amount' => $totalStats->amount ?? 0,
                    ],
                ],
                'recent_transactions' => $recentTransactions,
            ];
            
            return response()->json([
                'code' => 0,
                'message' => '获取商户工作台数据成功',
                'data' => $data
            ]);
        } catch (\Exception $e) {
            Log::error('获取商户工作台数据失败: ' . $e->getMessage());
            
            return response()->json([
                'code' => 1,
                'message' => '获取商户工作台数据失败: ' . $e->getMessage(),
                'data' => null
            ]);
        }
    }
    
    /**
     * 获取商户交易列表
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getTradeList(Request $request)
    {
        $user = Auth::user();
        
        if (!$user) {
            return response()->json([
                'code' => 1002,
                'message' => '无效的令牌或已过期',
                'data' => null
            ]);
        }
        
        try {
            // 验证用户是否为商户
            $merchant = DB::table('merchants')
                ->where('user_id', $user->id)
                ->first();
                
            if (!$merchant) {
                return response()->json([
                    'code' => 1,
                    'message' => '您不是商户，无权访问',
                    'data' => null
                ]);
            }
            
            // 获取分页参数
            $page = $request->input('page', 1);
            $pageSize = $request->input('pageSize', 10);
            $offset = ($page - 1) * $pageSize;
            
            // 获取筛选参数
            $startDate = $request->input('startDate');
            $endDate = $request->input('endDate');
            $status = $request->input('status');
            $keyword = $request->input('keyword');
            
            // 构建查询
            $query = DB::table('merchant_transactions')
                ->where('merchant_id', $merchant->id);
                
            // 应用筛选条件
            if ($startDate) {
                $query->whereDate('created_at', '>=', $startDate);
            }
            
            if ($endDate) {
                $query->whereDate('created_at', '<=', $endDate);
            }
            
            if ($status !== null && $status !== '') {
                $query->where('status', $status);
            }
            
            if ($keyword) {
                $query->where(function($q) use ($keyword) {
                    $q->where('transaction_no', 'like', "%{$keyword}%")
                      ->orWhere('customer_name', 'like', "%{$keyword}%")
                      ->orWhere('customer_phone', 'like', "%{$keyword}%");
                });
            }
            
            // 获取总数
            $total = $query->count();
            
            // 获取分页数据
            $transactions = $query->orderBy('created_at', 'desc')
                ->offset($offset)
                ->limit($pageSize)
                ->get();
                
            // 构建返回数据
            $data = [
                'total' => $total,
                'page' => (int)$page,
                'pageSize' => (int)$pageSize,
                'list' => $transactions,
            ];
            
            return response()->json([
                'code' => 0,
                'message' => '获取商户交易列表成功',
                'data' => $data
            ]);
        } catch (\Exception $e) {
            Log::error('获取商户交易列表失败: ' . $e->getMessage());
            
            return response()->json([
                'code' => 1,
                'message' => '获取商户交易列表失败: ' . $e->getMessage(),
                'data' => null
            ]);
        }
    }
    
    /**
     * 获取商户交易详情
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function getTradeDetail(Request $request, $id)
    {
        $user = Auth::user();
        
        if (!$user) {
            return response()->json([
                'code' => 1002,
                'message' => '无效的令牌或已过期',
                'data' => null
            ]);
        }
        
        try {
            // 验证用户是否为商户
            $merchant = DB::table('merchants')
                ->where('user_id', $user->id)
                ->first();
                
            if (!$merchant) {
                return response()->json([
                    'code' => 1,
                    'message' => '您不是商户，无权访问',
                    'data' => null
                ]);
            }
            
            // 获取交易详情
            $transaction = DB::table('merchant_transactions')
                ->where('id', $id)
                ->where('merchant_id', $merchant->id)
                ->first();
                
            if (!$transaction) {
                return response()->json([
                    'code' => 1,
                    'message' => '交易不存在或无权查看',
                    'data' => null
                ]);
            }
            
            // 获取交易商品
            $items = DB::table('merchant_transaction_items')
                ->where('transaction_id', $transaction->id)
                ->get();
                
            // 获取交易日志
            $logs = DB::table('merchant_transaction_logs')
                ->where('transaction_id', $transaction->id)
                ->orderBy('created_at', 'desc')
                ->get();
                
            // 构建返回数据
            $data = [
                'transaction' => $transaction,
                'items' => $items,
                'logs' => $logs,
            ];
            
            return response()->json([
                'code' => 0,
                'message' => '获取商户交易详情成功',
                'data' => $data
            ]);
        } catch (\Exception $e) {
            Log::error('获取商户交易详情失败: ' . $e->getMessage());
            
            return response()->json([
                'code' => 1,
                'message' => '获取商户交易详情失败: ' . $e->getMessage(),
                'data' => null
            ]);
        }
    }
    
    /**
     * 获取商户统计数据
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getStats(Request $request)
    {
        $user = Auth::user();
        
        if (!$user) {
            return response()->json([
                'code' => 1002,
                'message' => '无效的令牌或已过期',
                'data' => null
            ]);
        }
        
        try {
            // 验证用户是否为商户
            $merchant = DB::table('merchants')
                ->where('user_id', $user->id)
                ->first();
                
            if (!$merchant) {
                return response()->json([
                    'code' => 1,
                    'message' => '您不是商户，无权访问',
                    'data' => null
                ]);
            }
            
            // 获取统计周期
            $period = $request->input('period', 'month');
            $startDate = null;
            $endDate = null;
            
            // 根据周期设置起止日期
            if ($period === 'day') {
                $startDate = Carbon::today()->format('Y-m-d');
                $endDate = Carbon::today()->format('Y-m-d');
            } elseif ($period === 'week') {
                $startDate = Carbon::today()->startOfWeek()->format('Y-m-d');
                $endDate = Carbon::today()->endOfWeek()->format('Y-m-d');
            } elseif ($period === 'month') {
                $startDate = Carbon::today()->startOfMonth()->format('Y-m-d');
                $endDate = Carbon::today()->endOfMonth()->format('Y-m-d');
            } elseif ($period === 'year') {
                $startDate = Carbon::today()->startOfYear()->format('Y-m-d');
                $endDate = Carbon::today()->endOfYear()->format('Y-m-d');
            } elseif ($period === 'custom') {
                $startDate = $request->input('startDate');
                $endDate = $request->input('endDate');
            }
            
            // 获取交易统计数据
            $transactionStats = DB::table('merchant_transactions')
                ->where('merchant_id', $merchant->id)
                ->when($startDate, function($query) use ($startDate) {
                    return $query->whereDate('created_at', '>=', $startDate);
                })
                ->when($endDate, function($query) use ($endDate) {
                    return $query->whereDate('created_at', '<=', $endDate);
                })
                ->selectRaw('
                    COUNT(*) as total_count,
                    SUM(amount) as total_amount,
                    SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as success_count,
                    SUM(CASE WHEN status = 1 THEN amount ELSE 0 END) as success_amount,
                    SUM(CASE WHEN status = 0 THEN 1 ELSE 0 END) as pending_count,
                    SUM(CASE WHEN status = 0 THEN amount ELSE 0 END) as pending_amount,
                    SUM(CASE WHEN status = 2 THEN 1 ELSE 0 END) as failed_count,
                    SUM(CASE WHEN status = 2 THEN amount ELSE 0 END) as failed_amount
                ')
                ->first();
                
            // 获取交易趋势数据
            $trendQuery = DB::table('merchant_transactions')
                ->where('merchant_id', $merchant->id)
                ->when($startDate, function($query) use ($startDate) {
                    return $query->whereDate('created_at', '>=', $startDate);
                })
                ->when($endDate, function($query) use ($endDate) {
                    return $query->whereDate('created_at', '<=', $endDate);
                });
                
            // 根据周期设置趋势数据的分组
            if ($period === 'day') {
                $trendQuery->selectRaw('DATE_FORMAT(created_at, "%H:00") as label, COUNT(*) as count, SUM(amount) as amount')
                    ->groupBy(DB::raw('DATE_FORMAT(created_at, "%H:00")'))
                    ->orderBy(DB::raw('DATE_FORMAT(created_at, "%H:00")'));
            } elseif ($period === 'week' || $period === 'month') {
                $trendQuery->selectRaw('DATE(created_at) as label, COUNT(*) as count, SUM(amount) as amount')
                    ->groupBy(DB::raw('DATE(created_at)'))
                    ->orderBy(DB::raw('DATE(created_at)'));
            } elseif ($period === 'year') {
                $trendQuery->selectRaw('DATE_FORMAT(created_at, "%Y-%m") as label, COUNT(*) as count, SUM(amount) as amount')
                    ->groupBy(DB::raw('DATE_FORMAT(created_at, "%Y-%m")'))
                    ->orderBy(DB::raw('DATE_FORMAT(created_at, "%Y-%m")'));
            } elseif ($period === 'custom') {
                // 根据日期范围的长度决定分组方式
                $days = Carbon::parse($startDate)->diffInDays(Carbon::parse($endDate));
                
                if ($days <= 31) {
                    $trendQuery->selectRaw('DATE(created_at) as label, COUNT(*) as count, SUM(amount) as amount')
                        ->groupBy(DB::raw('DATE(created_at)'))
                        ->orderBy(DB::raw('DATE(created_at)'));
                } else {
                    $trendQuery->selectRaw('DATE_FORMAT(created_at, "%Y-%m") as label, COUNT(*) as count, SUM(amount) as amount')
                        ->groupBy(DB::raw('DATE_FORMAT(created_at, "%Y-%m")'))
                        ->orderBy(DB::raw('DATE_FORMAT(created_at, "%Y-%m")'));
                }
            }
            
            $trends = $trendQuery->get();
            
            // 获取支付方式分布
            $paymentMethods = DB::table('merchant_transactions')
                ->where('merchant_id', $merchant->id)
                ->when($startDate, function($query) use ($startDate) {
                    return $query->whereDate('created_at', '>=', $startDate);
                })
                ->when($endDate, function($query) use ($endDate) {
                    return $query->whereDate('created_at', '<=', $endDate);
                })
                ->selectRaw('payment_method, COUNT(*) as count, SUM(amount) as amount')
                ->groupBy('payment_method')
                ->get();
                
            // 构建返回数据
            $data = [
                'period' => [
                    'type' => $period,
                    'startDate' => $startDate,
                    'endDate' => $endDate,
                ],
                'stats' => $transactionStats,
                'trends' => $trends,
                'payment_methods' => $paymentMethods,
            ];
            
            return response()->json([
                'code' => 0,
                'message' => '获取商户统计数据成功',
                'data' => $data
            ]);
        } catch (\Exception $e) {
            Log::error('获取商户统计数据失败: ' . $e->getMessage());
            
            return response()->json([
                'code' => 1,
                'message' => '获取商户统计数据失败: ' . $e->getMessage(),
                'data' => null
            ]);
        }
    }
    
    /**
     * 获取商户信息
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getInfo(Request $request)
    {
        $user = Auth::user();
        
        if (!$user) {
            return response()->json([
                'code' => 1002,
                'message' => '无效的令牌或已过期',
                'data' => null
            ]);
        }
        
        try {
            // 获取商户信息
            $merchant = DB::table('merchants')
                ->where('user_id', $user->id)
                ->first();
                
            if (!$merchant) {
                return response()->json([
                    'code' => 1,
                    'message' => '您不是商户，无权访问',
                    'data' => null
                ]);
            }
            
            // 获取商户结算账户
            $accounts = DB::table('merchant_accounts')
                ->where('merchant_id', $merchant->id)
                ->get();
                
            // 获取商户支付配置
            $paymentConfigs = DB::table('merchant_payment_configs')
                ->where('merchant_id', $merchant->id)
                ->get();
                
            // 构建返回数据
            $data = [
                'merchant' => [
                    'id' => $merchant->id,
                    'name' => $merchant->name,
                    'logo' => $merchant->logo,
                    'address' => $merchant->address,
                    'phone' => $merchant->phone,
                    'email' => $merchant->email,
                    'contact_person' => $merchant->contact_person,
                    'contact_phone' => $merchant->contact_phone,
                    'business_license' => $merchant->business_license,
                    'status' => $merchant->status,
                    'created_at' => $merchant->created_at,
                ],
                'accounts' => $accounts,
                'payment_configs' => $paymentConfigs,
            ];
            
            return response()->json([
                'code' => 0,
                'message' => '获取商户信息成功',
                'data' => $data
            ]);
        } catch (\Exception $e) {
            Log::error('获取商户信息失败: ' . $e->getMessage());
            
            return response()->json([
                'code' => 1,
                'message' => '获取商户信息失败: ' . $e->getMessage(),
                'data' => null
            ]);
        }
    }
}
