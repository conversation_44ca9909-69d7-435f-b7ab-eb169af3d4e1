<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use App\Models\AppUser;
use App\Models\Merchant;

class UserController extends Controller
{
    /**
     * 获取用户信息
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function info(Request $request)
    {
        // 记录请求信息
        Log::info('用户信息请求', [
            'headers' => $request->header(),
            'token' => $request->bearerToken(),
            'ip' => $request->ip(),
            'user_agent' => $request->userAgent()
        ]);

        // 尝试获取用户认证信息，但不强制要求认证
        $user = Auth::user();

        // 如果用户未认证，返回空的基础用户信息
        if (!$user) {
            Log::info('未认证用户访问info接口，返回默认用户信息');
            
            // 返回默认的用户信息结构
            return response()->json([
                'code' => 0,
                'message' => '获取用户信息成功',
                'data' => [
                    'id' => '',
                    'userId' => '',
                    'nickname' => '用户',
                    'wechat_nickname' => '',
                    'avatar' => '',
                    'wechat_avatar' => '',
                    'phone' => '',
                    'email' => '',
                    'gender' => '',
                    'is_pay_institution' => 0,
                    'is_water_purifier_user' => 0,
                    'is_engineer' => 0, 
                    'is_water_purifier_agent' => 0,
                    'is_pay_merchant' => 0,
                    'is_vip' => 0,
                    'is_salesman' => 0,
                    'is_admin' => 0,
                    'roles' => [],
                    'name' => ''
                ]
            ]);
        }

        Log::info('用户认证成功', [
            'user_id' => $user->id,
            'phone' => $user->phone
        ]);

        // 确保所有角色值存在且类型正确
        $role_fields = [
            'is_pay_institution',
            'is_water_purifier_user',
            'is_engineer',
            'is_water_purifier_agent',
            'is_pay_merchant',
            'is_vip',
            'is_salesman',
            'is_admin'
        ];

        foreach ($role_fields as $field) {
            // 如果字段不存在或为null，设置默认值
            if (!isset($user[$field])) {
                // 业务员角色默认为1，其他角色默认为0
                $user[$field] = ($field === 'is_salesman') ? 1 : 0;
                Log::info("字段 $field 不存在，设置默认值: " . $user[$field]);
            } else {
                // 确保值为整数
                $user[$field] = intval($user[$field]);
                Log::info("字段 $field 值为: " . $user[$field]);
            }
        }

        // 构建中文友好的角色名称数组
        $roles = [];

        if ($user['is_admin'] == 1) $roles[] = '管理员';
        if ($user['is_pay_institution'] == 1) $roles[] = '支付机构';
        if ($user['is_water_purifier_agent'] == 1) $roles[] = '净水器渠道商';
        if ($user['is_engineer'] == 1) $roles[] = '工程师';
        if ($user['is_salesman'] == 1) $roles[] = '业务员';
        if ($user['is_pay_merchant'] == 1) $roles[] = '支付商户';
        if ($user['is_vip'] == 1) $roles[] = 'VIP会员';
        if ($user['is_water_purifier_user'] == 1) $roles[] = '净水器用户';

        // 如果没有角色，则添加默认角色
        if (empty($roles)) {
            $roles[] = '普通用户';
        }

        // 将角色名称数组添加到用户信息中
        $user['roles'] = $roles;
        Log::info("最终角色数组: " . json_encode($roles));

        return response()->json([
            'code' => 0,
            'message' => '获取用户信息成功',
            'data' => $user
        ]);
    }

    /**
     * 更新用户信息
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateInfo(Request $request)
    {
        $user = Auth::user();

        if (!$user) {
            return response()->json([
                'code' => 1002,
                'message' => '无效的令牌或已过期',
                'data' => null
            ]);
        }

        // 验证请求数据
        $validatedData = $request->validate([
            'nickname' => 'sometimes|string|max:50',
            'avatar' => 'sometimes|string|max:255',
            'gender' => 'sometimes|integer|in:0,1,2',
            'birthday' => 'sometimes|date',
            'province' => 'sometimes|string|max:50',
            'city' => 'sometimes|string|max:50',
            'district' => 'sometimes|string|max:50',
            'address' => 'sometimes|string|max:255',
        ]);

        // 更新用户信息
        $user->update($validatedData);

        return response()->json([
            'code' => 0,
            'message' => '更新用户信息成功',
            'data' => $user
        ]);
    }

    /**
     * 更新用户密码
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function updatePassword(Request $request)
    {
        $user = Auth::user();

        if (!$user) {
            return response()->json([
                'code' => 1002,
                'message' => '无效的令牌或已过期',
                'data' => null
            ]);
        }

        // 验证请求数据
        $validatedData = $request->validate([
            'old_password' => 'required|string|min:6',
            'new_password' => 'required|string|min:6|confirmed',
        ]);

        // 验证旧密码
        if (!Hash::check($validatedData['old_password'], $user->password)) {
            return response()->json([
                'code' => 1003,
                'message' => '旧密码不正确',
                'data' => null
            ]);
        }

        // 更新密码
        $user->password = Hash::make($validatedData['new_password']);
        $user->save();

        return response()->json([
            'code' => 0,
            'message' => '更新密码成功',
            'data' => null
        ]);
    }
    
    /**
     * 检查用户角色
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function checkUserRoles(Request $request)
    {
        // 记录请求
        Log::info('检查用户角色请求', [
            'params' => $request->all(),
            'ip' => $request->ip()
        ]);
        
        // 验证请求
        $validated = $request->validate([
            'mobile' => 'required|string'
        ]);
        
        $mobile = $validated['mobile'];
        
        try {
            // 查询用户
            $user = AppUser::where('phone', $mobile)->first();
            $roles = [];
            $roleDetails = [];
            
            if ($user) {
                // 获取用户的角色
                $roles = $user->roles ? explode(',', $user->roles) : [];
                
                // 检查是否商户角色
                $merchants = Merchant::where('principal_mobile', $mobile)
                    ->orWhere('app_user_id', $user->id)
                    ->get();
                
                if ($merchants->count() > 0 && !in_array('merchant', $roles)) {
                    $roles[] = 'merchant';
                    // 更新用户角色
                    $user->roles = implode(',', array_unique($roles));
                    $user->save();
                }
                
                // 获取商户信息
                if (in_array('merchant', $roles)) {
                    $merchantList = [];
                    foreach ($merchants as $merchant) {
                        $merchantList[] = [
                            'id' => $merchant->id,
                            'merchant_id' => $merchant->merchant_id,
                            'name' => $merchant->name,
                            'logo' => $merchant->logo ?: 'https://img.itapgo.com/profile/default-merchant.png',
                            'status' => $merchant->status,
                            'balance' => floatval($merchant->balance)
                        ];
                    }
                    $roleDetails['merchant'] = [
                        'count' => count($merchantList),
                        'items' => $merchantList
                    ];
                }
                
                // 获取其他角色信息
                if (in_array('salesman', $roles)) {
                    $roleDetails['salesman'] = [
                        'has_role' => true
                    ];
                }
                
                if (in_array('agent', $roles)) {
                    $roleDetails['agent'] = [
                        'has_role' => true
                    ];
                }
            }
            
            // 返回用户角色信息
            return response()->json([
                'code' => 0,
                'message' => '获取成功',
                'data' => [
                    'has_user' => !empty($user),
                    'user_id' => $user ? $user->id : null,
                    'roles' => $roles,
                    'role_details' => $roleDetails
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('检查用户角色失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'code' => 1,
                'message' => '检查用户角色失败: ' . $e->getMessage(),
                'data' => null
            ]);
        }
    }
}
