<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class NavController extends Controller
{
    /**
     * 获取配置
     */
    public function getConfig()
    {
        return response()->json([
            'code' => 0,
            'message' => '获取成功',
            'data' => [
                'app_name' => '点点够',
                'version' => '1.0.0',
                'api_version' => '1.0.0',
                'copyright' => '© ' . date('Y') . ' 点点够. All rights reserved.'
            ]
        ]);
    }

    /**
     * 保存配置
     */
    public function saveConfig(Request $request)
    {
        try {
            $data = $request->validate([
                'app_name' => 'nullable|string|max:50',
                'copyright' => 'nullable|string|max:100',
            ]);

            // 这里可以实现保存配置的逻辑
            // 例如保存到数据库或配置文件

            return response()->json([
                'code' => 0,
                'message' => '保存成功',
                'data' => $data
            ]);
        } catch (\Exception $e) {
            Log::error('保存配置失败', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'code' => 500,
                'message' => '保存失败: ' . $e->getMessage()
            ], 500);
        }
    }
}
