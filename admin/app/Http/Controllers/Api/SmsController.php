<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\SystemConfig;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class SmsController extends Controller
{
    /**
     * 发送短信验证码
     */
    public function send(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'phone' => 'required|regex:/^1[3-9]\d{9}$/',
            'type' => 'required|in:login,register,bind,reset_password',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'code' => 1,
                'message' => $validator->errors()->first(),
                'data' => null
            ]);
        }

        $phone = $request->input('phone');
        $type = $request->input('type');

        // 检查发送频率限制
        if (!$this->checkSendLimit($phone, $type)) {
            return response()->json([
                'code' => 1,
                'message' => '发送过于频繁，请稍后再试',
                'data' => null
            ]);
        }

        // 生成验证码
        $code = $this->generateCode();

        // 储存验证码
        $this->storeCode($phone, $type, $code);

        // 发送验证码，这里先模拟发送
        $result = $this->sendSmsCode($phone, $code, $type);

        if ($result) {
            // 记录发送次数
            $this->recordSendCount($phone);

            return response()->json([
                'code' => 0,
                'message' => '验证码发送成功',
                'data' => null
            ]);
        } else {
            return response()->json([
                'code' => 1,
                'message' => '验证码发送失败，请稍后再试',
                'data' => null
            ]);
        }
    }

    /**
     * 验证短信验证码
     */
    public function verify(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'phone' => 'required|regex:/^1[3-9]\d{9}$/',
            'code' => 'required|string',
            'type' => 'required|in:login,register,bind,reset_password',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'code' => 1,
                'message' => $validator->errors()->first(),
                'data' => null
            ]);
        }

        $phone = $request->input('phone');
        $code = $request->input('code');
        $type = $request->input('type');

        $isValid = $this->validateCode($phone, $type, $code);

        return response()->json([
            'code' => $isValid ? 0 : 1,
            'message' => $isValid ? '验证成功' : '验证码无效或已过期',
            'data' => null
        ]);
    }

    /**
     * 检查发送频率限制
     *
     * @param string $phone 手机号
     * @param string $type 验证码类型
     * @return bool 是否允许发送
     */
    private function checkSendLimit($phone, $type)
    {
        // 每分钟限制1条
        $minuteKey = "sms:limit:minute:{$phone}:{$type}";
        $minuteCount = Cache::get($minuteKey, 0);
        if ($minuteCount >= 1) {
            return false;
        }

        // 每小时限制5条
        $hourKey = "sms:limit:hour:{$phone}:{$type}";
        $hourCount = Cache::get($hourKey, 0);
        if ($hourCount >= 5) {
            return false;
        }

        // 每日限制10条
        $dayKey = "sms:limit:day:{$phone}";
        $dayCount = Cache::get($dayKey, 0);
        if ($dayCount >= 10) {
            return false;
        }

        return true;
    }

    /**
     * 记录发送次数
     *
     * @param string $phone 手机号
     */
    private function recordSendCount($phone)
    {
        // 更新每分钟计数，有效期1分钟
        $minuteKey = "sms:limit:minute:{$phone}";
        Cache::put($minuteKey, Cache::get($minuteKey, 0) + 1, now()->addMinutes(1));

        // 更新每小时计数，有效期1小时
        $hourKey = "sms:limit:hour:{$phone}";
        Cache::put($hourKey, Cache::get($hourKey, 0) + 1, now()->addHours(1));

        // 更新每日计数，有效期1天
        $dayKey = "sms:limit:day:{$phone}";
        Cache::put($dayKey, Cache::get($dayKey, 0) + 1, now()->addDays(1));
    }

    /**
     * 生成随机验证码
     *
     * @return string 验证码
     */
    private function generateCode()
    {
        return str_pad(random_int(0, 999999), 6, '0', STR_PAD_LEFT);
    }

    /**
     * 存储验证码
     *
     * @param string $phone 手机号
     * @param string $type 验证码类型
     * @param string $code 验证码
     */
    private function storeCode($phone, $type, $code)
    {
        $key = "sms:code:{$phone}:{$type}";

        // 验证码有效期5分钟
        Cache::put($key, $code, now()->addMinutes(5));

        // 记录日志
        Log::info('发送验证码', [
            'phone' => substr_replace($phone, '****', 3, 4),
            'type' => $type,
            'code' => $code,
            'ip' => request()->ip()
        ]);
    }

    /**
     * 发送短信验证码
     *
     * @param string $phone 手机号
     * @param string $code 验证码
     * @param string $type 验证码类型
     * @return bool 是否发送成功
     */
    private function sendSmsCode($phone, $code, $type)
    {
        // 从环境变量获取阿里云短信配置
        $accessKeyId = env('ACCESS_KEY_ID');
        $accessKeySecret = env('ACCESS_KEY_SECRET');
        $signName = env('SIGN_NAME');
        $templateCode = env('TEMPLATE_CODE');

        // 检查配置是否完整
        if (empty($accessKeyId) || empty($accessKeySecret) || empty($signName) || empty($templateCode)) {
            Log::error('阿里云短信配置不完整', [
                'ACCESS_KEY_ID' => $accessKeyId ? '已设置' : '未设置',
                'ACCESS_KEY_SECRET' => $accessKeySecret ? '已设置' : '未设置',
                'SIGN_NAME' => $signName,
                'TEMPLATE_CODE' => $templateCode
            ]);

            // 配置不完整，返回模拟发送成功，同时记录详细日志
            Log::info('短信配置不完整，改为模拟发送', [
                'phone' => $phone,
                'code' => $code,
                'type' => $type,
                'ip' => request()->ip()
            ]);

            return true;
        }

        try {
            // 准备请求参数
            $params = [
                'PhoneNumbers' => $phone,
                'SignName' => $signName,
                'TemplateCode' => $templateCode,
                'TemplateParam' => json_encode(['code' => $code]),
            ];

            // 准备公共参数
            $publicParams = [
                'AccessKeyId' => $accessKeyId,
                'Timestamp' => gmdate('Y-m-d\TH:i:s\Z'),
                'Format' => 'JSON',
                'SignatureMethod' => 'HMAC-SHA1',
                'SignatureVersion' => '1.0',
                'SignatureNonce' => uniqid(),
                'Version' => '2017-05-25',
                'RegionId' => 'cn-hangzhou',
                'Action' => 'SendSms',
            ];

            // 合并参数
            $allParams = array_merge($params, $publicParams);

            // 排序参数
            ksort($allParams);

            // 构造规范化请求字符串
            $canonicalizedQueryString = '';
            foreach ($allParams as $key => $value) {
                $canonicalizedQueryString .= '&' . $this->percentEncode($key) . '=' . $this->percentEncode($value);
            }
            $stringToSign = 'GET&%2F&' . $this->percentEncode(substr($canonicalizedQueryString, 1));

            // 计算签名
            $signature = base64_encode(hash_hmac('sha1', $stringToSign, $accessKeySecret . '&', true));

            // 添加签名到参数
            $allParams['Signature'] = $signature;

            // 构造请求URL
            $url = 'https://dysmsapi.aliyuncs.com/?' . http_build_query($allParams);

            // 发送请求
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
            curl_setopt($ch, CURLOPT_TIMEOUT, 10);
            $response = curl_exec($ch);

            // 检查请求是否成功
            if (curl_errno($ch)) {
                throw new \Exception('Curl错误: ' . curl_error($ch));
            }

            curl_close($ch);

            // 解析响应
            $result = json_decode($response, true);

            // 检查发送结果
            if (isset($result['Code']) && $result['Code'] === 'OK') {
                Log::info('阿里云短信发送成功', [
                    'phone' => $phone,
                    'code' => $code,
                    'type' => $type,
                    'requestId' => $result['RequestId'] ?? '',
                    'bizId' => $result['BizId'] ?? ''
                ]);
                return true;
            } else {
                Log::error('阿里云短信发送失败', [
                    'phone' => $phone,
                    'code' => $code,
                    'error' => $result['Message'] ?? '未知错误',
                    'code' => $result['Code'] ?? '未知错误码',
                    'response' => $response
                ]);
                return false;
            }
        } catch (\Exception $e) {
            Log::error('阿里云短信发送异常', [
                'phone' => $phone,
                'code' => $code,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return false;
        }
    }

    /**
     * 编码请求参数
     */
    private function percentEncode($string)
    {
        $result = urlencode($string);
        $result = str_replace(['+', '*'], ['%20', '%2A'], $result);
        $result = preg_replace('/%7E/', '~', $result);
        return $result;
    }

    /**
     * 验证验证码
     *
     * @param string $phone 手机号
     * @param string $type 验证码类型
     * @param string $code 验证码
     * @return bool 是否有效
     */
    private function validateCode($phone, $type, $code)
    {
        $key = "sms:code:{$phone}:{$type}";
        $storedCode = Cache::get($key);

        // 验证码匹配且未过期
        $isValid = $storedCode && $storedCode === $code;

        if ($isValid) {
            // 验证成功后删除，防止重复使用
            Cache::forget($key);
        }

        return $isValid;
    }

    /**
     * 测试发送短信
     */
    public function testSend(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'phone' => 'required|regex:/^1[3-9]\d{9}$/',
            'template_code' => 'required|string',
            'sign_name' => 'required|string',
            'access_key_id' => 'required|string',
            'access_key_secret' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'code' => 1,
                'message' => $validator->errors()->first(),
            ], 422);
        }

        // 获取参数
        $phone = $request->input('phone');
        $templateCode = $request->input('template_code');
        $signName = $request->input('sign_name');
        $accessKeyId = $request->input('access_key_id');
        $accessKeySecret = $request->input('access_key_secret');

        // 尝试发送短信
        try {
            // 调用阿里云API发送短信
            $code = mt_rand(100000, 999999); // 生成随机验证码
            $result = $this->sendAliyunSms($phone, $code, 'test', [
                'access_key_id' => $accessKeyId,
                'access_key_secret' => $accessKeySecret,
                'sign_name' => $signName,
                'template_code' => $templateCode,
            ]);

            // 记录日志
            Log::info('测试短信发送结果', [
                'phone' => substr_replace($phone, '****', 3, 4),
                'result' => $result
            ]);

            // 判断发送结果
            if ($result['success']) {
                return response()->json([
                    'code' => 0,
                    'message' => '测试短信发送成功',
                    'data' => [
                        'code' => $code
                    ]
                ]);
            } else {
                return response()->json([
                    'code' => 1,
                    'message' => '测试短信发送失败: ' . $result['message']
                ]);
            }
        } catch (\Exception $e) {
            Log::error('测试短信发送异常', [
                'phone' => substr_replace($phone, '****', 3, 4),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'code' => 1,
                'message' => '测试短信发送异常: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 发送阿里云短信
     *
     * @param string $phone 手机号
     * @param string $code 验证码
     * @param string $type 验证码类型
     * @param array $config 配置参数
     * @return array 发送结果
     */
    private function sendAliyunSms($phone, $code, $type, $config = [])
    {
        // 如果未提供配置，则从数据库获取
        if (empty($config)) {
            $config = [
                'access_key_id' => SystemConfig::getConfigValue('sms', 'aliyun_sms_access_key_id', ''),
                'access_key_secret' => SystemConfig::getConfigValue('sms', 'aliyun_sms_access_key_secret', ''),
                'sign_name' => SystemConfig::getConfigValue('sms', 'aliyun_sms_sign_name', ''),
                'template_code' => SystemConfig::getConfigValue('sms', 'aliyun_sms_template_code', ''),
            ];
        }

        // 记录日志函数
        $logSmsMessage = function($message, $isError = false) {
            $logMessage = date('[Y-m-d H:i:s]') . ' ' . $message . "\n";
            $logPath = storage_path('logs/sms_log.txt');

            // 确保日志目录存在
            $logDir = dirname($logPath);
            if (!is_dir($logDir)) {
                mkdir($logDir, 0755, true);
            }

            file_put_contents($logPath, $logMessage, FILE_APPEND);

            // 同时使用Laravel的日志记录
            if ($isError) {
                Log::error('SMS错误: ' . $message);
            } else {
                Log::info('SMS信息: ' . $message);
            }
        };

        // 记录发送请求
        $logSmsMessage("开始发送短信, 手机号: " . substr_replace($phone, '****', 3, 4) . ", 类型: {$type}, 验证码: {$code}");

        // 检查配置是否完整
        if (empty($config['access_key_id']) || empty($config['access_key_secret']) ||
            empty($config['sign_name']) || empty($config['template_code'])) {
            $logSmsMessage('阿里云短信配置不完整，无法发送', true);

            return [
                'success' => false,
                'message' => '短信配置不完整，请检查配置信息'
            ];
        }

        try {
            // 引入阿里云SDK（确保已通过Composer安装）
            require_once base_path('vendor/autoload.php');

            // 创建客户端
            $client = new \AlibabaCloud\Client\AlibabaCloud();
            $client::accessKeyClient($config['access_key_id'], $config['access_key_secret'])
                ->regionId('cn-hangzhou')
                ->asDefaultClient();

            // 组装请求
            $templateParam = json_encode(['code' => $code]);

            // 记录请求详情
            $requestInfo = [
                'PhoneNumbers' => $phone,
                'SignName' => $config['sign_name'],
                'TemplateCode' => $config['template_code'],
                'TemplateParam' => $templateParam
            ];
            $logSmsMessage("准备发送阿里云短信请求: " . json_encode($requestInfo, JSON_UNESCAPED_UNICODE));

            // 发起请求
            $result = \AlibabaCloud\Client\AlibabaCloud::rpc()
                ->product('Dysmsapi')
                ->version('2017-05-25')
                ->action('SendSms')
                ->method('POST')
                ->host('dysmsapi.aliyuncs.com')
                ->options([
                    'query' => [
                        'RegionId' => "cn-hangzhou",
                        'PhoneNumbers' => $phone,
                        'SignName' => $config['sign_name'],
                        'TemplateCode' => $config['template_code'],
                        'TemplateParam' => $templateParam,
                    ],
                ])
                ->request();

            // 处理返回结果
            $response = $result->toArray();
            $logSmsMessage("阿里云短信API响应: " . json_encode($response, JSON_UNESCAPED_UNICODE));

            // 检查返回结果
            if (!isset($response['Code']) || $response['Code'] !== 'OK') {
                $errorMsg = isset($response['Message']) ? $response['Message'] : '未知错误';
                $logSmsMessage("阿里云短信发送失败: " . $errorMsg, true);
                return ['success' => false, 'message' => '短信发送失败: ' . $errorMsg];
            }

            $logSmsMessage("阿里云短信发送成功，发送ID: " . ($response['BizId'] ?? '未知'));
            return ['success' => true, 'message' => '短信发送成功'];
        } catch (\AlibabaCloud\Client\Exception\ClientException $e) {
            $errorMsg = "阿里云短信客户端异常: " . $e->getErrorMessage();
            $logSmsMessage($errorMsg, true);
            return ['success' => false, 'message' => '短信发送失败: ' . $e->getErrorMessage()];
        } catch (\AlibabaCloud\Client\Exception\ServerException $e) {
            $errorMsg = "阿里云短信服务器异常: " . $e->getErrorMessage();
            $logSmsMessage($errorMsg, true);
            return ['success' => false, 'message' => '短信发送失败: ' . $e->getErrorMessage()];
        } catch (\Exception $e) {
            $errorMsg = "阿里云短信其他异常: " . $e->getMessage();
            $logSmsMessage($errorMsg, true);
            return ['success' => false, 'message' => '短信发送失败: ' . $e->getMessage()];
        }
    }

    /**
     * 获取短信模块配置
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getConfigSettings()
    {
        try {
            // 获取系统配置中的短信配置
            $configs = \App\Models\SystemConfig::getConfigsByModule('sms');

            if ($configs->isEmpty()) {
                // 如果没有配置，创建默认配置
                $this->createDefaultSmsConfig();
                $configs = \App\Models\SystemConfig::getConfigsByModule('sms');
            }

            $result = [];
            foreach ($configs as $config) {
                $result[$config->key] = $config->value;
            }

            return response()->json([
                'code' => 0,
                'message' => '获取短信配置成功',
                'data' => $result
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'code' => 1,
                'message' => '获取短信配置失败: ' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

    /**
     * 创建默认短信配置
     */
    public function createDefaultSmsConfig()
    {
        $defaultConfigs = [
            [
                'module' => 'sms',
                'key' => 'provider',
                'value' => 'aliyun',
                'title' => '短信服务提供商',
                'description' => '选择短信服务提供商',
                'type' => 'select',
                'options' => ['aliyun' => '阿里云短信', 'tencent' => '腾讯云短信'],
                'is_system' => true,
                'sort' => 10,
            ],
            [
                'module' => 'sms',
                'key' => 'access_key_id',
                'value' => '',
                'title' => 'AccessKey ID',
                'description' => '阿里云AccessKey ID',
                'type' => 'text',
                'options' => null,
                'is_system' => true,
                'sort' => 20,
            ],
            [
                'module' => 'sms',
                'key' => 'access_key_secret',
                'value' => '',
                'title' => 'AccessKey Secret',
                'description' => '阿里云AccessKey Secret',
                'type' => 'text',
                'options' => null,
                'is_system' => true,
                'sort' => 30,
            ],
            [
                'module' => 'sms',
                'key' => 'sign_name',
                'value' => '',
                'title' => '短信签名',
                'description' => '短信签名名称',
                'type' => 'text',
                'options' => null,
                'is_system' => true,
                'sort' => 40,
            ],
            [
                'module' => 'sms',
                'key' => 'template_code',
                'value' => '',
                'title' => '验证码模板ID',
                'description' => '验证码短信模板ID',
                'type' => 'text',
                'options' => null,
                'is_system' => true,
                'sort' => 50,
            ],
            [
                'module' => 'sms',
                'key' => 'template_param',
                'value' => 'code',
                'title' => '模板参数名',
                'description' => '验证码在模板中的参数名',
                'type' => 'text',
                'options' => null,
                'is_system' => true,
                'sort' => 60,
            ]
        ];

        foreach ($defaultConfigs as $config) {
            \App\Models\SystemConfig::firstOrCreate(
                ['module' => $config['module'], 'key' => $config['key']],
                $config
            );
        }
    }
}
