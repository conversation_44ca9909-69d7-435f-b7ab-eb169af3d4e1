<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class PointController extends Controller
{
    /**
     * 获取用户积分信息
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getUserPoints(Request $request)
    {
        try {
            $user = $request->user();
            if (!$user) {
                return response()->json([
                    'code' => 1,
                    'message' => '用户未登录',
                    'data' => null
                ]);
            }

            // 获取用户积分信息
            $points = DB::table('app_users')
                ->where('id', $user->id)
                ->select('id', 'points', 'points_frozen', 'points_expired')
                ->first();

            if (!$points) {
                return response()->json([
                    'code' => 1,
                    'message' => '用户信息不存在',
                    'data' => null
                ]);
            }

            return response()->json([
                'code' => 0,
                'message' => '获取成功',
                'data' => [
                    'user_id' => $points->id,
                    'points' => $points->points ?? 0,
                    'points_frozen' => $points->points_frozen ?? 0,
                    'points_expired' => $points->points_expired ?? 0,
                    'points_available' => ($points->points ?? 0) - ($points->points_frozen ?? 0),
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('获取用户积分信息失败: ' . $e->getMessage());
            return response()->json([
                'code' => 1,
                'message' => '获取用户积分信息失败: ' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

    /**
     * 获取用户积分记录
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getUserPointRecords(Request $request)
    {
        try {
            $user = $request->user();
            if (!$user) {
                return response()->json([
                    'code' => 1,
                    'message' => '用户未登录',
                    'data' => null
                ]);
            }

            $page = $request->input('page', 1);
            $pageSize = $request->input('pageSize', 10);
            $type = $request->input('type', 'all');

            $query = DB::table('app_user_point_records')
                ->where('user_id', $user->id)
                ->orderBy('created_at', 'desc');

            // 根据类型筛选
            if ($type !== 'all') {
                $query->where('type', $type);
            }

            $total = $query->count();
            $records = $query->offset(($page - 1) * $pageSize)
                ->limit($pageSize)
                ->get();

            return response()->json([
                'code' => 0,
                'message' => '获取成功',
                'data' => [
                    'total' => $total,
                    'page' => $page,
                    'pageSize' => $pageSize,
                    'list' => $records
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('获取用户积分记录失败: ' . $e->getMessage());
            return response()->json([
                'code' => 1,
                'message' => '获取用户积分记录失败: ' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

    /**
     * 获取用户资产信息
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getUserAssets(Request $request)
    {
        try {
            $user = $request->user();
            if (!$user) {
                return response()->json([
                    'code' => 1,
                    'message' => '用户未登录',
                    'data' => null
                ]);
            }

            // 获取用户资产信息
            $userData = DB::table('app_users')
                ->where('id', $user->id)
                ->select('id', 'points', 'points_frozen', 'balance', 'coupon_amount')
                ->first();

            if (!$userData) {
                return response()->json([
                    'code' => 1,
                    'message' => '用户信息不存在',
                    'data' => null
                ]);
            }

            // 获取用户优惠券数量
            $couponCount = DB::table('app_user_coupons')
                ->where('user_id', $user->id)
                ->where('status', 'valid')
                ->where('expire_time', '>', now())
                ->count();

            return response()->json([
                'code' => 0,
                'message' => '获取成功',
                'data' => [
                    'user_id' => $userData->id,
                    'balance' => $userData->balance ?? 0,
                    'points' => $userData->points ?? 0,
                    'points_frozen' => $userData->points_frozen ?? 0,
                    'points_available' => ($userData->points ?? 0) - ($userData->points_frozen ?? 0),
                    'coupon_amount' => $userData->coupon_amount ?? 0,
                    'coupon_count' => $couponCount ?? 0
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('获取用户资产信息失败: ' . $e->getMessage());
            return response()->json([
                'code' => 1,
                'message' => '获取用户资产信息失败',
                'data' => null
            ]);
        }
    }

    /**
     * 获取用户订单统计
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getUserOrderStats(Request $request)
    {
        try {
            $user = $request->user();
            if (!$user) {
                return response()->json([
                    'code' => 1,
                    'message' => '用户未登录',
                    'data' => null
                ]);
            }

            // 获取各种状态的订单数量
            $pendingPayment = DB::table('app_orders')
                ->where('user_id', $user->id)
                ->where('status', 'pending_payment')
                ->count();

            $pendingShipment = DB::table('app_orders')
                ->where('user_id', $user->id)
                ->where('status', 'pending_shipment')
                ->count();

            $pendingReceipt = DB::table('app_orders')
                ->where('user_id', $user->id)
                ->where('status', 'pending_receipt')
                ->count();

            $pendingReview = DB::table('app_orders')
                ->where('user_id', $user->id)
                ->where('status', 'completed')
                ->where('is_reviewed', 0)
                ->count();

            $pendingRefund = DB::table('app_order_refunds')
                ->join('app_orders', 'app_orders.id', '=', 'app_order_refunds.order_id')
                ->where('app_orders.user_id', $user->id)
                ->where('app_order_refunds.status', 'pending')
                ->count();

            return response()->json([
                'code' => 0,
                'message' => '获取成功',
                'data' => [
                    'pending_payment' => $pendingPayment,
                    'pending_shipment' => $pendingShipment,
                    'pending_receipt' => $pendingReceipt,
                    'pending_review' => $pendingReview,
                    'pending_refund' => $pendingRefund
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('获取用户订单统计失败: ' . $e->getMessage());
            return response()->json([
                'code' => 1,
                'message' => '获取用户订单统计失败',
                'data' => null
            ]);
        }
    }

    /**
     * 获取积分规则
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getPointRules(Request $request)
    {
        try {
            $rules = DB::table('app_point_rules')
                ->where('status', 'active')
                ->get();

            return response()->json([
                'code' => 0,
                'message' => '获取成功',
                'data' => $rules
            ]);
        } catch (\Exception $e) {
            Log::error('获取积分规则失败: ' . $e->getMessage());
            return response()->json([
                'code' => 1,
                'message' => '获取积分规则失败',
                'data' => null
            ]);
        }
    }

    /**
     * 获取积分兑换商品
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getExchangeItems(Request $request)
    {
        try {
            $page = $request->input('page', 1);
            $pageSize = $request->input('pageSize', 10);
            $category = $request->input('category', 'all');

            $query = DB::table('app_point_exchange_items')
                ->where('status', 'active')
                ->orderBy('sort', 'asc');

            // 根据分类筛选
            if ($category !== 'all') {
                $query->where('category', $category);
            }

            $total = $query->count();
            $items = $query->offset(($page - 1) * $pageSize)
                ->limit($pageSize)
                ->get();

            return response()->json([
                'code' => 0,
                'message' => '获取成功',
                'data' => [
                    'total' => $total,
                    'page' => $page,
                    'pageSize' => $pageSize,
                    'list' => $items
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('获取积分兑换商品失败: ' . $e->getMessage());
            return response()->json([
                'code' => 1,
                'message' => '获取积分兑换商品失败',
                'data' => null
            ]);
        }
    }

    /**
     * 积分兑换
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function exchange(Request $request)
    {
        try {
            $user = $request->user();
            if (!$user) {
                return response()->json([
                    'code' => 1,
                    'message' => '用户未登录',
                    'data' => null
                ]);
            }

            $itemId = $request->input('item_id');
            $quantity = $request->input('quantity', 1);

            // 获取兑换商品信息
            $item = DB::table('app_point_exchange_items')
                ->where('id', $itemId)
                ->where('status', 'active')
                ->first();

            if (!$item) {
                return response()->json([
                    'code' => 1,
                    'message' => '兑换商品不存在或已下架',
                    'data' => null
                ]);
            }

            // 检查库存
            if ($item->stock < $quantity) {
                return response()->json([
                    'code' => 1,
                    'message' => '商品库存不足',
                    'data' => null
                ]);
            }

            // 计算所需积分
            $pointsNeeded = $item->points * $quantity;

            // 检查用户积分是否足够
            $userPoints = DB::table('app_users')
                ->where('id', $user->id)
                ->value('points');

            if ($userPoints < $pointsNeeded) {
                return response()->json([
                    'code' => 1,
                    'message' => '积分不足',
                    'data' => null
                ]);
            }

            // 开始事务
            DB::beginTransaction();

            try {
                // 扣减用户积分
                DB::table('app_users')
                    ->where('id', $user->id)
                    ->decrement('points', $pointsNeeded);

                // 添加积分记录
                DB::table('app_user_point_records')->insert([
                    'user_id' => $user->id,
                    'points' => -$pointsNeeded,
                    'type' => 'exchange',
                    'description' => '兑换商品: ' . $item->name,
                    'created_at' => now(),
                    'updated_at' => now()
                ]);

                // 扣减商品库存
                DB::table('app_point_exchange_items')
                    ->where('id', $itemId)
                    ->decrement('stock', $quantity);

                // 添加兑换记录
                $exchangeId = DB::table('app_point_exchanges')->insertGetId([
                    'user_id' => $user->id,
                    'item_id' => $itemId,
                    'item_name' => $item->name,
                    'points' => $pointsNeeded,
                    'quantity' => $quantity,
                    'status' => 'pending',
                    'created_at' => now(),
                    'updated_at' => now()
                ]);

                DB::commit();

                return response()->json([
                    'code' => 0,
                    'message' => '兑换成功',
                    'data' => [
                        'exchange_id' => $exchangeId,
                        'points_used' => $pointsNeeded,
                        'points_remaining' => $userPoints - $pointsNeeded
                    ]
                ]);
            } catch (\Exception $e) {
                DB::rollBack();
                Log::error('积分兑换失败: ' . $e->getMessage());
                throw $e;
            }
        } catch (\Exception $e) {
            Log::error('积分兑换失败: ' . $e->getMessage());
            return response()->json([
                'code' => 1,
                'message' => '积分兑换失败',
                'data' => null
            ]);
        }
    }

    /**
     * 获取兑换记录
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getExchangeRecords(Request $request)
    {
        try {
            $user = $request->user();
            if (!$user) {
                return response()->json([
                    'code' => 1,
                    'message' => '用户未登录',
                    'data' => null
                ]);
            }

            $page = $request->input('page', 1);
            $pageSize = $request->input('pageSize', 10);
            $status = $request->input('status', 'all');

            $query = DB::table('app_point_exchanges')
                ->where('user_id', $user->id)
                ->orderBy('created_at', 'desc');

            // 根据状态筛选
            if ($status !== 'all') {
                $query->where('status', $status);
            }

            $total = $query->count();
            $records = $query->offset(($page - 1) * $pageSize)
                ->limit($pageSize)
                ->get();

            return response()->json([
                'code' => 0,
                'message' => '获取成功',
                'data' => [
                    'total' => $total,
                    'page' => $page,
                    'pageSize' => $pageSize,
                    'list' => $records
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('获取兑换记录失败: ' . $e->getMessage());
            return response()->json([
                'code' => 1,
                'message' => '获取兑换记录失败',
                'data' => null
            ]);
        }
    }
}
