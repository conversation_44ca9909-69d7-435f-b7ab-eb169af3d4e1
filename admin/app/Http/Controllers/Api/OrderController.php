<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Carbon\Carbon;

class OrderController extends Controller
{
    /**
     * 创建订单
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function create(Request $request)
    {
        $user = Auth::user();
        
        if (!$user) {
            return response()->json([
                'code' => 1002,
                'message' => '无效的令牌或已过期',
                'data' => null
            ]);
        }
        
        // 验证请求数据
        $validator = Validator::make($request->all(), [
            'product_id' => 'required|integer',
            'quantity' => 'required|integer|min:1',
            'address_id' => 'required|integer',
            'payment_method' => 'required|string|in:wechat,alipay,balance',
            'coupon_id' => 'nullable|integer',
            'remark' => 'nullable|string|max:255',
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                'code' => 1,
                'message' => $validator->errors()->first(),
                'data' => null
            ]);
        }
        
        // 获取产品信息
        $product = DB::table('products')
            ->where('id', $request->product_id)
            ->first();
            
        if (!$product) {
            return response()->json([
                'code' => 1,
                'message' => '产品不存在',
                'data' => null
            ]);
        }
        
        // 检查产品库存
        if ($product->stock < $request->quantity) {
            return response()->json([
                'code' => 1,
                'message' => '产品库存不足',
                'data' => null
            ]);
        }
        
        // 获取收货地址
        $address = DB::table('user_addresses')
            ->where('id', $request->address_id)
            ->where('user_id', $user->id)
            ->first();
            
        if (!$address) {
            return response()->json([
                'code' => 1,
                'message' => '收货地址不存在',
                'data' => null
            ]);
        }
        
        // 计算订单金额
        $totalAmount = $product->price * $request->quantity;
        $discountAmount = 0;
        
        // 处理优惠券
        if ($request->coupon_id) {
            $coupon = DB::table('coupons')
                ->where('id', $request->coupon_id)
                ->where('user_id', $user->id)
                ->where('status', 1) // 未使用
                ->where('expire_time', '>', Carbon::now())
                ->first();
                
            if ($coupon) {
                $discountAmount = min($coupon->amount, $totalAmount);
            }
        }
        
        // 计算实际支付金额
        $payAmount = $totalAmount - $discountAmount;
        
        // 生成订单号
        $orderNo = 'ORD' . date('YmdHis') . rand(1000, 9999);
        
        // 开始事务
        DB::beginTransaction();
        
        try {
            // 创建订单
            $orderId = DB::table('orders')->insertGetId([
                'order_no' => $orderNo,
                'user_id' => $user->id,
                'product_id' => $product->id,
                'product_name' => $product->name,
                'product_image' => $product->image,
                'quantity' => $request->quantity,
                'price' => $product->price,
                'total_amount' => $totalAmount,
                'discount_amount' => $discountAmount,
                'pay_amount' => $payAmount,
                'payment_method' => $request->payment_method,
                'status' => 'pending', // 待支付
                'address_name' => $address->name,
                'address_phone' => $address->phone,
                'address_province' => $address->province,
                'address_city' => $address->city,
                'address_district' => $address->district,
                'address_detail' => $address->detail,
                'remark' => $request->remark,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ]);
            
            // 更新产品库存
            DB::table('products')
                ->where('id', $product->id)
                ->decrement('stock', $request->quantity);
                
            // 如果使用了优惠券，更新优惠券状态
            if ($request->coupon_id && $coupon) {
                DB::table('coupons')
                    ->where('id', $coupon->id)
                    ->update([
                        'status' => 2, // 已使用
                        'used_time' => Carbon::now(),
                        'order_id' => $orderId,
                    ]);
            }
            
            // 提交事务
            DB::commit();
            
            // 返回订单信息
            return response()->json([
                'code' => 0,
                'message' => '创建订单成功',
                'data' => [
                    'order_id' => $orderId,
                    'order_no' => $orderNo,
                    'pay_amount' => $payAmount,
                    'payment_method' => $request->payment_method,
                ]
            ]);
            
        } catch (\Exception $e) {
            // 回滚事务
            DB::rollBack();
            
            Log::error('创建订单失败: ' . $e->getMessage());
            
            return response()->json([
                'code' => 1,
                'message' => '创建订单失败: ' . $e->getMessage(),
                'data' => null
            ]);
        }
    }
    
    /**
     * 获取订单状态
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function status(Request $request)
    {
        $user = Auth::user();
        
        if (!$user) {
            return response()->json([
                'code' => 1002,
                'message' => '无效的令牌或已过期',
                'data' => null
            ]);
        }
        
        // 验证请求数据
        $validator = Validator::make($request->all(), [
            'order_no' => 'required|string',
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                'code' => 1,
                'message' => $validator->errors()->first(),
                'data' => null
            ]);
        }
        
        // 获取订单信息
        $order = DB::table('orders')
            ->where('order_no', $request->order_no)
            ->where('user_id', $user->id)
            ->first();
            
        if (!$order) {
            return response()->json([
                'code' => 1,
                'message' => '订单不存在',
                'data' => null
            ]);
        }
        
        // 返回订单状态
        return response()->json([
            'code' => 0,
            'message' => '获取订单状态成功',
            'data' => [
                'order_id' => $order->id,
                'order_no' => $order->order_no,
                'status' => $order->status,
                'pay_amount' => $order->pay_amount,
                'payment_method' => $order->payment_method,
                'created_at' => $order->created_at,
                'paid_at' => $order->paid_at,
            ]
        ]);
    }
    
    /**
     * 获取用户订单列表
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getUserOrders(Request $request)
    {
        $user = Auth::user();
        
        if (!$user) {
            return response()->json([
                'code' => 1002,
                'message' => '无效的令牌或已过期',
                'data' => null
            ]);
        }
        
        // 验证请求数据
        $validator = Validator::make($request->all(), [
            'status' => 'nullable|string|in:all,pending,paid,shipped,completed,cancelled',
            'page' => 'nullable|integer|min:1',
            'limit' => 'nullable|integer|min:1|max:50',
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                'code' => 1,
                'message' => $validator->errors()->first(),
                'data' => null
            ]);
        }
        
        // 获取请求参数
        $status = $request->input('status', 'all');
        $page = $request->input('page', 1);
        $limit = $request->input('limit', 10);
        
        // 构建查询
        $query = DB::table('orders')
            ->where('user_id', $user->id);
            
        // 根据状态筛选
        if ($status !== 'all') {
            $query->where('status', $status);
        }
        
        // 获取总数
        $total = $query->count();
        
        // 获取分页数据
        $orders = $query->orderBy('created_at', 'desc')
            ->offset(($page - 1) * $limit)
            ->limit($limit)
            ->get();
            
        // 返回订单列表
        return response()->json([
            'code' => 0,
            'message' => '获取用户订单列表成功',
            'data' => [
                'total' => $total,
                'page' => $page,
                'limit' => $limit,
                'orders' => $orders,
            ]
        ]);
    }
    
    /**
     * 取消订单
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function cancel(Request $request)
    {
        $user = Auth::user();
        
        if (!$user) {
            return response()->json([
                'code' => 1002,
                'message' => '无效的令牌或已过期',
                'data' => null
            ]);
        }
        
        // 验证请求数据
        $validator = Validator::make($request->all(), [
            'order_no' => 'required|string',
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                'code' => 1,
                'message' => $validator->errors()->first(),
                'data' => null
            ]);
        }
        
        // 获取订单信息
        $order = DB::table('orders')
            ->where('order_no', $request->order_no)
            ->where('user_id', $user->id)
            ->first();
            
        if (!$order) {
            return response()->json([
                'code' => 1,
                'message' => '订单不存在',
                'data' => null
            ]);
        }
        
        // 检查订单状态
        if ($order->status !== 'pending') {
            return response()->json([
                'code' => 1,
                'message' => '只能取消待支付的订单',
                'data' => null
            ]);
        }
        
        // 开始事务
        DB::beginTransaction();
        
        try {
            // 更新订单状态
            DB::table('orders')
                ->where('id', $order->id)
                ->update([
                    'status' => 'cancelled',
                    'updated_at' => Carbon::now(),
                ]);
                
            // 恢复产品库存
            DB::table('products')
                ->where('id', $order->product_id)
                ->increment('stock', $order->quantity);
                
            // 如果使用了优惠券，恢复优惠券状态
            DB::table('coupons')
                ->where('order_id', $order->id)
                ->update([
                    'status' => 1, // 未使用
                    'used_time' => null,
                    'order_id' => null,
                ]);
                
            // 提交事务
            DB::commit();
            
            // 返回成功信息
            return response()->json([
                'code' => 0,
                'message' => '取消订单成功',
                'data' => null
            ]);
            
        } catch (\Exception $e) {
            // 回滚事务
            DB::rollBack();
            
            Log::error('取消订单失败: ' . $e->getMessage());
            
            return response()->json([
                'code' => 1,
                'message' => '取消订单失败: ' . $e->getMessage(),
                'data' => null
            ]);
        }
    }
    
    /**
     * 获取订单详情
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function detail(Request $request)
    {
        $user = Auth::user();
        
        if (!$user) {
            return response()->json([
                'code' => 1002,
                'message' => '无效的令牌或已过期',
                'data' => null
            ]);
        }
        
        // 验证请求数据
        $validator = Validator::make($request->all(), [
            'order_no' => 'required|string',
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                'code' => 1,
                'message' => $validator->errors()->first(),
                'data' => null
            ]);
        }
        
        // 获取订单信息
        $order = DB::table('orders')
            ->where('order_no', $request->order_no)
            ->where('user_id', $user->id)
            ->first();
            
        if (!$order) {
            return response()->json([
                'code' => 1,
                'message' => '订单不存在',
                'data' => null
            ]);
        }
        
        // 获取订单使用的优惠券
        $coupon = DB::table('coupons')
            ->where('order_id', $order->id)
            ->first();
            
        // 获取订单物流信息
        $logistics = DB::table('order_logistics')
            ->where('order_id', $order->id)
            ->first();
            
        // 构建订单详情
        $orderDetail = [
            'id' => $order->id,
            'order_no' => $order->order_no,
            'user_id' => $order->user_id,
            'product_id' => $order->product_id,
            'product_name' => $order->product_name,
            'product_image' => $order->product_image,
            'quantity' => $order->quantity,
            'price' => $order->price,
            'total_amount' => $order->total_amount,
            'discount_amount' => $order->discount_amount,
            'pay_amount' => $order->pay_amount,
            'payment_method' => $order->payment_method,
            'status' => $order->status,
            'address_name' => $order->address_name,
            'address_phone' => $order->address_phone,
            'address_province' => $order->address_province,
            'address_city' => $order->address_city,
            'address_district' => $order->address_district,
            'address_detail' => $order->address_detail,
            'remark' => $order->remark,
            'created_at' => $order->created_at,
            'paid_at' => $order->paid_at,
            'shipped_at' => $order->shipped_at,
            'completed_at' => $order->completed_at,
            'cancelled_at' => $order->cancelled_at,
            'coupon' => $coupon,
            'logistics' => $logistics,
        ];
        
        // 返回订单详情
        return response()->json([
            'code' => 0,
            'message' => '获取订单详情成功',
            'data' => $orderDetail
        ]);
    }
}
