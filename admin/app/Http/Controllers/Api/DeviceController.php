<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class DeviceController extends Controller
{
    /**
     * 获取用户设备列表
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function userDevices(Request $request)
    {
        $user = Auth::user();
        
        if (!$user) {
            return response()->json([
                'code' => 1002,
                'message' => '无效的令牌或已过期',
                'data' => null
            ]);
        }
        
        try {
            // 获取分页参数
            $page = $request->input('page', 1);
            $pageSize = $request->input('pageSize', 10);
            $offset = ($page - 1) * $pageSize;
            
            // 查询用户设备总数
            $total = DB::table('tapp_devices')
                ->where('app_user_id', $user->id)
                ->count();
                
            // 查询用户设备列表
            $devices = DB::table('tapp_devices')
                ->where('app_user_id', $user->id)
                ->orderBy('created_at', 'desc')
                ->offset($offset)
                ->limit($pageSize)
                ->get();
                
            // 处理设备数据
            $deviceList = [];
            foreach ($devices as $device) {
                // 查询设备最新状态
                $deviceStatus = DB::table('tapp_device_status')
                    ->where('device_id', $device->id)
                    ->orderBy('created_at', 'desc')
                    ->first();
                    
                // 查询设备类型信息
                $deviceType = DB::table('tapp_device_types')
                    ->where('id', $device->device_type_id)
                    ->first();
                    
                // 构建设备数据
                $deviceData = [
                    'id' => $device->id,
                    'device_id' => $device->device_id,
                    'name' => $device->name,
                    'model' => $device->model,
                    'type' => $deviceType ? $deviceType->name : '未知类型',
                    'status' => $deviceStatus ? $deviceStatus->status : 'offline',
                    'is_online' => $deviceStatus ? ($deviceStatus->status === 'online') : false,
                    'last_online_time' => $deviceStatus ? $deviceStatus->created_at : null,
                    'created_at' => $device->created_at,
                    'is_self_use' => $device->is_self_use,
                    'location' => $device->location,
                    'image' => $device->image,
                ];
                
                $deviceList[] = $deviceData;
            }
            
            return response()->json([
                'code' => 0,
                'message' => '获取设备列表成功',
                'data' => [
                    'total' => $total,
                    'page' => (int)$page,
                    'pageSize' => (int)$pageSize,
                    'list' => $deviceList
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('获取设备列表失败: ' . $e->getMessage());
            
            return response()->json([
                'code' => 1,
                'message' => '获取设备列表失败: ' . $e->getMessage(),
                'data' => null
            ]);
        }
    }
    
    /**
     * 获取设备详情
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function deviceDetail(Request $request, $id)
    {
        $user = Auth::user();
        
        if (!$user) {
            return response()->json([
                'code' => 1002,
                'message' => '无效的令牌或已过期',
                'data' => null
            ]);
        }
        
        try {
            // 查询设备信息
            $device = DB::table('tapp_devices')
                ->where('id', $id)
                ->first();
                
            if (!$device) {
                return response()->json([
                    'code' => 1,
                    'message' => '设备不存在',
                    'data' => null
                ]);
            }
            
            // 验证设备是否属于当前用户
            if ($device->app_user_id != $user->id) {
                return response()->json([
                    'code' => 1,
                    'message' => '无权查看此设备',
                    'data' => null
                ]);
            }
            
            // 查询设备类型信息
            $deviceType = DB::table('tapp_device_types')
                ->where('id', $device->device_type_id)
                ->first();
                
            // 查询设备最新状态
            $deviceStatus = DB::table('tapp_device_status')
                ->where('device_id', $device->id)
                ->orderBy('created_at', 'desc')
                ->first();
                
            // 查询设备滤芯信息
            $filters = DB::table('tapp_device_filters')
                ->where('device_id', $device->id)
                ->get();
                
            $filterList = [];
            foreach ($filters as $filter) {
                $filterList[] = [
                    'id' => $filter->id,
                    'name' => $filter->name,
                    'type' => $filter->type,
                    'capacity' => $filter->capacity,
                    'remaining' => $filter->remaining,
                    'remaining_percentage' => $filter->capacity > 0 ? round(($filter->remaining / $filter->capacity) * 100) : 0,
                    'last_replaced_at' => $filter->last_replaced_at,
                ];
            }
            
            // 构建设备详情数据
            $deviceDetail = [
                'id' => $device->id,
                'device_id' => $device->device_id,
                'name' => $device->name,
                'model' => $device->model,
                'type' => $deviceType ? $deviceType->name : '未知类型',
                'status' => $deviceStatus ? $deviceStatus->status : 'offline',
                'is_online' => $deviceStatus ? ($deviceStatus->status === 'online') : false,
                'last_online_time' => $deviceStatus ? $deviceStatus->created_at : null,
                'created_at' => $device->created_at,
                'is_self_use' => $device->is_self_use,
                'location' => $device->location,
                'image' => $device->image,
                'filters' => $filterList,
                'water_quality' => [
                    'tds_in' => $deviceStatus ? $deviceStatus->tds_in : 0,
                    'tds_out' => $deviceStatus ? $deviceStatus->tds_out : 0,
                    'temperature' => $deviceStatus ? $deviceStatus->temperature : 0,
                ],
                'usage_stats' => [
                    'total_usage' => $device->total_usage ?? 0,
                    'today_usage' => $device->today_usage ?? 0,
                    'yesterday_usage' => $device->yesterday_usage ?? 0,
                    'this_month_usage' => $device->this_month_usage ?? 0,
                    'last_month_usage' => $device->last_month_usage ?? 0,
                ],
            ];
            
            return response()->json([
                'code' => 0,
                'message' => '获取设备详情成功',
                'data' => $deviceDetail
            ]);
        } catch (\Exception $e) {
            Log::error('获取设备详情失败: ' . $e->getMessage());
            
            return response()->json([
                'code' => 1,
                'message' => '获取设备详情失败: ' . $e->getMessage(),
                'data' => null
            ]);
        }
    }
    
    /**
     * 获取设备状态
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function deviceStatus(Request $request, $id)
    {
        $user = Auth::user();
        
        if (!$user) {
            return response()->json([
                'code' => 1002,
                'message' => '无效的令牌或已过期',
                'data' => null
            ]);
        }
        
        try {
            // 查询设备信息
            $device = DB::table('tapp_devices')
                ->where('id', $id)
                ->first();
                
            if (!$device) {
                return response()->json([
                    'code' => 1,
                    'message' => '设备不存在',
                    'data' => null
                ]);
            }
            
            // 验证设备是否属于当前用户
            if ($device->app_user_id != $user->id) {
                return response()->json([
                    'code' => 1,
                    'message' => '无权查看此设备',
                    'data' => null
                ]);
            }
            
            // 查询设备最新状态
            $deviceStatus = DB::table('tapp_device_status')
                ->where('device_id', $device->id)
                ->orderBy('created_at', 'desc')
                ->first();
                
            // 查询设备历史状态（最近7天）
            $startDate = Carbon::now()->subDays(7)->startOfDay();
            $historyStatus = DB::table('tapp_device_status')
                ->where('device_id', $device->id)
                ->where('created_at', '>=', $startDate)
                ->orderBy('created_at', 'asc')
                ->get();
                
            // 按天统计用水量
            $dailyUsage = [];
            $dates = [];
            for ($i = 6; $i >= 0; $i--) {
                $date = Carbon::now()->subDays($i)->format('Y-m-d');
                $dates[] = $date;
                $dailyUsage[$date] = 0;
            }
            
            foreach ($historyStatus as $status) {
                $date = Carbon::parse($status->created_at)->format('Y-m-d');
                if (isset($dailyUsage[$date])) {
                    $dailyUsage[$date] += $status->water_usage ?? 0;
                }
            }
            
            // 构建设备状态数据
            $statusData = [
                'device_id' => $device->id,
                'device_name' => $device->name,
                'status' => $deviceStatus ? $deviceStatus->status : 'offline',
                'is_online' => $deviceStatus ? ($deviceStatus->status === 'online') : false,
                'last_online_time' => $deviceStatus ? $deviceStatus->created_at : null,
                'water_quality' => [
                    'tds_in' => $deviceStatus ? $deviceStatus->tds_in : 0,
                    'tds_out' => $deviceStatus ? $deviceStatus->tds_out : 0,
                    'temperature' => $deviceStatus ? $deviceStatus->temperature : 0,
                ],
                'current_usage' => $deviceStatus ? ($deviceStatus->water_usage ?? 0) : 0,
                'daily_usage' => [
                    'dates' => $dates,
                    'values' => array_values($dailyUsage),
                ],
                'alerts' => [
                    'filter_alerts' => $deviceStatus ? ($deviceStatus->filter_alerts ?? []) : [],
                    'system_alerts' => $deviceStatus ? ($deviceStatus->system_alerts ?? []) : [],
                ],
            ];
            
            return response()->json([
                'code' => 0,
                'message' => '获取设备状态成功',
                'data' => $statusData
            ]);
        } catch (\Exception $e) {
            Log::error('获取设备状态失败: ' . $e->getMessage());
            
            return response()->json([
                'code' => 1,
                'message' => '获取设备状态失败: ' . $e->getMessage(),
                'data' => null
            ]);
        }
    }
}
