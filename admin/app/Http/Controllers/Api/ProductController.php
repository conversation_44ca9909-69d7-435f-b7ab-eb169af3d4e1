<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Product;
use Illuminate\Http\Request;

class ProductController extends Controller
{
    /**
     * 获取商品列表
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        try {
            $query = Product::query();
            
            // 只获取上架状态的商品
            $query->where('status', 'active');
            
            // 分类筛选
            if ($request->has('category_id') && $request->category_id) {
                $query->where('category_id', $request->category_id);
            }
            
            // 关键词搜索
            if ($request->has('keyword') && $request->keyword) {
                $keyword = $request->keyword;
                $query->where(function($q) use ($keyword) {
                    $q->where('name', 'like', "%{$keyword}%")
                      ->orWhere('description', 'like', "%{$keyword}%");
                });
            }
            
            // 价格区间筛选
            if ($request->has('min_price') && $request->min_price) {
                $query->where('price', '>=', $request->min_price);
            }
            
            if ($request->has('max_price') && $request->max_price) {
                $query->where('price', '<=', $request->max_price);
            }
            
            // 排序
            $sortField = $request->input('sort_field', 'created_at');
            $sortOrder = $request->input('sort_order', 'desc');
            $query->orderBy($sortField, $sortOrder);
            
            // 分页
            $perPage = $request->input('per_page', 10);
            $products = $query->paginate($perPage);
            
            return response()->json([
                'code' => 0,
                'message' => 'success',
                'data' => $products->items(),
                'meta' => [
                    'total' => $products->total(),
                    'per_page' => $products->perPage(),
                    'current_page' => $products->currentPage(),
                    'last_page' => $products->lastPage()
                ]
            ]);
        } catch (\Exception $e) {
            \Log::error('获取商品列表失败：' . $e->getMessage());
            return response()->json([
                'code' => 1,
                'message' => '获取商品列表失败',
                'data' => []
            ]);
        }
    }

    /**
     * 获取商品详情
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        try {
            $product = Product::find($id);
            
            if (!$product) {
                return response()->json([
                    'code' => 1,
                    'message' => '商品不存在',
                    'data' => null
                ]);
            }
            
            // 加载分类信息
            $product->load('category');
            
            return response()->json([
                'code' => 0,
                'message' => 'success',
                'data' => $product
            ]);
        } catch (\Exception $e) {
            \Log::error('获取商品详情失败：' . $e->getMessage());
            return response()->json([
                'code' => 1,
                'message' => '获取商品详情失败',
                'data' => null
            ]);
        }
    }

    /**
     * 获取推荐商品
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function featured(Request $request)
    {
        try {
            $query = Product::query();
            
            // 只获取上架状态的商品
            $query->where('status', 'active');
            
            // 只获取推荐商品
            $query->where('is_featured', true);
            
            // 限制数量
            $limit = $request->input('limit', 6);
            $products = $query->limit($limit)->get();
            
            return response()->json([
                'code' => 0,
                'message' => 'success',
                'data' => $products
            ]);
        } catch (\Exception $e) {
            \Log::error('获取推荐商品失败：' . $e->getMessage());
            return response()->json([
                'code' => 1,
                'message' => '获取推荐商品失败',
                'data' => []
            ]);
        }
    }
}
