<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\SystemConfig;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class SettingsController extends Controller
{
    /**
     * 获取模块配置
     *
     * @param Request $request
     * @param string $module
     * @return \Illuminate\Http\JsonResponse
     */
    public function getModuleConfigs(Request $request, $module = 'basic')
    {
        try {
            Log::info('获取模块配置', ['module' => $module, 'ip' => $request->ip()]);
            
            // 获取系统配置中的模块配置
            $configs = SystemConfig::getConfigsByModule($module);
            
            if ($configs->isEmpty()) {
                // 如果没有配置，创建默认配置
                $this->createDefaultConfig($module);
                $configs = SystemConfig::getConfigsByModule($module);
            }
            
            $result = [];
            foreach ($configs as $config) {
                $result[$config->key] = $config->value;
            }
            
            Log::info('获取模块配置成功', ['module' => $module, 'count' => count($result)]);
            
            return response()->json([
                'code' => 0,
                'message' => '获取配置成功',
                'data' => $result
            ]);
        } catch (\Exception $e) {
            Log::error('获取模块配置失败', [
                'module' => $module,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            // 返回默认配置
            $defaultConfig = $this->getDefaultConfig($module);
            
            return response()->json([
                'code' => 0,
                'message' => '获取配置失败，使用默认配置',
                'data' => $defaultConfig
            ]);
        }
    }
    
    /**
     * 保存模块配置
     *
     * @param Request $request
     * @param string $module
     * @return \Illuminate\Http\JsonResponse
     */
    public function saveModuleConfig(Request $request, $module)
    {
        try {
            Log::info('保存模块配置', ['module' => $module, 'data' => $request->all()]);
            
            $data = $request->all();
            
            foreach ($data as $key => $value) {
                SystemConfig::setConfigValue($module, $key, $value);
            }
            
            return response()->json([
                'code' => 0,
                'message' => '保存配置成功'
            ]);
        } catch (\Exception $e) {
            Log::error('保存模块配置失败', [
                'module' => $module,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'code' => 1,
                'message' => '保存配置失败: ' . $e->getMessage()
            ]);
        }
    }
    
    /**
     * 获取微信配置
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getWechatConfig()
    {
        try {
            Log::info('获取微信配置');
            
            $config = SystemConfig::getWechatConfig();
            
            return response()->json([
                'code' => 0,
                'message' => '获取微信配置成功',
                'data' => $config
            ]);
        } catch (\Exception $e) {
            Log::error('获取微信配置失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            // 返回默认配置
            return response()->json([
                'code' => 0,
                'message' => '获取微信配置失败，使用默认配置',
                'data' => [
                    'app_id' => env('WECHAT_APP_ID', ''),
                    'app_secret' => env('WECHAT_APP_SECRET', ''),
                    'token' => env('WECHAT_TOKEN', ''),
                    'aes_key' => env('WECHAT_AES_KEY', ''),
                    'mch_id' => env('WECHAT_MCH_ID', ''),
                    'key' => env('WECHAT_KEY', ''),
                    'cert_path' => env('WECHAT_CERT_PATH', ''),
                    'key_path' => env('WECHAT_KEY_PATH', ''),
                    'notify_url' => env('WECHAT_NOTIFY_URL', '')
                ]
            ]);
        }
    }
    
    /**
     * 保存微信配置
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function saveWechatConfig(Request $request)
    {
        try {
            Log::info('保存微信配置', ['data' => $request->all()]);
            
            $data = $request->all();
            
            foreach ($data as $key => $value) {
                SystemConfig::setConfigValue('wechat', $key, $value);
            }
            
            return response()->json([
                'code' => 0,
                'message' => '保存微信配置成功'
            ]);
        } catch (\Exception $e) {
            Log::error('保存微信配置失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'code' => 1,
                'message' => '保存微信配置失败: ' . $e->getMessage()
            ]);
        }
    }
    
    /**
     * 创建默认配置
     *
     * @param string $module
     */
    private function createDefaultConfig($module)
    {
        Log::info('创建默认配置', ['module' => $module]);
        
        $configs = $this->getDefaultConfigArray($module);
        
        foreach ($configs as $config) {
            SystemConfig::updateOrCreate(
                ['module' => $module, 'key' => $config['key']],
                $config
            );
        }
    }
    
    /**
     * 获取默认配置
     *
     * @param string $module
     * @return array
     */
    private function getDefaultConfig($module)
    {
        $configs = $this->getDefaultConfigArray($module);
        
        $result = [];
        foreach ($configs as $config) {
            $result[$config['key']] = $config['value'];
        }
        
        return $result;
    }
    
    /**
     * 获取默认配置数组
     *
     * @param string $module
     * @return array
     */
    private function getDefaultConfigArray($module)
    {
        switch ($module) {
            case 'basic':
                return [
                    [
                        'module' => 'basic',
                        'key' => 'site_name',
                        'value' => '点点够管理系统',
                        'title' => '网站名称',
                        'description' => '网站名称，显示在浏览器标题栏',
                        'type' => 'text',
                        'options' => null,
                        'is_system' => true,
                        'sort' => 10,
                    ],
                    [
                        'module' => 'basic',
                        'key' => 'site_logo',
                        'value' => '/images/logo.png',
                        'title' => '网站Logo',
                        'description' => '网站Logo，显示在页面左上角',
                        'type' => 'image',
                        'options' => null,
                        'is_system' => true,
                        'sort' => 20,
                    ],
                    [
                        'module' => 'basic',
                        'key' => 'contact_phone',
                        'value' => '************',
                        'title' => '联系电话',
                        'description' => '联系电话，显示在页面底部',
                        'type' => 'text',
                        'options' => null,
                        'is_system' => true,
                        'sort' => 30,
                    ],
                    [
                        'module' => 'basic',
                        'key' => 'contact_email',
                        'value' => '<EMAIL>',
                        'title' => '联系邮箱',
                        'description' => '联系邮箱，显示在页面底部',
                        'type' => 'text',
                        'options' => null,
                        'is_system' => true,
                        'sort' => 40,
                    ],
                    [
                        'module' => 'basic',
                        'key' => 'copyright',
                        'value' => '©2023 点点够科技有限公司 版权所有',
                        'title' => '版权信息',
                        'description' => '版权信息，显示在页面底部',
                        'type' => 'text',
                        'options' => null,
                        'is_system' => true,
                        'sort' => 50,
                    ],
                    [
                        'module' => 'basic',
                        'key' => 'icp',
                        'value' => '粤ICP备12345678号',
                        'title' => '备案号',
                        'description' => '网站备案号，显示在页面底部',
                        'type' => 'text',
                        'options' => null,
                        'is_system' => true,
                        'sort' => 60,
                    ],
                ];
                
            case 'wechat':
                return [
                    [
                        'module' => 'wechat',
                        'key' => 'app_id',
                        'value' => env('WECHAT_APP_ID', ''),
                        'title' => 'AppID',
                        'description' => '微信公众号AppID',
                        'type' => 'text',
                        'options' => null,
                        'is_system' => true,
                        'sort' => 10,
                    ],
                    [
                        'module' => 'wechat',
                        'key' => 'app_secret',
                        'value' => env('WECHAT_APP_SECRET', ''),
                        'title' => 'AppSecret',
                        'description' => '微信公众号AppSecret',
                        'type' => 'text',
                        'options' => null,
                        'is_system' => true,
                        'sort' => 20,
                    ],
                    [
                        'module' => 'wechat',
                        'key' => 'token',
                        'value' => env('WECHAT_TOKEN', ''),
                        'title' => 'Token',
                        'description' => '微信公众号Token',
                        'type' => 'text',
                        'options' => null,
                        'is_system' => true,
                        'sort' => 30,
                    ],
                    [
                        'module' => 'wechat',
                        'key' => 'aes_key',
                        'value' => env('WECHAT_AES_KEY', ''),
                        'title' => 'EncodingAESKey',
                        'description' => '微信公众号EncodingAESKey',
                        'type' => 'text',
                        'options' => null,
                        'is_system' => true,
                        'sort' => 40,
                    ],
                    [
                        'module' => 'wechat',
                        'key' => 'mch_id',
                        'value' => env('WECHAT_MCH_ID', ''),
                        'title' => '商户号',
                        'description' => '微信支付商户号',
                        'type' => 'text',
                        'options' => null,
                        'is_system' => true,
                        'sort' => 50,
                    ],
                    [
                        'module' => 'wechat',
                        'key' => 'key',
                        'value' => env('WECHAT_KEY', ''),
                        'title' => '商户密钥',
                        'description' => '微信支付商户密钥',
                        'type' => 'text',
                        'options' => null,
                        'is_system' => true,
                        'sort' => 60,
                    ],
                    [
                        'module' => 'wechat',
                        'key' => 'cert_path',
                        'value' => env('WECHAT_CERT_PATH', ''),
                        'title' => '证书路径',
                        'description' => '微信支付证书路径',
                        'type' => 'text',
                        'options' => null,
                        'is_system' => true,
                        'sort' => 70,
                    ],
                    [
                        'module' => 'wechat',
                        'key' => 'key_path',
                        'value' => env('WECHAT_KEY_PATH', ''),
                        'title' => '密钥路径',
                        'description' => '微信支付密钥路径',
                        'type' => 'text',
                        'options' => null,
                        'is_system' => true,
                        'sort' => 80,
                    ],
                    [
                        'module' => 'wechat',
                        'key' => 'notify_url',
                        'value' => env('WECHAT_NOTIFY_URL', ''),
                        'title' => '回调地址',
                        'description' => '微信支付回调地址',
                        'type' => 'text',
                        'options' => null,
                        'is_system' => true,
                        'sort' => 90,
                    ],
                ];
                
            case 'sms':
                return [
                    [
                        'module' => 'sms',
                        'key' => 'provider',
                        'value' => 'aliyun',
                        'title' => '短信服务商',
                        'description' => '短信服务提供商',
                        'type' => 'select',
                        'options' => json_encode(['aliyun' => '阿里云', 'tencent' => '腾讯云']),
                        'is_system' => true,
                        'sort' => 10,
                    ],
                    [
                        'module' => 'sms',
                        'key' => 'access_key_id',
                        'value' => '',
                        'title' => 'AccessKeyId',
                        'description' => '短信服务AccessKeyId',
                        'type' => 'text',
                        'options' => null,
                        'is_system' => true,
                        'sort' => 20,
                    ],
                    [
                        'module' => 'sms',
                        'key' => 'access_key_secret',
                        'value' => '',
                        'title' => 'AccessKeySecret',
                        'description' => '短信服务AccessKeySecret',
                        'type' => 'text',
                        'options' => null,
                        'is_system' => true,
                        'sort' => 30,
                    ],
                    [
                        'module' => 'sms',
                        'key' => 'sign_name',
                        'value' => '点点够',
                        'title' => '短信签名',
                        'description' => '短信签名',
                        'type' => 'text',
                        'options' => null,
                        'is_system' => true,
                        'sort' => 40,
                    ],
                    [
                        'module' => 'sms',
                        'key' => 'template_code',
                        'value' => '',
                        'title' => '模板代码',
                        'description' => '短信模板代码',
                        'type' => 'text',
                        'options' => null,
                        'is_system' => true,
                        'sort' => 50,
                    ],
                ];
                
            default:
                return [];
        }
    }
}
