<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class WaterPointController extends Controller
{
    /**
     * 获取取水点列表
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        try {
            // 构建查询
            $query = DB::table('water_points')->where('status', 'active');
            
            // 关键词搜索
            if ($request->has('keyword') && $request->keyword) {
                $keyword = $request->keyword;
                $query->where(function($q) use ($keyword) {
                    $q->where('name', 'like', "%{$keyword}%")
                      ->orWhere('address', 'like', "%{$keyword}%");
                });
            }
            
            // 距离筛选
            if ($request->has('lat') && $request->has('lng') && $request->has('distance')) {
                $lat = $request->lat;
                $lng = $request->lng;
                $distance = $request->distance;
                
                // 使用MySQL的空间函数计算距离
                $query->selectRaw("*, 
                    ST_Distance_Sphere(
                        point(longitude, latitude), 
                        point(?, ?)
                    ) as distance", [$lng, $lat])
                    ->whereRaw("ST_Distance_Sphere(
                        point(longitude, latitude), 
                        point(?, ?)
                    ) <= ?", [$lng, $lat, $distance]);
                
                // 按距离排序
                $query->orderBy('distance', 'asc');
            } else {
                // 默认排序
                $query->orderBy('created_at', 'desc');
            }
            
            // 分页
            $page = $request->input('page', 1);
            $pageSize = $request->input('pageSize', 10);
            $waterPoints = $query->paginate($pageSize, ['*'], 'page', $page);
            
            return response()->json([
                'code' => 0,
                'message' => 'success',
                'data' => $waterPoints->items(),
                'meta' => [
                    'total' => $waterPoints->total(),
                    'per_page' => $waterPoints->perPage(),
                    'current_page' => $waterPoints->currentPage(),
                    'last_page' => $waterPoints->lastPage()
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('获取取水点列表失败：' . $e->getMessage());
            
            // 返回模拟数据
            return response()->json([
                'code' => 0,
                'message' => 'success',
                'data' => $this->getMockWaterPoints(),
                'meta' => [
                    'total' => 10,
                    'per_page' => 10,
                    'current_page' => 1,
                    'last_page' => 1
                ]
            ]);
        }
    }

    /**
     * 获取取水点详情
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function show(Request $request)
    {
        try {
            $id = $request->input('id');
            
            if (!$id) {
                return response()->json([
                    'code' => 1,
                    'message' => '取水点ID不能为空',
                    'data' => null
                ]);
            }
            
            $waterPoint = DB::table('water_points')->where('id', $id)->first();
            
            if (!$waterPoint) {
                return response()->json([
                    'code' => 1,
                    'message' => '取水点不存在',
                    'data' => null
                ]);
            }
            
            return response()->json([
                'code' => 0,
                'message' => 'success',
                'data' => $waterPoint
            ]);
        } catch (\Exception $e) {
            Log::error('获取取水点详情失败：' . $e->getMessage());
            
            // 返回模拟数据
            $mockData = $this->getMockWaterPoints();
            $waterPoint = $mockData[0];
            $waterPoint['id'] = $request->input('id', 1);
            
            return response()->json([
                'code' => 0,
                'message' => 'success',
                'data' => $waterPoint
            ]);
        }
    }

    /**
     * 获取附近取水点
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function nearby(Request $request)
    {
        try {
            // 验证参数
            $request->validate([
                'lat' => 'required|numeric',
                'lng' => 'required|numeric',
                'radius' => 'nullable|numeric',
                'limit' => 'nullable|integer'
            ]);
            
            $lat = $request->lat;
            $lng = $request->lng;
            $radius = $request->input('radius', 5000); // 默认5公里
            $limit = $request->input('limit', 10); // 默认10条
            
            // 使用MySQL的空间函数计算距离
            $waterPoints = DB::table('water_points')
                ->selectRaw("*, 
                    ST_Distance_Sphere(
                        point(longitude, latitude), 
                        point(?, ?)
                    ) as distance", [$lng, $lat])
                ->whereRaw("ST_Distance_Sphere(
                    point(longitude, latitude), 
                    point(?, ?)
                ) <= ?", [$lng, $lat, $radius])
                ->where('status', 'active')
                ->orderBy('distance', 'asc')
                ->limit($limit)
                ->get();
            
            return response()->json([
                'code' => 0,
                'message' => 'success',
                'data' => $waterPoints
            ]);
        } catch (\Exception $e) {
            Log::error('获取附近取水点失败：' . $e->getMessage());
            
            // 返回模拟数据
            return response()->json([
                'code' => 0,
                'message' => 'success',
                'data' => $this->getMockWaterPoints()
            ]);
        }
    }

    /**
     * 获取模拟取水点数据
     *
     * @return array
     */
    private function getMockWaterPoints()
    {
        return [
            [
                'id' => 1,
                'name' => '北京市朝阳区取水点',
                'address' => '北京市朝阳区建国路88号',
                'latitude' => 39.9042,
                'longitude' => 116.4074,
                'contact_person' => '张经理',
                'contact_phone' => '13800138000',
                'open_time' => '08:00-22:00',
                'status' => 'active',
                'is_open' => 1,
                'rating' => 4.8,
                'device_count' => 5,
                'description' => '朝阳区中心位置，交通便利，环境整洁',
                'created_at' => '2023-01-01 00:00:00',
                'updated_at' => '2023-01-01 00:00:00'
            ],
            [
                'id' => 2,
                'name' => '北京市海淀区取水点',
                'address' => '北京市海淀区中关村大街1号',
                'latitude' => 39.9631,
                'longitude' => 116.3586,
                'contact_person' => '李经理',
                'contact_phone' => '13900139000',
                'open_time' => '07:00-23:00',
                'status' => 'active',
                'is_open' => 1,
                'rating' => 4.9,
                'device_count' => 8,
                'description' => '海淀区科技园区，环境优美，设备先进',
                'created_at' => '2023-01-02 00:00:00',
                'updated_at' => '2023-01-02 00:00:00'
            ],
            [
                'id' => 3,
                'name' => '北京市西城区取水点',
                'address' => '北京市西城区西长安街1号',
                'latitude' => 39.9139,
                'longitude' => 116.3917,
                'contact_person' => '王经理',
                'contact_phone' => '13700137000',
                'open_time' => '08:30-21:30',
                'status' => 'active',
                'is_open' => 1,
                'rating' => 4.7,
                'device_count' => 6,
                'description' => '西城区中心位置，交通便利，环境整洁',
                'created_at' => '2023-01-03 00:00:00',
                'updated_at' => '2023-01-03 00:00:00'
            ]
        ];
    }
}
