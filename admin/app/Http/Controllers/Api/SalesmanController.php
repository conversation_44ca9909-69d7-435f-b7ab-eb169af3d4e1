<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class SalesmanController extends Controller
{
    /**
     * 获取销售人员工作台数据
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getWorkspace(Request $request)
    {
        try {
            $user = Auth::user();
            
            if (!$user) {
                return response()->json([
                    'code' => 1002,
                    'message' => '无效的令牌或已过期',
                    'data' => null
                ]);
            }

            Log::info("业务员工作台API - 用户ID: {$user->id}");
            
            // 验证用户是否为销售人员
            $salesman = DB::table('salesmen')
                ->where('user_id', $user->id)
                ->first();
                
            if (!$salesman) {
                // 如果没有业务员记录，但用户有业务员角色，自动创建
                $appUser = DB::table('app_users')->where('id', $user->id)->first();
                if ($appUser && isset($appUser->is_salesman) && $appUser->is_salesman == 1) {
                    // 自动创建业务员记录
                    $salesmanId = DB::table('salesmen')->insertGetId([
                        'user_id' => $user->id,
                        'employee_id' => 'S' . str_pad($user->id, 6, '0', STR_PAD_LEFT),
                        'title' => '业务员',
                        'status' => 'active',
                        'created_at' => now(),
                        'updated_at' => now()
                    ]);
                    
                    $salesman = DB::table('salesmen')->where('id', $salesmanId)->first();
                    Log::info("自动创建业务员记录 - 用户ID: {$user->id}, 业务员ID: {$salesmanId}");
                } else {
                    return response()->json([
                        'code' => 1,
                        'message' => '您不是销售人员，无权访问',
                        'data' => null
                    ]);
                }
            }
            
            // 获取当前日期
            $today = Carbon::today()->format('Y-m-d');
            $yesterday = Carbon::yesterday()->format('Y-m-d');
            $currentMonth = Carbon::today()->format('Y-m');
            $lastMonth = Carbon::today()->subMonth()->format('Y-m');
            
            // 获取用户信息
            $userInfo = DB::table('app_users')->where('id', $user->id)->first();
            
            // 使用tapp_devices表统计业务员销售的设备（is_self_use=0表示销售给客户的设备）
            // 获取今日销售数据
            $todaySales = DB::table('tapp_devices')
                ->where('app_user_id', $user->id)
                ->whereDate('created_at', $today)
                ->where('is_self_use', 0) // 只统计销售给客户的设备
                ->count();
                
            // 获取昨日销售数据
            $yesterdaySales = DB::table('tapp_devices')
                ->where('app_user_id', $user->id)
                ->whereDate('created_at', $yesterday)
                ->where('is_self_use', 0)
                ->count();
                
            // 获取本月销售数据
            $currentMonthSales = DB::table('tapp_devices')
                ->where('app_user_id', $user->id)
                ->whereRaw("DATE_FORMAT(created_at, '%Y-%m') = ?", [$currentMonth])
                ->where('is_self_use', 0)
                ->count();
                
            // 获取上月销售数据
            $lastMonthSales = DB::table('tapp_devices')
                ->where('app_user_id', $user->id)
                ->whereRaw("DATE_FORMAT(created_at, '%Y-%m') = ?", [$lastMonth])
                ->where('is_self_use', 0)
                ->count();
                
            // 获取总销售数据
            $totalSales = DB::table('tapp_devices')
                ->where('app_user_id', $user->id)
                ->where('is_self_use', 0)
                ->count();
                
            // 设备固定价格（可以后续从配置表或产品表获取）
            $devicePrice = 3000; // 假设每台设备3000元
            
            // 获取今日销售金额（设备数量 * 单价）
            $todayAmount = $todaySales * $devicePrice;
                
            // 获取本月销售金额
            $currentMonthAmount = $currentMonthSales * $devicePrice;
                
            // 获取总销售金额
            $totalAmount = $totalSales * $devicePrice;
                
            // 获取最近5条销售记录（设备记录）
            $recentSales = DB::table('tapp_devices')
                ->where('app_user_id', $user->id)
                ->where('is_self_use', 0)
                ->orderBy('created_at', 'desc')
                ->limit(5)
                ->select('id', 'device_id', 'device_type', 'client_name', 'created_at')
                ->get()
                ->map(function($device) use ($devicePrice) {
                    return [
                        'id' => $device->id,
                        'order_no' => 'DEV-' . $device->device_id,
                        'total_amount' => $devicePrice,
                        'status' => 1,
                        'created_at' => $device->created_at,
                        'product_name' => $device->device_type . '净水器'
                    ];
                });
                
            // 获取客户统计（通过推荐关系）
            $customerCount = DB::table('app_users')
                ->where('referrer_id', $user->id)
                ->count();
                
            // 获取本月新增客户数
            $newCustomerCount = DB::table('app_users')
                ->where('referrer_id', $user->id)
                ->whereRaw("DATE_FORMAT(created_at, '%Y-%m') = ?", [$currentMonth])
                ->count();

            // 获取自用设备数量
            $selfUseDevices = DB::table('tapp_devices')
                ->where('app_user_id', $user->id)
                ->where('is_self_use', 1)
                ->count();
                
            // 构建返回数据
            $data = [
                'salesmanInfo' => [
                    'name' => $userInfo->name ?? $userInfo->wechat_nickname ?? '业务员',
                    'avatar' => $userInfo->avatar ?? '/app/images/profile/default-avatar.png',
                    'title' => $salesman->title ?? '业务员',
                    'employeeId' => $salesman->employee_id ?? 'S' . str_pad($user->id, 6, '0', STR_PAD_LEFT),
                    'phone' => $userInfo->phone ?? '',
                    'status' => $salesman->status ?? 'active',
                    'userId' => $user->id
                ],
                'statsInfo' => [
                    'todaySales' => (string)$todaySales,
                    'monthSales' => (string)$currentMonthSales,
                    'yearSales' => (string)$totalSales, // 暂时用总销量代替年销量
                    'totalSales' => (string)$totalSales,
                    'selfUseDevices' => (string)$selfUseDevices
                ],
                'commissionInfo' => [
                    'pendingAmount' => '0.00',
                    'monthAmount' => number_format($currentMonthAmount * 0.3, 2), // 假设30%提成
                    'totalAmount' => number_format($totalAmount * 0.3, 2)
                ],
                'targetInfo' => [
                    'monthTarget' => 30,
                    'yearTarget' => 360
                ],
                'rankingList' => $this->getRankingList('month'),
                'totalRankingList' => $this->getRankingList('total'),
                'rankingDate' => '本月',
                'targetDate' => Carbon::now()->format('Y-m-d')
            ];

            Log::info("业务员工作台数据获取成功 - 用户ID: {$user->id}, 总销量: {$totalSales}");
            
            return response()->json([
                'code' => 0,
                'message' => '获取销售人员工作台数据成功',
                'data' => $data
            ]);
        } catch (\Exception $e) {
            Log::error('获取销售人员工作台数据失败: ' . $e->getMessage());
            Log::error('错误堆栈: ' . $e->getTraceAsString());
            
            return response()->json([
                'code' => 1,
                'message' => '获取销售人员工作台数据失败: ' . $e->getMessage(),
                'data' => null
            ]);
        }
    }
    
    /**
     * 获取排行榜数据
     *
     * @param string $type month|total
     * @return array
     */
    private function getRankingList($type = 'month')
    {
        try {
            // 设备固定价格
            $devicePrice = 3000;
            
            $query = DB::table('app_users as u')
                ->leftJoin('tapp_devices as d', 'u.id', '=', 'd.app_user_id')
                ->where('u.is_salesman', 1)
                ->where('d.is_self_use', 0)
                ->select('u.id', 'u.name', 'u.wechat_nickname', 'u.avatar')
                ->selectRaw('COUNT(d.id) as sales_count')
                ->selectRaw('COUNT(d.id) * ? as sales_amount', [$devicePrice])
                ->groupBy('u.id', 'u.name', 'u.wechat_nickname', 'u.avatar');
                
            if ($type === 'month') {
                $currentMonth = Carbon::today()->format('Y-m');
                $query->whereRaw("DATE_FORMAT(d.created_at, '%Y-%m') = ?", [$currentMonth]);
            }
            
            $rankings = $query->orderBy('sales_count', 'desc')
                ->limit(10)
                ->get();
                
            $result = [];
            $rank = 1;
            foreach ($rankings as $item) {
                $result[] = [
                    'rank' => $rank++,
                    'name' => $item->name ?: $item->wechat_nickname ?: '用户' . $item->id,
                    'avatar' => $item->avatar ?: '/app/images/profile/default-avatar.png',
                    'sales' => (int)$item->sales_count,
                    'amount' => number_format($item->sales_amount ?: 0, 2)
                ];
            }
            
            return $result;
        } catch (\Exception $e) {
            Log::error('获取排行榜数据失败: ' . $e->getMessage());
            return [];
        }
    }
}
