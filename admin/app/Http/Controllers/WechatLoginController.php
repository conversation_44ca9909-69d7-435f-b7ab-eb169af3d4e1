<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Redirect;

class WechatLoginController extends Controller
{
    /**
     * 处理微信登录二维码扫码
     *
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function handleLogin(Request $request)
    {
        try {
            $scene = $request->get('scene');
            
            if (!$scene) {
                Log::error('微信登录缺少scene参数');
                return response('缺少必要参数', 400);
            }

            // 检查scene是否存在于缓存中
            $loginData = Cache::get('wechat_login_' . $scene);
            if (!$loginData) {
                Log::error('微信登录scene无效或已过期: ' . $scene);
                return response('二维码已过期，请重新获取', 400);
            }

            // 获取微信配置
            $wechatConfig = config('wechat.official_account.default');
            if (!$wechatConfig || !isset($wechatConfig['app_id'])) {
                Log::error('微信配置不完整');
                return response('微信配置错误', 500);
            }

            $appId = $wechatConfig['app_id'];
            $redirectUri = urlencode(config('app.url') . '/api/admin/v1/auth/wechat/login-callback');
            $state = $scene; // 使用scene作为state参数

            // 构建微信授权URL
            $authUrl = "https://open.weixin.qq.com/connect/oauth2/authorize?" .
                "appid={$appId}&" .
                "redirect_uri={$redirectUri}&" .
                "response_type=code&" .
                "scope=snsapi_userinfo&" .
                "state={$state}#wechat_redirect";

            Log::info('微信登录重定向', [
                'scene' => $scene,
                'auth_url' => $authUrl
            ]);

            return Redirect::to($authUrl);

        } catch (\Exception $e) {
            Log::error('微信登录处理失败: ' . $e->getMessage());
            return response('服务器错误', 500);
        }
    }

    /**
     * 处理微信绑定二维码扫码
     *
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function handleBind(Request $request)
    {
        try {
            $scene = $request->get('scene');
            
            if (!$scene) {
                Log::error('微信绑定缺少scene参数');
                return response('缺少必要参数', 400);
            }

            // 检查scene是否存在于缓存中
            $bindData = Cache::get('wechat_bind_' . $scene);
            if (!$bindData) {
                Log::error('微信绑定scene无效或已过期: ' . $scene);
                return response('二维码已过期，请重新获取', 400);
            }

            // 获取微信配置
            $wechatConfig = config('wechat.official_account.default');
            if (!$wechatConfig || !isset($wechatConfig['app_id'])) {
                Log::error('微信配置不完整');
                return response('微信配置错误', 500);
            }

            $appId = $wechatConfig['app_id'];
            $redirectUri = urlencode(config('app.url') . '/api/wechat/binding/callback');
            $state = $scene; // 使用scene作为state参数

            // 构建微信授权URL
            $authUrl = "https://open.weixin.qq.com/connect/oauth2/authorize?" .
                "appid={$appId}&" .
                "redirect_uri={$redirectUri}&" .
                "response_type=code&" .
                "scope=snsapi_userinfo&" .
                "state={$state}#wechat_redirect";

            Log::info('微信绑定重定向', [
                'scene' => $scene,
                'auth_url' => $authUrl
            ]);

            return Redirect::to($authUrl);

        } catch (\Exception $e) {
            Log::error('微信绑定处理失败: ' . $e->getMessage());
            return response('服务器错误', 500);
        }
    }
}