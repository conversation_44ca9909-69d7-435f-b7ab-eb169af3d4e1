<?php

namespace App\Http\Controllers\Common;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class ExampleController extends Controller
{
    /**
     * 示例方法
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        return response()->json([
            'code' => 0,
            'message' => '示例方法',
            'data' => [
                'timestamp' => time(),
                'date' => date('Y-m-d H:i:s')
            ]
        ]);
    }
}
