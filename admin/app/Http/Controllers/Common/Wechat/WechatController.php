<?php

namespace App\Http\Controllers\Common\Wechat;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Http;
use Exception;

class WechatController extends Controller
{
    /**
     * 微信应用配置
     */
    protected $appId = 'wxfa0f2b87e3a68cf9';
    protected $appSecret = '1fe5172b4a4c362cd7e166e4d8164cf1';

    /**
     * 获取微信登录URL
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getLoginUrl(Request $request)
    {
        // 记录请求
        Log::info('微信登录URL请求', [
            'params' => $request->all(),
            'user_agent' => $request->header('User-Agent')
        ]);

        // 获取回调地址
        $redirectUri = $request->input('redirect_uri', 'https://pay.itapgo.com/Tapp/admin/public/api/admin/v1/auth/wechat/callback');
        $state = $request->input('state', md5(uniqid(mt_rand(), true)));

        // 构建授权URL - 微信开放平台网站应用扫码登录
        $url = "https://open.weixin.qq.com/connect/qrconnect?appid={$this->appId}&redirect_uri=" .
               urlencode($redirectUri) . "&response_type=code&scope=snsapi_login&state={$state}#wechat_redirect";

        // 返回URL
        return response()->json([
            'code' => 0,
            'message' => '获取微信登录URL成功',
            'data' => [
                'url' => $url,
                'state' => $state
            ]
        ]);
    }

    /**
     * 处理微信登录回调
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function handleCallback(Request $request)
    {
        // 记录详细的请求信息，便于调试
        Log::info('微信登录回调请求', [
            'method' => $request->method(),
            'url' => $request->fullUrl(),
            'params' => $request->all(),
            'user_agent' => $request->header('User-Agent'),
            'ip' => $request->ip(),
            'headers' => $request->header()
        ]);

        // 获取code和state - 支持多种来源
        $code = null;
        $state = null;

        // 1. 尝试从查询参数获取
        if ($request->has('code')) {
            $code = $request->query('code');
            $state = $request->query('state');
            Log::info('从查询参数中获取code', ['code' => substr($code, 0, 6) . '...', 'state' => $state]);
        }

        // 2. 尝试从POST表单数据获取
        if (empty($code) && $request->has('code')) {
            $code = $request->input('code');
            $state = $request->input('state');
            Log::info('从表单数据中获取code', ['code' => substr($code, 0, 6) . '...', 'state' => $state]);
        }

        // 3. 尝试从JSON请求体中获取
        if (empty($code) && $request->getContent()) {
            try {
                $jsonData = json_decode($request->getContent(), true);
                if (is_array($jsonData) && isset($jsonData['code'])) {
                    $code = $jsonData['code'];
                    $state = $jsonData['state'] ?? null;
                    Log::info('从JSON请求体中获取code', ['code' => substr($code, 0, 6) . '...', 'state' => $state]);
                }
            } catch (\Exception $e) {
                Log::warning('解析JSON请求体失败', ['error' => $e->getMessage()]);
            }
        }

        // 如果仍然没有找到code参数，记录详细错误并返回友好提示
        if (empty($code)) {
            Log::error('微信登录回调缺少code参数', [
                'request_method' => $request->method(),
                'query_string' => $request->getQueryString(),
                'request_uri' => $request->getRequestUri(),
                'get' => $request->query(),
                'post' => $request->post(),
                'request_body' => substr($request->getContent(), 0, 200)
            ]);
            
            return response()->json([
                'code' => 400,
                'message' => '缺少必要参数：code，请确保微信授权回调参数正确',
                'data' => null,
                'debug' => [
                    'request_method' => $request->method(),
                    'query_string' => $request->getQueryString(),
                    'request_uri' => $request->getRequestUri(),
                    'get' => $request->query(),
                    'post' => $request->post(),
                    'headers' => $request->header()
                ]
            ], 400)->header('Access-Control-Allow-Origin', '*')
              ->header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
              ->header('Access-Control-Allow-Headers', 'X-Requested-With, Content-Type, Authorization');
        }

        // 如果是测试代码，使用模拟数据
        if ($code === 'test_code' || Str::startsWith($code, 'test_')) {
            Log::info('使用测试代码登录', ['code' => $code]);

            try {
                // 查询一个真实用户
                $user = DB::table('app_users')
                    ->where('is_vip', 1)
                    ->first();

                if (!$user) {
                    // 如果没有VIP用户，查询任意用户
                    $user = DB::table('app_users')
                        ->first();
                }

                if ($user) {
                    // 生成token
                    $timestamp = time();
                    $randomString = Str::random(32);
                    $secretKey = config('app.key');
                    $signature = hash_hmac('sha256', $user->id . '|' . $timestamp . '|' . $randomString, $secretKey);
                    $token = $user->id . '|' . $timestamp . '|' . $randomString . '|' . $signature;

                    // 处理字段，确保统一
                    $userData = (array)$user;
                    
                    // 确保用户ID字段命名一致
                    if (isset($userData['id']) && !isset($userData['userId'])) {
                        $userData['userId'] = $userData['id'];
                    } else if (isset($userData['userId']) && !isset($userData['id'])) {
                        $userData['id'] = $userData['userId'];
                    }
                    
                    // 返回成功响应
                    return response()->json([
                        'code' => 0,
                        'message' => '登录成功',
                        'data' => [
                            'token' => $token,
                            'user' => $userData,
                            'openid' => $userData['wechat_openid'] ?? 'test_openid_' . uniqid(),
                            'unionid' => $userData['wechat_unionid'] ?? '',
                            'nickname' => $userData['wechat_nickname'] ?? $userData['nickname'] ?? '测试用户',
                            'headimgurl' => $userData['wechat_avatar'] ?? 'https://thirdwx.qlogo.cn/mmopen/vi_32/ajNVdqHZLLAtbKEiaiaLWsZibHAVDia9ebEDmzENgr7NlDVtM7oKvAgSw/132',
                            'needBindPhone' => empty($userData['phone']) ? true : false
                        ]
                    ])->header('Access-Control-Allow-Origin', '*')
                      ->header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
                      ->header('Access-Control-Allow-Headers', 'X-Requested-With, Content-Type, Authorization');
                }
            } catch (Exception $e) {
                Log::error('测试登录异常', ['error' => $e->getMessage()]);
            }

            // 如果没有找到用户，返回模拟数据
            return response()->json([
                'code' => 0,
                'message' => '登录成功',
                'data' => [
                    'token' => 'test_token_' . uniqid() . '|' . rand(1000, 9999),
                    'user' => [
                        'id' => 1,
                        'userId' => 1,
                        'name' => '测试用户',
                        'nickname' => '陈来意',
                        'avatar' => 'https://thirdwx.qlogo.cn/mmopen/vi_32/ajNVdqHZLLAtbKEiaiaLWsZibHAVDia9ebEDmzENgr7NlDVtM7oKvAgSw/132',
                        'is_vip' => 1,
                        'balance' => 100,
                        'points' => 500,
                        'wechat_openid' => 'test_openid_' . uniqid()
                    ],
                    'openid' => 'test_openid_' . uniqid(),
                    'unionid' => 'test_unionid_' . uniqid(),
                    'nickname' => '陈来意',
                    'headimgurl' => 'https://thirdwx.qlogo.cn/mmopen/vi_32/ajNVdqHZLLAtbKEiaiaLWsZibHAVDia9ebEDmzENgr7NlDVtM7oKvAgSw/132',
                    'needBindPhone' => true
                ]
            ])->header('Access-Control-Allow-Origin', '*')
              ->header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
              ->header('Access-Control-Allow-Headers', 'X-Requested-With, Content-Type, Authorization');
        }

        try {
            // 获取微信访问令牌
            Log::info('开始获取微信访问令牌', ['code' => substr($code, 0, 6) . '...']);

            $tokenUrl = "https://api.weixin.qq.com/sns/oauth2/access_token";
            $tokenParams = [
                'appid' => $this->appId,
                'secret' => $this->appSecret,
                'code' => $code,
                'grant_type' => 'authorization_code'
            ];

            // 使用Laravel的HTTP客户端
            $tokenResponse = Http::timeout(10)
                ->retry(3, 1000)
                ->get($tokenUrl, $tokenParams);

            $tokenData = $tokenResponse->json();

            if (!$tokenResponse->successful() || empty($tokenData) || isset($tokenData['errcode'])) {
                Log::error('获取微信访问令牌失败', ['response' => $tokenData]);

                // 如果是授权码无效，可能是过期了，返回特定错误
                if (isset($tokenData['errcode']) && ($tokenData['errcode'] == 40029 || $tokenData['errcode'] == 40163)) {
                    return response()->json([
                        'code' => 401,
                        'message' => '授权码已过期或无效，请重新授权',
                        'data' => null
                    ], 401)->header('Access-Control-Allow-Origin', '*')
                      ->header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
                      ->header('Access-Control-Allow-Headers', 'X-Requested-With, Content-Type, Authorization');
                }

                return response()->json([
                    'code' => 500,
                    'message' => '获取微信访问令牌失败：' . ($tokenData['errmsg'] ?? '未知错误'),
                    'data' => null
                ], 500)->header('Access-Control-Allow-Origin', '*')
                  ->header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
                  ->header('Access-Control-Allow-Headers', 'X-Requested-With, Content-Type, Authorization');
            }

            // 获取微信用户信息
            $accessToken = $tokenData['access_token'];
            $openId = $tokenData['openid'];
            $unionId = $tokenData['unionid'] ?? '';

            Log::info('获取到微信OpenID', ['openid' => $openId, 'unionid' => $unionId]);

            $userInfoUrl = "https://api.weixin.qq.com/sns/userinfo";
            $userInfoParams = [
                'access_token' => $accessToken,
                'openid' => $openId,
                'lang' => 'zh_CN'
            ];

            $userInfoResponse = Http::timeout(10)
                ->retry(3, 1000)
                ->get($userInfoUrl, $userInfoParams);

            $userInfo = $userInfoResponse->json();

            if (!$userInfoResponse->successful() || empty($userInfo) || isset($userInfo['errcode'])) {
                Log::error('获取微信用户信息失败', ['response' => $userInfo]);

                // 如果是授权问题，可能需要重新授权
                if (isset($userInfo['errcode']) && ($userInfo['errcode'] == 40001 || $userInfo['errcode'] == 41001)) {
                    return response()->json([
                        'code' => 401,
                        'message' => '访问令牌已过期或无效，请重新授权',
                        'data' => null
                    ], 401)->header('Access-Control-Allow-Origin', '*')
                      ->header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
                      ->header('Access-Control-Allow-Headers', 'X-Requested-With, Content-Type, Authorization');
                }

                return response()->json([
                    'code' => 500,
                    'message' => '获取微信用户信息失败：' . ($userInfo['errmsg'] ?? '未知错误'),
                    'data' => null
                ], 500)->header('Access-Control-Allow-Origin', '*')
                  ->header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
                  ->header('Access-Control-Allow-Headers', 'X-Requested-With, Content-Type, Authorization');
            }

            Log::info('获取到微信用户信息', [
                'nickname' => $userInfo['nickname'],
                'openid' => $openId,
                'headimgurl' => $userInfo['headimgurl']
            ]);

            // 查询是否已存在此微信用户
            $user = DB::table('app_users')
                ->where('wechat_openid', $openId)
                ->first();

            $needBindPhone = false;

            if ($user) {
                Log::info('找到已存在的微信用户', ['user_id' => $user->id]);

                // 更新微信用户信息
                DB::table('app_users')
                    ->where('id', $user->id)
                    ->update([
                        'wechat_nickname' => $userInfo['nickname'],
                        'wechat_avatar' => $userInfo['headimgurl'],
                        'wechat_unionid' => $unionId,
                        'last_login_at' => now(),
                        'last_login_ip' => $request->ip()
                    ]);

                // 检查是否需要绑定手机
                $needBindPhone = empty($user->phone);
            } else {
                Log::info('未找到微信用户，创建新用户');

                // 创建新用户
                $userId = DB::table('app_users')->insertGetId([
                    'nickname' => $userInfo['nickname'],
                    'avatar' => $userInfo['headimgurl'],
                    'wechat_openid' => $openId,
                    'wechat_unionid' => $unionId,
                    'wechat_nickname' => $userInfo['nickname'],
                    'wechat_avatar' => $userInfo['headimgurl'],
                    'created_at' => now(),
                    'updated_at' => now(),
                    'last_login_at' => now(),
                    'last_login_ip' => $request->ip()
                ]);

                // 获取新创建的用户
                $user = DB::table('app_users')
                    ->where('id', $userId)
                    ->first();

                $needBindPhone = true;
            }

            // 生成token
            $timestamp = time();
            $randomString = Str::random(32);
            $secretKey = config('app.key');
            $signature = hash_hmac('sha256', $user->id . '|' . $timestamp . '|' . $randomString, $secretKey);
            $token = $user->id . '|' . $timestamp . '|' . $randomString . '|' . $signature;

            // 处理用户数据，确保字段一致性
            $userData = (array)$user;
            
            // 确保用户ID字段命名一致
            if (isset($userData['id']) && !isset($userData['userId'])) {
                $userData['userId'] = $userData['id'];
            } else if (isset($userData['userId']) && !isset($userData['id'])) {
                $userData['id'] = $userData['userId'];
            }

            // 返回成功响应
            return response()->json([
                'code' => 0,
                'message' => '登录成功',
                'data' => [
                    'token' => $token,
                    'user' => $userData,
                    'openid' => $openId,
                    'unionid' => $unionId,
                    'nickname' => $userInfo['nickname'],
                    'headimgurl' => $userInfo['headimgurl'],
                    'needBindPhone' => $needBindPhone
                ]
            ])->header('Access-Control-Allow-Origin', '*')
              ->header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
              ->header('Access-Control-Allow-Headers', 'X-Requested-With, Content-Type, Authorization');

        } catch (Exception $e) {
            Log::error('微信登录回调处理异常', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'code' => 500,
                'message' => '处理微信登录时发生错误：' . $e->getMessage(),
                'data' => null
            ], 500)->header('Access-Control-Allow-Origin', '*')
              ->header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
              ->header('Access-Control-Allow-Headers', 'X-Requested-With, Content-Type, Authorization');
        }
    }

    /**
     * 模拟微信用户信息（用于测试环境）
     *
     * @param string $code 微信授权码
     * @return array 模拟的用户信息
     */
    protected function mockWechatUserInfo($code)
    {
        // 生成一个基于code的唯一openid
        $openid = 'mock_' . substr(md5($code), 0, 16);

        // 根据code的不同部分生成不同的用户信息
        $codeHash = md5($code);
        $userType = hexdec(substr($codeHash, 0, 2)) % 5; // 0-4的用户类型

        // 默认用户信息
        $userInfo = [
            'openid' => $openid,
            'nickname' => '测试用户_' . substr($openid, -4),
            'sex' => 1,
            'language' => 'zh_CN',
            'city' => '广州',
            'province' => '广东',
            'country' => '中国',
            'headimgurl' => 'https://pay.itapgo.com/app/images/profile/default-avatar.png',
            'privilege' => [],
            'unionid' => 'mock_unionid_' . substr($codeHash, 16, 16)
        ];

        // 根据用户类型设置不同的信息
        switch ($userType) {
            case 0: // 普通用户
                // 使用默认值
                break;

            case 1: // VIP用户
                $userInfo['nickname'] = 'VIP用户_' . substr($openid, -4);
                $userInfo['headimgurl'] = 'https://pay.itapgo.com/app/images/profile/vip-avatar.png';
                $userInfo['city'] = '深圳';
                break;

            case 2: // 女性用户
                $userInfo['nickname'] = '女士_' . substr($openid, -4);
                $userInfo['sex'] = 2;
                $userInfo['headimgurl'] = 'https://pay.itapgo.com/app/images/profile/female-avatar.png';
                $userInfo['city'] = '上海';
                break;

            case 3: // 外国用户
                $userInfo['nickname'] = 'Foreign_' . substr($openid, -4);
                $userInfo['language'] = 'en';
                $userInfo['country'] = 'United States';
                $userInfo['province'] = 'California';
                $userInfo['city'] = 'San Francisco';
                $userInfo['headimgurl'] = 'https://pay.itapgo.com/app/images/profile/foreign-avatar.png';
                break;

            case 4: // 企业用户
                $userInfo['nickname'] = '企业_' . substr($openid, -4);
                $userInfo['headimgurl'] = 'https://pay.itapgo.com/app/images/profile/business-avatar.png';
                $userInfo['city'] = '北京';
                break;
        }

        // 记录模拟用户信息
        Log::info('生成模拟微信用户', $userInfo);

        return $userInfo;
    }

    /**
     * 检查微信用户角色并返回用户信息
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function checkUserRoles(Request $request)
    {
        // 记录请求
        Log::info('检查微信用户角色请求', [
            'params' => $request->all(),
            'user_agent' => $request->header('User-Agent')
        ]);

        // 验证请求
        $validated = $request->validate([
            'token' => 'required|string',
            'openid' => 'required|string',
        ]);

        try {
            // 解析token获取用户ID
            $tokenParts = explode('|', $validated['token']);
            if (count($tokenParts) < 3) {
                Log::error('无效的token格式', ['token' => $validated['token']]);
                return response()->json([
                    'code' => 401,
                    'message' => '无效的用户令牌',
                    'data' => null
                ], 401);
            }

            $userId = $tokenParts[0];

            // 查询用户信息
            $user = DB::table('app_users')->where('id', $userId)->first();
            if (!$user) {
                Log::error('未找到用户', ['userId' => $userId]);
                return response()->json([
                    'code' => 404,
                    'message' => '未找到用户',
                    'data' => null
                ], 404);
            }

            // 验证openid是否匹配
            if ($user->wechat_openid !== $validated['openid']) {
                Log::error('openid不匹配', [
                    'user_openid' => $user->wechat_openid,
                    'request_openid' => $validated['openid']
                ]);
                return response()->json([
                    'code' => 403,
                    'message' => '用户身份验证失败',
                    'data' => null
                ], 403);
            }

            // 获取用户角色信息
            $roles = DB::table('role_user')
                ->join('roles', 'role_user.role_id', '=', 'roles.id')
                ->where('role_user.user_id', $userId)
                ->select('roles.id', 'roles.name', 'roles.display_name', 'roles.description')
                ->get();

            // 处理用户信息，移除敏感字段
            $userArray = (array)$user;
            unset($userArray['password']);

            // 构建响应数据
            $responseData = [
                'user' => $userArray,
                'roles' => $roles,
                'needBindPhone' => empty($user->phone),
                'isVip' => $user->is_vip == 1,
                'isDealers' => false, // 默认非经销商
                'isSalesman' => false, // 默认非业务员
                'isAdmin' => false, // 默认非管理员
            ];

            // 判断特殊角色
            foreach ($roles as $role) {
                if (strtolower($role->name) === 'dealer' || strtolower($role->name) === 'dealers') {
                    $responseData['isDealers'] = true;
                }
                if (strtolower($role->name) === 'salesman') {
                    $responseData['isSalesman'] = true;
                }
                if (strtolower($role->name) === 'admin' || strtolower($role->name) === 'administrator') {
                    $responseData['isAdmin'] = true;
                }
            }

            return response()->json([
                'code' => 0,
                'message' => '获取用户角色成功',
                'data' => $responseData
            ]);

        } catch (Exception $e) {
            Log::error('检查用户角色异常', ['error' => $e->getMessage(), 'trace' => $e->getTraceAsString()]);
            return response()->json([
                'code' => 500,
                'message' => '服务器处理异常：' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    /**
     * 绑定手机号到微信用户
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function bindPhone(Request $request)
    {
        // 记录请求
        Log::info('绑定手机号请求', [
            'params' => $request->all(),
            'user_agent' => $request->header('User-Agent')
        ]);

        // 验证请求
        $validated = $request->validate([
            'token' => 'required|string',
            'phone' => 'required|string|regex:/^1[3-9]\d{9}$/',
            'code' => 'required|string',
        ]);

        try {
            // 验证短信验证码
            $smsVerified = $this->verifySmsCode($validated['phone'], $validated['code']);
            if (!$smsVerified) {
                return response()->json([
                    'code' => 400,
                    'message' => '短信验证码无效或已过期',
                    'data' => null
                ], 400);
            }

            // 解析token获取用户ID
            $tokenParts = explode('|', $validated['token']);
            if (count($tokenParts) < 3) {
                Log::error('无效的token格式', ['token' => $validated['token']]);
                return response()->json([
                    'code' => 401,
                    'message' => '无效的用户令牌',
                    'data' => null
                ], 401);
            }

            $userId = $tokenParts[0];

            // 查询用户是否存在
            $user = DB::table('app_users')->where('id', $userId)->first();
            if (!$user) {
                Log::error('未找到用户', ['userId' => $userId]);
                return response()->json([
                    'code' => 404,
                    'message' => '未找到用户',
                    'data' => null
                ], 404);
            }

            // 检查手机号是否已被其他用户使用
            $existingUser = DB::table('app_users')
                ->where('phone', $validated['phone'])
                ->where('id', '!=', $userId)
                ->first();

            if ($existingUser) {
                Log::warning('绑定手机号冲突', [
                    'phone' => $validated['phone'],
                    'current_userId' => $userId,
                    'existing_userId' => $existingUser->id
                ]);

                return response()->json([
                    'code' => 409,
                    'message' => '该手机号已被其他用户绑定',
                    'data' => null
                ], 409);
            }

            // 更新用户手机号
            DB::table('app_users')
                ->where('id', $userId)
                ->update([
                    'phone' => $validated['phone'],
                    'updated_at' => now()
                ]);

            // 获取更新后的用户信息
            $updatedUser = DB::table('app_users')->where('id', $userId)->first();
            $userArray = (array)$updatedUser;
            unset($userArray['password']);

            return response()->json([
                'code' => 0,
                'message' => '绑定手机号成功',
                'data' => [
                    'user' => $userArray,
                    'needBindPhone' => false
                ]
            ]);

        } catch (Exception $e) {
            Log::error('绑定手机号异常', ['error' => $e->getMessage(), 'trace' => $e->getTraceAsString()]);
            return response()->json([
                'code' => 500,
                'message' => '服务器处理异常：' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    /**
     * 验证短信验证码
     *
     * @param string $phone 手机号
     * @param string $code 验证码
     * @return bool 验证结果
     */
    protected function verifySmsCode($phone, $code)
    {
        try {
            // 查询是否有有效的验证码记录
            $record = DB::table('sms_verification')
                ->where('phone', $phone)
                ->where('code', $code)
                ->where('expires_at', '>', now())
                ->where('verified', 0)
                ->orderBy('created_at', 'desc')
                ->first();

            if (!$record) {
                Log::info('短信验证码无效', ['phone' => $phone, 'code' => $code]);
                return false;
            }

            // 标记验证码已使用
            DB::table('sms_verification')
                ->where('id', $record->id)
                ->update([
                    'verified' => 1,
                    'verified_at' => now()
                ]);

            Log::info('短信验证码验证成功', ['phone' => $phone, 'code' => $code]);
            return true;

        } catch (Exception $e) {
            Log::error('验证短信验证码异常', ['error' => $e->getMessage()]);
            return false;
        }
    }
}
