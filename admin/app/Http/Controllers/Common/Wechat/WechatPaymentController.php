<?php

namespace App\Http\Controllers\Common\Wechat;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class WechatPaymentController extends Controller
{
    /**
     * 创建微信支付订单
     */
    public function createOrder(Request $request): JsonResponse
    {
        // 临时返回空数据，避免路由错误
        return response()->json([
            'code' => 200,
            'message' => '微信支付功能开发中',
            'data' => [
                'order_id' => 'temp_' . time(),
                'pay_url' => '',
                'status' => 'developing'
            ]
        ]);
    }

    /**
     * 微信支付回调通知
     */
    public function notify(Request $request): JsonResponse
    {
        return response()->json([
            'code' => 200,
            'message' => '微信支付回调功能开发中',
            'data' => null
        ]);
    }
} 