<?php

namespace App\Http\Controllers\Common\Wechat;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Log;

class WechatJssdkController extends Controller
{
    // 微信配置
    protected $wechatConfig = [
        'APP_ID' => 'wx501332efbaae387c',
        'APP_SECRET' => 'f70ad4faefb54e68e3a5e7b5885a7c28',
    ];

    /**
     * 获取微信JSSDK配置
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getConfig(Request $request)
    {
        // 记录请求信息，便于调试
        Log::info('微信JSSDK配置请求', [
            'url' => $request->input('url'),
            'method' => $request->method(),
            'ip' => $request->ip(),
            'user_agent' => $request->header('User-Agent'),
            'headers' => $request->header(),
        ]);

        // 设置允许跨域请求的响应头
        $headers = [
            'Access-Control-Allow-Origin' => '*',
            'Access-Control-Allow-Methods' => 'GET, POST, OPTIONS',
            'Access-Control-Allow-Headers' => 'Content-Type, Authorization, X-Requested-With',
            'Content-Type' => 'application/json; charset=utf-8',
            'Cache-Control' => 'no-cache, no-store, must-revalidate',
            'Pragma' => 'no-cache',
            'Expires' => '0'
        ];

        // 如果是OPTIONS请求，直接返回成功，不进行任何处理
        if ($request->method() === 'OPTIONS') {
            Log::info('处理微信JSSDK配置OPTIONS预检请求');
            return response()->json([], 200, $headers);
        }

        // 获取当前URL
        $url = $request->input('url');

        if (empty($url)) {
            Log::warning('微信JSSDK配置请求缺少URL参数');
            return response()->json([
                'code' => 1001,
                'message' => '参数错误：缺少URL参数',
                'data' => null
            ], 200, $headers);
        }

        try {
            // 检查微信配置是否存在
            if (empty($this->wechatConfig['APP_ID']) || empty($this->wechatConfig['APP_SECRET'])) {
                Log::error('微信配置缺失');
                return response()->json([
                    'code' => 1002,
                    'message' => '微信配置缺失',
                    'data' => null
                ], 200, $headers);
            }

            // 获取access_token
            $accessToken = $this->getAccessToken();
            if (!$accessToken) {
                Log::error('获取微信access_token失败');
                return response()->json([
                    'code' => 1003,
                    'message' => '获取微信access_token失败',
                    'data' => null
                ], 200, $headers);
            }

            // 获取jsapi_ticket
            $jsapiTicket = $this->getJsapiTicket($accessToken);
            if (!$jsapiTicket) {
                Log::error('获取微信jsapi_ticket失败');
                return response()->json([
                    'code' => 1004,
                    'message' => '获取微信jsapi_ticket失败',
                    'data' => null
                ], 200, $headers);
            }

            // 生成签名参数
            $timestamp = time();
            $nonceStr = $this->createNonceStr();

            // 按照字典序排序参数
            $string = "jsapi_ticket=$jsapiTicket&noncestr=$nonceStr&timestamp=$timestamp&url=$url";
            $signature = sha1($string);

            // 记录签名过程，便于调试
            Log::info('微信JSSDK签名过程', [
                'jsapi_ticket' => $jsapiTicket,
                'noncestr' => $nonceStr,
                'timestamp' => $timestamp,
                'url' => $url,
                'string' => $string,
                'signature' => $signature
            ]);

            $result = [
                'code' => 0,
                'message' => '获取微信JSSDK配置成功',
                'data' => [
                    'appId' => $this->wechatConfig['APP_ID'],
                    'timestamp' => $timestamp,
                    'nonceStr' => $nonceStr,
                    'signature' => $signature,
                    'jsApiList' => [
                        'updateAppMessageShareData',
                        'updateTimelineShareData',
                        'onMenuShareTimeline',
                        'onMenuShareAppMessage',
                        'chooseWXPay',
                        'getBrandWCPayRequest'
                    ]
                ]
            ];

            // 记录响应结果
            Log::info('微信JSSDK配置响应', [
                'result' => $result
            ]);

            // 返回配置信息
            return response()->json($result, 200, $headers);
        } catch (\Exception $e) {
            // 记录异常信息
            Log::error('获取微信JSSDK配置异常', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'code' => 1099,
                'message' => '获取微信JSSDK配置失败: ' . $e->getMessage(),
                'data' => null
            ], 200, $headers);
        }
    }

    /**
     * 获取微信access_token
     *
     * @return string|null
     */
    protected function getAccessToken()
    {
        // 尝试从缓存获取
        $cacheKey = 'wechat_access_token';
        if (Cache::has($cacheKey)) {
            Log::info('从缓存获取微信access_token');
            return Cache::get($cacheKey);
        }

        // 从微信API获取
        $url = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid={$this->wechatConfig['APP_ID']}&secret={$this->wechatConfig['APP_SECRET']}";
        try {
            Log::info('请求微信access_token', ['url' => $url]);
            $response = Http::timeout(10)->get($url);

            if ($response->successful() && isset($response['access_token'])) {
                $accessToken = $response['access_token'];
                // 缓存7000秒（微信官方有效期为7200秒）
                Cache::put($cacheKey, $accessToken, 7000);
                Log::info('获取微信access_token成功', ['token_length' => strlen($accessToken)]);
                return $accessToken;
            }

            Log::error('获取微信access_token失败', ['response' => $response->json()]);
            return null;
        } catch (\Exception $e) {
            Log::error('请求微信access_token异常', ['error' => $e->getMessage()]);
            return null;
        }
    }

    /**
     * 获取微信jsapi_ticket
     *
     * @param string $accessToken
     * @return string|null
     */
    protected function getJsapiTicket($accessToken)
    {
        // 尝试从缓存获取
        $cacheKey = 'wechat_jsapi_ticket';
        if (Cache::has($cacheKey)) {
            Log::info('从缓存获取微信jsapi_ticket');
            return Cache::get($cacheKey);
        }

        // 从微信API获取
        $url = "https://api.weixin.qq.com/cgi-bin/ticket/getticket?access_token={$accessToken}&type=jsapi";
        try {
            Log::info('请求微信jsapi_ticket', ['url' => $url]);
            $response = Http::timeout(10)->get($url);

            if ($response->successful() && isset($response['ticket'])) {
                $ticket = $response['ticket'];
                // 缓存7000秒（微信官方有效期为7200秒）
                Cache::put($cacheKey, $ticket, 7000);
                Log::info('获取微信jsapi_ticket成功', ['ticket_length' => strlen($ticket)]);
                return $ticket;
            }

            Log::error('获取微信jsapi_ticket失败', ['response' => $response->json()]);
            return null;
        } catch (\Exception $e) {
            Log::error('请求微信jsapi_ticket异常', ['error' => $e->getMessage()]);
            return null;
        }
    }

    /**
     * 生成随机字符串
     *
     * @param int $length 字符串长度
     * @return string 随机字符串
     */
    protected function createNonceStr($length = 16)
    {
        $chars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
        $str = "";
        for ($i = 0; $i < $length; $i++) {
            $str .= substr($chars, mt_rand(0, strlen($chars) - 1), 1);
        }
        return $str;
    }
}
