<?php

namespace App\Http\Controllers\Admin\Payment;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Logistics;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Carbon\Carbon;

class OrderController extends Controller
{
    /**
     * 显示订单列表
     */
    public function index(Request $request)
    {
        $query = Order::query();
        
        // 搜索条件
        if ($request->has('keyword') && $request->keyword) {
            $query->where(function($q) use ($request) {
                $q->where('order_no', 'like', '%' . $request->keyword . '%')
                  ->orWhere('receiver_name', 'like', '%' . $request->keyword . '%')
                  ->orWhere('receiver_phone', 'like', '%' . $request->keyword . '%');
            });
        }
        
        if ($request->has('status') && $request->status !== null) {
            $query->where('status', $request->status);
        }
        
        if ($request->has('pay_status') && $request->pay_status !== null) {
            $query->where('pay_status', $request->pay_status);
        }
        
        if ($request->has('ship_status') && $request->ship_status !== null) {
            $query->where('ship_status', $request->ship_status);
        }
        
        if ($request->has('start_date') && $request->start_date) {
            $query->where('created_at', '>=', $request->start_date . ' 00:00:00');
        }
        
        if ($request->has('end_date') && $request->end_date) {
            $query->where('created_at', '<=', $request->end_date . ' 23:59:59');
        }
        
        // 默认按照创建时间倒序排序
        $query->orderBy($request->input('sort', 'created_at'), $request->input('order', 'desc'));
        
        // 分页
        $perPage = $request->input('limit', 15);
        $orders = $query->paginate($perPage);
        
        // 处理订单数据，添加状态文本等
        $data = $orders->map(function ($order) {
            $order->status_text = $order->getStatusTextAttribute();
            $order->refund_status_text = $order->getRefundStatusTextAttribute();
            $order->payment_status_text = $order->getPaymentStatusTextAttribute();
            $order->shipping_status_text = $order->getShippingStatusTextAttribute();
            return $order;
        });
        
        return response()->json([
            'code' => 0,
            'message' => 'success',
            'data' => $data,
            'total' => $orders->total()
        ]);
    }

    /**
     * 显示订单详情
     */
    public function show($id)
    {
        $order = Order::with('items')->find($id);
        
        if (!$order) {
            return response()->json([
                'code' => 1,
                'message' => '订单不存在'
            ], 404);
        }
        
        // 加载用户信息
        $order->load('user');
        
        // 添加状态文本
        $order->status_text = $order->getStatusTextAttribute();
        $order->refund_status_text = $order->getRefundStatusTextAttribute();
        $order->payment_status_text = $order->getPaymentStatusTextAttribute();
        $order->shipping_status_text = $order->getShippingStatusTextAttribute();
        
        return response()->json([
            'code' => 0,
            'message' => 'success',
            'data' => $order
        ]);
    }

    /**
     * 发货操作
     */
    public function ship(Request $request, $id)
    {
        $order = Order::find($id);
        
        if (!$order) {
            return response()->json([
                'code' => 1,
                'message' => '订单不存在'
            ], 404);
        }
        
        if ($order->status != 1 || $order->paid_at == null) {
            return response()->json([
                'code' => 1,
                'message' => '订单状态不正确，无法发货'
            ], 422);
        }
        
        // 验证请求数据
        $validator = Validator::make($request->all(), [
            'ship_area_name' => 'required|string|max:100',
            'ship_area_id' => 'required|string|max:50'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'code' => 1,
                'message' => $validator->errors()->first()
            ], 422);
        }

        try {
            // 开启事务
            DB::beginTransaction();
            
            // 更新订单为已发货状态
            $order->status = 2; // 已发货
            $order->express_company = $request->ship_area_name;
            $order->express_no = $request->ship_area_id;
            $order->shipped_at = now();
            $order->save();
            
            // 更新订单项状态
            OrderItem::where('order_id', $order->id)
                ->where('status', 1)
                ->update([
                    'status' => 2,
                    'updated_at' => now()
                ]);
            
            // 记录物流信息
            $logistics = Logistics::where('number', $request->ship_area_id)->first();
            if (!$logistics) {
                $logistics = new Logistics();
                $logistics->number = $request->ship_area_id;
                $logistics->expName = $request->ship_area_name;
                $logistics->deliverystatus = 0; // 快递收件(揽件)
                $logistics->save();
            }
            
            // 提交事务
            DB::commit();
            
            return response()->json([
                'code' => 0,
                'message' => '订单发货成功',
                'data' => $order
            ]);
        } catch (\Exception $e) {
            // 回滚事务
            DB::rollBack();
            Log::error('订单发货失败: ' . $e->getMessage());
            
            return response()->json([
                'code' => 1,
                'message' => '订单发货失败: ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * 修改订单收货信息
     */
    public function updateAddress(Request $request, $id)
    {
        $order = Order::find($id);
        
        if (!$order) {
            return response()->json([
                'code' => 1,
                'message' => '订单不存在'
            ], 404);
        }
        
        // 已发货的订单不允许修改收货信息
        if ($order->shipped_at) {
            return response()->json([
                'code' => 1,
                'message' => '订单已发货，无法修改收货信息'
            ], 422);
        }

        // 验证请求数据
        $validator = Validator::make($request->all(), [
            'ship_name' => 'required|string|max:50',
            'ship_mobile' => 'required|string|max:15',
            'city' => 'required|string|max:100',
            'ship_address' => 'required|string|max:200'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'code' => 1,
                'message' => $validator->errors()->first()
            ], 422);
        }

        try {
            // 更新订单收货信息
            $order->receiver_name = $request->ship_name;
            $order->receiver_phone = $request->ship_mobile;
            $order->receiver_city = $request->city;
            $order->receiver_address = $request->ship_address;
            $order->updated_at = now();
            $order->save();
            
            return response()->json([
                'code' => 0,
                'message' => '收货信息更新成功',
                'data' => $order
            ]);
        } catch (\Exception $e) {
            Log::error('收货信息更新失败: ' . $e->getMessage());
            
            return response()->json([
                'code' => 1,
                'message' => '收货信息更新失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 取消订单
     */
    public function cancel($id)
    {
        $order = Order::find($id);
        
        if (!$order) {
            return response()->json([
                'code' => 1,
                'message' => '订单不存在'
            ], 404);
        }
        
        if ($order->status != 0) {
            return response()->json([
                'code' => 1,
                'message' => '只有待付款的订单可以取消'
            ], 422);
        }
        
        try {
            // 开启事务
            DB::beginTransaction();
            
            // 更新订单状态
            $order->status = 4; // 已取消
            $order->cancelled_at = now();
            $order->cancel_reason = '管理员取消';
            $order->save();
            
            // 更新订单项状态
            OrderItem::where('order_id', $order->id)
                ->where('status', 0)
                ->update([
                    'status' => 4,
                    'updated_at' => now()
                ]);
            
            // 提交事务
            DB::commit();
            
            return response()->json([
                'code' => 0,
                'message' => '订单取消成功',
                'data' => $order
            ]);
        } catch (\Exception $e) {
            // 回滚事务
            DB::rollBack();
            Log::error('订单取消失败: ' . $e->getMessage());
            
            return response()->json([
                'code' => 1,
                'message' => '订单取消失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 确认收货（完成订单）
     */
    public function confirm($id)
    {
        $order = Order::find($id);
        
        if (!$order) {
            return response()->json([
                'code' => 1,
                'message' => '订单不存在'
            ], 404);
        }
        
        if ($order->status != 2) {
            return response()->json([
                'code' => 1,
                'message' => '只有已发货的订单可以确认收货'
            ], 422);
        }
        
        try {
            // 开启事务
            DB::beginTransaction();
            
            // 更新订单状态
            $order->status = 3; // 已完成
            $order->completed_at = now();
            $order->save();
            
            // 更新订单项状态
            OrderItem::where('order_id', $order->id)
                ->where('status', 2)
                ->update([
                    'status' => 3,
                    'updated_at' => now()
                ]);
            
            // 提交事务
            DB::commit();
            
            return response()->json([
                'code' => 0,
                'message' => '订单完成成功',
                'data' => $order
            ]);
        } catch (\Exception $e) {
            // 回滚事务
            DB::rollBack();
            Log::error('订单完成失败: ' . $e->getMessage());
            
            return response()->json([
                'code' => 1,
                'message' => '订单完成失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 批量发货
     */
    public function batchShip(Request $request)
    {
        // 验证请求数据
        $validator = Validator::make($request->all(), [
            'orders' => 'required|array',
            'orders.*.id' => 'required|exists:orders,id',
            'orders.*.ship_area_name' => 'required|string|max:100',
            'orders.*.ship_area_id' => 'required|string|max:50'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'code' => 1,
                'message' => $validator->errors()->first()
            ], 422);
        }

        $successCount = 0;
        $failedOrders = [];
        
        foreach ($request->orders as $orderData) {
            $order = Order::find($orderData['id']);
            
            if (!$order || $order->status != 1 || $order->paid_at == null) {
                $failedOrders[] = [
                    'id' => $orderData['id'],
                    'reason' => '订单状态不正确，无法发货'
                ];
                continue;
            }
            
            try {
                // 更新订单为已发货状态
                $order->status = 2; // 已发货
                $order->express_company = $orderData['ship_area_name'];
                $order->express_no = $orderData['ship_area_id'];
                $order->shipped_at = now();
                $order->save();
                
                // 更新订单项状态
                OrderItem::where('order_id', $order->id)
                    ->where('status', 1)
                    ->update([
                        'status' => 2,
                        'updated_at' => now()
                    ]);
                
                // 记录物流信息
                $logistics = Logistics::where('number', $orderData['ship_area_id'])->first();
                if (!$logistics) {
                    $logistics = new Logistics();
                    $logistics->number = $orderData['ship_area_id'];
                    $logistics->expName = $orderData['ship_area_name'];
                    $logistics->deliverystatus = 0; // 快递收件(揽件)
                    $logistics->save();
                }
                
                $successCount++;
            } catch (\Exception $e) {
                Log::error('批量发货失败, 订单ID: ' . $orderData['id'] . ', 错误: ' . $e->getMessage());
                
                $failedOrders[] = [
                    'id' => $orderData['id'],
                    'reason' => $e->getMessage()
                ];
            }
        }
        
        return response()->json([
            'code' => 0,
            'message' => '批量发货处理完成',
            'data' => [
                'success_count' => $successCount,
                'failed_count' => count($failedOrders),
                'failed_orders' => $failedOrders
            ]
        ]);
    }

    /**
     * 导出订单
     */
    public function export(Request $request)
    {
        $query = Order::query();
        
        // 搜索条件
        if ($request->has('keyword') && $request->keyword) {
            $query->where(function($q) use ($request) {
                $q->where('order_no', 'like', '%' . $request->keyword . '%')
                  ->orWhere('receiver_name', 'like', '%' . $request->keyword . '%')
                  ->orWhere('receiver_phone', 'like', '%' . $request->keyword . '%');
            });
        }
        
        if ($request->has('status') && $request->status !== null) {
            $query->where('status', $request->status);
        }
        
        if ($request->has('start_date') && $request->start_date) {
            $query->where('created_at', '>=', $request->start_date . ' 00:00:00');
        }
        
        if ($request->has('end_date') && $request->end_date) {
            $query->where('created_at', '<=', $request->end_date . ' 23:59:59');
        }
        
        // 获取所有符合条件的订单
        $orders = $query->orderBy('created_at', 'desc')->get();
        
        // 创建Excel文件
        $fileName = '订单数据_' . date('YmdHis') . '.xlsx';
        
        $data = [];
        $data[] = [
            '订单号', '商品名称', '订单金额', '实付金额', '收货人', '手机号', '收货地址',
            '支付方式', '订单状态', '支付时间', '发货时间', '完成时间', '订单备注', '下单时间'
        ];
        
        foreach ($orders as $order) {
            $data[] = [
                $order->order_no,
                $order->product_name,
                $order->total_amount,
                $order->actual_amount,
                $order->receiver_name,
                $order->receiver_phone,
                $order->receiver_province . $order->receiver_city . $order->receiver_district . $order->receiver_address,
                $order->payment_method == 'wechat' ? '微信支付' : ($order->payment_method == 'alipay' ? '支付宝' : $order->payment_method),
                $order->getStatusTextAttribute(),
                $order->paid_at,
                $order->shipped_at,
                $order->completed_at,
                $order->remark,
                $order->created_at
            ];
        }
        
        // 使用Laravel Excel或其他库导出Excel文件
        // 这里以简单的CSV导出为例
        $headers = [
            'Content-Type' => 'text/csv; charset=utf-8',
            'Content-Disposition' => 'attachment; filename="' . $fileName . '"',
        ];
        
        $callback = function() use ($data) {
            $file = fopen('php://output', 'w');
            // 添加UTF-8 BOM，以便Excel正确显示中文
            fprintf($file, chr(0xEF).chr(0xBB).chr(0xBF));
            
            foreach ($data as $row) {
                fputcsv($file, $row);
            }
            fclose($file);
        };
        
        return response()->stream($callback, 200, $headers);
    }

    /**
     * 订单统计数据
     */
    public function statistics(Request $request)
    {
        // 日期范围
        $startDate = $request->input('start_date') ? Carbon::parse($request->input('start_date')) : Carbon::now()->subDays(7);
        $endDate = $request->input('end_date') ? Carbon::parse($request->input('end_date')) : Carbon::now();
        $period = $request->input('period', 'week');
        
        // 统计总数据
        $totalOrders = Order::whereBetween('created_at', [$startDate->startOfDay(), $endDate->endOfDay()])->count();
        $totalAmount = Order::whereBetween('created_at', [$startDate->startOfDay(), $endDate->endOfDay()])->sum('total_amount');
        $paidOrders = Order::whereBetween('created_at', [$startDate->startOfDay(), $endDate->endOfDay()])->whereNotNull('paid_at')->count();
        $totalUsers = Order::whereBetween('created_at', [$startDate->startOfDay(), $endDate->endOfDay()])->distinct('user_id')->count('user_id');
        
        // 订单状态分布
        $statusCounts = Order::whereBetween('created_at', [$startDate->startOfDay(), $endDate->endOfDay()])
            ->select('status', DB::raw('count(*) as count'))
            ->groupBy('status')
            ->get();
        
        $statusData = [];
        $statusMap = [
            0 => '待付款',
            1 => '待发货',
            2 => '已发货',
            3 => '已完成',
            4 => '已取消',
            5 => '已关闭'
        ];
        
        foreach ($statusCounts as $item) {
            $statusData[] = [
                'name' => $statusMap[$item->status] ?? '未知状态',
                'value' => $item->count
            ];
        }
        
        // 支付方式分布
        $paymentCounts = Order::whereBetween('created_at', [$startDate->startOfDay(), $endDate->endOfDay()])
            ->whereNotNull('paid_at')
            ->select('payment_method', DB::raw('count(*) as count'))
            ->groupBy('payment_method')
            ->get();
        
        $paymentData = [];
        $paymentMap = [
            'wechat' => '微信支付',
            'alipay' => '支付宝',
            'balance' => '余额支付'
        ];
        
        foreach ($paymentCounts as $item) {
            $paymentData[] = [
                'name' => $paymentMap[$item->payment_method] ?? $item->payment_method,
                'value' => $item->count
            ];
        }
        
        // 热销商品TOP5
        $topProducts = OrderItem::join('orders', 'order_items.order_id', '=', 'orders.id')
            ->whereBetween('orders.created_at', [$startDate->startOfDay(), $endDate->endOfDay()])
            ->whereNotNull('orders.paid_at')
            ->select('order_items.product_name', DB::raw('sum(order_items.product_num) as sales'))
            ->groupBy('order_items.product_name')
            ->orderBy('sales', 'desc')
            ->limit(5)
            ->get();
        
        $productData = [];
        foreach ($topProducts as $item) {
            $productData[] = [
                'name' => $item->product_name,
                'sales' => $item->sales
            ];
        }
        
        // 订单趋势
        $trendData = [];
        
        if ($period == 'week') {
            // 最近7天
            $currentDate = clone $startDate;
            while ($currentDate <= $endDate) {
                $day = $currentDate->format('Y-m-d');
                $dayStart = $currentDate->startOfDay();
                $dayEnd = $currentDate->endOfDay();
                
                $orders = Order::whereBetween('created_at', [$dayStart, $dayEnd])->count();
                $amount = Order::whereBetween('created_at', [$dayStart, $dayEnd])->sum('total_amount');
                
                $trendData[] = [
                    'date' => $day,
                    'orders' => $orders,
                    'amount' => $amount
                ];
                
                $currentDate->addDay();
            }
        } else {
            // 按月统计
            $currentDate = clone $startDate;
            $currentDate->startOfMonth();
            $endMonth = clone $endDate;
            $endMonth->endOfMonth();
            
            while ($currentDate <= $endMonth) {
                $month = $currentDate->format('Y-m');
                $monthStart = $currentDate->startOfMonth();
                $monthEnd = $currentDate->endOfMonth();
                
                $orders = Order::whereBetween('created_at', [$monthStart, $monthEnd])->count();
                $amount = Order::whereBetween('created_at', [$monthStart, $monthEnd])->sum('total_amount');
                
                $trendData[] = [
                    'date' => $month,
                    'orders' => $orders,
                    'amount' => $amount
                ];
                
                $currentDate->addMonth();
            }
        }
        
        // 按日期统计明细
        $dailyData = [];
        $currentDate = clone $startDate;
        
        while ($currentDate <= $endDate) {
            $day = $currentDate->format('Y-m-d');
            $dayStart = $currentDate->startOfDay();
            $dayEnd = $currentDate->endOfDay();
            
            $totalOrders = Order::whereBetween('created_at', [$dayStart, $dayEnd])->count();
            $totalAmount = Order::whereBetween('created_at', [$dayStart, $dayEnd])->sum('total_amount');
            $paidOrders = Order::whereBetween('created_at', [$dayStart, $dayEnd])->whereNotNull('paid_at')->count();
            $paidAmount = Order::whereBetween('created_at', [$dayStart, $dayEnd])->whereNotNull('paid_at')->sum('actual_amount');
            $unpaidOrders = Order::whereBetween('created_at', [$dayStart, $dayEnd])->whereNull('paid_at')->where('status', 0)->count();
            $canceledOrders = Order::whereBetween('created_at', [$dayStart, $dayEnd])->where('status', 4)->count();
            $completedOrders = Order::whereBetween('created_at', [$dayStart, $dayEnd])->where('status', 3)->count();
            
            $dailyData[] = [
                'date' => $day,
                'total_orders' => $totalOrders,
                'total_amount' => $totalAmount,
                'paid_orders' => $paidOrders,
                'paid_amount' => $paidAmount,
                'unpaid_orders' => $unpaidOrders,
                'canceled_orders' => $canceledOrders,
                'completed_orders' => $completedOrders
            ];
            
            $currentDate->addDay();
        }
        
        return response()->json([
            'code' => 0,
            'message' => 'success',
            'data' => [
                'stats' => [
                    'totalOrders' => $totalOrders,
                    'totalAmount' => round($totalAmount, 2),
                    'paidOrders' => $paidOrders,
                    'totalUsers' => $totalUsers
                ],
                'status' => $statusData,
                'payment' => $paymentData,
                'products' => $productData,
                'trend' => $trendData,
                'daily' => $dailyData
            ]
        ]);
    }
} 