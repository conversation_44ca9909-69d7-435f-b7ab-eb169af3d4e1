<?php

namespace App\Http\Controllers\Admin\Payment;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class PaymentMethodController extends Controller
{
    /**
     * 获取支付方式列表
     */
    public function index()
    {
        try {
            $paymentMethods = DB::table('system_configs')
                ->where('module', 'payment')
                ->orderBy('sort', 'asc')
                ->get();
                
            // 整理数据结构
            $result = [];
            foreach ($paymentMethods as $method) {
                $result[] = [
                    'id' => $method->id,
                    'key' => $method->key,
                    'title' => $method->title,
                    'value' => $method->value,
                    'description' => $method->description,
                    'is_enabled' => in_array($method->key, ['enable_wechat_pay', 'enable_alipay', 'enable_bank_transfer']) 
                        ? (bool)$method->value 
                        : null,
                    'sort' => $method->sort
                ];
            }
            
            return response()->json([
                'code' => 0,
                'message' => 'success',
                'data' => $result
            ]);
        } catch (\Exception $e) {
            Log::error('获取支付方式列表失败: ' . $e->getMessage());
            return response()->json([
                'code' => 1,
                'message' => '获取支付方式列表失败: ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * 更新支付方式配置
     */
    public function update(Request $request)
    {
        try {
            $validated = $request->validate([
                'settings' => 'required|array',
                'settings.*.key' => 'required|string',
                'settings.*.value' => 'present|string'
            ]);
            
            $settings = $validated['settings'];
            
            DB::beginTransaction();
            
            foreach ($settings as $setting) {
                DB::table('system_configs')
                    ->where('module', 'payment')
                    ->where('key', $setting['key'])
                    ->update(['value' => $setting['value']]);
            }
            
            DB::commit();
            
            return response()->json([
                'code' => 0,
                'message' => '支付方式配置更新成功'
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('更新支付方式配置失败: ' . $e->getMessage());
            return response()->json([
                'code' => 1,
                'message' => '更新支付方式配置失败: ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * 切换支付方式启用状态
     */
    public function toggleStatus(Request $request, $key)
    {
        try {
            // 验证key是否有效
            $paymentMethod = DB::table('system_configs')
                ->where('module', 'payment')
                ->where('key', $key)
                ->first();
                
            if (!$paymentMethod) {
                return response()->json([
                    'code' => 1,
                    'message' => '支付方式不存在'
                ], 404);
            }
            
            // 只允许切换启用类型的配置
            if (!in_array($key, ['enable_wechat_pay', 'enable_alipay', 'enable_bank_transfer'])) {
                return response()->json([
                    'code' => 1,
                    'message' => '无法切换该配置项'
                ], 422);
            }
            
            // 当前状态
            $currentStatus = (bool)$paymentMethod->value;
            
            // 更新状态
            DB::table('system_configs')
                ->where('module', 'payment')
                ->where('key', $key)
                ->update(['value' => $currentStatus ? '0' : '1']);
                
            return response()->json([
                'code' => 0,
                'message' => '支付方式状态已切换',
                'data' => [
                    'key' => $key,
                    'status' => !$currentStatus
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('切换支付方式状态失败: ' . $e->getMessage());
            return response()->json([
                'code' => 1,
                'message' => '切换支付方式状态失败: ' . $e->getMessage()
            ], 500);
        }
    }
} 