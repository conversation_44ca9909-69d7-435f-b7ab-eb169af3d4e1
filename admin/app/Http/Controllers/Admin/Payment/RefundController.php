<?php

namespace App\Http\Controllers\Admin\Payment;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use App\Models\OrderRefund;
use App\Models\Order;
use Carbon\Carbon;

class RefundController extends Controller
{
    /**
     * 获取退款记录列表
     */
    public function index(Request $request)
    {
        try {
            $query = OrderRefund::with(['order', 'user']);
            
            // 搜索条件
            if ($request->has('keyword') && $request->keyword) {
                $query->where(function($q) use ($request) {
                    $q->where('refund_no', 'like', '%' . $request->keyword . '%')
                      ->orWhereHas('order', function($subQuery) use ($request) {
                          $subQuery->where('order_no', 'like', '%' . $request->keyword . '%');
                      });
                });
            }
            
            if ($request->has('status') && $request->status !== null) {
                $query->where('verify_status', $request->status);
            }
            
            if ($request->has('start_date') && $request->start_date) {
                $query->where('created_at', '>=', $request->start_date . ' 00:00:00');
            }
            
            if ($request->has('end_date') && $request->end_date) {
                $query->where('created_at', '<=', $request->end_date . ' 23:59:59');
            }
            
            // 默认按照创建时间倒序排序
            $query->orderBy($request->input('sort', 'created_at'), $request->input('order', 'desc'));
            
            // 分页
            $perPage = $request->input('limit', 15);
            $refunds = $query->paginate($perPage);
            
            // 处理退款数据
            $data = $refunds->map(function ($refund) {
                $statusMap = [
                    0 => '待审核',
                    1 => '已通过',
                    2 => '已拒绝',
                    3 => '退款中',
                    4 => '已退款'
                ];
                
                $refund->status_text = $statusMap[$refund->verify_status] ?? '未知状态';
                $refund->order_no = $refund->order ? $refund->order->order_no : '';
                $refund->user_name = $refund->user ? ($refund->user->nickname ?? $refund->user->username) : '未知用户';
                
                return $refund;
            });
            
            return response()->json([
                'code' => 0,
                'message' => 'success',
                'data' => $data,
                'total' => $refunds->total()
            ]);
        } catch (\Exception $e) {
            Log::error('获取退款记录失败: ' . $e->getMessage());
            return response()->json([
                'code' => 1,
                'message' => '获取退款记录失败: ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * 获取退款详情
     */
    public function show($id)
    {
        try {
            $refund = OrderRefund::with(['order', 'user', 'orderItem'])->find($id);
            
            if (!$refund) {
                return response()->json([
                    'code' => 1,
                    'message' => '退款记录不存在'
                ], 404);
            }
            
            $statusMap = [
                0 => '待审核',
                1 => '已通过',
                2 => '已拒绝',
                3 => '退款中',
                4 => '已退款'
            ];
            
            $refund->status_text = $statusMap[$refund->verify_status] ?? '未知状态';
            
            return response()->json([
                'code' => 0,
                'message' => 'success',
                'data' => $refund
            ]);
        } catch (\Exception $e) {
            Log::error('获取退款详情失败: ' . $e->getMessage());
            return response()->json([
                'code' => 1,
                'message' => '获取退款详情失败: ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * 审核退款申请
     */
    public function audit(Request $request, $id)
    {
        try {
            $refund = OrderRefund::find($id);
            
            if (!$refund) {
                return response()->json([
                    'code' => 1,
                    'message' => '退款记录不存在'
                ], 404);
            }
            
            if ($refund->verify_status != 0) {
                return response()->json([
                    'code' => 1,
                    'message' => '只有待审核状态的退款申请可以审核'
                ], 422);
            }
            
            // 验证请求数据
            $validator = Validator::make($request->all(), [
                'action' => 'required|in:approve,reject',
                'remark' => 'nullable|string|max:255'
            ]);
            
            if ($validator->fails()) {
                return response()->json([
                    'code' => 1,
                    'message' => $validator->errors()->first()
                ], 422);
            }
            
            // 开启事务
            DB::beginTransaction();
            
            if ($request->action === 'approve') {
                // 审核通过
                $refund->verify_status = 3; // 退款中
                $refund->verify_time = now();
                $refund->verify_remark = $request->remark;
                $refund->save();
                
                // 更新订单状态
                if ($refund->order_id) {
                    Order::where('id', $refund->order_id)->update([
                        'refund_status' => 1, // 退款中
                        'updated_at' => now()
                    ]);
                }
            } else {
                // 审核拒绝
                $refund->verify_status = 2; // 已拒绝
                $refund->verify_time = now();
                $refund->verify_remark = $request->remark;
                $refund->save();
            }
            
            // 提交事务
            DB::commit();
            
            return response()->json([
                'code' => 0,
                'message' => $request->action === 'approve' ? '退款申请审核通过' : '退款申请已拒绝',
                'data' => $refund
            ]);
        } catch (\Exception $e) {
            // 回滚事务
            DB::rollBack();
            Log::error('审核退款申请失败: ' . $e->getMessage());
            return response()->json([
                'code' => 1,
                'message' => '审核退款申请失败: ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * 处理退款
     */
    public function refund(Request $request, $id)
    {
        try {
            $refund = OrderRefund::find($id);
            
            if (!$refund) {
                return response()->json([
                    'code' => 1,
                    'message' => '退款记录不存在'
                ], 404);
            }
            
            if ($refund->verify_status != 3) {
                return response()->json([
                    'code' => 1,
                    'message' => '只有退款中状态的申请可以处理退款'
                ], 422);
            }
            
            // 验证请求数据
            $validator = Validator::make($request->all(), [
                'real_refund_money' => 'required|numeric|min:0',
                'remark' => 'nullable|string|max:255',
                'transaction_id' => 'nullable|string|max:100'
            ]);
            
            if ($validator->fails()) {
                return response()->json([
                    'code' => 1,
                    'message' => $validator->errors()->first()
                ], 422);
            }
            
            // 开启事务
            DB::beginTransaction();
            
            // 更新退款记录
            $refund->real_refund_money = $request->real_refund_money;
            $refund->verify_status = 4; // 已退款
            $refund->refund_time = now();
            $refund->refund_remark = $request->remark;
            $refund->transaction_id = $request->transaction_id;
            $refund->save();
            
            // 更新订单状态
            if ($refund->order_id) {
                Order::where('id', $refund->order_id)->update([
                    'refund_status' => 2, // 已退款
                    'updated_at' => now()
                ]);
            }
            
            // 提交事务
            DB::commit();
            
            return response()->json([
                'code' => 0,
                'message' => '退款处理成功',
                'data' => $refund
            ]);
        } catch (\Exception $e) {
            // 回滚事务
            DB::rollBack();
            Log::error('处理退款失败: ' . $e->getMessage());
            return response()->json([
                'code' => 1,
                'message' => '处理退款失败: ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * 获取退款统计数据
     */
    public function statistics(Request $request)
    {
        try {
            // 默认获取最近30天的数据
            $startDate = $request->input('start_date') ? Carbon::parse($request->input('start_date')) : Carbon::now()->subDays(30);
            $endDate = $request->input('end_date') ? Carbon::parse($request->input('end_date')) : Carbon::now();
            
            // 总退款金额
            $totalAmount = OrderRefund::where('verify_status', 4) // 已退款
                ->whereBetween('refund_time', [$startDate, $endDate])
                ->sum('real_refund_money');
                
            // 总退款笔数
            $totalCount = OrderRefund::where('verify_status', 4) // 已退款
                ->whereBetween('refund_time', [$startDate, $endDate])
                ->count();
                
            // 退款状态分布
            $statusStats = OrderRefund::selectRaw('verify_status, COUNT(*) as count')
                ->whereBetween('created_at', [$startDate, $endDate])
                ->groupBy('verify_status')
                ->get()
                ->mapWithKeys(function ($item) {
                    $statusMap = [
                        0 => '待审核',
                        1 => '已通过',
                        2 => '已拒绝',
                        3 => '退款中',
                        4 => '已退款'
                    ];
                    
                    return [$statusMap[$item->verify_status] ?? '未知状态' => $item->count];
                });
                
            // 每日退款金额统计
            $dailyStats = OrderRefund::selectRaw('DATE(refund_time) as date, SUM(real_refund_money) as daily_amount, COUNT(*) as daily_count')
                ->where('verify_status', 4) // 已退款
                ->whereBetween('refund_time', [$startDate, $endDate])
                ->groupBy(DB::raw('DATE(refund_time)'))
                ->orderBy('date')
                ->get();
                
            return response()->json([
                'code' => 0,
                'message' => 'success',
                'data' => [
                    'overview' => [
                        'total_amount' => $totalAmount,
                        'total_count' => $totalCount
                    ],
                    'status_stats' => $statusStats,
                    'daily_stats' => $dailyStats
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('获取退款统计数据失败: ' . $e->getMessage());
            return response()->json([
                'code' => 1,
                'message' => '获取退款统计数据失败: ' . $e->getMessage()
            ], 500);
        }
    }
} 