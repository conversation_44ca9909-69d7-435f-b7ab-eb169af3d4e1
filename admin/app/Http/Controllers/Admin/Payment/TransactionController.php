<?php

namespace App\Http\Controllers\Admin\Payment;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Models\Order;
use Carbon\Carbon;

class TransactionController extends Controller
{
    /**
     * 获取交易记录列表
     */
    public function index(Request $request)
    {
        try {
            $query = Order::whereNotNull('paid_at')
                ->with('user');
            
            // 搜索条件
            if ($request->has('keyword') && $request->keyword) {
                $query->where(function($q) use ($request) {
                    $q->where('order_no', 'like', '%' . $request->keyword . '%')
                      ->orWhere('transaction_id', 'like', '%' . $request->keyword . '%');
                });
            }
            
            if ($request->has('payment_method') && $request->payment_method) {
                $query->where('payment_method', $request->payment_method);
            }
            
            if ($request->has('start_date') && $request->start_date) {
                $query->where('paid_at', '>=', $request->start_date . ' 00:00:00');
            }
            
            if ($request->has('end_date') && $request->end_date) {
                $query->where('paid_at', '<=', $request->end_date . ' 23:59:59');
            }
            
            // 默认按照支付时间倒序排序
            $query->orderBy($request->input('sort', 'paid_at'), $request->input('order', 'desc'));
            
            // 分页
            $perPage = $request->input('limit', 15);
            $transactions = $query->paginate($perPage);
            
            // 处理交易数据
            $data = $transactions->map(function ($transaction) {
                $transaction->user_name = $transaction->user ? $transaction->user->nickname ?? $transaction->user->username : '未知用户';
                return $transaction;
            });
            
            return response()->json([
                'code' => 0,
                'message' => 'success',
                'data' => $data,
                'total' => $transactions->total()
            ]);
        } catch (\Exception $e) {
            Log::error('获取交易记录失败: ' . $e->getMessage());
            return response()->json([
                'code' => 1,
                'message' => '获取交易记录失败: ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * 获取交易统计数据
     */
    public function statistics(Request $request)
    {
        try {
            // 默认获取最近30天的数据
            $startDate = $request->input('start_date') ? Carbon::parse($request->input('start_date')) : Carbon::now()->subDays(30);
            $endDate = $request->input('end_date') ? Carbon::parse($request->input('end_date')) : Carbon::now();
            
            // 总交易金额
            $totalAmount = Order::whereNotNull('paid_at')
                ->whereBetween('paid_at', [$startDate, $endDate])
                ->sum('actual_amount');
                
            // 总交易笔数
            $totalCount = Order::whereNotNull('paid_at')
                ->whereBetween('paid_at', [$startDate, $endDate])
                ->count();
                
            // 每日交易金额统计
            $dailyStats = Order::selectRaw('DATE(paid_at) as date, SUM(actual_amount) as daily_amount, COUNT(*) as daily_count')
                ->whereNotNull('paid_at')
                ->whereBetween('paid_at', [$startDate, $endDate])
                ->groupBy(DB::raw('DATE(paid_at)'))
                ->orderBy('date')
                ->get();
                
            // 支付方式分布
            $paymentMethods = Order::selectRaw('payment_method, COUNT(*) as count, SUM(actual_amount) as amount')
                ->whereNotNull('paid_at')
                ->whereBetween('paid_at', [$startDate, $endDate])
                ->groupBy('payment_method')
                ->get()
                ->map(function ($item) {
                    $methodMap = [
                        'wechat' => '微信支付',
                        'alipay' => '支付宝',
                        'balance' => '余额支付',
                        'bank' => '银行转账'
                    ];
                    
                    $item->payment_method_text = $methodMap[$item->payment_method] ?? $item->payment_method;
                    return $item;
                });
                
            return response()->json([
                'code' => 0,
                'message' => 'success',
                'data' => [
                    'overview' => [
                        'total_amount' => $totalAmount,
                        'total_count' => $totalCount
                    ],
                    'daily_stats' => $dailyStats,
                    'payment_methods' => $paymentMethods
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('获取交易统计数据失败: ' . $e->getMessage());
            return response()->json([
                'code' => 1,
                'message' => '获取交易统计数据失败: ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * 导出交易记录
     */
    public function export(Request $request)
    {
        try {
            // 查询条件
            $query = Order::whereNotNull('paid_at')
                ->with('user');
            
            if ($request->has('keyword') && $request->keyword) {
                $query->where(function($q) use ($request) {
                    $q->where('order_no', 'like', '%' . $request->keyword . '%')
                      ->orWhere('transaction_id', 'like', '%' . $request->keyword . '%');
                });
            }
            
            if ($request->has('payment_method') && $request->payment_method) {
                $query->where('payment_method', $request->payment_method);
            }
            
            if ($request->has('start_date') && $request->start_date) {
                $query->where('paid_at', '>=', $request->start_date . ' 00:00:00');
            }
            
            if ($request->has('end_date') && $request->end_date) {
                $query->where('paid_at', '<=', $request->end_date . ' 23:59:59');
            }
            
            $transactions = $query->orderBy('paid_at', 'desc')->get();
            
            // 导出数据
            $exportData = $transactions->map(function ($transaction) {
                $paymentMethodMap = [
                    'wechat' => '微信支付',
                    'alipay' => '支付宝',
                    'balance' => '余额支付'
                ];
                
                return [
                    '订单号' => $transaction->order_no,
                    '交易号' => $transaction->transaction_id ?? '无',
                    '金额' => $transaction->actual_amount,
                    '支付方式' => $paymentMethodMap[$transaction->payment_method] ?? $transaction->payment_method,
                    '用户' => $transaction->user ? ($transaction->user->nickname ?? $transaction->user->username) : '未知用户',
                    '支付时间' => $transaction->paid_at
                ];
            });
            
            // 这里只返回数据，实际导出功能需要在前端处理
            return response()->json([
                'code' => 0,
                'message' => 'success',
                'data' => $exportData
            ]);
        } catch (\Exception $e) {
            Log::error('导出交易记录失败: ' . $e->getMessage());
            return response()->json([
                'code' => 1,
                'message' => '导出交易记录失败: ' . $e->getMessage()
            ], 500);
        }
    }
} 