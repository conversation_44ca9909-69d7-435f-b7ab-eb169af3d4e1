<?php

namespace App\Http\Controllers\Admin\Installation;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Maatwebsite\Excel\Facades\Excel;
use App\Exports\BookingExport;

class BookingController extends Controller
{
    /**
     * 获取安装预约列表
     */
    public function index(Request $request)
    {
        try {
            $query = DB::table('install_bookings')
                ->leftJoin('app_users', 'install_bookings.user_id', '=', 'app_users.id')
                ->leftJoin('installation_engineers', 'install_bookings.engineer_id', '=', 'installation_engineers.id')
                ->select(
                    'install_bookings.*',
                    'app_users.name',
                    'app_users.wechat_nickname as user_display_name',
                    'app_users.wechat_avatar',
                    'app_users.avatar as user_avatar',
                    'installation_engineers.name as engineer_name',
                    'installation_engineers.name as engineer_display_name',
                    'installation_engineers.name as engineer_nickname',
                    'installation_engineers.name as engineer_username'
                );

            // 关键词搜索
            if ($request->has('keyword') && $request->keyword) {
                $keyword = $request->keyword;
                $query->where(function ($q) use ($keyword) {
                    $q->where('install_bookings.contact_name', 'like', "%{$keyword}%")
                      ->orWhere('install_bookings.contact_phone', 'like', "%{$keyword}%")
                      ->orWhere('install_bookings.install_address', 'like', "%{$keyword}%")
                      ->orWhere('install_bookings.booking_no', 'like', "%{$keyword}%");
                });
            }

            // 状态筛选
            if ($request->has('status') && $request->status) {
                $query->where('install_bookings.status', $request->status);
            }

            // 支付状态筛选
            if ($request->has('payment_status') && $request->payment_status) {
                $query->where('install_bookings.payment_status', $request->payment_status);
            }

            // 日期范围筛选
            if ($request->has('start_date') && $request->has('end_date')) {
                $query->whereBetween('install_bookings.install_time', [$request->start_date, $request->end_date]);
            }

            // 获取分页数据
            $perPage = $request->per_page ?? 15;
            $bookings = $query->orderBy('install_bookings.created_at', 'desc')
                             ->paginate($perPage);

            // 处理状态显示文本
            foreach ($bookings as $booking) {
                $booking->status_text = $this->getStatusText($booking->status);
                $booking->package_type_text = $this->getPackageTypeText($booking->package_type);
            }

            return response()->json([
                'code' => 0,
                'message' => '获取安装预约列表成功',
                'data' => $bookings
            ]);
        } catch (\Exception $e) {
            Log::error('获取安装预约列表失败: ' . $e->getMessage());
            return response()->json([
                'code' => 1,
                'message' => '获取安装预约列表失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 获取安装预约详情
     */
    public function show($id)
    {
        try {
            $booking = DB::table('install_bookings')
                ->leftJoin('app_users', 'install_bookings.user_id', '=', 'app_users.id')
                ->leftJoin('installation_engineers', 'install_bookings.engineer_id', '=', 'installation_engineers.id')
                ->leftJoin('app_users as referrer', 'install_bookings.referrer_id', '=', 'referrer.id')
                ->select(
                    'install_bookings.*',
                    'app_users.name',
                    'app_users.wechat_nickname as user_display_name',
                    'app_users.wechat_avatar',
                    'app_users.avatar as user_avatar',
                    'installation_engineers.name as engineer_name',
                    'referrer.wechat_nickname as referrer_display_name'
                )
                ->where('install_bookings.id', $id)
                ->first();

            if (!$booking) {
                return response()->json([
                    'code' => 1,
                    'message' => '预约信息不存在'
                ]);
            }

            $booking->status_text = $this->getStatusText($booking->status);
            $booking->package_type_text = $this->getPackageTypeText($booking->package_type);

            return response()->json([
                'code' => 0,
                'message' => '获取预约详情成功',
                'data' => $booking
            ]);
        } catch (\Exception $e) {
            Log::error('获取预约详情失败: ' . $e->getMessage());
            return response()->json([
                'code' => 1,
                'message' => '获取预约详情失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 更新安装预约状态
     */
    public function update(Request $request, $id)
    {
        try {
            // 验证请求数据
            $validator = Validator::make($request->all(), [
                'status' => 'required|string'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'code' => 1,
                    'message' => '数据验证失败',
                    'errors' => $validator->errors()
                ]);
            }

            // 检查预约是否存在
            $booking = DB::table('install_bookings')->where('id', $id)->first();
            if (!$booking) {
                return response()->json([
                    'code' => 1,
                    'message' => '预约信息不存在'
                ]);
            }

            // 准备更新数据
            $updateData = [
                'status' => $request->status,
                'updated_at' => now()
            ];

            // 根据不同状态处理不同逻辑
            switch ($request->status) {
                case 'assigned':
                    if (!$request->has('engineer_id') || !$request->engineer_id) {
                        return response()->json([
                            'code' => 1,
                            'message' => '请选择工程师'
                        ]);
                    }
                    $updateData['engineer_id'] = $request->engineer_id;
                    break;

                case 'completed':
                    if ($request->has('completed_at') && $request->completed_at) {
                        $updateData['completed_at'] = $request->completed_at;
                    } else {
                        $updateData['completed_at'] = now();
                    }

                    if ($request->has('device_id')) {
                        $updateData['device_id'] = $request->device_id;
                    }

                    // 注释掉工程师完成安装数量更新，因为字段不存在
                    // if ($booking->engineer_id) {
                    //     DB::table('installation_engineers')
                    //         ->where('id', $booking->engineer_id)
                    //         ->increment('completed_installations');
                    // }
                    break;

                case 'cancelled':
                    if (!$request->has('cancel_reason') || !$request->cancel_reason) {
                        return response()->json([
                            'code' => 1,
                            'message' => '请输入取消原因'
                        ]);
                    }
                    $updateData['cancel_reason'] = $request->cancel_reason;
                    break;
            }

            // 添加备注
            if ($request->has('remark')) {
                $updateData['remark'] = $request->remark;
            }

            // 更新数据
            DB::table('install_bookings')
                ->where('id', $id)
                ->update($updateData);

            // 记录操作日志
            DB::table('admin_log')->insert([
                'admin_id' => auth()->id() ?? 0,
                'action' => 'update_booking_status',
                'target_id' => $id,
                'details' => json_encode([
                    'old_status' => $booking->status,
                    'new_status' => $request->status,
                    'data' => $updateData
                ]),
                'created_at' => now()
            ]);

            return response()->json([
                'code' => 0,
                'message' => '更新状态成功',
                'data' => null
            ]);
        } catch (\Exception $e) {
            Log::error('更新预约状态失败: ' . $e->getMessage());
            return response()->json([
                'code' => 1,
                'message' => '更新预约状态失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 派工程师
     */
    public function assignEngineer(Request $request, $id)
    {
        try {
            // 验证请求数据
            $validator = Validator::make($request->all(), [
                'engineer_id' => 'required|integer|exists:installation_engineers,id'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'code' => 1,
                    'message' => '数据验证失败',
                    'errors' => $validator->errors()
                ]);
            }

            // 检查预约是否存在
            $booking = DB::table('install_bookings')->where('id', $id)->first();
            if (!$booking) {
                return response()->json([
                    'code' => 1,
                    'message' => '预约信息不存在'
                ]);
            }

            // 检查预约状态是否允许派工
            if (!in_array($booking->status, ['pending', 'confirmed'])) {
                return response()->json([
                    'code' => 1,
                    'message' => '当前状态不允许派工'
                ]);
            }

            // 获取工程师信息
            $engineer = DB::table('installation_engineers')->where('id', $request->engineer_id)->first();
            if (!$engineer) {
                return response()->json([
                    'code' => 1,
                    'message' => '工程师不存在'
                ]);
            }

            // 更新预约数据
            $updateData = [
                'engineer_id' => $request->engineer_id,
                'status' => 'assigned',
                'updated_at' => now()
            ];

            // 添加备注
            if ($request->has('remark')) {
                $updateData['remark'] = $request->remark;
            }

            // 更新数据
            DB::table('install_bookings')
                ->where('id', $id)
                ->update($updateData);

            // 记录操作日志
            DB::table('admin_log')->insert([
                'admin_id' => auth()->id() ?? 0,
                'action' => 'assign_engineer',
                'target_id' => $id,
                'details' => json_encode([
                    'old_status' => $booking->status,
                    'new_status' => 'assigned',
                    'engineer_id' => $request->engineer_id,
                    'engineer_name' => $engineer->name
                ]),
                'created_at' => now()
            ]);

            return response()->json([
                'code' => 0,
                'message' => '派工成功',
                'data' => null
            ]);
        } catch (\Exception $e) {
            Log::error('派工失败: ' . $e->getMessage());
            return response()->json([
                'code' => 1,
                'message' => '派工失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 导出安装预约数据
     */
    public function export(Request $request)
    {
        try {
            $filename = '安装预约数据_' . date('YmdHis') . '.xlsx';
            return Excel::download(new BookingExport($request), $filename);
        } catch (\Exception $e) {
            Log::error('导出预约数据失败: ' . $e->getMessage());
            return response()->json([
                'code' => 1,
                'message' => '导出预约数据失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 获取状态文本
     */
    private function getStatusText($status)
    {
        $statusMap = [
            'pending' => '待处理',
            'confirmed' => '已确认',
            'assigned' => '已分配',
            'in_progress' => '进行中',
            'completed' => '已完成',
            'cancelled' => '已取消'
        ];

        return $statusMap[$status] ?? $status;
    }

    /**
     * 获取套餐类型文本
     */
    private function getPackageTypeText($type)
    {
        $typeMap = [
            'basic' => '基础套餐',
            'premium' => '高级套餐',
            'custom' => '定制套餐',
            'personal' => '个人/企业套餐',
            'unlimited' => '无限畅饮套餐',
            'business_year' => '商务机包年套餐',
            'business_flow' => '商务机流量套餐'
        ];

        return $typeMap[$type] ?? $type;
    }

    /**
     * 获取可用工程师列表
     */
    public function getAvailableEngineers()
    {
        try {
            $engineers = DB::table('installation_engineers')
                ->where('status', 1) // 1表示在职
                ->select('id', 'name', 'phone', 'region as area', 'status', 'address')
                ->orderBy('name', 'asc')
                ->get();

            // 为兼容性添加level字段和completed_installations字段
            foreach ($engineers as $engineer) {
                $engineer->level = 'senior'; // 默认设为高级
                $engineer->completed_installations = 0; // 默认为0，后续可以通过统计获取
            }

            return response()->json([
                'code' => 0,
                'message' => '获取可用工程师列表成功',
                'data' => $engineers
            ]);
        } catch (\Exception $e) {
            Log::error('获取可用工程师列表失败: ' . $e->getMessage());
            return response()->json([
                'code' => 1,
                'message' => '获取可用工程师列表失败: ' . $e->getMessage()
            ]);
        }
    }
}