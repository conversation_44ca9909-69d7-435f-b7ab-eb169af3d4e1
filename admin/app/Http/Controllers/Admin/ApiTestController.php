<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class ApiTestController extends Controller
{
    /**
     * 返回Hello World，用于简单测试API连通性
     */
    public function hello()
    {
        return response()->json([
            'code' => 0,
            'message' => 'API连接正常',
            'data' => [
                'time' => now()->toDateTimeString(),
                'hello' => 'world'
            ]
        ]);
    }
    
    /**
     * Echo请求数据，测试请求是否正确传递
     */
    public function echo(Request $request)
    {
        return response()->json([
            'code' => 0,
            'message' => '请求数据回显',
            'data' => [
                'method' => $request->method(),
                'path' => $request->path(),
                'url' => $request->url(),
                'fullUrl' => $request->fullUrl(),
                'params' => $request->all(),
                'headers' => $request->header(),
                'auth' => auth()->check() ? auth()->user() : null,
                'time' => now()->toDateTimeString()
            ]
        ]);
    }
} 