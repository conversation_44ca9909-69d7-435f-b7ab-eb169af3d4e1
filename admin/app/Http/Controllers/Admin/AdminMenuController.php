<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

/**
 * 管理员菜单控制器
 */
class AdminMenuController extends Controller
{
    /**
     * 获取菜单列表
     */
    public function getMenus(Request $request): JsonResponse
    {
        // 返回基本的菜单结构
        $menus = [
            [
                'id' => 1,
                'name' => '首页',
                'path' => '/dashboard',
                'icon' => 'dashboard',
                'children' => []
            ],
            [
                'id' => 2,
                'name' => '用户管理',
                'path' => '/users',
                'icon' => 'user',
                'children' => []
            ],
            [
                'id' => 3,
                'name' => '设备管理',
                'path' => '/devices',
                'icon' => 'device',
                'children' => [
                    [
                        'id' => 31,
                        'name' => '取水点管理',
                        'path' => '/devices/water-points',
                        'icon' => 'water-drop'
                    ]
                ]
            ]
        ];

        return response()->json([
            'code' => 0,
            'message' => 'success',
            'data' => $menus
        ]);
    }

    /**
     * 其他必要的方法
     */
    public function index(Request $request): JsonResponse
    {
        return $this->getMenus($request);
    }

    public function store(Request $request): JsonResponse
    {
        return response()->json(['code' => 0, 'message' => 'success']);
    }

    public function show($id): JsonResponse
    {
        return response()->json(['code' => 0, 'message' => 'success']);
    }

    public function update(Request $request, $id): JsonResponse
    {
        return response()->json(['code' => 0, 'message' => 'success']);
    }

    public function destroy($id): JsonResponse
    {
        return response()->json(['code' => 0, 'message' => 'success']);
    }

    public function testRoutes(): JsonResponse
    {
        return response()->json(['code' => 0, 'message' => 'Routes working']);
    }
} 