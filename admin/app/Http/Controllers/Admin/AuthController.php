<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use App\Models\Admin;
use Illuminate\Support\Facades\Log;

class AuthController extends Controller
{
    /**
     * 显示登录页面
     */
    public function showLoginForm()
    {
        return view('admin.auth.login');
    }

    /**
     * 处理登录请求
     */
    public function login(Request $request)
    {
        $request->validate([
            'username' => 'required|string',
            'password' => 'required|string',
        ]);

        $credentials = [
            'username' => $request->username,
            'password' => $request->password,
        ];

        if (Auth::guard('admin')->attempt($credentials, $request->has('remember'))) {
            // 记录登录日志
            Log::info('管理员登录成功', [
                'username' => $request->username,
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent(),
            ]);
            
            $request->session()->regenerate();
            return redirect()->intended(route('admin.dashboard'));
        }

        // 记录登录失败
        Log::warning('管理员登录失败', [
            'username' => $request->username,
            'ip' => $request->ip(),
        ]);

        return back()->withErrors([
            'username' => '用户名或密码错误',
        ])->withInput($request->only('username'));
    }

    /**
     * 退出登录
     */
    public function logout(Request $request)
    {
        Auth::guard('admin')->logout();
        $request->session()->invalidate();
        $request->session()->regenerateToken();
        
        return redirect()->route('admin.login');
    }

    /**
     * 显示管理员控制面板
     */
    public function dashboard()
    {
        return view('admin.dashboard');
    }
}
