<?php

namespace App\Http\Controllers\Admin\Api;

use App\Http\Controllers\Controller;
use App\Models\AppUser;
use App\Models\Salesman;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rule;

class SalesmanController extends Controller
{
    /**
     * 获取业务员列表
     */
    public function index(Request $request)
    {
        try {
            // 记录API请求
            \Log::info('获取业务员列表请求', [
                'params' => $request->all(),
                'ip' => $request->ip(),
                'user_id' => auth()->id() ?? 'unauthenticated'
            ]);

            $query = Salesman::with('user')->orderBy('created_at', 'desc');
            
            // 搜索条件
            if ($request->filled('search')) {
                $search = $request->input('search');
                $query->whereHas('user', function ($q) use ($search) {
                    $q->where('name', 'like', "%{$search}%")
                      ->orWhere('phone', 'like', "%{$search}%");
                })->orWhere('employee_id', 'like', "%{$search}%");
            }
            
            // 状态筛选
            if ($request->filled('status') && $request->input('status') !== 'all') {
                $query->where('status', $request->input('status'));
            }
            
            $perPage = $request->input('per_page', 15);
            $salesmen = $query->paginate($perPage);
            
            \Log::info('业务员列表获取成功', [
                'count' => $salesmen->count(),
                'total' => $salesmen->total()
            ]);
            
            return response()->json($salesmen);
        } catch (\Exception $e) {
            \Log::error('获取业务员列表失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'message' => '获取业务员列表失败：' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * 获取可用的用户列表（未分配为业务员的用户）
     */
    public function getAvailableUsers()
    {
        $users = AppUser::where('is_salesman', 0)->get();
        return response()->json($users);
    }
    
    /**
     * 获取可选的上级经理列表
     */
    public function getManagers()
    {
        $managers = Salesman::with('user')
            ->where('status', 'active')
            ->get();
        return response()->json($managers);
    }
    
    /**
     * 存储新创建的业务员
     */
    public function store(Request $request)
    {
        $request->validate([
            'user_id' => 'required|exists:app_users,id|unique:salesmen,user_id',
            'employee_id' => 'nullable|string|max:50|unique:salesmen,employee_id',
            'title' => 'required|string|max:50',
            'department' => 'nullable|string|max:100',
            'region' => 'nullable|string|max:100',
            'manager_id' => 'nullable|exists:salesmen,id',
            'status' => 'required|in:active,leave,suspend',
            'join_date' => 'nullable|date',
            'remark' => 'nullable|string|max:500'
        ]);
        
        DB::beginTransaction();
        
        try {
            // 创建业务员记录
            $salesman = new Salesman();
            $salesman->user_id = $request->input('user_id');
            $salesman->employee_id = $request->input('employee_id') ?: 'S'.str_pad($request->input('user_id'), 6, '0', STR_PAD_LEFT);
            $salesman->title = $request->input('title');
            $salesman->department = $request->input('department');
            $salesman->region = $request->input('region');
            $salesman->manager_id = $request->input('manager_id');
            $salesman->status = $request->input('status');
            $salesman->join_date = $request->input('join_date');
            $salesman->remark = $request->input('remark');
            $salesman->save();
            
            // 更新用户标记为业务员
            $user = AppUser::find($request->input('user_id'));
            $user->is_salesman = 1;
            $user->save();
            
            // 如果选择了上级经理，更新manager字段
            if ($request->filled('manager_id')) {
                $manager = Salesman::with('user')->find($request->input('manager_id'));
                if ($manager && $manager->user) {
                    $salesman->manager = $manager->user->name;
                    $salesman->save();
                }
            }
            
            DB::commit();
            
            return response()->json([
                'message' => '业务员创建成功',
                'salesman' => $salesman
            ], 201);
                
        } catch (\Exception $e) {
            DB::rollBack();
            
            return response()->json([
                'message' => '业务员创建失败：'.$e->getMessage()
            ], 500);
        }
    }
    
    /**
     * 显示指定业务员详情
     */
    public function show($id)
    {
        $salesman = Salesman::with(['user', 'managerUser.user'])->findOrFail($id);
        
        // 获取销售统计数据
        $stats = [
            'total_quantity' => $salesman->sales()->sum('quantity'),
            'total_amount' => $salesman->sales()->sum('amount'),
            'total_commission' => $salesman->commissions()->where('status', 'paid')->sum('amount'),
            'customer_count' => $salesman->customers()->count(),
            'monthly_stats' => $this->getMonthlyStats($salesman->id)
        ];
        
        return response()->json([
            'salesman' => $salesman,
            'stats' => $stats
        ]);
    }
    
    /**
     * 获取月度销售统计
     */
    private function getMonthlyStats($salesmanId)
    {
        try {
            // 获取最近6个月的销售数据
            $sixMonthsAgo = now()->subMonths(6)->startOfMonth();
            
            $monthlySales = DB::table('salesman_sales')
                ->select(
                    DB::raw('DATE_FORMAT(sale_date, "%Y-%m") as month'),
                    DB::raw('SUM(quantity) as quantity'),
                    DB::raw('SUM(amount) as amount'),
                    DB::raw('SUM(commission_amount) as commission')
                )
                ->where('salesman_id', $salesmanId)
                ->where('sale_date', '>=', $sixMonthsAgo)
                ->groupBy(DB::raw('DATE_FORMAT(sale_date, "%Y-%m")'))
                ->orderBy('month', 'asc')
                ->get();
                
            \Log::info('业务员月度销售统计获取成功', [
                'salesman_id' => $salesmanId,
                'count' => $monthlySales->count()
            ]);
                
            return $monthlySales;
        } catch (\Exception $e) {
            \Log::error('获取业务员月度销售统计失败', [
                'salesman_id' => $salesmanId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            // 发生错误时返回空数组而不是抛出异常
            return [];
        }
    }
    
    /**
     * 显示编辑业务员表单
     */
    public function edit($id)
    {
        $salesman = Salesman::findOrFail($id);
        return response()->json($salesman);
    }
    
    /**
     * 更新业务员信息
     */
    public function update(Request $request, $id)
    {
        $salesman = Salesman::findOrFail($id);
        
        $request->validate([
            'employee_id' => [
                'nullable',
                'string',
                'max:50',
                Rule::unique('salesmen')->ignore($salesman->id),
            ],
            'title' => 'required|string|max:50',
            'department' => 'nullable|string|max:100',
            'region' => 'nullable|string|max:100',
            'manager_id' => [
                'nullable',
                'exists:salesmen,id',
                function ($attribute, $value, $fail) use ($salesman) {
                    if ($value == $salesman->id) {
                        $fail('上级经理不能选择自己');
                    }
                },
            ],
            'status' => 'required|in:active,leave,suspend',
            'join_date' => 'nullable|date',
            'remark' => 'nullable|string|max:500',
        ]);
        
        try {
            $salesman->employee_id = $request->input('employee_id');
            $salesman->title = $request->input('title');
            $salesman->department = $request->input('department', '');
            $salesman->region = $request->input('region', '');
            $salesman->manager_id = $request->input('manager_id');
            $salesman->status = $request->input('status');
            $salesman->join_date = $request->input('join_date');
            $salesman->remark = $request->input('remark', '');
            
            // 如果选择了上级经理，更新manager字段
            if ($request->filled('manager_id')) {
                $manager = Salesman::with('user')->find($request->input('manager_id'));
                if ($manager && $manager->user) {
                    $salesman->manager = $manager->user->name;
                    $salesman->save();
                }
            } else {
                $salesman->manager = null;
            }
            
            $salesman->save();
            
            return response()->json([
                'message' => '业务员信息更新成功',
                'salesman' => $salesman
            ]);
                
        } catch (\Exception $e) {
            return response()->json([
                'message' => '业务员更新失败：'.$e->getMessage()
            ], 500);
        }
    }
    
    /**
     * 删除业务员
     */
    public function destroy($id)
    {
        $salesman = Salesman::findOrFail($id);
        
        try {
            DB::beginTransaction();
            
            // 获取关联的用户
            $user = $salesman->user;
            
            // 删除业务员记录
            $salesman->delete();
            
            // 更新用户不再是业务员
            if ($user) {
                $user->is_salesman = 0;
                $user->save();
            }
            
            DB::commit();
            
            return response()->json([
                'message' => '业务员删除成功'
            ]);
                
        } catch (\Exception $e) {
            DB::rollBack();
            
            return response()->json([
                'message' => '业务员删除失败：'.$e->getMessage()
            ], 500);
        }
    }
    
    /**
     * 获取业务员调试统计数据
     */
    public function getDebugStats()
    {
        try {
            $salesmenCount = Salesman::count();
            $appUsersSalesmanCount = AppUser::where('is_salesman', 1)->count();
            
            // 查看最新创建的业务员
            $latestSalesmen = Salesman::with('user')
                ->orderBy('created_at', 'desc')
                ->limit(5)
                ->get()
                ->map(function ($item) {
                    return [
                        'id' => $item->id,
                        'user_id' => $item->user_id,
                        'employee_id' => $item->employee_id,
                        'title' => $item->title,
                        'user_name' => $item->user ? $item->user->name : null,
                        'user_phone' => $item->user ? $item->user->phone : null,
                        'created_at' => $item->created_at->format('Y-m-d H:i:s')
                    ];
                });
            
            // 获取有问题的业务员数据
            $issueRecords = [];
            
            // 查找用户ID为null的业务员
            $noUserRecords = Salesman::whereNotExists(function ($query) {
                $query->select(DB::raw(1))
                    ->from('app_users')
                    ->whereRaw('app_users.id = salesmen.user_id');
            })->count();
            
            if ($noUserRecords > 0) {
                $issueRecords[] = [
                    'issue' => 'no_user',
                    'count' => $noUserRecords,
                    'description' => '用户ID不存在的业务员记录'
                ];
            }
            
            // 查找没有被标记为业务员的用户
            $invalidUserMarkCount = Salesman::whereExists(function ($query) {
                $query->select(DB::raw(1))
                    ->from('app_users')
                    ->whereRaw('app_users.id = salesmen.user_id')
                    ->where('app_users.is_salesman', '!=', 1);
            })->count();
            
            if ($invalidUserMarkCount > 0) {
                $issueRecords[] = [
                    'issue' => 'invalid_user_mark',
                    'count' => $invalidUserMarkCount,
                    'description' => '用户未被标记为业务员的记录'
                ];
            }
            
            return response()->json([
                'code' => 0,
                'message' => '获取调试统计数据成功',
                'data' => [
                    'salesmen' => $salesmenCount,
                    'appUsersSalesman' => $appUsersSalesmanCount,
                    'latestSalesmen' => $latestSalesmen,
                    'issueRecords' => $issueRecords
                ]
            ]);
        } catch (\Exception $e) {
            \Log::error('获取业务员调试统计失败: ' . $e->getMessage());
            
            return response()->json([
                'code' => 1,
                'message' => '获取调试统计数据失败: ' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }
} 