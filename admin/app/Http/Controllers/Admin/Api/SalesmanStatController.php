<?php

namespace App\Http\Controllers\Admin\Api;

use App\Http\Controllers\Controller;
use App\Models\Salesman;
use App\Models\SalesmanSale;
use App\Models\SalesmanCommission;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class SalesmanStatController extends Controller
{
    /**
     * 显示业务员统计概览
     */
    public function index(Request $request)
    {
        // 获取时间范围
        $period = $request->input('period', 'month');
        $rankBy = $request->input('rank_by', 'amount');
        $startDate = null;
        $endDate = null;

        switch ($period) {
            case 'today':
                $startDate = now()->startOfDay();
                $endDate = now()->endOfDay();
                break;
            case 'yesterday':
                $startDate = now()->subDay()->startOfDay();
                $endDate = now()->subDay()->endOfDay();
                break;
            case 'week':
                $startDate = now()->startOfWeek();
                $endDate = now()->endOfWeek();
                break;
            case 'month':
                $startDate = now()->startOfMonth();
                $endDate = now()->endOfMonth();
                break;
            case 'quarter':
                $startDate = now()->startOfQuarter();
                $endDate = now()->endOfQuarter();
                break;
            case 'year':
                $startDate = now()->startOfYear();
                $endDate = now()->endOfYear();
                break;
            default:
                // 不设置时间范围
                break;
        }

        // 获取活跃的业务员数量
        $activeSalesmenCount = Salesman::where('status', 'active')->count();

        // 获取销售总量和总金额
        $salesQuery = SalesmanSale::query();
        if ($startDate && $endDate) {
            $salesQuery->whereBetween('sale_date', [$startDate, $endDate]);
        }
        $totalSales = $salesQuery->selectRaw('SUM(quantity) as total_quantity, SUM(amount) as total_amount, SUM(commission_amount) as total_commission')
            ->first();

        // 获取月度销售趋势数据
        $monthlyStats = $this->getMonthlyStats($startDate, $endDate);

        // 获取业务员销售排名
        $quantityRanking = $this->getSalesRanking('quantity', $startDate, $endDate, 10);
        $amountRanking = $this->getSalesRanking('amount', $startDate, $endDate, 10);
        $commissionRanking = $this->getSalesRanking('commission', $startDate, $endDate, 10);

        // 处理月度销售趋势数据格式
        $labels = [];
        $quantities = [];
        $amounts = [];

        foreach ($monthlyStats as $stat) {
            $labels[] = $stat->month;
            $quantities[] = (int)$stat->quantity;
            $amounts[] = (float)$stat->amount;
        }

        $formattedMonthlyStats = [
            'labels' => $labels,
            'quantities' => $quantities,
            'amounts' => $amounts
        ];

        // 根据排序字段获取排名
        $topSalesmen = [];
        switch ($rankBy) {
            case 'quantity':
                $topSalesmen = $quantityRanking;
                break;
            case 'amount':
                $topSalesmen = $amountRanking;
                break;
            case 'commission':
                $topSalesmen = $commissionRanking;
                break;
            default:
                $topSalesmen = $amountRanking;
                break;
        }

        return response()->json([
            'activeSalesmen' => $activeSalesmenCount,
            'totalSales' => $totalSales->total_quantity ?? 0,
            'totalAmount' => $totalSales->total_amount ?? 0,
            'totalCommission' => $totalSales->total_commission ?? 0,
            'monthlySales' => $formattedMonthlyStats,
            'topSalesmen' => $topSalesmen,
            'rankings' => [
                'quantity' => $quantityRanking,
                'amount' => $amountRanking,
                'commission' => $commissionRanking
            ],
            'salesmenData' => array_slice(json_decode(json_encode($topSalesmen), true), 0, 20)
        ]);
    }

    /**
     * 获取月度销售统计
     */
    private function getMonthlyStats($startDate = null, $endDate = null)
    {
        // 默认获取最近6个月的销售数据
        $query = DB::table('salesman_sales')
            ->select(
                DB::raw('DATE_FORMAT(sale_date, "%Y-%m") as month'),
                DB::raw('SUM(quantity) as quantity'),
                DB::raw('SUM(amount) as amount'),
                DB::raw('SUM(commission_amount) as commission')
            )
            ->groupBy(DB::raw('DATE_FORMAT(sale_date, "%Y-%m")'))
            ->orderBy('month', 'asc');

        if ($startDate) {
            $query->where('sale_date', '>=', $startDate);
        } else {
            $query->where('sale_date', '>=', now()->subMonths(6)->startOfMonth());
        }

        if ($endDate) {
            $query->where('sale_date', '<=', $endDate);
        }

        return $query->get();
    }

    /**
     * 获取业务员销售排名
     */
    private function getSalesRanking($type, $startDate = null, $endDate = null, $limit = 10)
    {
        $query = Salesman::with('user')
            ->where('salesmen.status', 'active')
            ->join('salesman_sales', 'salesmen.id', '=', 'salesman_sales.salesman_id');

        if ($startDate) {
            $query->where('salesman_sales.sale_date', '>=', $startDate);
        }

        if ($endDate) {
            $query->where('salesman_sales.sale_date', '<=', $endDate);
        }

        $query->select(
                'salesmen.id',
                'salesmen.title',
                'salesmen.department',
                'salesmen.region',
                DB::raw('SUM(salesman_sales.quantity) as total_quantity'),
                DB::raw('SUM(salesman_sales.amount) as total_amount'),
                DB::raw('SUM(salesman_sales.commission_amount) as total_commission'),
                DB::raw('COUNT(DISTINCT salesman_sales.customer_phone) as customer_count')
            )
            ->groupBy('salesmen.id', 'salesmen.title', 'salesmen.department', 'salesmen.region');

        switch ($type) {
            case 'quantity':
                $query->orderBy('total_quantity', 'desc');
                break;
            case 'amount':
                $query->orderBy('total_amount', 'desc');
                break;
            case 'commission':
                $query->orderBy('total_commission', 'desc');
                break;
            default:
                $query->orderBy('total_amount', 'desc');
                break;
        }

        return $query->limit($limit)->get();
    }

    /**
     * 获取特定业务员的销售排名
     */
    public function getQuantityRanking(Request $request)
    {
        $limit = $request->input('limit', 10);
        $startDate = $request->input('start_date');
        $endDate = $request->input('end_date');

        $ranking = $this->getSalesRanking('quantity', $startDate, $endDate, $limit);
        return response()->json($ranking);
    }

    /**
     * 获取特定业务员的销售金额排名
     */
    public function getAmountRanking(Request $request)
    {
        $limit = $request->input('limit', 10);
        $startDate = $request->input('start_date');
        $endDate = $request->input('end_date');

        $ranking = $this->getSalesRanking('amount', $startDate, $endDate, $limit);
        return response()->json($ranking);
    }

    /**
     * 获取特定业务员的佣金排名
     */
    public function getCommissionRanking(Request $request)
    {
        $limit = $request->input('limit', 10);
        $startDate = $request->input('start_date');
        $endDate = $request->input('end_date');

        $ranking = $this->getSalesRanking('commission', $startDate, $endDate, $limit);
        return response()->json($ranking);
    }

    /**
     * 获取业务员排名
     */
    public function getRankings(Request $request, $type)
    {
        // 获取时间范围
        $period = $request->input('period', 'all');
        $startDate = null;
        $endDate = null;

        switch ($period) {
            case 'today':
                $startDate = now()->startOfDay();
                $endDate = now()->endOfDay();
                break;
            case 'yesterday':
                $startDate = now()->subDay()->startOfDay();
                $endDate = now()->subDay()->endOfDay();
                break;
            case 'week':
                $startDate = now()->startOfWeek();
                $endDate = now()->endOfWeek();
                break;
            case 'month':
                $startDate = now()->startOfMonth();
                $endDate = now()->endOfMonth();
                break;
            case 'quarter':
                $startDate = now()->startOfQuarter();
                $endDate = now()->endOfQuarter();
                break;
            case 'year':
                $startDate = now()->startOfYear();
                $endDate = now()->endOfYear();
                break;
            default:
                // 不设置时间范围
                break;
        }

        $limit = $request->input('limit', 10);
        $ranking = $this->getSalesRanking($type, $startDate, $endDate, $limit);
        return response()->json($ranking);
    }

    /**
     * 显示指定业务员的详细统计
     */
    public function show($id)
    {
        $salesman = Salesman::with('user')->findOrFail($id);

        // 获取销售统计
        $salesStats = SalesmanSale::where('salesman_id', $id)
            ->selectRaw('SUM(quantity) as total_quantity, SUM(amount) as total_amount, SUM(commission_amount) as total_commission')
            ->first();

        // 获取佣金统计
        $commissionStats = SalesmanCommission::where('salesman_id', $id)
            ->selectRaw('
                SUM(CASE WHEN status = "pending" THEN amount ELSE 0 END) as pending_commission,
                SUM(CASE WHEN status = "paid" THEN amount ELSE 0 END) as paid_commission
            ')
            ->first();

        // 获取月度销售趋势
        $monthlyStats = DB::table('salesman_sales')
            ->where('salesman_id', $id)
            ->where('sale_date', '>=', now()->subMonths(6)->startOfMonth())
            ->select(
                DB::raw('DATE_FORMAT(sale_date, "%Y-%m") as month'),
                DB::raw('SUM(quantity) as quantity'),
                DB::raw('SUM(amount) as amount'),
                DB::raw('SUM(commission_amount) as commission')
            )
            ->groupBy(DB::raw('DATE_FORMAT(sale_date, "%Y-%m")'))
            ->orderBy('month', 'asc')
            ->get();

        // 获取客户数量
        $customerCount = DB::table('salesman_customers')
            ->where('salesman_id', $id)
            ->count();

        // 获取转化率（成交客户数/总客户数）
        $conversionRate = 0;
        if ($customerCount > 0) {
            $dealCustomerCount = DB::table('salesman_customers')
                ->where('salesman_id', $id)
                ->where('deal_count', '>', 0)
                ->count();

            $conversionRate = round(($dealCustomerCount / $customerCount) * 100, 2);
        }

        return response()->json([
            'salesman' => $salesman,
            'stats' => [
                'total_quantity' => $salesStats->total_quantity ?? 0,
                'total_amount' => $salesStats->total_amount ?? 0,
                'total_commission' => $salesStats->total_commission ?? 0,
                'pending_commission' => $commissionStats->pending_commission ?? 0,
                'paid_commission' => $commissionStats->paid_commission ?? 0,
                'customer_count' => $customerCount,
                'conversion_rate' => $conversionRate,
                'monthly_stats' => $monthlyStats
            ]
        ]);
    }
}