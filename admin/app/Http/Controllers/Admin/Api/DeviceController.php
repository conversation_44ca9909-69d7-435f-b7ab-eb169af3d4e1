<?php

namespace App\Http\Controllers\Admin\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class DeviceController extends Controller
{
    /**
     * 获取设备列表
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        try {
            // 获取请求参数
            $page = $request->input('page', 1);
            $limit = $request->input('limit', 10);
            $keyword = $request->input('keyword', '');
            $status = $request->input('status', '');
            $deviceType = $request->input('device_type', '');
            
            // 计算偏移量
            $offset = ($page - 1) * $limit;
            
            // 构建查询条件
            $query = DB::table('water_devices');
            
            // 添加关键字搜索条件
            if (!empty($keyword)) {
                $query->where(function($q) use ($keyword) {
                    $q->where('device_name', 'like', "%{$keyword}%")
                      ->orWhere('device_sn', 'like', "%{$keyword}%")
                      ->orWhere('merchant_name', 'like', "%{$keyword}%");
                });
            }
            
            // 添加状态过滤条件
            if (!empty($status)) {
                $query->where('status', $status);
            }
            
            // 添加设备类型过滤条件
            if (!empty($deviceType)) {
                $query->where('device_type', $deviceType);
            }
            
            // 获取总数
            $total = $query->count();
            
            // 如果没有结果，直接返回空列表
            if ($total === 0) {
                return response()->json([
                    'code' => 0,
                    'message' => '获取成功',
                    'data' => [
                        'list' => [],
                        'total' => 0,
                        'page' => $page,
                        'limit' => $limit
                    ]
                ]);
            }
            
            // 查询分页数据
            $devices = $query->select([
                'id', 
                'device_sn', 
                'device_name', 
                'device_type',
                'device_model',
                'merchant_id',
                'merchant_name',
                'install_time',
                'install_address',
                'status',
                'remark',
                'firmware_version', 
                'hardware_version',
                'software_version',
                'created_at as create_date', 
                'updated_at as update_date'
            ])
            ->orderBy('id', 'desc')
            ->offset($offset)
            ->limit($limit)
            ->get();
            
            // 格式化结果
            $formattedDevices = $devices->map(function($device) {
                // 格式化状态文本
                $statusText = $device->status === 'E' ? '在线' : '离线';
                
                // 格式化设备类型文本
                $deviceTypeText = '';
                switch ($device->device_type) {
                    case 'purifier':
                        $deviceTypeText = '净水器';
                        break;
                    case 'dispenser':
                        $deviceTypeText = '饮水机';
                        break;
                    default:
                        $deviceTypeText = $device->device_type;
                }
                
                return [
                    'id' => $device->id,
                    'device_sn' => $device->device_sn,
                    'device_name' => $device->device_name,
                    'device_type' => $device->device_type,
                    'device_type_text' => $deviceTypeText,
                    'device_model' => $device->device_model,
                    'merchant_id' => $device->merchant_id,
                    'merchant_name' => $device->merchant_name,
                    'install_time' => $device->install_time,
                    'install_address' => $device->install_address,
                    'status' => $device->status,
                    'status_text' => $statusText,
                    'remark' => $device->remark,
                    'firmware_version' => $device->firmware_version,
                    'hardware_version' => $device->hardware_version,
                    'software_version' => $device->software_version,
                    'create_date' => $device->create_date,
                    'update_date' => $device->update_date
                ];
            });
            
            // 返回结果
            return response()->json([
                'code' => 0,
                'message' => '获取成功',
                'data' => [
                    'list' => $formattedDevices,
                    'total' => $total,
                    'page' => intval($page),
                    'limit' => intval($limit)
                ]
            ]);
            
        } catch (\Exception $e) {
            Log::error("获取设备列表失败", ['message' => $e->getMessage(), 'trace' => $e->getTraceAsString()]);
            
            return response()->json([
                'code' => 500,
                'message' => "服务器内部错误: " . $e->getMessage(),
                'data' => null
            ]);
        }
    }
    
    /**
     * 获取设备客户列表
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function clients(Request $request)
    {
        try {
            // 获取请求参数
            $page = $request->input('page', 1);
            $limit = $request->input('limit', 10);
            $keyword = $request->input('keyword', '');
            $status = $request->input('status', '');
            
            // 计算偏移量
            $offset = ($page - 1) * $limit;
            
            // 构建查询条件
            $query = DB::table('water_clients');
            
            // 添加关键字搜索条件
            if (!empty($keyword)) {
                $query->where(function($q) use ($keyword) {
                    $q->where('name', 'like', "%{$keyword}%")
                      ->orWhere('phone', 'like', "%{$keyword}%");
                });
            }
            
            // 添加状态过滤条件
            if (!empty($status)) {
                $query->where('status', $status);
            }
            
            // 获取总数
            $total = $query->count();
            
            // 如果没有结果，直接返回空列表
            if ($total === 0) {
                return response()->json([
                    'code' => 0,
                    'message' => '获取成功',
                    'data' => [
                        'list' => [],
                        'total' => 0,
                        'page' => $page,
                        'limit' => $limit
                    ]
                ]);
            }
            
            // 查询分页数据
            $clients = $query->select([
                'id', 
                'name', 
                'phone', 
                'province', 
                'city', 
                'area', 
                'address', 
                'status', 
                'remark', 
                'wx_nickname', 
                'wx_head_img', 
                'client_device_id', 
                'client_product_name as client_device_name',
                'created_at as create_date', 
                'updated_at as update_date'
            ])
            ->orderBy('id', 'desc')
            ->offset($offset)
            ->limit($limit)
            ->get();
            
            // 格式化结果
            $formattedClients = $clients->map(function($client) {
                // 处理地址
                $address = '';
                if (!empty($client->province)) $address .= $client->province;
                if (!empty($client->city)) $address .= $client->city;
                if (!empty($client->area)) $address .= $client->area;
                if (!empty($client->address)) $address .= $client->address;
                
                // 格式化状态文本
                $statusText = $client->status === 'E' ? '启用' : '禁用';
                
                return [
                    'id' => $client->id,
                    'name' => $client->name,
                    'phone' => $client->phone,
                    'address' => $address,
                    'status' => $client->status,
                    'status_text' => $statusText,
                    'remark' => $client->remark,
                    'wx_nickname' => $client->wx_nickname,
                    'wx_head_img' => $client->wx_head_img,
                    'client_device_id' => $client->client_device_id,
                    'client_device_name' => $client->client_device_name,
                    'create_date' => $client->create_date,
                    'update_date' => $client->update_date
                ];
            });
            
            // 返回结果
            return response()->json([
                'code' => 0,
                'message' => '获取成功',
                'data' => [
                    'list' => $formattedClients,
                    'total' => $total,
                    'page' => intval($page),
                    'limit' => intval($limit)
                ]
            ]);
            
        } catch (\Exception $e) {
            Log::error("获取设备客户列表失败", ['message' => $e->getMessage(), 'trace' => $e->getTraceAsString()]);
            
            return response()->json([
                'code' => 500,
                'message' => "服务器内部错误: " . $e->getMessage(),
                'data' => null
            ]);
        }
    }
} 