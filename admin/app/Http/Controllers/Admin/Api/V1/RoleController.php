<?php

namespace App\Http\Controllers\Admin\Api\V1;

use App\Http\Controllers\Controller;
use App\Traits\ApiResponseTrait;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use App\Models\Role;
use App\Models\Permission;

class RoleController extends Controller
{
    use ApiResponseTrait;

    /**
     * 获取角色列表
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        $query = Role::query();
        
        // 搜索条件
        if ($request->has('keyword') && !empty($request->keyword)) {
            $keyword = $request->keyword;
            $query->where('name', 'like', "%{$keyword}%");
        }
        
        // 排序
        $orderBy = $request->input('order_by', 'id');
        $orderDir = $request->input('order_dir', 'asc');
        $query->orderBy($orderBy, $orderDir);
        
        // 分页
        $perPage = $request->input('per_page', 15);
        $roles = $query->paginate($perPage);
        
        // 加载权限关系
        $roles->load('permissions');
        
        return $this->paginate($roles);
    }

    /**
     * 创建角色
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|unique:admin_roles,name',
            'display_name' => 'required|string',
            'description' => 'nullable|string',
            'permissions' => 'nullable|array',
        ]);

        if ($validator->fails()) {
            return $this->error('验证失败', 422, $validator->errors());
        }

        $role = Role::create([
            'name' => $request->name,
            'display_name' => $request->display_name,
            'description' => $request->description,
            'is_system' => false
        ]);
        
        // 分配权限
        if ($request->has('permissions') && is_array($request->permissions)) {
            $permissions = Permission::whereIn('id', $request->permissions)->get();
            $role->permissions()->sync($permissions->pluck('id'));
        }
        
        return $this->success($role, '角色创建成功');
    }

    /**
     * 获取单个角色详情
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        $role = Role::with('permissions')->find($id);
        
        if (!$role) {
            return $this->error('角色不存在', 404);
        }
        
        return $this->success($role);
    }

    /**
     * 更新角色
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id)
    {
        $role = Role::find($id);
        
        if (!$role) {
            return $this->error('角色不存在', 404);
        }
        
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|unique:admin_roles,name,'.$id,
            'display_name' => 'required|string',
            'description' => 'nullable|string',
            'permissions' => 'nullable|array',
        ]);

        if ($validator->fails()) {
            return $this->error('验证失败', 422, $validator->errors());
        }
        
        $role->name = $request->name;
        $role->display_name = $request->display_name;
        $role->description = $request->description;
        $role->save();
        
        // 更新权限
        if ($request->has('permissions')) {
            $permissions = Permission::whereIn('id', $request->permissions)->get();
            $role->permissions()->sync($permissions->pluck('id'));
        }
        
        return $this->success($role, '角色更新成功');
    }

    /**
     * 删除角色
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy($id)
    {
        $role = Role::find($id);
        
        if (!$role) {
            return $this->error('角色不存在', 404);
        }
        
        // 系统角色不允许删除
        if ($role->is_system) {
            return $this->error('系统角色不允许删除', 400);
        }
        
        // 检查是否有用户使用此角色
        $userCount = $role->admins()->count();
        if ($userCount > 0) {
            return $this->error('该角色下有用户，无法删除', 400);
        }
        
        // 删除角色权限关联
        $role->permissions()->detach();
        
        // 删除角色
        $role->delete();
        
        return $this->success(null, '角色删除成功');
    }

    /**
     * 更新角色权限
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function updatePermissions(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'permissions' => 'required|array',
            'permissions.*' => 'exists:admin_permissions,id'
        ]);

        if ($validator->fails()) {
            return $this->error('验证失败', 422, $validator->errors());
        }

        $role = Role::find($id);
        
        if (!$role) {
            return $this->error('角色不存在', 404);
        }

        // 同步权限
        $role->permissions()->sync($request->input('permissions'));

        return $this->success($role->load('permissions'), '角色权限更新成功');
    }
}
