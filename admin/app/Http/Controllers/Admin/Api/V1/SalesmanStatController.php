<?php

namespace App\Http\Controllers\Admin\Api\V1;

use App\Http\Controllers\Controller;
use App\Traits\ApiResponseTrait;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Models\Salesman;
use App\Models\Order;
use App\Models\User;
use App\Models\Device;
use Carbon\Carbon;

class SalesmanStatController extends Controller
{
    use ApiResponseTrait;

    /**
     * 获取业务员统计概览
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function overview(Request $request)
    {
        // 时间范围
        $startDate = $request->input('start_date', Carbon::now()->startOfMonth()->toDateString());
        $endDate = $request->input('end_date', Carbon::now()->toDateString());
        
        // 业务员ID
        $salesmanId = $request->input('salesman_id');
        
        // 查询条件
        $whereCondition = [];
        if ($salesmanId) {
            $whereCondition[] = ['salesman_id', '=', $salesmanId];
        }
        
        // 总业务员数量
        $totalSalesmen = Salesman::where('status', 1)->count();
        
        // 总客户数量
        $totalCustomers = User::where('is_admin', 0)
            ->where('is_salesman', 0)
            ->whereNotNull('salesman_id')
            ->when($salesmanId, function($query) use ($salesmanId) {
                return $query->where('salesman_id', $salesmanId);
            })
            ->count();
        
        // 总订单数量
        $totalOrders = Order::whereNotNull('salesman_id')
            ->when($salesmanId, function($query) use ($salesmanId) {
                return $query->where('salesman_id', $salesmanId);
            })
            ->count();
        
        // 总销售额
        $totalSales = Order::whereNotNull('salesman_id')
            ->where('pay_status', 1)
            ->when($salesmanId, function($query) use ($salesmanId) {
                return $query->where('salesman_id', $salesmanId);
            })
            ->sum('total_amount');
        
        // 总佣金
        $totalCommission = Order::whereNotNull('salesman_id')
            ->where('pay_status', 1)
            ->when($salesmanId, function($query) use ($salesmanId) {
                return $query->where('salesman_id', $salesmanId);
            })
            ->sum('commission_amount');
        
        // 本期新增客户
        $newCustomers = User::where('is_admin', 0)
            ->where('is_salesman', 0)
            ->whereNotNull('salesman_id')
            ->when($salesmanId, function($query) use ($salesmanId) {
                return $query->where('salesman_id', $salesmanId);
            })
            ->whereBetween('created_at', [$startDate, $endDate . ' 23:59:59'])
            ->count();
        
        // 本期订单数量
        $periodOrders = Order::whereNotNull('salesman_id')
            ->when($salesmanId, function($query) use ($salesmanId) {
                return $query->where('salesman_id', $salesmanId);
            })
            ->whereBetween('created_at', [$startDate, $endDate . ' 23:59:59'])
            ->count();
        
        // 本期销售额
        $periodSales = Order::whereNotNull('salesman_id')
            ->where('pay_status', 1)
            ->when($salesmanId, function($query) use ($salesmanId) {
                return $query->where('salesman_id', $salesmanId);
            })
            ->whereBetween('created_at', [$startDate, $endDate . ' 23:59:59'])
            ->sum('total_amount');
        
        // 本期佣金
        $periodCommission = Order::whereNotNull('salesman_id')
            ->where('pay_status', 1)
            ->when($salesmanId, function($query) use ($salesmanId) {
                return $query->where('salesman_id', $salesmanId);
            })
            ->whereBetween('created_at', [$startDate, $endDate . ' 23:59:59'])
            ->sum('commission_amount');
        
        // 返回数据
        $data = [
            'total' => [
                'salesmen' => $totalSalesmen,
                'customers' => $totalCustomers,
                'orders' => $totalOrders,
                'sales' => $totalSales,
                'commission' => $totalCommission,
            ],
            'period' => [
                'start_date' => $startDate,
                'end_date' => $endDate,
                'new_customers' => $newCustomers,
                'orders' => $periodOrders,
                'sales' => $periodSales,
                'commission' => $periodCommission,
            ]
        ];
        
        return $this->success($data);
    }

    /**
     * 获取业务员销售排行榜
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function salesRanking(Request $request)
    {
        // 时间范围
        $startDate = $request->input('start_date', Carbon::now()->startOfMonth()->toDateString());
        $endDate = $request->input('end_date', Carbon::now()->toDateString());
        
        // 排序方式
        $orderBy = $request->input('order_by', 'sales');
        
        // 查询业务员销售数据
        $query = DB::table('orders')
            ->join('salesmen', 'orders.salesman_id', '=', 'salesmen.id')
            ->where('orders.pay_status', 1)
            ->whereBetween('orders.created_at', [$startDate, $endDate . ' 23:59:59'])
            ->groupBy('orders.salesman_id', 'salesmen.id', 'salesmen.name', 'salesmen.phone')
            ->select(
                'salesmen.id',
                'salesmen.name',
                'salesmen.phone',
                DB::raw('COUNT(orders.id) as order_count'),
                DB::raw('SUM(orders.total_amount) as sales_amount'),
                DB::raw('SUM(orders.commission_amount) as commission_amount')
            );
        
        // 排序
        if ($orderBy === 'sales') {
            $query->orderBy('sales_amount', 'desc');
        } elseif ($orderBy === 'commission') {
            $query->orderBy('commission_amount', 'desc');
        } elseif ($orderBy === 'orders') {
            $query->orderBy('order_count', 'desc');
        }
        
        // 获取结果
        $rankings = $query->get();
        
        // 添加排名
        $rank = 1;
        foreach ($rankings as $item) {
            $item->rank = $rank++;
        }
        
        return $this->success($rankings);
    }

    /**
     * 获取业务员客户统计
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function customerStats(Request $request)
    {
        // 时间范围
        $startDate = $request->input('start_date', Carbon::now()->startOfMonth()->toDateString());
        $endDate = $request->input('end_date', Carbon::now()->toDateString());
        
        // 查询业务员客户数据
        $query = DB::table('users')
            ->join('salesmen', 'users.salesman_id', '=', 'salesmen.id')
            ->where('users.is_admin', 0)
            ->where('users.is_salesman', 0)
            ->groupBy('users.salesman_id', 'salesmen.id', 'salesmen.name', 'salesmen.phone')
            ->select(
                'salesmen.id',
                'salesmen.name',
                'salesmen.phone',
                DB::raw('COUNT(users.id) as total_customers'),
                DB::raw('SUM(CASE WHEN users.created_at BETWEEN "' . $startDate . '" AND "' . $endDate . ' 23:59:59" THEN 1 ELSE 0 END) as new_customers')
            );
        
        // 排序
        $query->orderBy('total_customers', 'desc');
        
        // 获取结果
        $stats = $query->get();
        
        return $this->success($stats);
    }

    /**
     * 获取业务员销售趋势
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function salesTrend(Request $request)
    {
        // 时间范围
        $startDate = $request->input('start_date', Carbon::now()->subDays(30)->toDateString());
        $endDate = $request->input('end_date', Carbon::now()->toDateString());
        
        // 业务员ID
        $salesmanId = $request->input('salesman_id');
        
        // 时间粒度
        $granularity = $request->input('granularity', 'day');
        
        // 根据时间粒度设置分组和格式
        if ($granularity === 'month') {
            $groupFormat = '%Y-%m';
            $displayFormat = 'Y-m';
        } elseif ($granularity === 'week') {
            $groupFormat = '%Y-%u';
            $displayFormat = 'Y-W';
        } else { // day
            $groupFormat = '%Y-%m-%d';
            $displayFormat = 'Y-m-d';
        }
        
        // 查询销售趋势
        $query = DB::table('orders')
            ->where('pay_status', 1)
            ->whereBetween('created_at', [$startDate, $endDate . ' 23:59:59'])
            ->when($salesmanId, function($query) use ($salesmanId) {
                return $query->where('salesman_id', $salesmanId);
            })
            ->groupBy('date')
            ->select(
                DB::raw('DATE_FORMAT(created_at, "' . $groupFormat . '") as date'),
                DB::raw('COUNT(id) as order_count'),
                DB::raw('SUM(total_amount) as sales_amount'),
                DB::raw('SUM(commission_amount) as commission_amount')
            )
            ->orderBy('date');
        
        // 获取结果
        $trends = $query->get();
        
        // 生成完整的日期范围
        $dateRange = [];
        $currentDate = Carbon::parse($startDate);
        $lastDate = Carbon::parse($endDate);
        
        while ($currentDate->lte($lastDate)) {
            $dateKey = $currentDate->format($displayFormat);
            
            if ($granularity === 'week') {
                $dateKey = $currentDate->format('Y') . '-W' . $currentDate->format('W');
            }
            
            $dateRange[$dateKey] = [
                'date' => $dateKey,
                'order_count' => 0,
                'sales_amount' => 0,
                'commission_amount' => 0
            ];
            
            if ($granularity === 'month') {
                $currentDate->addMonth();
            } elseif ($granularity === 'week') {
                $currentDate->addWeek();
            } else {
                $currentDate->addDay();
            }
        }
        
        // 填充实际数据
        foreach ($trends as $trend) {
            $dateKey = $trend->date;
            
            if ($granularity === 'week') {
                list($year, $week) = explode('-', $trend->date);
                $dateKey = $year . '-W' . $week;
            }
            
            if (isset($dateRange[$dateKey])) {
                $dateRange[$dateKey]['order_count'] = $trend->order_count;
                $dateRange[$dateKey]['sales_amount'] = $trend->sales_amount;
                $dateRange[$dateKey]['commission_amount'] = $trend->commission_amount;
            }
        }
        
        return $this->success(array_values($dateRange));
    }

    /**
     * 获取业务员产品销售分析
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function productAnalysis(Request $request)
    {
        // 时间范围
        $startDate = $request->input('start_date', Carbon::now()->startOfMonth()->toDateString());
        $endDate = $request->input('end_date', Carbon::now()->toDateString());
        
        // 业务员ID
        $salesmanId = $request->input('salesman_id');
        
        // 查询产品销售数据
        $query = DB::table('order_items')
            ->join('orders', 'order_items.order_id', '=', 'orders.id')
            ->join('products', 'order_items.product_id', '=', 'products.id')
            ->where('orders.pay_status', 1)
            ->whereBetween('orders.created_at', [$startDate, $endDate . ' 23:59:59'])
            ->when($salesmanId, function($query) use ($salesmanId) {
                return $query->where('orders.salesman_id', $salesmanId);
            })
            ->groupBy('order_items.product_id', 'products.id', 'products.name', 'products.image')
            ->select(
                'products.id',
                'products.name',
                'products.image',
                DB::raw('SUM(order_items.quantity) as quantity'),
                DB::raw('SUM(order_items.quantity * order_items.price) as sales_amount')
            )
            ->orderBy('quantity', 'desc');
        
        // 获取结果
        $products = $query->get();
        
        // 计算总数量和总金额
        $totalQuantity = $products->sum('quantity');
        $totalAmount = $products->sum('sales_amount');
        
        // 计算占比
        foreach ($products as $product) {
            $product->quantity_percentage = $totalQuantity > 0 ? round(($product->quantity / $totalQuantity) * 100, 2) : 0;
            $product->amount_percentage = $totalAmount > 0 ? round(($product->sales_amount / $totalAmount) * 100, 2) : 0;
        }
        
        return $this->success([
            'products' => $products,
            'total' => [
                'quantity' => $totalQuantity,
                'amount' => $totalAmount
            ]
        ]);
    }

    /**
     * 获取业务员区域销售分析
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function regionAnalysis(Request $request)
    {
        // 时间范围
        $startDate = $request->input('start_date', Carbon::now()->startOfMonth()->toDateString());
        $endDate = $request->input('end_date', Carbon::now()->toDateString());
        
        // 业务员ID
        $salesmanId = $request->input('salesman_id');
        
        // 查询区域销售数据
        $query = DB::table('orders')
            ->join('users', 'orders.user_id', '=', 'users.id')
            ->where('orders.pay_status', 1)
            ->whereBetween('orders.created_at', [$startDate, $endDate . ' 23:59:59'])
            ->when($salesmanId, function($query) use ($salesmanId) {
                return $query->where('orders.salesman_id', $salesmanId);
            })
            ->groupBy('users.province', 'users.city')
            ->select(
                'users.province',
                'users.city',
                DB::raw('COUNT(orders.id) as order_count'),
                DB::raw('SUM(orders.total_amount) as sales_amount')
            )
            ->orderBy('sales_amount', 'desc');
        
        // 获取结果
        $regions = $query->get();
        
        // 计算总订单数和总金额
        $totalOrders = $regions->sum('order_count');
        $totalAmount = $regions->sum('sales_amount');
        
        // 计算占比
        foreach ($regions as $region) {
            $region->order_percentage = $totalOrders > 0 ? round(($region->order_count / $totalOrders) * 100, 2) : 0;
            $region->amount_percentage = $totalAmount > 0 ? round(($region->sales_amount / $totalAmount) * 100, 2) : 0;
            
            // 合并省市
            $region->region_name = $region->province . ($region->city ? ' - ' . $region->city : '');
        }
        
        return $this->success([
            'regions' => $regions,
            'total' => [
                'orders' => $totalOrders,
                'amount' => $totalAmount
            ]
        ]);
    }

    /**
     * 获取业务员佣金统计
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function commissionStats(Request $request)
    {
        // 时间范围
        $startDate = $request->input('start_date', Carbon::now()->startOfMonth()->toDateString());
        $endDate = $request->input('end_date', Carbon::now()->toDateString());
        
        // 查询业务员佣金数据
        $query = DB::table('orders')
            ->join('salesmen', 'orders.salesman_id', '=', 'salesmen.id')
            ->where('orders.pay_status', 1)
            ->whereBetween('orders.created_at', [$startDate, $endDate . ' 23:59:59'])
            ->groupBy('orders.salesman_id', 'salesmen.id', 'salesmen.name', 'salesmen.phone', 'salesmen.commission_rate')
            ->select(
                'salesmen.id',
                'salesmen.name',
                'salesmen.phone',
                'salesmen.commission_rate',
                DB::raw('COUNT(orders.id) as order_count'),
                DB::raw('SUM(orders.total_amount) as sales_amount'),
                DB::raw('SUM(orders.commission_amount) as commission_amount')
            )
            ->orderBy('commission_amount', 'desc');
        
        // 分页
        $perPage = $request->input('per_page', 15);
        $commissions = $query->paginate($perPage);
        
        return $this->paginate($commissions);
    }

    /**
     * 获取业务员团队业绩
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function teamPerformance(Request $request)
    {
        // 时间范围
        $startDate = $request->input('start_date', Carbon::now()->startOfMonth()->toDateString());
        $endDate = $request->input('end_date', Carbon::now()->toDateString());
        
        // 查询业务员团队数据
        $salesmen = Salesman::where('parent_id', null)
            ->where('status', 1)
            ->with(['children' => function($query) {
                $query->where('status', 1);
            }])
            ->get();
        
        $result = [];
        
        foreach ($salesmen as $salesman) {
            // 获取团队成员ID
            $teamIds = [$salesman->id];
            foreach ($salesman->children as $child) {
                $teamIds[] = $child->id;
            }
            
            // 查询团队销售数据
            $teamSales = DB::table('orders')
                ->where('pay_status', 1)
                ->whereBetween('created_at', [$startDate, $endDate . ' 23:59:59'])
                ->whereIn('salesman_id', $teamIds)
                ->select(
                    DB::raw('COUNT(id) as order_count'),
                    DB::raw('SUM(total_amount) as sales_amount'),
                    DB::raw('SUM(commission_amount) as commission_amount')
                )
                ->first();
            
            // 查询团队客户数据
            $teamCustomers = User::where('is_admin', 0)
                ->where('is_salesman', 0)
                ->whereIn('salesman_id', $teamIds)
                ->count();
            
            // 查询团队新增客户
            $teamNewCustomers = User::where('is_admin', 0)
                ->where('is_salesman', 0)
                ->whereIn('salesman_id', $teamIds)
                ->whereBetween('created_at', [$startDate, $endDate . ' 23:59:59'])
                ->count();
            
            // 组装数据
            $result[] = [
                'team_leader' => [
                    'id' => $salesman->id,
                    'name' => $salesman->name,
                    'phone' => $salesman->phone
                ],
                'team_size' => count($teamIds),
                'team_customers' => $teamCustomers,
                'team_new_customers' => $teamNewCustomers,
                'team_orders' => $teamSales->order_count ?? 0,
                'team_sales' => $teamSales->sales_amount ?? 0,
                'team_commission' => $teamSales->commission_amount ?? 0
            ];
        }
        
        // 按销售额排序
        usort($result, function($a, $b) {
            return $b['team_sales'] <=> $a['team_sales'];
        });
        
        return $this->success($result);
    }
}
