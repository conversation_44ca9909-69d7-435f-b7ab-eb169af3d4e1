<?php

namespace App\Http\Controllers\Admin\Api\V1;

use App\Http\Controllers\Controller;
use App\Traits\ApiResponseTrait;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Models\SystemLog;
use Carbon\Carbon;

class SystemLogController extends Controller
{
    use ApiResponseTrait;

    /**
     * 获取系统日志列表
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        $query = SystemLog::query();
        
        // 日志类型筛选
        if ($request->has('type') && !empty($request->type)) {
            $query->where('type', $request->type);
        }
        
        // 操作人筛选
        if ($request->has('user_id') && !empty($request->user_id)) {
            $query->where('user_id', $request->user_id);
        }
        
        // 关键词搜索
        if ($request->has('keyword') && !empty($request->keyword)) {
            $keyword = $request->keyword;
            $query->where(function($q) use ($keyword) {
                $q->where('action', 'like', "%{$keyword}%")
                  ->orWhere('content', 'like', "%{$keyword}%")
                  ->orWhere('ip', 'like', "%{$keyword}%");
            });
        }
        
        // 日期范围筛选
        if ($request->has('start_date') && !empty($request->start_date)) {
            $query->where('created_at', '>=', $request->start_date);
        }
        
        if ($request->has('end_date') && !empty($request->end_date)) {
            $query->where('created_at', '<=', $request->end_date . ' 23:59:59');
        }
        
        // 排序
        $orderBy = $request->input('order_by', 'created_at');
        $orderDir = $request->input('order_dir', 'desc');
        $query->orderBy($orderBy, $orderDir);
        
        // 分页
        $perPage = $request->input('per_page', 15);
        $logs = $query->paginate($perPage);
        
        // 加载关联数据
        $logs->load('user');
        
        return $this->paginate($logs);
    }

    /**
     * 获取单个日志详情
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        $log = SystemLog::with('user')->find($id);
        
        if (!$log) {
            return $this->error('日志不存在', 404);
        }
        
        return $this->success($log);
    }

    /**
     * 删除日志
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy($id)
    {
        $log = SystemLog::find($id);
        
        if (!$log) {
            return $this->error('日志不存在', 404);
        }
        
        try {
            $log->delete();
            
            return $this->success(null, '日志删除成功');
        } catch (\Exception $e) {
            return $this->error('日志删除失败: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * 批量删除日志
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function batchDelete(Request $request)
    {
        $validator = \Validator::make($request->all(), [
            'ids' => 'required|array',
            'ids.*' => 'required|integer|exists:system_logs,id',
        ]);

        if ($validator->fails()) {
            return $this->error('验证失败', 422, $validator->errors());
        }
        
        try {
            SystemLog::whereIn('id', $request->ids)->delete();
            
            return $this->success(null, '批量删除日志成功');
        } catch (\Exception $e) {
            return $this->error('批量删除日志失败: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * 清空日志
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function clear(Request $request)
    {
        try {
            // 如果指定了日期，则删除该日期之前的日志
            if ($request->has('before_date') && !empty($request->before_date)) {
                SystemLog::where('created_at', '<', $request->before_date)->delete();
                return $this->success(null, '清空指定日期前的日志成功');
            }
            
            // 否则清空所有日志
            SystemLog::truncate();
            
            return $this->success(null, '清空所有日志成功');
        } catch (\Exception $e) {
            return $this->error('清空日志失败: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * 获取日志类型列表
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function types()
    {
        $types = SystemLog::select('type')
            ->distinct()
            ->orderBy('type')
            ->get()
            ->pluck('type');
        
        return $this->success($types);
    }
    
    /**
     * 获取日志统计信息
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function statistics(Request $request)
    {
        // 时间范围
        $startDate = $request->input('start_date', Carbon::now()->subDays(30)->toDateString());
        $endDate = $request->input('end_date', Carbon::now()->toDateString());
        
        // 总日志数
        $totalLogs = SystemLog::count();
        
        // 时间范围内的日志数
        $periodLogs = SystemLog::whereBetween('created_at', [$startDate, $endDate . ' 23:59:59'])->count();
        
        // 按类型统计
        $typeStats = SystemLog::select('type', DB::raw('COUNT(*) as count'))
            ->whereBetween('created_at', [$startDate, $endDate . ' 23:59:59'])
            ->groupBy('type')
            ->orderBy('count', 'desc')
            ->get();
        
        // 按用户统计
        $userStats = SystemLog::select('user_id', DB::raw('COUNT(*) as count'))
            ->whereBetween('created_at', [$startDate, $endDate . ' 23:59:59'])
            ->groupBy('user_id')
            ->orderBy('count', 'desc')
            ->limit(10)
            ->get();
        
        // 加载用户信息
        $userStats->load('user:id,name');
        
        // 按日期统计
        $dateStats = SystemLog::select(DB::raw('DATE(created_at) as date'), DB::raw('COUNT(*) as count'))
            ->whereBetween('created_at', [$startDate, $endDate . ' 23:59:59'])
            ->groupBy('date')
            ->orderBy('date')
            ->get();
        
        return $this->success([
            'total' => $totalLogs,
            'period' => $periodLogs,
            'by_type' => $typeStats,
            'by_user' => $userStats,
            'by_date' => $dateStats,
        ]);
    }
    
    /**
     * 导出日志
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function export(Request $request)
    {
        // 构建查询
        $query = SystemLog::query();
        
        // 日志类型筛选
        if ($request->has('type') && !empty($request->type)) {
            $query->where('type', $request->type);
        }
        
        // 操作人筛选
        if ($request->has('user_id') && !empty($request->user_id)) {
            $query->where('user_id', $request->user_id);
        }
        
        // 关键词搜索
        if ($request->has('keyword') && !empty($request->keyword)) {
            $keyword = $request->keyword;
            $query->where(function($q) use ($keyword) {
                $q->where('action', 'like', "%{$keyword}%")
                  ->orWhere('content', 'like', "%{$keyword}%")
                  ->orWhere('ip', 'like', "%{$keyword}%");
            });
        }
        
        // 日期范围筛选
        if ($request->has('start_date') && !empty($request->start_date)) {
            $query->where('created_at', '>=', $request->start_date);
        }
        
        if ($request->has('end_date') && !empty($request->end_date)) {
            $query->where('created_at', '<=', $request->end_date . ' 23:59:59');
        }
        
        // 排序
        $orderBy = $request->input('order_by', 'created_at');
        $orderDir = $request->input('order_dir', 'desc');
        $query->orderBy($orderBy, $orderDir);
        
        // 加载关联数据
        $query->with('user:id,name');
        
        // 获取数据
        $logs = $query->get();
        
        // 导出文件名
        $filename = 'system_logs_' . date('YmdHis') . '.csv';
        
        // 导出路径
        $exportPath = public_path('exports/' . $filename);
        
        // 确保导出目录存在
        if (!file_exists(public_path('exports'))) {
            mkdir(public_path('exports'), 0755, true);
        }
        
        // 打开文件
        $file = fopen($exportPath, 'w');
        
        // 写入标题行
        fputcsv($file, ['ID', '类型', '操作', '内容', '操作人', 'IP地址', '用户代理', '创建时间']);
        
        // 写入数据行
        foreach ($logs as $log) {
            fputcsv($file, [
                $log->id,
                $log->type,
                $log->action,
                $log->content,
                $log->user ? $log->user->name : '未知',
                $log->ip,
                $log->user_agent,
                $log->created_at,
            ]);
        }
        
        // 关闭文件
        fclose($file);
        
        // 返回下载链接
        $downloadUrl = url('exports/' . $filename);
        
        return $this->success([
            'url' => $downloadUrl,
            'filename' => $filename,
        ], '导出成功');
    }
}
