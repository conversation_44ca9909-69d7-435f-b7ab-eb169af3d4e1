<?php

namespace App\Http\Controllers\Admin\Api\V1;

use App\Http\Controllers\Controller;
use App\Traits\ApiResponseTrait;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;
use App\Models\Installation;
use App\Models\InstallationEngineer;

class InstallationController extends Controller
{
    use ApiResponseTrait;

    /**
     * 获取安装预约列表
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        $query = Installation::query();
        
        // 搜索条件
        if ($request->has('keyword') && !empty($request->keyword)) {
            $keyword = $request->keyword;
            $query->where(function($q) use ($keyword) {
                $q->where('customer_name', 'like', "%{$keyword}%")
                  ->orWhere('customer_phone', 'like', "%{$keyword}%")
                  ->orWhere('address', 'like', "%{$keyword}%");
            });
        }
        
        // 状态筛选
        if ($request->has('status') && $request->status !== '') {
            $query->where('status', $request->status);
        }
        
        // 工程师筛选
        if ($request->has('engineer_id') && !empty($request->engineer_id)) {
            $query->where('engineer_id', $request->engineer_id);
        }
        
        // 设备类型筛选
        if ($request->has('device_type') && !empty($request->device_type)) {
            $query->where('device_type', $request->device_type);
        }
        
        // 日期范围筛选
        if ($request->has('start_date') && !empty($request->start_date)) {
            $query->where('installation_date', '>=', $request->start_date);
        }
        
        if ($request->has('end_date') && !empty($request->end_date)) {
            $query->where('installation_date', '<=', $request->end_date);
        }
        
        // 排序
        $orderBy = $request->input('order_by', 'created_at');
        $orderDir = $request->input('order_dir', 'desc');
        $query->orderBy($orderBy, $orderDir);
        
        // 分页
        $perPage = $request->input('per_page', 15);
        $installations = $query->paginate($perPage);
        
        // 加载关联数据
        $installations->load(['engineer', 'device']);
        
        return $this->paginate($installations);
    }

    /**
     * 创建安装预约
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'customer_name' => 'required|string|max:50',
            'customer_phone' => 'required|string|regex:/^1[3-9]\d{9}$/',
            'address' => 'required|string|max:200',
            'device_id' => 'nullable|integer|exists:devices,id',
            'device_type' => 'required|string|max:50',
            'installation_date' => 'required|date',
            'installation_time' => 'required|string|max:20',
            'engineer_id' => 'nullable|integer|exists:installation_engineers,id',
            'status' => 'required|integer|in:0,1,2,3,4',
            'remark' => 'nullable|string',
            'images' => 'nullable|array',
            'images.*' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return $this->error('验证失败', 422, $validator->errors());
        }

        try {
            $installation = new Installation();
            $installation->customer_name = $request->customer_name;
            $installation->customer_phone = $request->customer_phone;
            $installation->address = $request->address;
            $installation->device_id = $request->device_id;
            $installation->device_type = $request->device_type;
            $installation->installation_date = $request->installation_date;
            $installation->installation_time = $request->installation_time;
            $installation->engineer_id = $request->engineer_id;
            $installation->status = $request->status;
            $installation->remark = $request->remark;
            $installation->images = $request->images ? json_encode($request->images) : null;
            $installation->save();
            
            // 加载关联数据
            $installation->load(['engineer', 'device']);
            
            return $this->success($installation, '安装预约创建成功');
        } catch (\Exception $e) {
            return $this->error('安装预约创建失败: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 获取单个安装预约详情
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        $installation = Installation::with(['engineer', 'device'])->find($id);
        
        if (!$installation) {
            return $this->error('安装预约不存在', 404);
        }
        
        // 处理图片数据
        if ($installation->images) {
            $installation->images = json_decode($installation->images, true);
        }
        
        return $this->success($installation);
    }

    /**
     * 更新安装预约
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id)
    {
        $installation = Installation::find($id);
        
        if (!$installation) {
            return $this->error('安装预约不存在', 404);
        }
        
        $validator = Validator::make($request->all(), [
            'customer_name' => 'required|string|max:50',
            'customer_phone' => 'required|string|regex:/^1[3-9]\d{9}$/',
            'address' => 'required|string|max:200',
            'device_id' => 'nullable|integer|exists:devices,id',
            'device_type' => 'required|string|max:50',
            'installation_date' => 'required|date',
            'installation_time' => 'required|string|max:20',
            'engineer_id' => 'nullable|integer|exists:installation_engineers,id',
            'status' => 'required|integer|in:0,1,2,3,4',
            'remark' => 'nullable|string',
            'images' => 'nullable|array',
            'images.*' => 'nullable|string',
            'completion_note' => 'nullable|string',
            'completion_time' => 'nullable|date',
        ]);

        if ($validator->fails()) {
            return $this->error('验证失败', 422, $validator->errors());
        }
        
        try {
            $installation->customer_name = $request->customer_name;
            $installation->customer_phone = $request->customer_phone;
            $installation->address = $request->address;
            $installation->device_id = $request->device_id;
            $installation->device_type = $request->device_type;
            $installation->installation_date = $request->installation_date;
            $installation->installation_time = $request->installation_time;
            $installation->engineer_id = $request->engineer_id;
            $installation->status = $request->status;
            $installation->remark = $request->remark;
            $installation->images = $request->images ? json_encode($request->images) : null;
            
            // 如果状态是已完成，记录完成时间和备注
            if ($request->status == 3) {
                $installation->completion_time = $request->completion_time ?? now();
                $installation->completion_note = $request->completion_note;
            }
            
            $installation->save();
            
            // 加载关联数据
            $installation->load(['engineer', 'device']);
            
            // 处理图片数据
            if ($installation->images) {
                $installation->images = json_decode($installation->images, true);
            }
            
            return $this->success($installation, '安装预约更新成功');
        } catch (\Exception $e) {
            return $this->error('安装预约更新失败: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 删除安装预约
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy($id)
    {
        $installation = Installation::find($id);
        
        if (!$installation) {
            return $this->error('安装预约不存在', 404);
        }
        
        try {
            $installation->delete();
            
            return $this->success(null, '安装预约删除成功');
        } catch (\Exception $e) {
            return $this->error('安装预约删除失败: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * 更新安装预约状态
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateStatus(Request $request, $id)
    {
        $installation = Installation::find($id);
        
        if (!$installation) {
            return $this->error('安装预约不存在', 404);
        }
        
        $validator = Validator::make($request->all(), [
            'status' => 'required|integer|in:0,1,2,3,4',
            'remark' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return $this->error('验证失败', 422, $validator->errors());
        }
        
        try {
            $installation->status = $request->status;
            
            // 如果有备注，更新备注
            if ($request->has('remark')) {
                $installation->remark = $request->remark;
            }
            
            // 如果状态是已完成，记录完成时间
            if ($request->status == 3) {
                $installation->completion_time = now();
                
                // 如果有完成备注，更新完成备注
                if ($request->has('completion_note')) {
                    $installation->completion_note = $request->completion_note;
                }
            }
            
            $installation->save();
            
            // 加载关联数据
            $installation->load(['engineer', 'device']);
            
            return $this->success($installation, '安装预约状态更新成功');
        } catch (\Exception $e) {
            return $this->error('安装预约状态更新失败: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * 分配工程师
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function assignEngineer(Request $request, $id)
    {
        $installation = Installation::find($id);
        
        if (!$installation) {
            return $this->error('安装预约不存在', 404);
        }
        
        $validator = Validator::make($request->all(), [
            'engineer_id' => 'required|integer|exists:installation_engineers,id',
        ]);

        if ($validator->fails()) {
            return $this->error('验证失败', 422, $validator->errors());
        }
        
        try {
            $installation->engineer_id = $request->engineer_id;
            
            // 如果当前状态是待分配，更新为已分配
            if ($installation->status == 0) {
                $installation->status = 1;
            }
            
            $installation->save();
            
            // 加载关联数据
            $installation->load(['engineer', 'device']);
            
            return $this->success($installation, '工程师分配成功');
        } catch (\Exception $e) {
            return $this->error('工程师分配失败: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * 获取安装统计数据
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function statistics(Request $request)
    {
        // 时间范围筛选
        $startDate = $request->input('start_date', date('Y-m-d', strtotime('-30 days')));
        $endDate = $request->input('end_date', date('Y-m-d'));
        
        // 总安装数
        $totalCount = Installation::count();
        
        // 时间范围内安装数
        $periodCount = Installation::where('created_at', '>=', $startDate)
            ->where('created_at', '<=', $endDate . ' 23:59:59')
            ->count();
        
        // 各状态安装数
        $statusCounts = Installation::selectRaw('status, COUNT(*) as count')
            ->groupBy('status')
            ->get()
            ->pluck('count', 'status')
            ->toArray();
        
        // 待分配数量
        $pendingCount = $statusCounts[0] ?? 0;
        
        // 已分配数量
        $assignedCount = $statusCounts[1] ?? 0;
        
        // 安装中数量
        $installingCount = $statusCounts[2] ?? 0;
        
        // 已完成数量
        $completedCount = $statusCounts[3] ?? 0;
        
        // 已取消数量
        $cancelledCount = $statusCounts[4] ?? 0;
        
        // 工程师安装统计
        $engineerStats = Installation::select('installation_engineers.id', 'installation_engineers.name')
            ->selectRaw('COUNT(installations.id) as total_count')
            ->selectRaw('SUM(CASE WHEN installations.status = 3 THEN 1 ELSE 0 END) as completed_count')
            ->join('installation_engineers', 'installations.engineer_id', '=', 'installation_engineers.id')
            ->where('installations.created_at', '>=', $startDate)
            ->where('installations.created_at', '<=', $endDate . ' 23:59:59')
            ->groupBy('installation_engineers.id', 'installation_engineers.name')
            ->orderByRaw('COUNT(installations.id) DESC')
            ->limit(10)
            ->get();
        
        // 设备类型安装统计
        $deviceTypeStats = Installation::selectRaw('device_type, COUNT(*) as count')
            ->where('created_at', '>=', $startDate)
            ->where('created_at', '<=', $endDate . ' 23:59:59')
            ->groupBy('device_type')
            ->orderByRaw('COUNT(*) DESC')
            ->get();
        
        // 每日安装趋势
        $dailyTrend = Installation::selectRaw('DATE(created_at) as date, COUNT(*) as count, SUM(CASE WHEN status = 3 THEN 1 ELSE 0 END) as completed_count')
            ->where('created_at', '>=', $startDate)
            ->where('created_at', '<=', $endDate . ' 23:59:59')
            ->groupBy('date')
            ->orderBy('date', 'asc')
            ->get();
        
        $data = [
            'total_count' => $totalCount,
            'period_count' => $periodCount,
            'pending_count' => $pendingCount,
            'assigned_count' => $assignedCount,
            'installing_count' => $installingCount,
            'completed_count' => $completedCount,
            'cancelled_count' => $cancelledCount,
            'engineer_stats' => $engineerStats,
            'device_type_stats' => $deviceTypeStats,
            'daily_trend' => $dailyTrend
        ];
        
        return $this->success($data);
    }
}
