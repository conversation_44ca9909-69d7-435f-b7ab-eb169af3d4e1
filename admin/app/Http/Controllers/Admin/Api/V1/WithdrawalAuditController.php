<?php

namespace App\Http\Controllers\Admin\Api\V1;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;
use Carbon\Carbon;
use Exception;

class WithdrawalAuditController extends Controller
{
    /**
     * 获取提现列表
     */
    public function getWithdrawals(Request $request)
    {
        try {
            $page = $request->get('page', 1);
            $limit = $request->get('limit', 10);
            $search = $request->get('search', '');
            $status = $request->get('status', '');
            $date = $request->get('date', '');

            // 使用payment_db连接查询b.tapgo.cn数据库
            $query = DB::connection('payment_db')
                ->table('sft_institution_withdrawal as w')
                ->leftJoin('ddg_institution_relation as ir', 'w.institution_id', '=', 'ir.institution_id')
                ->leftJoin('sft_users as u', function($join) {
                    $join->on(DB::raw('CONVERT(u.channel_id USING utf8) COLLATE utf8_general_ci'), '=', 
                             DB::raw('CONVERT(ir.xs_number USING utf8) COLLATE utf8_general_ci'));
                })
                ->select([
                    'w.withdrawal_id',
                    'w.institution_id',
                    'w.withdrawal_amount',
                    'w.actual_amount as arrival_amount',
                    'w.withdrawal_date as withdrawal_time',
                    'w.created_at',
                    'w.updated_at as update_time',
                    'w.status',
                    'w.order_id',
                    'w.pay_fund_order_id',
                    'ir.name as institution_name',
                    'u.openid as alipay_openid'
                ]);

            // 搜索条件
            if (!empty($search)) {
                $query->where(function($q) use ($search) {
                    $q->where('ir.name', 'like', "%{$search}%")
                      ->orWhere('w.institution_id', 'like', "%{$search}%");
                });
            }

            // 状态筛选
            if (!empty($status)) {
                $query->where('w.status', $status);
            }

            // 日期筛选
            if (!empty($date)) {
                $query->whereDate('w.withdrawal_date', $date);
            }

            // 排序：状态为3的在前，然后按时间倒序
            $query->orderByRaw('w.status DESC, w.withdrawal_date DESC');

            // 分页
            $total = $query->count();
            $items = $query->offset(($page - 1) * $limit)
                          ->limit($limit)
                          ->get()
                          ->map(function($item) {
                              return [
                                  'withdrawal_id' => (string)$item->withdrawal_id,
                                  'institution_id' => (string)$item->institution_id,
                                  'institution_name' => $item->institution_name ?: "未知机构_{$item->institution_id}",
                                  'withdrawal_amount' => (string)$item->withdrawal_amount,
                                  'arrival_amount' => (string)$item->arrival_amount,
                                  'withdrawal_time' => $item->withdrawal_time ? Carbon::parse($item->withdrawal_time)->format('Y-m-d H:i:s') : null,
                                  'created_at' => $item->created_at ? Carbon::parse($item->created_at)->format('Y-m-d H:i:s') : null,
                                  'update_time' => $item->update_time ? Carbon::parse($item->update_time)->format('Y-m-d H:i:s') : null,
                                  'status' => (string)$item->status,
                                  'order_id' => $item->order_id,
                                  'pay_fund_order_id' => $item->pay_fund_order_id,
                                  'alipay_openid' => $item->alipay_openid
                              ];
                          });

            return response()->json([
                'code' => 20000,
                'data' => [
                    'total' => $total,
                    'items' => $items
                ]
            ]);

        } catch (Exception $e) {
            Log::error('获取提现列表失败: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'code' => 50000,
                'message' => '获取提现列表失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 审核提现申请
     */
    public function auditWithdrawal(Request $request)
    {
        try {
            $withdrawalId = $request->input('withdrawal_id');
            $status = $request->input('status'); // 1: 通过, 2: 驳回
            $password = $request->input('password');

            // 验证管理密码
            $adminPassword = env('ADMIN_PASSWORD');
            if ($password !== $adminPassword) {
                return response()->json([
                    'code' => 40001,
                    'message' => '管理密码错误'
                ]);
            }

            // 查询提现记录和相关信息
            $withdrawal = DB::connection('payment_db')
                ->table('sft_institution_withdrawal as w')
                ->leftJoin('ddg_institution_relation as ir', 'w.institution_id', '=', 'ir.institution_id')
                ->leftJoin('sft_users as u', function($join) {
                    $join->on(DB::raw('CONVERT(u.channel_id USING utf8) COLLATE utf8_general_ci'), '=', 
                             DB::raw('CONVERT(ir.xs_number USING utf8) COLLATE utf8_general_ci'));
                })
                ->where('w.withdrawal_id', $withdrawalId)
                ->select([
                    'w.*',
                    'u.openid as alipay_openid',
                    'ir.name as institution_name'
                ])
                ->first();

            if (!$withdrawal) {
                return response()->json([
                    'code' => 40004,
                    'message' => '提现记录不存在'
                ]);
            }

            // 如果是通过操作，检查是否有支付宝账户
            if ($status == 1 && !$withdrawal->alipay_openid) {
                return response()->json([
                    'code' => 40004,
                    'message' => '未找到该机构对应的支付宝账户信息，请确认机构是否已绑定支付宝'
                ]);
            }

            DB::connection('payment_db')->beginTransaction();

            try {
                if ($status == 1) {
                    // 审核通过 - 调用支付宝转账
                    $transferResult = $this->transferToAlipayAccount(
                        $withdrawalId,
                        $withdrawal->alipay_openid,
                        $withdrawal->actual_amount
                    );

                    if (!$transferResult) {
                        throw new Exception('支付宝转账失败');
                    }

                } elseif ($status == 2) {
                    // 审核驳回
                    // 获取机构信息
                    $institution = DB::connection('payment_db')
                        ->table('ddg_institution')
                        ->where('id', $withdrawal->institution_id)
                        ->select('phone', 'name')
                        ->first();

                    if (!$institution) {
                        throw new Exception('机构信息不存在');
                    }

                    // 发送驳回通知短信
                    $this->sendWithdrawalRejectSms(
                        $withdrawal->institution_id,
                        $institution->phone,
                        $institution->name,
                        $withdrawal->withdrawal_amount
                    );

                    // 加回到可提现余额
                    DB::connection('payment_db')
                        ->table('sft_institution_balance')
                        ->where('institution_id', $withdrawal->institution_id)
                        ->increment('withdrawable_balance', $withdrawal->withdrawal_amount);
                }

                // 更新提现记录状态
                DB::connection('payment_db')
                    ->table('sft_institution_withdrawal')
                    ->where('withdrawal_id', $withdrawalId)
                    ->update([
                        'status' => $status,
                        'updated_at' => now()
                    ]);

                DB::connection('payment_db')->commit();

                return response()->json([
                    'code' => 20000,
                    'message' => '审核操作成功'
                ]);

            } catch (Exception $e) {
                DB::connection('payment_db')->rollback();
                throw $e;
            }

        } catch (Exception $e) {
            Log::error('审核失败: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'code' => 50000,
                'message' => $e->getMessage()
            ]);
        }
    }

    /**
     * 获取统计数据
     */
    public function getStatistics()
    {
        try {
            // 获取各状态的提现金额统计
            $stats = DB::connection('payment_db')
                ->table('sft_institution_withdrawal')
                ->selectRaw('
                    COALESCE(SUM(CASE WHEN status = "3" THEN withdrawal_amount ELSE 0 END), 0) as pending_amount,
                    COALESCE(SUM(CASE WHEN status = "1" THEN withdrawal_amount ELSE 0 END), 0) as paid_amount,
                    COALESCE(SUM(CASE WHEN status = "2" THEN withdrawal_amount ELSE 0 END), 0) as rejected_amount
                ')
                ->first();

            // 获取支付宝余额
            $alipayBalance = $this->getAlipayBalance();

            return response()->json([
                'code' => 20000,
                'data' => [
                    'pending_amount' => (string)$stats->pending_amount,
                    'paid_amount' => (string)$stats->paid_amount,
                    'rejected_amount' => (string)$stats->rejected_amount,
                    'alipay_balance' => $alipayBalance
                ]
            ]);

        } catch (Exception $e) {
            Log::error('获取统计数据失败: ' . $e->getMessage());
            
            return response()->json([
                'code' => 50000,
                'message' => '获取统计数据失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 检查新订单
     */
    public function checkNewWithdrawals()
    {
        try {
            $twentyFourHoursAgo = Carbon::now()->subHours(24);
            
            $count = DB::connection('payment_db')
                ->table('sft_institution_withdrawal')
                ->where('status', '3')
                ->where('created_at', '>=', $twentyFourHoursAgo)
                ->count();

            $latestOrder = DB::connection('payment_db')
                ->table('sft_institution_withdrawal')
                ->where('status', '3')
                ->orderBy('created_at', 'desc')
                ->first();

            $latestTime = $latestOrder ? Carbon::parse($latestOrder->created_at)->timestamp * 1000 : 0;

            return response()->json([
                'code' => 20000,
                'data' => [
                    'count' => $count,
                    'latest_order_time' => $latestTime
                ]
            ]);

        } catch (Exception $e) {
            Log::error('检查新订单失败: ' . $e->getMessage());
            
            return response()->json([
                'code' => 50000,
                'message' => '检查新订单失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 获取可提现余额
     */
    public function getWithdrawableBalance($institutionId)
    {
        try {
            $balance = DB::connection('payment_db')
                ->table('sft_institution_balance')
                ->where('institution_id', $institutionId)
                ->value('withdrawable_balance');

            if ($balance !== null) {
                return response()->json([
                    'code' => 20000,
                    'data' => [
                        'withdrawable_balance' => (string)$balance
                    ]
                ]);
            } else {
                return response()->json([
                    'code' => 40004,
                    'message' => '余额记录不存在'
                ]);
            }

        } catch (Exception $e) {
            Log::error('获取可提现余额失败: ' . $e->getMessage());
            
            return response()->json([
                'code' => 50000,
                'message' => '获取可提现余额失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 支付宝转账 - 完全按照源项目实现
     */
    private function transferToAlipayAccount($outBizNo, $payeeOpenid, $amount)
    {
        try {
            Log::info("\n=== 支付宝转账请求开始 ===");
            Log::info("商户订单号: {$outBizNo}");
            Log::info("收款方openid: {$payeeOpenid}");
            Log::info("转账金额: {$amount}");
            
            // 基础参数 - 完全按照源项目
            $baseUrl = "https://openapi.alipay.com/gateway.do";
            $appId = "****************";
            $method = "alipay.fund.trans.uni.transfer";
            $charset = "utf-8";
            $signType = "RSA2";
            $timestamp = now()->format('Y-m-d H:i:s');
            $version = "1.0";
            
            // 业务参数 - 完全按照源项目
            $bizContent = json_encode([
                "out_biz_no" => $outBizNo,
                "trans_amount" => (string)$amount,
                "product_code" => "TRANS_ACCOUNT_NO_PWD",
                "biz_scene" => "DIRECT_TRANSFER",
                "order_title" => "机构余额提现",
                "payee_info" => [
                    "identity" => $payeeOpenid,
                    "identity_type" => "ALIPAY_OPEN_ID"
                ],
                "remark" => "生态平台出款",
                "business_params" => ["payer_show_name" => "点点够"]
            ], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);

            // 构建参数字典 - 完全按照源项目
            $params = [
                "app_id" => $appId,
                "method" => $method,
                "charset" => $charset,
                "sign_type" => $signType,
                "timestamp" => $timestamp,
                "version" => $version,
                "biz_content" => $bizContent,
                "app_cert_sn" => env('ALIPAY_APP_CERT_SN'),
                "alipay_cert_sn" => env('ALIPAY_CERT_SN'),
                "alipay_root_cert_sn" => env('ALIPAY_ROOT_CERT_SN')
            ];

            // 按字典序排序并构建签名字符串 - 完全按照源项目
            ksort($params);
            $unsignedString = "";
            foreach ($params as $k => $v) {
                $unsignedString .= $k . "=" . $v . "&";
            }
            $unsignedString = rtrim($unsignedString, "&");
            
            // 读取私钥并签名 - 使用源项目的方式
            $privateKeyPath = storage_path('app/cert/private_key_pkcs1.pem');
            if (!file_exists($privateKeyPath)) {
                throw new Exception('私钥文件不存在: ' . $privateKeyPath);
            }
            
            $privateKeyContent = file_get_contents($privateKeyPath);
            if (!$privateKeyContent) {
                throw new Exception('无法读取私钥文件');
            }
            
            // 使用源项目的签名方式
            $sign = $this->signDataWithPHPSecLib($unsignedString, $privateKeyContent);
            
            // 添加签名到参数中
            $params['sign'] = $sign;
            
            // 使用cURL发送请求 - 按照源项目方式
            Log::info("\n=== 发送请求到支付宝 ===");
            $response = $this->sendCurlRequest($baseUrl, $params);
            $result = json_decode($response, true);
            
            $responseData = $result['alipay_fund_trans_uni_transfer_response'] ?? null;
            if ($responseData && $responseData['code'] == '10000') {
                Log::info("\n=== 转账成功 ===");
                Log::info("支付宝订单号: " . ($responseData['order_id'] ?? ''));
                Log::info("支付宝流水号: " . ($responseData['pay_fund_order_id'] ?? ''));
                Log::info("转账时间: " . ($responseData['trans_date'] ?? ''));
                
                try {
                    // 更新数据库 - 按照源项目方式
                    $transDate = $responseData['trans_date'] ?? null;
                    $updatedAt = $transDate ? Carbon::createFromFormat('Y-m-d H:i:s', $transDate) : now();
                    
                    $updateParams = [
                        'updated_at' => $updatedAt,
                        'order_id' => $responseData['order_id'] ?? null,
                        'pay_fund_order_id' => $responseData['pay_fund_order_id'] ?? null
                    ];
                    
                    Log::info("\n=== 执行数据库更新 ===");
                    Log::info("更新参数: " . json_encode($updateParams));
                    
                    $affected = DB::connection('payment_db')
                        ->table('sft_institution_withdrawal')
                        ->where('withdrawal_id', $outBizNo)
                        ->update($updateParams);
                    
                    Log::info("\n=== 数据库更新成功 ===");
                    Log::info("更新行数: {$affected}");
                    Log::info("提现记录ID: {$outBizNo}");
                    
                } catch (Exception $dbError) {
                    Log::error("\n=== 数据库更新失败 ===");
                    Log::error("错误信息: " . $dbError->getMessage());
                    Log::error("详细堆栈: " . $dbError->getTraceAsString());
                    return false;
                }
                
                return true;
            } else {
                Log::error("\n=== 转账失败 ===");
                Log::error("错误代码: " . ($responseData['sub_code'] ?? 'Unknown'));
                Log::error("错误信息: " . ($responseData['sub_msg'] ?? 'Unknown'));
                return false;
            }
            
        } catch (Exception $e) {
            Log::error("\n=== 转账发生异常 ===");
            Log::error("异常类型: " . get_class($e));
            Log::error("异常信息: " . $e->getMessage());
            Log::error("详细堆栈: " . $e->getTraceAsString());
            return false;
        }
    }

    /**
     * 使用PHPSecLib方式签名 - 按照源项目方式
     */
    private function signDataWithPHPSecLib($unsignedString, $privateKeyContent)
    {
        // 确保私钥内容正确
        if (strpos($privateKeyContent, '-----BEGIN RSA PRIVATE KEY-----') === false) {
            throw new Exception('私钥格式错误，必须是PKCS1格式');
        }
        
        // 使用phpseclib方式处理，模拟源项目的实现
        $privateKey = openssl_pkey_get_private($privateKeyContent);
        if (!$privateKey) {
            throw new Exception('私钥格式错误: ' . openssl_error_string());
        }
        
        $signature = '';
        $result = openssl_sign($unsignedString, $signature, $privateKey, OPENSSL_ALGO_SHA256);
        openssl_free_key($privateKey);
        
        if (!$result) {
            throw new Exception('签名失败: ' . openssl_error_string());
        }
        
        return base64_encode($signature);
    }

    /**
     * 发送cURL请求 - 按照源项目方式
     */
    private function sendCurlRequest($url, $params)
    {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($params));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/x-www-form-urlencoded'
        ]);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);
        
        if ($error) {
            throw new Exception('cURL错误: ' . $error);
        }
        
        if ($httpCode !== 200) {
            throw new Exception('HTTP错误: ' . $httpCode);
        }
        
        return $response;
    }

    /**
     * 使用RSA签名 - 按照源项目方式
     */
    private function signDataWithRSA($unsignedString, $privateKeyContent)
    {
        // 确保私钥格式正确
        if (strpos($privateKeyContent, '-----BEGIN RSA PRIVATE KEY-----') === false) {
            $privateKeyContent = "-----BEGIN RSA PRIVATE KEY-----\n" . 
                               chunk_split($privateKeyContent, 64, "\n") . 
                               "-----END RSA PRIVATE KEY-----\n";
        }
        
        $privateKey = openssl_pkey_get_private($privateKeyContent);
        if (!$privateKey) {
            throw new Exception('私钥格式错误: ' . openssl_error_string());
        }
        
        $signature = '';
        $result = openssl_sign($unsignedString, $signature, $privateKey, OPENSSL_ALGO_SHA256);
        openssl_free_key($privateKey);
        
        if (!$result) {
            throw new Exception('签名失败: ' . openssl_error_string());
        }
        
        return base64_encode($signature);
    }

    /**
     * 获取支付宝余额 - 完全按照源项目实现
     */
    private function getAlipayBalance()
    {
        try {
            // 基础参数
            $baseUrl = "https://openapi.alipay.com/gateway.do";
            $appId = "****************";
            $method = "alipay.fund.account.query";
            $charset = "utf-8";
            $signType = "RSA2";
            $timestamp = now()->format('Y-m-d H:i:s');
            $version = "1.0";
            
            // 业务参数
            $bizContent = json_encode([
                "alipay_user_id" => "****************",
                "account_type" => "ACCTRANS_ACCOUNT"
            ], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);

            // 构建参数字典
            $params = [
                "app_id" => $appId,
                "method" => $method,
                "charset" => $charset,
                "sign_type" => $signType,
                "timestamp" => $timestamp,
                "version" => $version,
                "biz_content" => $bizContent,
                "app_cert_sn" => env('ALIPAY_APP_CERT_SN'),
                "alipay_cert_sn" => env('ALIPAY_CERT_SN'),
                "alipay_root_cert_sn" => env('ALIPAY_ROOT_CERT_SN')
            ];

            // 按字典序排序并构建签名字符串
            ksort($params);
            $unsignedString = "";
            foreach ($params as $k => $v) {
                $unsignedString .= $k . "=" . $v . "&";
            }
            $unsignedString = rtrim($unsignedString, "&");
            
            // 读取私钥并签名
            $privateKeyPath = storage_path('app/cert/private_key_pkcs1.pem');
            $privateKeyContent = file_get_contents($privateKeyPath);
            $sign = $this->signDataWithPHPSecLib($unsignedString, $privateKeyContent);
            
            // 添加签名到参数中
            $params['sign'] = $sign;
            
            // 发送请求
            $response = $this->sendCurlRequest($baseUrl, $params);
            $result = json_decode($response, true);
            
            if (isset($result['alipay_fund_account_query_response']['code']) && 
                $result['alipay_fund_account_query_response']['code'] == '10000') {
                return $result['alipay_fund_account_query_response']['available_amount'];
            } else {
                $errorMsg = $result['alipay_fund_account_query_response']['sub_msg'] ?? 'Unknown error';
                Log::error('获取支付宝余额失败: ' . $errorMsg);
                return '0.00';
            }
            
        } catch (Exception $e) {
            Log::error('获取支付宝余额异常: ' . $e->getMessage());
            return '0.00';
        }
    }

    /**
     * 发送提现驳回短信
     */
    private function sendWithdrawalRejectSms($institutionId, $phone, $userNick, $money)
    {
        try {
            $smsContent = "亲爱的{$userNick}, 您的机构分润提现\${$money}审核未通过，此次提现未能成功，请核实信息后重新申请。如有疑问，欢迎随时联系我们！";

            Log::info('发送提现驳回短信', [
                'institution_id' => $institutionId,
                'phone' => $phone,
                'user_nick' => $userNick,
                'money' => $money,
                'content' => $smsContent
            ]);

            // 记录短信发送记录
            DB::connection('payment_db')
                ->table('sft_bsms_record')
                ->insert([
                    'institution_id' => $institutionId,
                    'name' => $userNick,
                    'phone' => $phone,
                    'birthday' => now()->toDateString(),
                    'gender' => '未知',
                    'age' => 0,
                    'send_time' => now(),
                    'status' => 'success',
                    'sms_content' => $smsContent,
                    'created_at' => now(),
                    'updated_at' => now()
                ]);

            return true;

        } catch (Exception $e) {
            Log::error('发送短信失败: ' . $e->getMessage());
            
            // 记录失败的短信
            try {
                DB::connection('payment_db')
                    ->table('sft_bsms_record')
                    ->insert([
                        'institution_id' => $institutionId,
                        'name' => $userNick,
                        'phone' => $phone,
                        'birthday' => now()->toDateString(),
                        'gender' => '未知',
                        'age' => 0,
                        'send_time' => now(),
                        'status' => 'failed',
                        'sms_content' => $smsContent ?? '',
                        'created_at' => now(),
                        'updated_at' => now()
                    ]);
            } catch (Exception $dbError) {
                Log::error('记录失败短信失败: ' . $dbError->getMessage());
            }
            
            return false;
        }
    }
} 