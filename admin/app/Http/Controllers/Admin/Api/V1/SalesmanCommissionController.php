<?php

namespace App\Http\Controllers\Admin\Api\V1;

use App\Http\Controllers\Controller;
use App\Traits\ApiResponseTrait;
use App\Services\SalesmanCommissionService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;
use App\Models\Salesman;
use App\Models\SalesmanCommission;
use App\Models\SalesmanSale;
use App\Models\TappDevice;
use Carbon\Carbon;

class SalesmanCommissionController extends Controller
{
    use ApiResponseTrait;

    protected $commissionService;

    public function __construct(SalesmanCommissionService $commissionService)
    {
        $this->commissionService = $commissionService;
    }

    /**
     * 获取业务员提成记录列表
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        try {
            $query = SalesmanCommission::with(['salesman.user']);
            
            // 业务员筛选
            if ($request->filled('salesman_id')) {
                $query->where('salesman_id', $request->input('salesman_id'));
            }
            
            // 状态筛选
            if ($request->filled('status') && $request->input('status') !== '') {
                $query->where('status', $request->input('status'));
            }
            
            // 期间筛选
            if ($request->filled('period')) {
                $query->where('period', $request->input('period'));
            }
            
            // 日期范围筛选
            if ($request->filled('start_date')) {
                $query->where('start_date', '>=', $request->input('start_date'));
            }
            if ($request->filled('end_date')) {
                $query->where('end_date', '<=', $request->input('end_date'));
            }
            
            // 排序
            $orderBy = $request->input('order_by', 'created_at');
            $orderDir = $request->input('order_dir', 'desc');
            $query->orderBy($orderBy, $orderDir);
            
            // 分页
            $perPage = $request->input('per_page', 15);
            $page = $request->input('page', 1);
            
            $commissions = $query->paginate($perPage, ['*'], 'page', $page);
            
            return $this->paginate($commissions);
            
        } catch (\Exception $e) {
            return $this->error('获取提成记录失败：'.$e->getMessage(), 500);
        }
    }

    /**
     * 创建提成记录
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'salesman_id' => 'required|exists:salesmen,id',
            'amount' => 'required|numeric|min:0',
            'period' => 'required|string|max:10',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after_or_equal:start_date',
            'status' => 'required|in:pending,paid,cancelled',
            'remarks' => 'nullable|string|max:500'
        ]);

        if ($validator->fails()) {
            return $this->error('验证失败', 422, $validator->errors());
        }

        // 检查该期间是否已有提成记录
        $existingCommission = SalesmanCommission::where('salesman_id', $request->input('salesman_id'))
            ->where('period', $request->input('period'))
            ->first();
            
        if ($existingCommission) {
            return $this->error('该期间已存在提成记录', 400);
        }
        
        try {
            // 创建提成记录
            $commission = SalesmanCommission::create([
                'salesman_id' => $request->input('salesman_id'),
                'amount' => $request->input('amount'),
                'period' => $request->input('period'),
                'start_date' => $request->input('start_date'),
                'end_date' => $request->input('end_date'),
                'status' => $request->input('status'),
                'payment_date' => $request->input('status') === 'paid' ? now() : null,
                'remarks' => $request->input('remarks')
            ]);
            
            // 加载关联数据
            $commission->load('salesman.user');
            
            return $this->success($commission, '提成记录创建成功');
                
        } catch (\Exception $e) {
            return $this->error('提成记录创建失败：'.$e->getMessage(), 500);
        }
    }

    /**
     * 获取提成记录详情
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        $commission = SalesmanCommission::with(['salesman.user'])->find($id);
        
        if (!$commission) {
            return $this->error('提成记录不存在', 404);
        }
        
        // 获取该期间的销售明细
        $salesDetails = SalesmanSale::where('salesman_id', $commission->salesman_id)
            ->whereBetween('sale_date', [$commission->start_date, $commission->end_date])
            ->where('status', 'completed')
            ->get();
            
        $commission->sales_details = $salesDetails;
        
        // 获取设备销售统计
        try {
            $deviceStats = $this->commissionService->calculateDeviceCommissions(
                $commission->salesman_id,
                $commission->start_date,
                $commission->end_date
            );
            $commission->device_sales = $deviceStats;
        } catch (\Exception $e) {
            $commission->device_sales = null;
        }
        
        return $this->success($commission);
    }

    /**
     * 更新提成记录
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id)
    {
        $commission = SalesmanCommission::find($id);
        
        if (!$commission) {
            return $this->error('提成记录不存在', 404);
        }
        
        $validator = Validator::make($request->all(), [
            'amount' => 'required|numeric|min:0',
            'period' => 'required|string|max:10',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after_or_equal:start_date',
            'status' => 'required|in:pending,paid,cancelled',
            'remarks' => 'nullable|string|max:500'
        ]);

        if ($validator->fails()) {
            return $this->error('验证失败', 422, $validator->errors());
        }
        
        // 检查期间是否与其他记录冲突
        $existingCommission = SalesmanCommission::where('salesman_id', $commission->salesman_id)
            ->where('period', $request->input('period'))
            ->where('id', '!=', $id)
            ->first();
            
        if ($existingCommission) {
            return $this->error('该期间已存在其他提成记录', 400);
        }
        
        try {
            // 更新提成记录
            $commission->update([
                'amount' => $request->input('amount'),
                'period' => $request->input('period'),
                'start_date' => $request->input('start_date'),
                'end_date' => $request->input('end_date'),
                'status' => $request->input('status'),
                'payment_date' => $request->input('status') === 'paid' ? now() : $commission->payment_date,
                'remarks' => $request->input('remarks')
            ]);
            
            // 加载关联数据
            $commission->load('salesman.user');
            
            return $this->success($commission, '提成记录更新成功');
                
        } catch (\Exception $e) {
            return $this->error('提成记录更新失败：'.$e->getMessage(), 500);
        }
    }

    /**
     * 删除提成记录
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy($id)
    {
        $commission = SalesmanCommission::find($id);
        
        if (!$commission) {
            return $this->error('提成记录不存在', 404);
        }
        
        try {
            $commission->delete();
            
            return $this->success(null, '提成记录删除成功');
                
        } catch (\Exception $e) {
            return $this->error('提成记录删除失败：'.$e->getMessage(), 500);
        }
    }

    /**
     * 批量结算提成
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function batchSettle(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'commission_ids' => 'required|array',
            'commission_ids.*' => 'exists:salesman_commissions,id',
            'payment_date' => 'nullable|date'
        ]);

        if ($validator->fails()) {
            return $this->error('验证失败', 422, $validator->errors());
        }

        try {
            $commissionIds = $request->input('commission_ids');
            $paymentDate = $request->input('payment_date', now());
            
            $updated = SalesmanCommission::whereIn('id', $commissionIds)
                ->where('status', 'pending')
                ->update([
                    'status' => 'paid',
                    'payment_date' => $paymentDate
                ]);
            
            return $this->success([
                'updated_count' => $updated
            ], '批量结算完成');
                
        } catch (\Exception $e) {
            return $this->error('批量结算失败：'.$e->getMessage(), 500);
        }
    }

    /**
     * 自动计算并生成提成记录（基于设备销售）
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function autoCalculateFromDevices(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'salesman_id' => 'required|exists:salesmen,id',
            'period' => 'required|string|max:10',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after_or_equal:start_date'
        ]);

        if ($validator->fails()) {
            return $this->error('验证失败', 422, $validator->errors());
        }

        try {
            $salesmanId = $request->input('salesman_id');
            $period = $request->input('period');
            $startDate = $request->input('start_date');
            $endDate = $request->input('end_date');
            
            // 使用服务类生成提成记录
            $commission = $this->commissionService->generateCommissionRecord(
                $salesmanId, 
                $period, 
                $startDate, 
                $endDate
            );
            
            // 加载关联数据
            $commission->load('salesman.user');
            
            return $this->success($commission, '基于设备销售的提成计算完成');
                
        } catch (\Exception $e) {
            return $this->error('提成计算失败：'.$e->getMessage(), 500);
        }
    }

    /**
     * 预览设备销售提成计算结果
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function previewDeviceCommissions(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'salesman_id' => 'required|exists:salesmen,id',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after_or_equal:start_date'
        ]);

        if ($validator->fails()) {
            return $this->error('验证失败', 422, $validator->errors());
        }

        try {
            $salesmanId = $request->input('salesman_id');
            $startDate = $request->input('start_date');
            $endDate = $request->input('end_date');
            
            // 计算设备提成数据
            $commissionData = $this->commissionService->calculateDeviceCommissions(
                $salesmanId, 
                $startDate, 
                $endDate
            );
            
            return $this->success($commissionData, '设备销售提成预览计算完成');
                
        } catch (\Exception $e) {
            return $this->error('提成预览计算失败：'.$e->getMessage(), 500);
        }
    }

    /**
     * 获取业务员设备销售排行榜
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function deviceSalesRanking(Request $request)
    {
        try {
            $timeRange = $request->input('time_range', 'month');
            $limit = $request->input('limit', 10);
            
            $ranking = $this->commissionService->getSalesmanRanking($timeRange, $limit);
            
            return $this->success($ranking, '设备销售排行榜获取成功');
                
        } catch (\Exception $e) {
            return $this->error('获取排行榜失败：'.$e->getMessage(), 500);
        }
    }

    /**
     * 自动计算提成（兼容原有逻辑）
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function autoCalculate(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'salesman_id' => 'required|exists:salesmen,id',
            'period' => 'required|string|max:10',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after_or_equal:start_date'
        ]);

        if ($validator->fails()) {
            return $this->error('验证失败', 422, $validator->errors());
        }

        try {
            $salesmanId = $request->input('salesman_id');
            $period = $request->input('period');
            $startDate = $request->input('start_date');
            $endDate = $request->input('end_date');
            
            // 检查该期间是否已有提成记录
            $existingCommission = SalesmanCommission::where('salesman_id', $salesmanId)
                ->where('period', $period)
                ->first();
                
            if ($existingCommission) {
                return $this->error('该期间已存在提成记录', 400);
            }
            
            // 计算该期间的提成总额（传统销售记录）
            $totalCommission = SalesmanSale::where('salesman_id', $salesmanId)
                ->whereBetween('sale_date', [$startDate, $endDate])
                ->where('status', 'completed')
                ->sum('commission_amount');
                
            // 计算设备销售提成
            $deviceCommissionData = $this->commissionService->calculateDeviceCommissions(
                $salesmanId, 
                $startDate, 
                $endDate
            );
            
            $deviceCommission = $deviceCommissionData['total_commission'];
            $totalCommission += $deviceCommission;
                
            if ($totalCommission <= 0) {
                return $this->error('该期间没有可计算的提成', 400);
            }
            
            // 创建提成记录
            $commission = SalesmanCommission::create([
                'salesman_id' => $salesmanId,
                'amount' => $totalCommission,
                'period' => $period,
                'start_date' => $startDate,
                'end_date' => $endDate,
                'status' => 'pending',
                'remarks' => "系统自动计算生成（包含设备销售提成：¥{$deviceCommission}）"
            ]);
            
            // 如果有设备销售，创建对应的销售记录
            if ($deviceCommission > 0) {
                $this->commissionService->createSalesRecords($salesmanId, $deviceCommissionData);
            }
            
            // 加载关联数据
            $commission->load('salesman.user');
            
            return $this->success($commission, '提成计算完成');
                
        } catch (\Exception $e) {
            return $this->error('提成计算失败：'.$e->getMessage(), 500);
        }
    }

    /**
     * 获取提成统计数据
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function stats(Request $request)
    {
        try {
            $salesmanId = $request->input('salesman_id');
            $timeRange = $request->input('time_range', 'year');
            $startDate = $request->input('start_date');
            $endDate = $request->input('end_date');
            $type = $request->input('type', 'overview');
            $limit = $request->input('limit', 10);
            
            // 根据时间范围设置默认日期
            if (!$startDate || !$endDate) {
                switch ($timeRange) {
                    case 'today':
                        $startDate = Carbon::today()->toDateString();
                        $endDate = Carbon::today()->toDateString();
                        break;
                    case 'week':
                        $startDate = Carbon::now()->startOfWeek()->toDateString();
                        $endDate = Carbon::now()->endOfWeek()->toDateString();
                        break;
                    case 'month':
                        $startDate = Carbon::now()->startOfMonth()->toDateString();
                        $endDate = Carbon::now()->endOfMonth()->toDateString();
                        break;
                    case 'quarter':
                        $startDate = Carbon::now()->startOfQuarter()->toDateString();
                        $endDate = Carbon::now()->endOfQuarter()->toDateString();
                        break;
                    case 'year':
                        $startDate = Carbon::now()->startOfYear()->toDateString();
                        $endDate = Carbon::now()->endOfYear()->toDateString();
                        break;
                    default:
                        $startDate = Carbon::now()->startOfYear()->toDateString();
                        $endDate = Carbon::now()->endOfYear()->toDateString();
                }
            }
            
            $query = SalesmanCommission::query();
            
            if ($salesmanId) {
                $query->where('salesman_id', $salesmanId);
            }
            
            $query->whereBetween('start_date', [$startDate, $endDate]);
            
            switch ($type) {
                case 'overview':
                    $stats = [
                        'total_commissions' => $query->count(),
                        'total_amount' => $query->sum('amount'),
                        'pending_amount' => $query->where('status', 'pending')->sum('amount'),
                        'paid_amount' => $query->where('status', 'paid')->sum('amount'),
                        'cancelled_amount' => $query->where('status', 'cancelled')->sum('amount'),
                        'pending_count' => $query->where('status', 'pending')->count(),
                        'paid_count' => $query->where('status', 'paid')->count(),
                        'cancelled_count' => $query->where('status', 'cancelled')->count(),
                        'avg_commission' => $query->avg('amount'),
                        'commission_change' => 0, // 可以后续计算同比变化
                    ];
                    
                    // 如果指定了业务员，获取设备销售统计
                    if ($salesmanId) {
                        $deviceStats = $this->commissionService->getDeviceSalesStats($salesmanId, $timeRange);
                        $stats['device_sales'] = $deviceStats['stats'];
                    }
                    break;
                    
                case 'commission_ranking':
                    // 提成排行榜
                    $stats = DB::table('salesman_commissions')
                        ->join('salesmen', 'salesman_commissions.salesman_id', '=', 'salesmen.id')
                        ->join('app_users', 'salesmen.user_id', '=', 'app_users.id')
                        ->whereBetween('salesman_commissions.start_date', [$startDate, $endDate])
                        ->groupBy('salesman_commissions.salesman_id', 'app_users.name', 'salesmen.employee_id')
                        ->select(
                            'salesman_commissions.salesman_id',
                            'app_users.name as salesman_name',
                            'salesmen.employee_id',
                            DB::raw('COUNT(salesman_commissions.id) as commission_count'),
                            DB::raw('SUM(salesman_commissions.amount) as total_amount'),
                            DB::raw('SUM(CASE WHEN salesman_commissions.status = "paid" THEN salesman_commissions.amount ELSE 0 END) as paid_amount'),
                            DB::raw('SUM(CASE WHEN salesman_commissions.status = "pending" THEN salesman_commissions.amount ELSE 0 END) as pending_amount'),
                            DB::raw('AVG(salesman_commissions.amount) as avg_amount')
                        )
                        ->orderBy('total_amount', 'desc')
                        ->limit($limit)
                        ->get();
                    break;
                    
                case 'commission_trend':
                    // 提成趋势图表数据
                    $stats = DB::table('salesman_commissions')
                        ->select(
                            DB::raw('DATE_FORMAT(start_date, "%Y-%m") as month'),
                            DB::raw('COUNT(*) as commission_count'),
                            DB::raw('SUM(amount) as total_amount'),
                            DB::raw('SUM(CASE WHEN status = "paid" THEN amount ELSE 0 END) as paid_amount')
                        )
                        ->when($salesmanId, function($query) use ($salesmanId) {
                            return $query->where('salesman_id', $salesmanId);
                        })
                        ->whereBetween('start_date', [$startDate, $endDate])
                        ->groupBy(DB::raw('DATE_FORMAT(start_date, "%Y-%m")'))
                        ->orderBy('month', 'asc')
                        ->get();
                    break;
                    
                default:
                    $stats = [];
            }
            
            return $this->success($stats);
            
        } catch (\Exception $e) {
            return $this->error('获取提成统计失败：'.$e->getMessage(), 500);
        }
    }
} 