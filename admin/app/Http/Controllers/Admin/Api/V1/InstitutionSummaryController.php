<?php

namespace App\Http\Controllers\Admin\Api\V1;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Exception;

class InstitutionSummaryController extends Controller
{
    /**
     * 获取机构汇总列表
     */
    public function index(Request $request)
    {
        try {
            $page = $request->get('page', 1);
            $size = $request->get('size', 15);
            $search = $request->get('search', '');
            $parentId = $request->get('parent_id');
            
            $offset = ($page - 1) * $size;
            
            // 构建查询条件
            $whereConditions = [];
            $params = [];
            
            if ($search) {
                $whereConditions[] = "(r.name LIKE ? OR r.xs_number LIKE ?)";
                $params[] = "%{$search}%";
                $params[] = "%{$search}%";
            }
            
            if ($parentId) {
                $whereConditions[] = "r.parent_id = ?";
                $params[] = $parentId;
            }
            
            $whereClause = empty($whereConditions) ? '' : 'WHERE ' . implode(' AND ', $whereConditions);
            
            // 获取总数
            $countSql = "
                SELECT COUNT(*) as count 
                FROM ddg_institution_relation r
                LEFT JOIN ddg_institution i ON i.id = r.institution_id
                {$whereClause}
            ";
            
            $totalResult = DB::connection('payment_db')->select($countSql, $params);
            $total = $totalResult[0]->count;
            
            // 获取列表数据
            $listSql = "
                SELECT 
                    r.institution_id,
                    r.name,
                    r.xs_number,
                    i.lv,
                    (SELECT name FROM ddg_institution WHERE id = r.parent_id LIMIT 1) AS super_institution_name,
                    r.current_transaction,
                    r.total_transaction,
                    COALESCE(m.direct_merchant_count, 0) as direct_merchant_count,
                    COALESCE(m.total_merchant_count, 0) as total_merchant_count,
                    COALESCE(m.team_merchant_count, 0) as team_merchant_count,
                    r.direct_sub_count,
                    r.sub_count
                FROM ddg_institution_relation r
                LEFT JOIN ddg_institution i ON i.id = r.institution_id
                LEFT JOIN ddg_sft_merchant_count m ON i.id = m.institution_id
                {$whereClause}
                ORDER BY r.sub_count DESC
                LIMIT ? OFFSET ?
            ";
            
            $listParams = array_merge($params, [$size, $offset]);
            $list = DB::connection('payment_db')->select($listSql, $listParams);
            
            return response()->json([
                'code' => 0,
                'message' => 'success',
                'data' => [
                    'list' => $list,
                    'total' => $total,
                    'page' => $page,
                    'size' => $size
                ]
            ]);
            
        } catch (Exception $e) {
            Log::error('获取机构汇总列表失败: ' . $e->getMessage());
            return response()->json([
                'code' => 500,
                'message' => '获取数据失败: ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * 同步机构数据
     */
    public function syncData()
    {
        try {
            $result = DB::connection('payment_db')->transaction(function () {
                // 第一步：从 ddg_institution 获取数据并更新到 ddg_institution_relation
                Log::info("开始执行第一步：从 ddg_institution 获取数据");
                
                $step1Sql = "
                    INSERT INTO ddg_institution_relation (
                        institution_id, name, number, xs_number, level, parent_id,
                        created_at, updated_at
                    )
                    SELECT 
                        i.id as institution_id,
                        i.name,
                        i.number,
                        i.xs_number,
                        i.lv as level,
                        CASE 
                            WHEN i.institution_id IS NULL THEN NULL
                            WHEN EXISTS (SELECT 1 FROM ddg_institution p WHERE p.id = i.institution_id) 
                            THEN i.institution_id 
                            ELSE NULL 
                        END as parent_id,
                        NOW(),
                        NOW()
                    FROM ddg_institution i
                    ON DUPLICATE KEY UPDATE
                        name = VALUES(name),
                        number = VALUES(number),
                        xs_number = VALUES(xs_number),
                        level = VALUES(level),
                        parent_id = VALUES(parent_id),
                        updated_at = NOW()
                ";
                
                DB::connection('payment_db')->statement($step1Sql);
                Log::info("第一步执行完成");
                
                // 第二步：更新交易数据
                Log::info("开始执行第二步：更新交易数据");
                
                $step2Sql = "
                    UPDATE ddg_institution_relation r
                    LEFT JOIN (
                        SELECT 
                            institution_id,
                            SUM(current_month_total_transaction) as total_trans,
                            MAX(CASE 
                                WHEN summary_period = DATE_FORMAT(CURRENT_DATE, '%Y-%m')
                                THEN current_month_total_transaction 
                                ELSE 0 
                            END) as current_trans
                        FROM sft_institution_count
                        GROUP BY institution_id
                    ) s ON r.institution_id = s.institution_id
                    SET 
                        r.total_transaction = COALESCE(s.total_trans, 0),
                        r.current_transaction = COALESCE(s.current_trans, 0)
                ";
                
                DB::connection('payment_db')->statement($step2Sql);
                Log::info("第二步执行完成");
                
                // 第三步：更新直属下级数量
                Log::info("开始执行第三步：更新直属下级数量");
                
                $step3Sql = "
                    UPDATE ddg_institution_relation r
                    LEFT JOIN (
                        SELECT parent_id, COUNT(*) as direct_count
                        FROM ddg_institution_relation
                        WHERE parent_id IS NOT NULL
                        GROUP BY parent_id
                    ) d ON r.institution_id = d.parent_id
                    SET r.direct_sub_count = COALESCE(d.direct_count, 0)
                ";
                
                DB::connection('payment_db')->statement($step3Sql);
                Log::info("第三步执行完成");
                
                // 第四步：更新总下级数量
                Log::info("开始执行第四步：更新总下级数量");
                
                // 先确保 institution_hierarchy 表存在
                $createTableSql = "
                    CREATE TABLE IF NOT EXISTS institution_hierarchy (
                        institution_id INT,
                        parent_id INT,
                        level INT,
                        PRIMARY KEY (institution_id, parent_id)
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
                ";
                DB::connection('payment_db')->statement($createTableSql);
                
                // 清空层级关系表
                DB::connection('payment_db')->statement("TRUNCATE TABLE institution_hierarchy");
                
                // 插入直接下级关系
                $directRelationSql = "
                    INSERT INTO institution_hierarchy
                    SELECT institution_id, parent_id, 1
                    FROM ddg_institution_relation
                    WHERE parent_id IS NOT NULL
                ";
                DB::connection('payment_db')->statement($directRelationSql);
                
                // 循环插入更深层级的关系
                $level = 1;
                while (true) {
                    $deeperLevelSql = "
                        INSERT IGNORE INTO institution_hierarchy
                        SELECT DISTINCT h.institution_id, r.parent_id, ?
                        FROM institution_hierarchy h
                        JOIN ddg_institution_relation r ON h.parent_id = r.institution_id
                        WHERE r.parent_id IS NOT NULL AND h.level = ?
                    ";
                    
                    $affectedRows = DB::connection('payment_db')->affectingStatement($deeperLevelSql, [$level + 1, $level]);
                    
                    if ($affectedRows == 0) {
                        break;
                    }
                    $level++;
                }
                
                // 更新总下级数量
                $updateTotalSubSql = "
                    UPDATE ddg_institution_relation r
                    LEFT JOIN (
                        SELECT parent_id, COUNT(DISTINCT institution_id) as total_count
                        FROM institution_hierarchy
                        GROUP BY parent_id
                    ) t ON r.institution_id = t.parent_id
                    SET r.sub_count = COALESCE(t.total_count, 0)
                ";
                
                DB::connection('payment_db')->statement($updateTotalSubSql);
                Log::info("第四步执行完成");
                
                return true;
            });
            
            Log::info("数据同步成功");
            
            return response()->json([
                'code' => 0,
                'message' => '数据同步成功'
            ]);
            
        } catch (Exception $e) {
            Log::error('数据同步失败: ' . $e->getMessage());
            
            return response()->json([
                'code' => 500,
                'message' => '数据同步失败: ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * 获取同步状态（模拟进度）
     */
    public function getSyncStatus()
    {
        // 这里可以实现真实的同步状态检查
        // 目前返回模拟数据
        return response()->json([
            'code' => 0,
            'message' => 'success',
            'data' => [
                'progress' => 100,
                'status' => 'completed',
                'message' => '同步完成',
                'current_step' => '数据同步完成'
            ]
        ]);
    }
} 