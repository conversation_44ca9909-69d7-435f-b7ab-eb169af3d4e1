<?php

namespace App\Http\Controllers\Admin\Api\V1;

use App\Http\Controllers\Controller;
use App\Traits\ApiResponseTrait;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use App\Models\SmsTemplate;

class SmsTemplateController extends Controller
{
    use ApiResponseTrait;

    /**
     * 获取短信模板列表
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        $query = SmsTemplate::query();
        
        // 搜索条件
        if ($request->has('keyword') && !empty($request->keyword)) {
            $keyword = $request->keyword;
            $query->where(function($q) use ($keyword) {
                $q->where('name', 'like', "%{$keyword}%")
                  ->orWhere('template_id', 'like', "%{$keyword}%")
                  ->orWhere('content', 'like', "%{$keyword}%");
            });
        }
        
        // 类型筛选
        if ($request->has('type') && !empty($request->type)) {
            $query->where('type', $request->type);
        }
        
        // 状态筛选
        if ($request->has('status') && $request->status !== '') {
            $query->where('status', $request->status);
        }
        
        // 排序
        $orderBy = $request->input('order_by', 'created_at');
        $orderDir = $request->input('order_dir', 'desc');
        $query->orderBy($orderBy, $orderDir);
        
        // 分页
        $perPage = $request->input('per_page', 15);
        $templates = $query->paginate($perPage);
        
        return $this->paginate($templates);
    }

    /**
     * 创建短信模板
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:50',
            'template_id' => 'required|string|max:50|unique:sms_templates,template_id',
            'content' => 'required|string',
            'type' => 'required|string|max:20',
            'params' => 'nullable|array',
            'remark' => 'nullable|string',
            'status' => 'required|in:0,1',
        ]);

        if ($validator->fails()) {
            return $this->error('验证失败', 422, $validator->errors());
        }

        $template = new SmsTemplate();
        $template->name = $request->name;
        $template->template_id = $request->template_id;
        $template->content = $request->content;
        $template->type = $request->type;
        $template->params = json_encode($request->params ?? []);
        $template->remark = $request->remark;
        $template->status = $request->status;
        $template->save();
        
        return $this->success($template, '短信模板创建成功');
    }

    /**
     * 获取单个短信模板详情
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        $template = SmsTemplate::find($id);
        
        if (!$template) {
            return $this->error('短信模板不存在', 404);
        }
        
        // 处理JSON字段
        $template->params = json_decode($template->params);
        
        return $this->success($template);
    }

    /**
     * 更新短信模板
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id)
    {
        $template = SmsTemplate::find($id);
        
        if (!$template) {
            return $this->error('短信模板不存在', 404);
        }
        
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:50',
            'template_id' => 'required|string|max:50|unique:sms_templates,template_id,'.$id,
            'content' => 'required|string',
            'type' => 'required|string|max:20',
            'params' => 'nullable|array',
            'remark' => 'nullable|string',
            'status' => 'required|in:0,1',
        ]);

        if ($validator->fails()) {
            return $this->error('验证失败', 422, $validator->errors());
        }
        
        $template->name = $request->name;
        $template->template_id = $request->template_id;
        $template->content = $request->content;
        $template->type = $request->type;
        $template->params = json_encode($request->params ?? []);
        $template->remark = $request->remark;
        $template->status = $request->status;
        $template->save();
        
        return $this->success($template, '短信模板更新成功');
    }

    /**
     * 删除短信模板
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy($id)
    {
        $template = SmsTemplate::find($id);
        
        if (!$template) {
            return $this->error('短信模板不存在', 404);
        }
        
        // 检查是否有关联的短信日志
        $logCount = 0;
        if (class_exists('App\Models\SmsLog')) {
            $logCount = \App\Models\SmsLog::where('template_id', $template->template_id)->count();
            if ($logCount > 0) {
                return $this->error('该模板已有关联的短信日志，无法删除', 400);
            }
        }
        
        $template->delete();
        
        return $this->success(null, '短信模板删除成功');
    }
    
    /**
     * 获取短信模板类型列表
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function types()
    {
        $types = SmsTemplate::select('type')
            ->distinct()
            ->pluck('type');
            
        return $this->success($types);
    }
    
    /**
     * 更新短信模板状态
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateStatus(Request $request, $id)
    {
        $template = SmsTemplate::find($id);
        
        if (!$template) {
            return $this->error('短信模板不存在', 404);
        }
        
        $validator = Validator::make($request->all(), [
            'status' => 'required|in:0,1',
        ]);

        if ($validator->fails()) {
            return $this->error('验证失败', 422, $validator->errors());
        }
        
        $template->status = $request->status;
        $template->save();
        
        return $this->success($template, '状态更新成功');
    }
}
