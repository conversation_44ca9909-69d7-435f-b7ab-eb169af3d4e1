<?php

namespace App\Http\Controllers\Admin\Api\V1;

use App\Http\Controllers\Controller;
use App\Traits\ApiResponseTrait;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use App\Models\InvitationRegistration;
use App\Models\Invitation;

class InvitationRegistrationController extends Controller
{
    use ApiResponseTrait;

    /**
     * 获取报名记录列表
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        $query = InvitationRegistration::query();
        
        // 邀请函ID筛选
        if ($request->has('invitation_id') && !empty($request->invitation_id)) {
            $query->where('invitation_id', $request->invitation_id);
        }
        
        // 搜索条件
        if ($request->has('keyword') && !empty($request->keyword)) {
            $keyword = $request->keyword;
            $query->where(function($q) use ($keyword) {
                $q->where('name', 'like', "%{$keyword}%")
                  ->orWhere('phone', 'like', "%{$keyword}%")
                  ->orWhere('email', 'like', "%{$keyword}%");
            });
        }
        
        // 状态筛选
        if ($request->has('status') && $request->status !== '') {
            $query->where('status', $request->status);
        }
        
        // 日期范围筛选
        if ($request->has('start_date') && !empty($request->start_date)) {
            $query->where('created_at', '>=', $request->start_date);
        }
        
        if ($request->has('end_date') && !empty($request->end_date)) {
            $query->where('created_at', '<=', $request->end_date . ' 23:59:59');
        }
        
        // 排序
        $orderBy = $request->input('order_by', 'created_at');
        $orderDir = $request->input('order_dir', 'desc');
        $query->orderBy($orderBy, $orderDir);
        
        // 分页
        $perPage = $request->input('per_page', 15);
        $registrations = $query->paginate($perPage);
        
        // 加载关联数据
        $registrations->load('invitation');
        
        // 处理表单数据
        foreach ($registrations as $registration) {
            if ($registration->form_data) {
                $registration->form_data = json_decode($registration->form_data, true);
            }
        }
        
        return $this->paginate($registrations);
    }

    /**
     * 创建报名记录
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'invitation_id' => 'required|integer|exists:invitations,id',
            'name' => 'required|string|max:50',
            'phone' => 'required|string|regex:/^1[3-9]\d{9}$/',
            'email' => 'nullable|email|max:100',
            'form_data' => 'nullable|array',
            'status' => 'required|integer|in:0,1,2',
            'remark' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return $this->error('验证失败', 422, $validator->errors());
        }

        // 检查邀请函状态
        $invitation = Invitation::find($request->invitation_id);
        if (!$invitation || $invitation->status != 1) {
            return $this->error('邀请函不存在或未开放报名', 400);
        }
        
        // 检查是否超过最大报名人数
        if ($invitation->max_registrations > 0) {
            $currentCount = InvitationRegistration::where('invitation_id', $request->invitation_id)->count();
            if ($currentCount >= $invitation->max_registrations) {
                return $this->error('报名人数已达上限', 400);
            }
        }
        
        // 检查是否已经报名过
        $existingRegistration = InvitationRegistration::where('invitation_id', $request->invitation_id)
            ->where('phone', $request->phone)
            ->first();
            
        if ($existingRegistration) {
            return $this->error('该手机号已经报名过', 400);
        }

        try {
            $registration = new InvitationRegistration();
            $registration->invitation_id = $request->invitation_id;
            $registration->name = $request->name;
            $registration->phone = $request->phone;
            $registration->email = $request->email;
            $registration->form_data = $request->form_data ? json_encode($request->form_data) : null;
            $registration->status = $request->status;
            $registration->remark = $request->remark;
            $registration->save();
            
            // 加载关联数据
            $registration->load('invitation');
            
            // 处理表单数据
            if ($registration->form_data) {
                $registration->form_data = json_decode($registration->form_data, true);
            }
            
            return $this->success($registration, '报名记录创建成功');
        } catch (\Exception $e) {
            return $this->error('报名记录创建失败: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 获取单个报名记录详情
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        $registration = InvitationRegistration::with('invitation')->find($id);
        
        if (!$registration) {
            return $this->error('报名记录不存在', 404);
        }
        
        // 处理表单数据
        if ($registration->form_data) {
            $registration->form_data = json_decode($registration->form_data, true);
        }
        
        return $this->success($registration);
    }

    /**
     * 更新报名记录
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id)
    {
        $registration = InvitationRegistration::find($id);
        
        if (!$registration) {
            return $this->error('报名记录不存在', 404);
        }
        
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:50',
            'phone' => 'required|string|regex:/^1[3-9]\d{9}$/',
            'email' => 'nullable|email|max:100',
            'form_data' => 'nullable|array',
            'status' => 'required|integer|in:0,1,2',
            'remark' => 'nullable|string',
            'check_in_time' => 'nullable|date',
        ]);

        if ($validator->fails()) {
            return $this->error('验证失败', 422, $validator->errors());
        }
        
        try {
            $registration->name = $request->name;
            $registration->phone = $request->phone;
            $registration->email = $request->email;
            $registration->form_data = $request->form_data ? json_encode($request->form_data) : null;
            $registration->status = $request->status;
            $registration->remark = $request->remark;
            
            // 如果有签到时间，更新签到时间
            if ($request->has('check_in_time') && !empty($request->check_in_time)) {
                $registration->check_in_time = $request->check_in_time;
            }
            
            $registration->save();
            
            // 加载关联数据
            $registration->load('invitation');
            
            // 处理表单数据
            if ($registration->form_data) {
                $registration->form_data = json_decode($registration->form_data, true);
            }
            
            return $this->success($registration, '报名记录更新成功');
        } catch (\Exception $e) {
            return $this->error('报名记录更新失败: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 删除报名记录
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy($id)
    {
        $registration = InvitationRegistration::find($id);
        
        if (!$registration) {
            return $this->error('报名记录不存在', 404);
        }
        
        try {
            $registration->delete();
            
            return $this->success(null, '报名记录删除成功');
        } catch (\Exception $e) {
            return $this->error('报名记录删除失败: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * 更新报名记录状态
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateStatus(Request $request, $id)
    {
        $registration = InvitationRegistration::find($id);
        
        if (!$registration) {
            return $this->error('报名记录不存在', 404);
        }
        
        $validator = Validator::make($request->all(), [
            'status' => 'required|integer|in:0,1,2',
        ]);

        if ($validator->fails()) {
            return $this->error('验证失败', 422, $validator->errors());
        }
        
        try {
            $registration->status = $request->status;
            $registration->save();
            
            // 加载关联数据
            $registration->load('invitation');
            
            return $this->success($registration, '报名记录状态更新成功');
        } catch (\Exception $e) {
            return $this->error('报名记录状态更新失败: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * 签到
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function checkIn(Request $request, $id)
    {
        $registration = InvitationRegistration::find($id);
        
        if (!$registration) {
            return $this->error('报名记录不存在', 404);
        }
        
        // 如果已经签到，返回错误
        if ($registration->check_in_time) {
            return $this->error('已经签到过了', 400);
        }
        
        try {
            $registration->check_in_time = now();
            $registration->status = 1; // 更新为已签到状态
            $registration->save();
            
            // 加载关联数据
            $registration->load('invitation');
            
            return $this->success($registration, '签到成功');
        } catch (\Exception $e) {
            return $this->error('签到失败: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * 导出报名记录
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function export(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'invitation_id' => 'required|integer|exists:invitations,id',
        ]);

        if ($validator->fails()) {
            return $this->error('验证失败', 422, $validator->errors());
        }
        
        $invitationId = $request->invitation_id;
        
        // 获取邀请函信息
        $invitation = Invitation::find($invitationId);
        if (!$invitation) {
            return $this->error('邀请函不存在', 404);
        }
        
        // 获取报名记录
        $registrations = InvitationRegistration::where('invitation_id', $invitationId)
            ->orderBy('created_at', 'desc')
            ->get();
        
        // 准备表头
        $headers = ['姓名', '手机号', '邮箱', '状态', '签到时间', '报名时间', '备注'];
        
        // 获取自定义表单字段
        $formFields = [];
        if ($invitation->form_fields) {
            $fields = json_decode($invitation->form_fields, true);
            foreach ($fields as $field) {
                $formFields[] = $field['label'];
                $headers[] = $field['label'];
            }
        }
        
        // 准备数据
        $data = [];
        foreach ($registrations as $registration) {
            $row = [
                $registration->name,
                $registration->phone,
                $registration->email,
                $this->getStatusText($registration->status),
                $registration->check_in_time,
                $registration->created_at,
                $registration->remark
            ];
            
            // 添加自定义表单字段数据
            if ($invitation->form_fields) {
                $formData = json_decode($registration->form_data, true);
                foreach ($fields as $field) {
                    $fieldName = $field['name'];
                    $row[] = $formData[$fieldName] ?? '';
                }
            }
            
            $data[] = $row;
        }
        
        return $this->success([
            'headers' => $headers,
            'data' => $data,
            'filename' => $invitation->title . '_报名记录_' . date('YmdHis') . '.xlsx'
        ]);
    }
    
    /**
     * 获取状态文本
     *
     * @param int $status
     * @return string
     */
    private function getStatusText($status)
    {
        $statusMap = [
            0 => '待审核',
            1 => '已确认',
            2 => '已取消'
        ];
        
        return $statusMap[$status] ?? '未知状态';
    }
}
