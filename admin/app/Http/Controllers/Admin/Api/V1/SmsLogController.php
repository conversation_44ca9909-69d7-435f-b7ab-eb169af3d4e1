<?php

namespace App\Http\Controllers\Admin\Api\V1;

use App\Http\Controllers\Controller;
use App\Traits\ApiResponseTrait;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use App\Models\SmsLog;

class SmsLogController extends Controller
{
    use ApiResponseTrait;

    /**
     * 获取短信日志列表
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        $query = SmsLog::query();
        
        // 搜索条件
        if ($request->has('keyword') && !empty($request->keyword)) {
            $keyword = $request->keyword;
            $query->where(function($q) use ($keyword) {
                $q->where('phone', 'like', "%{$keyword}%")
                  ->orWhere('template_id', 'like', "%{$keyword}%");
            });
        }
        
        // 状态筛选
        if ($request->has('status') && $request->status !== '') {
            $query->where('status', $request->status);
        }
        
        // 时间范围筛选
        if ($request->has('start_time') && !empty($request->start_time)) {
            $query->where('created_at', '>=', $request->start_time);
        }
        
        if ($request->has('end_time') && !empty($request->end_time)) {
            $query->where('created_at', '<=', $request->end_time . ' 23:59:59');
        }
        
        // 排序
        $orderBy = $request->input('order_by', 'created_at');
        $orderDir = $request->input('order_dir', 'desc');
        $query->orderBy($orderBy, $orderDir);
        
        // 分页
        $perPage = $request->input('per_page', 15);
        $logs = $query->paginate($perPage);
        
        // 加载管理员信息
        $logs->load('admin');
        
        return $this->paginate($logs);
    }

    /**
     * 获取单个短信日志详情
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        $log = SmsLog::with('admin')->find($id);
        
        if (!$log) {
            return $this->error('短信日志不存在', 404);
        }
        
        return $this->success($log);
    }

    /**
     * 删除短信日志
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy($id)
    {
        $log = SmsLog::find($id);
        
        if (!$log) {
            return $this->error('短信日志不存在', 404);
        }
        
        $log->delete();
        
        return $this->success(null, '短信日志删除成功');
    }

    /**
     * 批量删除短信日志
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function batchDelete(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'ids' => 'required|array',
            'ids.*' => 'required|integer|exists:sms_logs,id',
        ]);

        if ($validator->fails()) {
            return $this->error('验证失败', 422, $validator->errors());
        }
        
        SmsLog::whereIn('id', $request->ids)->delete();
        
        return $this->success(null, '批量删除成功');
    }

    /**
     * 清理过期短信日志
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function clearExpired(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'days' => 'required|integer|min:1',
        ]);

        if ($validator->fails()) {
            return $this->error('验证失败', 422, $validator->errors());
        }
        
        $days = $request->days;
        $expireDate = date('Y-m-d H:i:s', strtotime("-{$days} days"));
        
        $count = SmsLog::where('created_at', '<', $expireDate)->count();
        SmsLog::where('created_at', '<', $expireDate)->delete();
        
        return $this->success(['count' => $count], "成功清理 {$days} 天前的短信日志");
    }

    /**
     * 获取短信统计数据
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function statistics(Request $request)
    {
        // 时间范围筛选
        $startTime = $request->input('start_time', date('Y-m-d', strtotime('-30 days')));
        $endTime = $request->input('end_time', date('Y-m-d'));
        
        // 总发送数
        $totalSent = SmsLog::whereBetween('created_at', [$startTime, $endTime . ' 23:59:59'])->count();
        
        // 发送成功数
        $successCount = SmsLog::where('status', 1)
            ->whereBetween('created_at', [$startTime, $endTime . ' 23:59:59'])
            ->count();
        
        // 发送失败数
        $failCount = SmsLog::where('status', 0)
            ->whereBetween('created_at', [$startTime, $endTime . ' 23:59:59'])
            ->count();
        
        // 按模板统计
        $templateStats = SmsLog::whereBetween('created_at', [$startTime, $endTime . ' 23:59:59'])
            ->selectRaw('template_id, COUNT(*) as count')
            ->groupBy('template_id')
            ->orderByRaw('COUNT(*) DESC')
            ->limit(10)
            ->get();
        
        // 按日期统计
        $dailyStats = SmsLog::whereBetween('created_at', [$startTime, $endTime . ' 23:59:59'])
            ->selectRaw('DATE(created_at) as date, COUNT(*) as count, SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as success_count')
            ->groupBy('date')
            ->orderBy('date', 'asc')
            ->get();
        
        $data = [
            'total_sent' => $totalSent,
            'success_count' => $successCount,
            'fail_count' => $failCount,
            'success_rate' => $totalSent > 0 ? round($successCount / $totalSent * 100, 2) : 0,
            'template_stats' => $templateStats,
            'daily_stats' => $dailyStats,
        ];
        
        return $this->success($data);
    }
}
