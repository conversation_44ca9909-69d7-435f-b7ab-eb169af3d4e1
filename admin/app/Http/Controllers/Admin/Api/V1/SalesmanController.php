<?php

namespace App\Http\Controllers\Admin\Api\V1;

use App\Http\Controllers\Controller;
use App\Traits\ApiResponseTrait;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;
use App\Models\Salesman;
use App\Models\AppUser;

class SalesmanController extends Controller
{
    use ApiResponseTrait;

    /**
     * 获取业务员列表
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        try {
            // 记录API请求
            \Log::info('V1 获取业务员列表请求', [
                'params' => $request->all(),
                'ip' => $request->ip(),
                'user_id' => auth()->id() ?? 'unauthenticated'
            ]);

            $query = Salesman::with('user')->orderBy('created_at', 'desc');
            
            // 搜索条件 - 适配前端的search参数
            if ($request->filled('search')) {
                $search = $request->input('search');
                $query->whereHas('user', function ($q) use ($search) {
                    $q->where('name', 'like', "%{$search}%")
                      ->orWhere('phone', 'like', "%{$search}%");
                })->orWhere('employee_id', 'like', "%{$search}%");
            }
            
            // 兼容旧版本的keyword参数
            if ($request->filled('keyword')) {
                $keyword = $request->input('keyword');
                $query->whereHas('user', function ($q) use ($keyword) {
                    $q->where('name', 'like', "%{$keyword}%")
                      ->orWhere('phone', 'like', "%{$keyword}%");
                })->orWhere('employee_id', 'like', "%{$keyword}%");
            }
            
            // 状态筛选
            if ($request->filled('status') && $request->input('status') !== 'all' && $request->input('status') !== '') {
                $query->where('status', $request->input('status'));
            }
            
            // 区域筛选
            if ($request->filled('region')) {
                $query->where('region', $request->input('region'));
            }
            
            // 上级业务员筛选
            if ($request->filled('manager_id')) {
                $query->where('manager_id', $request->input('manager_id'));
            }
            
            // 排序
            $orderBy = $request->input('order_by', 'created_at');
            $orderDir = $request->input('order_dir', 'desc');
            $query->orderBy($orderBy, $orderDir);
            
            // 分页
            $perPage = $request->input('per_page', 15);
            $page = $request->input('page', 1);
            
            $salesmen = $query->paginate($perPage, ['*'], 'page', $page);
            
            \Log::info('V1 业务员列表获取成功', [
                'count' => $salesmen->count(),
                'total' => $salesmen->total(),
                'current_page' => $salesmen->currentPage(),
                'per_page' => $salesmen->perPage()
            ]);
            
            // 返回标准分页格式
            return response()->json([
                'code' => 200,
                'message' => '获取成功',
                'data' => $salesmen->items(),
                'total' => $salesmen->total(),
                'current_page' => $salesmen->currentPage(),
                'per_page' => $salesmen->perPage(),
                'last_page' => $salesmen->lastPage(),
                'from' => $salesmen->firstItem(),
                'to' => $salesmen->lastItem()
            ]);
            
        } catch (\Exception $e) {
            \Log::error('V1 获取业务员列表失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'code' => 500,
                'message' => '获取业务员列表失败：' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    /**
     * 创建业务员
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'user_id' => 'required|exists:app_users,id|unique:salesmen,user_id',
            'employee_id' => 'nullable|string|max:50|unique:salesmen,employee_id',
            'title' => 'required|string|max:50',
            'department' => 'nullable|string|max:100',
            'region' => 'nullable|string|max:100',
            'manager_id' => 'nullable|exists:salesmen,id',
            'status' => 'required|in:active,leave,suspend',
            'join_date' => 'nullable|date',
            'remark' => 'nullable|string|max:500'
        ]);

        if ($validator->fails()) {
            return $this->error('验证失败', 422, $validator->errors());
        }

        DB::beginTransaction();
        
        try {
            // 创建业务员记录
            $salesman = new Salesman();
            $salesman->user_id = $request->input('user_id');
            $salesman->employee_id = $request->input('employee_id') ?: 'S'.str_pad($request->input('user_id'), 6, '0', STR_PAD_LEFT);
            $salesman->title = $request->input('title');
            $salesman->department = $request->input('department');
            $salesman->region = $request->input('region');
            $salesman->manager_id = $request->input('manager_id');
            $salesman->status = $request->input('status');
            $salesman->extra_info = json_encode([
                'join_date' => $request->input('join_date'),
                'remark' => $request->input('remark')
            ]);
            $salesman->save();
            
            // 更新用户标记为业务员
            $user = AppUser::find($request->input('user_id'));
            $user->is_salesman = 1;
            $user->save();
            
            // 如果选择了上级经理，更新manager字段
            if ($request->filled('manager_id')) {
                $manager = Salesman::with('user')->find($request->input('manager_id'));
                if ($manager && $manager->user) {
                    $salesman->manager = $manager->user->name;
                    $salesman->save();
                }
            }
            
            DB::commit();
            
            // 加载关联数据
            $salesman->load('user');
            
            return $this->success($salesman, '业务员创建成功');
                
        } catch (\Exception $e) {
            DB::rollBack();
            
            return $this->error('业务员创建失败：'.$e->getMessage(), 500);
        }
    }

    /**
     * 获取单个业务员详情
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        $salesman = Salesman::with(['user'])->find($id);
        
        if (!$salesman) {
            return $this->error('业务员不存在', 404);
        }
        
        // 获取业务员统计数据
        $stats = $this->getSalesmanStats($id);
        $salesman->stats = $stats;
        
        return $this->success($salesman);
    }

    /**
     * 更新业务员
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id)
    {
        $salesman = Salesman::find($id);
        
        if (!$salesman) {
            return $this->error('业务员不存在', 404);
        }
        
        $validator = Validator::make($request->all(), [
            'user_id' => 'required|exists:app_users,id|unique:salesmen,user_id,'.$id,
            'employee_id' => 'nullable|string|max:50|unique:salesmen,employee_id,'.$id,
            'title' => 'required|string|max:50',
            'department' => 'nullable|string|max:100',
            'region' => 'nullable|string|max:100',
            'manager_id' => 'nullable|exists:salesmen,id',
            'status' => 'required|in:active,leave,suspend',
            'join_date' => 'nullable|date',
            'remark' => 'nullable|string|max:500'
        ]);

        if ($validator->fails()) {
            return $this->error('验证失败', 422, $validator->errors());
        }
        
        // 检查是否将自己设为自己的上级
        if ($request->manager_id == $id) {
            return $this->error('不能将自己设为自己的上级', 400);
        }
        
        DB::beginTransaction();
        
        try {
            // 更新业务员记录
            $salesman->user_id = $request->input('user_id');
            $salesman->employee_id = $request->input('employee_id') ?: $salesman->employee_id;
            $salesman->title = $request->input('title');
            $salesman->department = $request->input('department');
            $salesman->region = $request->input('region');
            $salesman->manager_id = $request->input('manager_id');
            $salesman->status = $request->input('status');
            
            // 更新额外信息
            $extraInfo = json_decode($salesman->extra_info, true) ?: [];
            $extraInfo['join_date'] = $request->input('join_date');
            $extraInfo['remark'] = $request->input('remark');
            $salesman->extra_info = json_encode($extraInfo);
            
            // 如果选择了上级经理，更新manager字段
            if ($request->filled('manager_id')) {
                $manager = Salesman::with('user')->find($request->input('manager_id'));
                if ($manager && $manager->user) {
                    $salesman->manager = $manager->user->name;
                }
            } else {
                $salesman->manager = null;
            }
            
            $salesman->save();
            
            DB::commit();
            
            // 加载关联数据
            $salesman->load('user');
            
            return $this->success($salesman, '业务员更新成功');
                
        } catch (\Exception $e) {
            DB::rollBack();
            
            return $this->error('业务员更新失败：'.$e->getMessage(), 500);
        }
    }

    /**
     * 删除业务员
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy($id)
    {
        $salesman = Salesman::find($id);
        
        if (!$salesman) {
            return $this->error('业务员不存在', 404);
        }
        
        DB::beginTransaction();
        
        try {
            // 更新用户标记
            if ($salesman->user_id) {
                $user = AppUser::find($salesman->user_id);
                if ($user) {
                    $user->is_salesman = 0;
                    $user->save();
                }
            }
            
            // 删除业务员记录
            $salesman->delete();
            
            DB::commit();
            
            return $this->success(null, '业务员删除成功');
                
        } catch (\Exception $e) {
            DB::rollBack();
            
            return $this->error('业务员删除失败：'.$e->getMessage(), 500);
        }
    }

    /**
     * 获取业务员统计数据
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function stats($id)
    {
        $salesman = Salesman::find($id);
        
        if (!$salesman) {
            return $this->error('业务员不存在', 404);
        }
        
        $stats = $this->getSalesmanStats($id);
        
        return $this->success($stats);
    }

    /**
     * 获取业务员团队信息
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function team($id)
    {
        $salesman = Salesman::with(['user'])->find($id);
        
        if (!$salesman) {
            return $this->error('业务员不存在', 404);
        }
        
        // 获取下级业务员
        $subordinates = Salesman::with(['user'])
            ->where('manager_id', $id)
            ->get();
        
        return $this->success([
            'salesman' => $salesman,
            'subordinates' => $subordinates,
            'subordinates_count' => $subordinates->count()
        ]);
    }

    /**
     * 获取可选的上级经理列表
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getManagers(Request $request)
    {
        try {
            $managers = Salesman::with('user')
                ->where('status', 'active')
                ->get();
                
            return response()->json([
                'code' => 200,
                'message' => '获取成功',
                'data' => $managers
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'code' => 500,
                'message' => '获取经理列表失败：' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    /**
     * 获取可用的用户列表（未分配为业务员的用户）
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getAvailableUsers()
    {
        try {
            $users = AppUser::where('is_salesman', 0)
                ->select('id', 'name', 'phone', 'created_at')
                ->get();
                
            return response()->json([
                'code' => 200,
                'message' => '获取成功',
                'data' => $users
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'code' => 500,
                'message' => '获取可用用户列表失败：' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    /**
     * 获取业务员统计数据
     *
     * @param int $salesmanId
     * @return array
     */
    private function getSalesmanStats($salesmanId)
    {
        try {
            // 获取最近6个月的销售数据
            $sixMonthsAgo = now()->subMonths(6)->startOfMonth();
            
            $monthlySales = DB::table('salesman_sales')
                ->select(
                    DB::raw('DATE_FORMAT(sale_date, "%Y-%m") as month'),
                    DB::raw('SUM(quantity) as quantity'),
                    DB::raw('SUM(amount) as amount'),
                    DB::raw('SUM(commission_amount) as commission')
                )
                ->where('salesman_id', $salesmanId)
                ->where('sale_date', '>=', $sixMonthsAgo)
                ->groupBy(DB::raw('DATE_FORMAT(sale_date, "%Y-%m")'))
                ->orderBy('month', 'asc')
                ->get();
                
            return [
                'total_quantity' => DB::table('salesman_sales')->where('salesman_id', $salesmanId)->sum('quantity'),
                'total_amount' => DB::table('salesman_sales')->where('salesman_id', $salesmanId)->sum('amount'),
                'total_commission' => DB::table('salesman_commissions')->where('salesman_id', $salesmanId)->where('status', 'paid')->sum('amount'),
                'customer_count' => DB::table('salesman_customers')->where('salesman_id', $salesmanId)->count(),
                'monthly_stats' => $monthlySales
            ];
        } catch (\Exception $e) {
            \Log::error('获取业务员统计数据失败', [
                'salesman_id' => $salesmanId,
                'error' => $e->getMessage()
            ]);
            
            return [
                'total_quantity' => 0,
                'total_amount' => 0,
                'total_commission' => 0,
                'customer_count' => 0,
                'monthly_stats' => []
            ];
        }
    }
}
