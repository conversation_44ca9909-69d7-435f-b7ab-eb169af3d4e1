<?php

namespace App\Http\Controllers\Admin\Api\V1;

use App\Http\Controllers\Controller;
use App\Traits\ApiResponseTrait;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Storage;
use App\Models\Banner;

class BannerController extends Controller
{
    use ApiResponseTrait;

    /**
     * 获取Banner列表
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        $query = Banner::query();
        
        // 搜索条件
        if ($request->has('keyword') && !empty($request->keyword)) {
            $keyword = $request->keyword;
            $query->where('title', 'like', "%{$keyword}%");
        }
        
        // 位置筛选
        if ($request->has('position') && !empty($request->position)) {
            $query->where('position', $request->position);
        }
        
        // 状态筛选
        if ($request->has('status') && $request->status !== '') {
            $query->where('status', $request->status);
        }
        
        // 排序
        $orderBy = $request->input('order_by', 'sort');
        $orderDir = $request->input('order_dir', 'asc');
        $query->orderBy($orderBy, $orderDir);
        
        // 分页
        $perPage = $request->input('per_page', 15);
        $banners = $query->paginate($perPage);
        
        return $this->paginate($banners);
    }

    /**
     * 创建Banner
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:100',
            'image_url' => 'required|string',
            'link_url' => 'nullable|string',
            'position' => 'required|string|max:50',
            'sort' => 'nullable|integer',
            'status' => 'required|in:0,1',
            'start_time' => 'nullable|date',
            'end_time' => 'nullable|date|after_or_equal:start_time',
        ]);

        if ($validator->fails()) {
            return $this->error('验证失败', 422, $validator->errors());
        }

        $banner = new Banner();
        $banner->title = $request->title;
        $banner->image_url = $request->image_url;
        $banner->link_url = $request->link_url;
        $banner->position = $request->position;
        $banner->sort = $request->sort ?? 0;
        $banner->status = $request->status;
        $banner->start_time = $request->start_time;
        $banner->end_time = $request->end_time;
        $banner->save();
        
        return $this->success($banner, 'Banner创建成功');
    }

    /**
     * 获取单个Banner详情
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        $banner = Banner::find($id);
        
        if (!$banner) {
            return $this->error('Banner不存在', 404);
        }
        
        return $this->success($banner);
    }

    /**
     * 更新Banner
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id)
    {
        $banner = Banner::find($id);
        
        if (!$banner) {
            return $this->error('Banner不存在', 404);
        }
        
        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:100',
            'image_url' => 'required|string',
            'link_url' => 'nullable|string',
            'position' => 'required|string|max:50',
            'sort' => 'nullable|integer',
            'status' => 'required|in:0,1',
            'start_time' => 'nullable|date',
            'end_time' => 'nullable|date|after_or_equal:start_time',
        ]);

        if ($validator->fails()) {
            return $this->error('验证失败', 422, $validator->errors());
        }
        
        $banner->title = $request->title;
        $banner->image_url = $request->image_url;
        $banner->link_url = $request->link_url;
        $banner->position = $request->position;
        $banner->sort = $request->sort ?? $banner->sort;
        $banner->status = $request->status;
        $banner->start_time = $request->start_time;
        $banner->end_time = $request->end_time;
        $banner->save();
        
        return $this->success($banner, 'Banner更新成功');
    }

    /**
     * 删除Banner
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy($id)
    {
        $banner = Banner::find($id);
        
        if (!$banner) {
            return $this->error('Banner不存在', 404);
        }
        
        // 删除图片文件（如果是本地存储）
        if ($banner->image_url && strpos($banner->image_url, '/storage/') === 0) {
            $path = str_replace('/storage/', 'public/', $banner->image_url);
            Storage::delete($path);
        }
        
        $banner->delete();
        
        return $this->success(null, 'Banner删除成功');
    }
    
    /**
     * 获取所有Banner位置
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function positions()
    {
        $positions = Banner::select('position')
            ->distinct()
            ->pluck('position');
            
        return $this->success($positions);
    }
    
    /**
     * 更新Banner排序
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateSort(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'items' => 'required|array',
            'items.*.id' => 'required|integer|exists:banners,id',
            'items.*.sort' => 'required|integer',
        ]);

        if ($validator->fails()) {
            return $this->error('验证失败', 422, $validator->errors());
        }
        
        foreach ($request->items as $item) {
            Banner::where('id', $item['id'])->update(['sort' => $item['sort']]);
        }
        
        return $this->success(null, '排序更新成功');
    }
}
