<?php

namespace App\Http\Controllers\Admin\Api\V1;

use App\Http\Controllers\Controller;
use App\Models\Shengfutong\SftUploadLog;
use App\Models\Shengfutong\SftAgentMchSum;
use App\Models\Shengfutong\SftDetail;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Storage;
use Carbon\Carbon;
use PhpOffice\PhpSpreadsheet\IOFactory;
use League\Csv\Reader;

class ShengfutongController extends Controller
{
    /**
     * 获取盛付通控制面板统计数据
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getDashboardStats(Request $request)
    {
        try {
            Log::info('盛付通API: 获取控制面板统计数据');

            // 获取数据库中最新的交易时间，以此为基准计算统计数据
            $latestTransaction = DB::connection('payment_db')->table('ddg_sft_detail')
                ->orderBy('transaction_time', 'desc')
                ->first();
                
            if (!$latestTransaction) {
                return response()->json([
                    'code' => 200,
                    'message' => 'success',
                    'data' => [
                        'total_transaction' => 0,
                        'total_commission' => 0,
                        'total_count' => 0,
                        'total_active_merchants' => 0,
                        'transaction_change' => 0,
                        'commission_change' => 0,
                        'count_change' => 0,
                        'merchants_change' => 0
                    ]
                ]);
            }

            // 使用数据中的最新时间作为当前时间
            $now = Carbon::parse($latestTransaction->transaction_time);
            $currentMonthStart = $now->copy()->startOfMonth();

            // 获取本月至今的总交易额
            $totalTransaction = DB::connection('payment_db')->table('ddg_sft_detail')
                ->where('transaction_time', '>=', $currentMonthStart->toDateTimeString())
                ->where('transaction_time', '<=', $now->toDateTimeString())
                ->sum('transaction_amount') ?? 0;

            // 获取本月至今的总分润
            $totalCommission = DB::connection('payment_db')->table('ddg_sft_detail')
                ->where('transaction_time', '>=', $currentMonthStart->toDateTimeString())
                ->where('transaction_time', '<=', $now->toDateTimeString())
                ->sum('reseller_commission') ?? 0;

            // 计算上月同期的日期范围
            $lastMonth = $now->copy()->subMonth();
            $lastMonthStart = $lastMonth->startOfMonth();
            $lastMonthEnd = $lastMonthStart->copy()->add($now->diff($currentMonthStart));

            // 获取上月同期交易数据
            $lastMonthTransaction = DB::connection('payment_db')->table('ddg_sft_detail')
                ->where('transaction_time', '>=', $lastMonthStart->toDateTimeString())
                ->where('transaction_time', '<=', $lastMonthEnd->toDateTimeString())
                ->sum('transaction_amount') ?? 0;

            // 获取上月同期分润数据
            $lastMonthCommission = DB::connection('payment_db')->table('ddg_sft_detail')
                ->where('transaction_time', '>=', $lastMonthStart->toDateTimeString())
                ->where('transaction_time', '<=', $lastMonthEnd->toDateTimeString())
                ->sum('reseller_commission') ?? 0;

            // 计算环比变化
            $transactionChange = $lastMonthTransaction > 0 
                ? round((($totalTransaction - $lastMonthTransaction) / $lastMonthTransaction) * 100, 1)
                : 0;

            $commissionChange = $lastMonthCommission > 0
                ? round((($totalCommission - $lastMonthCommission) / $lastMonthCommission) * 100, 1)
                : 0;

            // 获取本月至今的渠道总笔数
            $totalCount = DB::connection('payment_db')->table('ddg_sft_agent_mch_sum')
                ->where('summary_period', '>=', $currentMonthStart->toDateString())
                ->where('summary_period', '<=', $now->toDateString())
                ->sum('success_count') ?? 0;

            // 获取上月同期渠道总笔数
            $lastMonthCount = DB::connection('payment_db')->table('ddg_sft_agent_mch_sum')
                ->where('summary_period', '>=', $lastMonthStart->toDateString())
                ->where('summary_period', '<=', $lastMonthEnd->toDateString())
                ->sum('success_count') ?? 0;

            // 计算总笔数环比变化
            $countChange = $lastMonthCount > 0
                ? round((($totalCount - $lastMonthCount) / $lastMonthCount) * 100, 1)
                : 0;

            // 获取月动销商户总数
            $totalActiveMerchants = DB::connection('payment_db')->table('ddg_sft_agent_mch_sum')
                ->where('summary_period', '>=', $currentMonthStart->toDateString())
                ->where('summary_period', '<=', $now->toDateString())
                ->where('success_count', '>', 0)
                ->distinct('merchant_id')
                ->count('merchant_id') ?? 0;

            // 获取上月同期动销商户数
            $lastMonthActiveMerchants = DB::connection('payment_db')->table('ddg_sft_agent_mch_sum')
                ->where('summary_period', '>=', $lastMonthStart->toDateString())
                ->where('summary_period', '<=', $lastMonthEnd->toDateString())
                ->where('success_count', '>', 0)
                ->distinct('merchant_id')
                ->count('merchant_id') ?? 0;

            // 计算动销商户环比变化
            $merchantsChange = $lastMonthActiveMerchants > 0
                ? round((($totalActiveMerchants - $lastMonthActiveMerchants) / $lastMonthActiveMerchants) * 100, 1)
                : 0;

            return response()->json([
                'code' => 200,
                'message' => 'success',
                'data' => [
                    'total_transaction' => (float) $totalTransaction,
                    'total_commission' => (float) $totalCommission,
                    'total_count' => (float) $totalCount,
                    'total_active_merchants' => (float) $totalActiveMerchants,
                    'transaction_change' => (float) $transactionChange,
                    'commission_change' => (float) $commissionChange,
                    'count_change' => (float) $countChange,
                    'merchants_change' => (float) $merchantsChange
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('盛付通API: 获取控制面板统计数据失败 - ' . $e->getMessage());
            return response()->json([
                'code' => 500,
                'message' => '获取统计数据失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 获取盛付通趋势数据
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getTrendData(Request $request)
    {
        try {
            $range = $request->get('range', 'month');
            Log::info('盛付通API: 获取趋势数据', ['range' => $range]);

            // 获取数据库中最新的交易时间，以此为基准计算趋势数据
            $latestTransaction = DB::connection('payment_db')->table('ddg_sft_detail')
                ->orderBy('transaction_time', 'desc')
                ->first();
                
            if (!$latestTransaction) {
                return response()->json([
                    'code' => 200,
                    'message' => 'success',
                    'data' => [
                        'dates' => [],
                        'transaction_amounts' => [],
                        'commission_amounts' => []
                    ]
                ]);
            }

            // 使用数据中的最新时间作为当前时间
            $endDate = Carbon::parse($latestTransaction->transaction_time);
            
            // 根据范围确定开始时间
            switch ($range) {
                case 'week':
                    $startDate = $endDate->copy()->subDays(6); // 最近7天
                    break;
                case 'year':
                    $startDate = $endDate->copy()->subMonths(11)->startOfMonth(); // 最近12个月
                    break;
                case 'month':
                default:
                    $startDate = $endDate->copy()->subDays(29); // 最近30天
                    break;
            }

            $dates = [];
            $transactionAmounts = [];
            $commissionAmounts = [];

            if ($range === 'year') {
                // 按月统计
                $current = $startDate->copy();
                while ($current <= $endDate) {
                    $monthStart = $current->copy()->startOfMonth();
                    $monthEnd = $current->copy()->endOfMonth();
                    
                    // 确保不超过数据的最新时间
                    if ($monthEnd > $endDate) {
                        $monthEnd = $endDate->copy();
                    }

                    $monthlyData = DB::connection('payment_db')->table('ddg_sft_detail')
                        ->where('transaction_time', '>=', $monthStart->toDateTimeString())
                        ->where('transaction_time', '<=', $monthEnd->toDateTimeString())
                        ->selectRaw('
                            COALESCE(SUM(transaction_amount), 0) as total_amount,
                            COALESCE(SUM(reseller_commission), 0) as total_commission
                        ')
                        ->first();

                    $dates[] = $current->format('Y-m');
                    $transactionAmounts[] = (float) ($monthlyData->total_amount ?? 0);
                    $commissionAmounts[] = (float) ($monthlyData->total_commission ?? 0);

                    $current->addMonth();
                }
            } else {
                // 按天统计
                $current = $startDate->copy();
                while ($current <= $endDate) {
                    $dayStart = $current->copy()->startOfDay();
                    $dayEnd = $current->copy()->endOfDay();
                    
                    // 确保不超过数据的最新时间
                    if ($dayEnd > $endDate) {
                        $dayEnd = $endDate->copy();
                    }

                    $dailyData = DB::connection('payment_db')->table('ddg_sft_detail')
                        ->where('transaction_time', '>=', $dayStart->toDateTimeString())
                        ->where('transaction_time', '<=', $dayEnd->toDateTimeString())
                        ->selectRaw('
                            COALESCE(SUM(transaction_amount), 0) as total_amount,
                            COALESCE(SUM(reseller_commission), 0) as total_commission
                        ')
                        ->first();

                    $dates[] = $current->format('m-d');
                    $transactionAmounts[] = (float) ($dailyData->total_amount ?? 0);
                    $commissionAmounts[] = (float) ($dailyData->total_commission ?? 0);

                    $current->addDay();
                }
            }

            return response()->json([
                'code' => 200,
                'message' => 'success',
                'data' => [
                    'dates' => $dates,
                    'transaction_amounts' => $transactionAmounts,
                    'commission_amounts' => $commissionAmounts
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('盛付通API: 获取趋势数据失败 - ' . $e->getMessage());
            return response()->json([
                'code' => 500,
                'message' => '获取趋势数据失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 获取支付方式统计
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getPaymentStats(Request $request)
    {
        try {
            Log::info('盛付通API: 获取支付方式统计');

            // 获取当前月份
            $now = Carbon::now();
            $currentMonthStart = $now->copy()->startOfMonth();
            
            // 查询各支付方式的交易笔数和金额
            $results = DB::connection('payment_db')->table('ddg_sft_detail')
                ->selectRaw('payment_method, 
                            COUNT(*) as count,
                            COALESCE(SUM(transaction_amount), 0) as amount')
                ->whereBetween('transaction_time', [$currentMonthStart, $now])
                ->groupBy('payment_method')
                ->orderBy('amount', 'desc')
                ->get();
            
            // 处理结果，确保包含所有支付方式
            $paymentStats = [
                '微信' => ['count' => 0, 'amount' => 0],
                '支付宝' => ['count' => 0, 'amount' => 0],
                '银联' => ['count' => 0, 'amount' => 0]
            ];
            
            $totalAmount = 0;
            foreach ($results as $row) {
                if (isset($paymentStats[$row->payment_method])) {
                    $paymentStats[$row->payment_method]['count'] = $row->count;
                    $paymentStats[$row->payment_method]['amount'] = (float) $row->amount;
                    $totalAmount += (float) $row->amount;
                }
            }

            // 计算占比并格式化输出
            $paymentDistribution = [];
            foreach ($paymentStats as $method => $stats) {
                $percentage = $totalAmount > 0 ? round(($stats['amount'] / $totalAmount * 100), 1) : 0;
                $paymentDistribution[] = [
                    'name' => $method,
                    'value' => $stats['count'],
                    'amount' => $stats['amount'],
                    'percentage' => $percentage
                ];
            }

            Log::info('盛付通API: 支付方式统计', ['data' => $paymentDistribution]);

            return response()->json([
                'code' => 200,
                'message' => 'success',
                'data' => $paymentDistribution
            ]);

        } catch (\Exception $e) {
            Log::error('盛付通API: 获取支付统计失败 - ' . $e->getMessage());
            return response()->json([
                'code' => 500,
                'message' => '获取支付统计失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 获取商户排行
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getMerchantRanking(Request $request)
    {
        try {
            $limit = $request->get('limit', 10);
            Log::info('盛付通API: 获取商户排行', ['limit' => $limit]);

            // 获取数据库中最新的交易时间，以此为基准计算排行
            $latestTransaction = DB::connection('payment_db')->table('ddg_sft_detail')
                ->orderBy('transaction_time', 'desc')
                ->first();
                
            if (!$latestTransaction) {
                return response()->json([
                    'code' => 200,
                    'message' => 'success',
                    'data' => []
                ]);
            }

            // 使用数据中的最新时间作为当前时间
            $now = Carbon::parse($latestTransaction->transaction_time);
            $currentMonthStart = $now->copy()->startOfMonth();

            $ranking = DB::connection('payment_db')->table('ddg_sft_detail')
                ->select([
                    'merchant_id',
                    'merchant_name as name',
                    'reseller_name as reseller'
                ])
                ->selectRaw('
                    merchant_id as merchant_no,
                    COALESCE(SUM(transaction_amount), 0) as amount,
                    COUNT(*) as count
                ')
                ->where('transaction_time', '>=', $currentMonthStart->toDateTimeString())
                ->where('transaction_time', '<=', $now->toDateTimeString())
                ->groupBy('merchant_id', 'merchant_name', 'reseller_name')
                ->orderByDesc('amount')
                ->limit($limit)
                ->get();

            return response()->json([
                'code' => 200,
                'message' => 'success',
                'data' => $ranking->map(function ($item) {
                    return [
                        'merchant_no' => $item->merchant_no,
                        'name' => $item->name,
                        'reseller' => $item->reseller,
                        'amount' => (float) $item->amount,
                        'count' => (int) $item->count
                    ];
                })
            ]);

        } catch (\Exception $e) {
            Log::error('盛付通API: 获取商户排行失败 - ' . $e->getMessage());
            return response()->json([
                'code' => 500,
                'message' => '获取商户排行失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 获取渠道排行
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getChannelRanking(Request $request)
    {
        try {
            $limit = $request->get('limit', 10);
            Log::info('盛付通API: 获取渠道排行', ['limit' => $limit]);

            // 获取数据库中最新的交易时间，以此为基准计算排行
            $latestTransaction = DB::connection('payment_db')->table('ddg_sft_detail')
                ->orderBy('transaction_time', 'desc')
                ->first();
                
            if (!$latestTransaction) {
                return response()->json([
                    'code' => 200,
                    'message' => 'success',
                    'data' => []
                ]);
            }

            // 使用数据中的最新时间作为当前时间
            $now = Carbon::parse($latestTransaction->transaction_time);
            $currentMonthStart = $now->copy()->startOfMonth();

            $ranking = DB::connection('payment_db')->table('ddg_sft_detail as d')
                ->leftJoin('ddg_institution as i', 'd.reseller_id', '=', 'i.xs_number')
                ->leftJoin('ddg_institution as parent', 'i.institution_id', '=', 'parent.id')
                ->select([
                    'd.reseller_id',
                    'd.reseller_name as name',
                    'd.reseller_id as code',
                    'parent.name as parent'
                ])
                ->selectRaw('
                    COALESCE(SUM(d.transaction_amount), 0) as transaction,
                    COALESCE(SUM(d.reseller_commission), 0) as commission
                ')
                ->where('d.transaction_time', '>=', $currentMonthStart->toDateTimeString())
                ->where('d.transaction_time', '<=', $now->toDateTimeString())
                ->whereNotNull('d.reseller_id')  // 过滤掉reseller_id为空的记录
                ->where('d.reseller_id', '!=', '')  // 过滤掉reseller_id为空字符串的记录
                ->whereNotNull('d.reseller_name')  // 过滤掉reseller_name为空的记录
                ->where('d.reseller_name', '!=', '')  // 过滤掉reseller_name为空字符串的记录
                ->groupBy('d.reseller_id', 'd.reseller_name', 'parent.name')
                ->orderByDesc('transaction')
                ->limit($limit)
                ->get();

            return response()->json([
                'code' => 200,
                'message' => 'success',
                'data' => $ranking->map(function ($item) {
                    return [
                        'code' => $item->code ?: '--',  // 如果编号为空，显示 --
                        'name' => $item->name ?: '--',  // 如果名称为空，显示 --
                        'parent' => $item->parent ?: '--',  // 如果没有上级机构，显示 --
                        'transaction' => (float) $item->transaction,
                        'commission' => (float) $item->commission
                    ];
                })
            ]);

        } catch (\Exception $e) {
            Log::error('盛付通API: 获取渠道排行失败 - ' . $e->getMessage());
            return response()->json([
                'code' => 500,
                'message' => '获取渠道排行失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 获取实时交易数据
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getRealtimeTransactions(Request $request)
    {
        try {
            $limit = $request->get('limit', 20);
            
            Log::info('盛付通API: 获取实时交易数据', ['limit' => $limit]);

            // 获取今天的最新交易记录
            $today = Carbon::now()->startOfDay();
            $now = Carbon::now();
            
            $results = DB::connection('payment_db')->table('ddg_sft_detail')
                ->select([
                    'transaction_time',
                    'merchant_id',
                    'merchant_name',
                    'payment_method',
                    'transaction_amount'
                ])
                ->whereBetween('transaction_time', [$today, $now])
                ->orderBy('transaction_time', 'desc')
                ->limit($limit)
                ->get();
            
            // 处理结果
            $transactions = [];
            foreach ($results as $row) {
                $transactions[] = [
                    'time' => Carbon::parse($row->transaction_time)->format('H:i:s'),
                    'orderNo' => 'SFT' . date('Ymd') . $row->merchant_id . rand(100, 999),
                    'merchant' => $row->merchant_name ?? '未知商户',
                    'channel' => $row->payment_method ?? '未知',
                    'amount' => number_format((float) $row->transaction_amount, 2),
                    'status' => '成功' // 假设所有记录都是成功的
                ];
            }
            
            // 如果没有今天的数据，返回模拟数据
            if (empty($transactions)) {
                for ($i = 0; $i < min($limit, 5); $i++) {
                    $transactions[] = [
                        'time' => date('H:i:s', time() - rand(0, 3600)),
                        'orderNo' => 'SFT' . date('Ymd') . str_pad($i + 1, 6, '0', STR_PAD_LEFT),
                        'merchant' => '测试商户' . ($i + 1),
                        'channel' => ['微信', '支付宝', '银联'][rand(0, 2)],
                        'amount' => number_format(rand(100, 50000), 2),
                        'status' => rand(0, 1) ? '成功' : '失败'
                    ];
                }
            }

            return response()->json([
                'code' => 200,
                'message' => 'success',
                'data' => $transactions
            ]);

        } catch (\Exception $e) {
            Log::error('盛付通API: 获取实时交易数据失败 - ' . $e->getMessage());
            return response()->json([
                'code' => 500,
                'message' => '获取实时交易数据失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 获取日数据
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getDailyData(Request $request)
    {
        try {
            $page = $request->get('page', 1);
            $size = $request->get('size', 20);
            $dateRange = $request->get('dateRange', []);
            $merchantNo = $request->get('merchantNo', '');

            Log::info('盛付通API: 获取日数据', [
                'page' => $page,
                'size' => $size,
                'dateRange' => $dateRange,
                'merchantNo' => $merchantNo
            ]);

            $query = DB::connection('payment_db')->table('sft_institution_count_history as h')
                ->join('ddg_institution as i', 'h.institution_id', '=', 'i.id')
                ->select([
                    'h.summary_day as date',
                    'i.xs_number as merchantNo',
                    'i.name as merchantName',
                    'h.total_transaction as transactionAmount',
                    'h.direct_commission as commission',
                    DB::raw('1 as transactionCount'),
                    DB::raw('"success" as status'),
                    DB::raw('NOW() as updatedAt')
                ]);

            // 添加日期范围过滤
            if (!empty($dateRange) && count($dateRange) === 2) {
                $query->whereBetween('h.summary_day', $dateRange);
            }

            // 添加商户号过滤
            if (!empty($merchantNo)) {
                $query->where('i.xs_number', 'like', "%{$merchantNo}%");
            }

            $total = $query->count();
            
            $data = $query->orderBy('h.summary_day', 'desc')
                ->offset(($page - 1) * $size)
                ->limit($size)
                ->get()
                ->toArray();

            return response()->json([
                'code' => 200,
                'message' => '获取日数据成功',
                'data' => [
                    'list' => $data,
                    'total' => $total,
                    'page' => $page,
                    'size' => $size
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('盛付通API: 获取日数据失败 - ' . $e->getMessage());
            return response()->json([
                'code' => 500,
                'message' => '获取日数据失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 获取月数据列表
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getMonthlyData(Request $request)
    {
        try {
            $page = $request->get('page', 1);
            $size = $request->get('size', 15);
            $search = $request->get('search', '');
            $lv = $request->get('lv', '');
            $superInstitution = $request->get('super_institution', '');
            $month = $request->get('month', date('Y-m'));
            $sortBy = $request->get('sort_by', 'current_month_total_transaction');
            $order = $request->get('order', 'desc');

            Log::info('盛付通API: 获取月数据列表', [
                'page' => $page,
                'size' => $size,
                'search' => $search,
                'lv' => $lv,
                'super_institution' => $superInstitution,
                'month' => $month,
                'sort_by' => $sortBy,
                'order' => $order
            ]);

            // 构建基础查询
            $query = DB::connection('payment_db')->table('ddg_institution as si')
                ->leftJoin('sft_institution_count as sc', 'si.id', '=', 'sc.institution_id')
                ->leftJoin('ddg_institution as parent', 'si.institution_id', '=', 'parent.id')
                ->select([
                    'si.id as institution_id',
                    'si.name as institution_name',
                    'si.xs_number',
                    'parent.name as super_institution_name',
                    'si.lv as institution_lv',
                    'sc.current_month_total_transaction',
                    'sc.current_month_direct_transaction',
                    'sc.current_month_direct_commission',
                    'sc.commission_difference',
                    'sc.current_month_total_commission',
                    'sc.summary_period'
                ]);

            // 添加月份过滤
            if ($month) {
                $query->where('sc.summary_period', $month);
            }

            // 添加搜索条件
            if ($search) {
                $query->where(function($q) use ($search) {
                    $q->where('si.name', 'like', "%{$search}%")
                      ->orWhere('si.xs_number', 'like', "%{$search}%");
                });
            }

            // 添加等级过滤
            if ($lv) {
                $query->where('si.lv', $lv);
            }

            // 添加上级机构过滤
            if ($superInstitution) {
                $query->where('parent.name', 'like', "%{$superInstitution}%");
            }

            // 获取总记录数
            $total = $query->count();

            // 添加排序
            $sortFields = [
                'current_month_total_transaction' => 'sc.current_month_total_transaction',
                'current_month_direct_transaction' => 'sc.current_month_direct_transaction',
                'current_month_direct_commission' => 'sc.current_month_direct_commission',
                'commission_difference' => 'sc.commission_difference',
                'current_month_total_commission' => 'sc.current_month_total_commission',
                'institution_id' => 'si.id'
            ];
            $sortField = $sortFields[$sortBy] ?? 'sc.current_month_total_transaction';
            $query->orderBy($sortField, $order);

            // 添加分页
            $data = $query->offset(($page - 1) * $size)
                ->limit($size)
                ->get();

            // 处理数据
            $monthList = [];
            foreach ($data as $row) {
                $monthList[] = [
                    'institution_id' => $row->institution_id,
                    'institution_name' => $row->institution_name ?: '--',
                    'xs_number' => $row->xs_number ?: '--',
                    'super_institution_name' => $row->super_institution_name ?: '--',
                    'institution_lv' => $row->institution_lv,
                    'current_month_total_transaction' => (float) ($row->current_month_total_transaction ?? 0),
                    'current_month_direct_transaction' => (float) ($row->current_month_direct_transaction ?? 0),
                    'current_month_direct_commission' => (float) ($row->current_month_direct_commission ?? 0),
                    'commission_difference' => (float) ($row->commission_difference ?? 0),
                    'current_month_total_commission' => (float) ($row->current_month_total_commission ?? 0)
                ];
            }

            return response()->json([
                'code' => 200,
                'message' => '获取月数据成功',
                'data' => [
                    'list' => $monthList,
                    'total' => $total,
                    'page' => $page,
                    'size' => $size
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('盛付通API: 获取月数据失败 - ' . $e->getMessage());
            return response()->json([
                'code' => 500,
                'message' => '获取月数据失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 计算月数据
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function calculateMonthly(Request $request)
    {
        try {
            $month = $request->input('month');
            $institutionId = $request->input('institution_id');

            Log::info('盛付通API: 开始计算月数据', [
                'month' => $month,
                'institution_id' => $institutionId
            ]);

            if (!$month) {
                return response()->json([
                    'code' => 400,
                    'message' => '请选择月份'
                ]);
            }

            DB::connection('payment_db')->beginTransaction();

            try {
                // 获取要计算的机构列表
                $institutionsQuery = DB::connection('payment_db')->table('ddg_institution');
                if ($institutionId) {
                    $institutionsQuery->where('id', $institutionId);
                }
                $institutions = $institutionsQuery->get();

                // 步骤1：计算所有机构的直营交易和直营分润
                Log::info('计算直营交易和分润');
                foreach ($institutions as $institution) {
                    $directData = $this->calculateDirectData($institution->xs_number, $month);
                    
                    DB::connection('payment_db')->table('sft_institution_count')
                        ->updateOrInsert(
                            [
                                'institution_id' => $institution->id,
                                'summary_period' => $month
                            ],
                            [
                                'current_month_direct_transaction' => $directData['transaction'],
                                'current_month_direct_commission' => $directData['commission']
                            ]
                        );
                }

                // 步骤2：计算所有机构的总交易量
                Log::info('开始计算总交易量');
                foreach ($institutions as $institution) {
                    $totalTransaction = $this->calculateTotalTransaction($institution->id, $month);
                    
                    DB::connection('payment_db')->table('sft_institution_count')
                        ->where('institution_id', $institution->id)
                        ->where('summary_period', $month)
                        ->update([
                            'current_month_total_transaction' => $totalTransaction
                        ]);
                }

                // 步骤3：计算所有机构的分润差额
                Log::info('开始计算分润差额');
                foreach ($institutions as $institution) {
                    $parentRate = $this->getCommissionRate($institution->lv);
                    $commissionDifference = $this->calculateCommissionDifference($institution->id, $month, $parentRate);
                    
                    DB::connection('payment_db')->table('sft_institution_count')
                        ->where('institution_id', $institution->id)
                        ->where('summary_period', $month)
                        ->update([
                            'commission_difference' => $commissionDifference
                        ]);
                }

                // 步骤4：计算所有机构的总分润
                Log::info('开始计算总分润');
                foreach ($institutions as $institution) {
                    $countData = DB::connection('payment_db')->table('sft_institution_count')
                        ->where('institution_id', $institution->id)
                        ->where('summary_period', $month)
                        ->first();
                    
                    if ($countData) {
                        $directCommission = (float) $countData->current_month_direct_commission;
                        $commissionDifference = (float) $countData->commission_difference;
                        $totalCommission = $directCommission + $commissionDifference;
                        
                        DB::connection('payment_db')->table('sft_institution_count')
                            ->where('institution_id', $institution->id)
                            ->where('summary_period', $month)
                            ->update([
                                'current_month_total_commission' => $totalCommission
                            ]);
                    }
                }

                DB::connection('payment_db')->commit();
                Log::info('月数据计算完成');

                return response()->json([
                    'code' => 200,
                    'message' => '计算成功'
                ]);

            } catch (\Exception $e) {
                DB::connection('payment_db')->rollback();
                throw $e;
            }

        } catch (\Exception $e) {
            Log::error('盛付通API: 计算月数据失败 - ' . $e->getMessage());
            return response()->json([
                'code' => 500,
                'message' => '计算失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 检查月数据
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function checkMonthlyData(Request $request)
    {
        try {
            $month = $request->input('month');

            Log::info('盛付通API: 检查月数据', ['month' => $month]);

            if (!$month) {
                return response()->json([
                    'code' => 400,
                    'message' => '请选择月份'
                ]);
            }

            // 检查月份是否有数据
            $count = DB::connection('payment_db')->table('ddg_sft_reseller_sum')
                ->whereRaw('LEFT(summary_period, 7) = ?', [$month])
                ->count();

            Log::info('查询结果', ['count' => $count]);

            if ($count == 0) {
                // 获取最近5个月份的数据用于诊断
                $availableMonths = DB::connection('payment_db')->table('ddg_sft_reseller_sum')
                    ->selectRaw('DISTINCT LEFT(summary_period, 7) as month')
                    ->orderBy('month', 'desc')
                    ->limit(5)
                    ->pluck('month');

                Log::info('最近5个月份的数据', ['months' => $availableMonths->toArray()]);

                return response()->json([
                    'code' => 400,
                    'message' => '该月份没有数据，请重新选择',
                    'available_months' => $availableMonths
                ]);
            }

            return response()->json([
                'code' => 200,
                'message' => '数据检查通过'
            ]);

        } catch (\Exception $e) {
            Log::error('盛付通API: 检查月数据失败 - ' . $e->getMessage());
            return response()->json([
                'code' => 500,
                'message' => '检查失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 计算直营交易和分润
     */
    private function calculateDirectData($xsNumber, $month)
    {
        $data = DB::connection('payment_db')->table('ddg_sft_reseller_sum')
            ->selectRaw('SUM(actual_amount) as amount, SUM(reseller_commission) as commission')
            ->where('reseller_id', $xsNumber)
            ->whereRaw('LEFT(summary_period, 7) = ?', [$month])
            ->first();

        return [
            'transaction' => (float) ($data->amount ?? 0),
            'commission' => (float) ($data->commission ?? 0)
        ];
    }

    /**
     * 计算总交易量
     */
    private function calculateTotalTransaction($institutionId, $month)
    {
        $totalAmount = 0;

        // 获取当前机构的直营交易
        $directData = DB::connection('payment_db')->table('sft_institution_count')
            ->where('institution_id', $institutionId)
            ->where('summary_period', $month)
            ->first();

        if ($directData) {
            $totalAmount += (float) $directData->current_month_direct_transaction;
        }

        // 获取并计算下级机构的交易量
        $subInstitutions = DB::connection('payment_db')->table('ddg_institution')
            ->where('institution_id', $institutionId)
            ->get();

        foreach ($subInstitutions as $sub) {
            $subTotal = $this->calculateTotalTransaction($sub->id, $month);
            $totalAmount += $subTotal;
        }

        return $totalAmount;
    }

    /**
     * 计算分润差额
     */
    private function calculateCommissionDifference($institutionId, $month, $parentRate)
    {
        $totalCommissionDifference = 0;

        // 查询下级机构的交易量
        $subInstitutions = DB::connection('payment_db')->table('sft_institution_count as c')
            ->join('ddg_institution as i', 'c.institution_id', '=', 'i.id')
            ->where('i.institution_id', $institutionId)
            ->where('c.summary_period', $month)
            ->select('c.institution_id', 'c.current_month_total_transaction', 'i.lv')
            ->get();

        foreach ($subInstitutions as $sub) {
            $subTotalTransaction = (float) $sub->current_month_total_transaction;
            $subRate = $this->getCommissionRate($sub->lv);

            // 如果上级分润比例大于子机构分润比例，计算分润差额
            if ($parentRate > $subRate) {
                $commissionDifference = $subTotalTransaction * ($parentRate - $subRate);
            } else {
                $commissionDifference = 0;
            }

            $totalCommissionDifference += $commissionDifference;
        }

        return $totalCommissionDifference;
    }

    /**
     * 获取分润比率
     */
    private function getCommissionRate($lv)
    {
        $rateMap = [
            1 => 0.0008,
            2 => 0.0010,
            3 => 0.0012,
            4 => 0.0013,
            5 => 0.0014,
            6 => 0.0015,
            7 => 0.0016,
            8 => 0.0017,
        ];

        return $rateMap[$lv] ?? 0.0008;
    }

    /**
     * 数据上传
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function uploadData(Request $request)
    {
        try {
            Log::info('盛付通API: 开始处理文件上传', ['request_data' => $request->all()]);

            // 验证上传文件
            $validator = Validator::make($request->all(), [
                'file' => 'required|file|max:10240'
            ], [
                'file.required' => '请选择要上传的文件',
                'file.max' => '文件大小不能超过10MB'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'code' => 400,
                    'message' => '文件验证失败',
                    'errors' => $validator->errors()
                ]);
            }

            $file = $request->file('file');
            $originalFilename = $file->getClientOriginalName();
            
            // 验证文件名格式
            if (!$this->validateFilename($originalFilename)) {
                return response()->json([
                    'code' => 400,
                    'message' => '文件名格式不正确，请按照规定格式命名：42083878_YYYY-MM-DD_表名_YYYY-MM-DD.csv 或 42083878_YYYY-MM-DD_DETAIL_YYYY-MM-DD_数字.csv'
                ]);
            }

            // 解析文件名
            $parsedInfo = $this->parseFilename($originalFilename);
            if (!$parsedInfo) {
                return response()->json([
                    'code' => 400,
                    'message' => '文件名解析失败，请检查文件名格式'
                ]);
            }

            $dataTime = $parsedInfo['data_time'];
            $tableName = $parsedInfo['table_name'];
            $downloadTime = $parsedInfo['download_time'];

            Log::info('盛付通API: 文件名解析成功', [
                'data_time' => $dataTime,
                'table_name' => $tableName,
                'download_time' => $downloadTime
            ]);

            // 检查文件是否已经上传过
            $existingLog = SftUploadLog::where('original_filename', $originalFilename)->first();
            if ($existingLog) {
                return response()->json([
                    'code' => 400,
                    'message' => '该文件已经上传，请勿重复上传'
                ]);
            }

            // 保存文件
            $filename = time() . '_' . $originalFilename;
            $path = $file->storeAs('shengfutong/uploads', $filename);
            $fullPath = storage_path('app/' . $path);

            Log::info('盛付通API: 文件保存成功', [
                'original_name' => $originalFilename,
                'saved_name' => $filename,
                'path' => $path,
                'full_path' => $fullPath,
                'size' => $file->getSize()
            ]);

            // 创建上传日志记录
            $uploadLog = SftUploadLog::create([
                'filename' => $filename,
                'original_filename' => $originalFilename,
                'data_time' => $dataTime,
                'table_name' => $tableName,
                'file_path' => $path,
                'file_size' => $file->getSize(),
                'status' => SftUploadLog::STATUS_PROCESSING,
                'message' => '文件上传成功，开始处理数据',
                'uploader_id' => 1, // 暂时使用固定值
                'upload_type' => SftUploadLog::TYPE_MANUAL,
                'upload_time' => now()
            ]);

            // 处理文件并插入数据
            $result = $this->processFile($fullPath, $tableName, $dataTime, $uploadLog);

            if ($result['success']) {
                // 更新上传日志状态
                $uploadLog->update([
                    'status' => SftUploadLog::STATUS_SUCCESS,
                    'message' => "文件处理成功，插入了 {$result['inserted_count']} 条数据",
                    'processed_rows' => $result['inserted_count'],
                    'insert_success_time' => now()
                ]);

                return response()->json([
                    'code' => 200,
                    'message' => "文件上传成功，插入了 {$result['inserted_count']} 条数据到 {$tableName} 表中",
                    'data' => [
                        'filename' => $filename,
                        'original_filename' => $originalFilename,
                        'path' => $path,
                        'size' => $file->getSize(),
                        'table_name' => $tableName,
                        'data_time' => $dataTime,
                        'inserted_count' => $result['inserted_count'],
                        'uploadTime' => now()->format('Y-m-d H:i:s')
                    ]
                ]);
            } else {
                // 更新上传日志状态为失败，限制错误消息长度
                $errorMessage = $result['error_message'];
                if (strlen($errorMessage) > 500) {
                    $errorMessage = substr($errorMessage, 0, 500) . '...';
                }
                
                $uploadLog->update([
                    'status' => SftUploadLog::STATUS_FAILED,
                    'message' => $errorMessage
                ]);

                return response()->json([
                    'code' => 500,
                    'message' => $result['error_message']
                ]);
            }

        } catch (\Exception $e) {
            Log::error('盛付通API: 文件上传失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'code' => 500,
                'message' => '文件上传失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 导出日数据
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function exportDailyData(Request $request)
    {
        try {
            Log::info('盛付通API: 导出日数据');

            // 这里应该生成Excel文件并返回下载链接
            // 暂时返回成功响应
            return response()->json([
                'code' => 200,
                'message' => '导出成功',
                'data' => [
                    'downloadUrl' => '/storage/exports/shengfutong_daily_' . date('YmdHis') . '.xlsx'
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('盛付通API: 导出日数据失败 - ' . $e->getMessage());
            return response()->json([
                'code' => 500,
                'message' => '导出失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 导出月数据
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function exportMonthlyData(Request $request)
    {
        try {
            Log::info('盛付通API: 导出月数据');

            // 这里应该生成Excel文件并返回下载链接
            // 暂时返回成功响应
            return response()->json([
                'code' => 200,
                'message' => '导出成功',
                'data' => [
                    'downloadUrl' => '/storage/exports/shengfutong_monthly_' . date('YmdHis') . '.xlsx'
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('盛付通API: 导出月数据失败 - ' . $e->getMessage());
            return response()->json([
                'code' => 500,
                'message' => '导出失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 获取余额管理数据
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getBalanceData(Request $request)
    {
        try {
            Log::info('盛付通API: 获取余额管理数据');

            // 模拟余额数据，实际应从数据库获取
            $balanceData = [
                'totalBalance' => 1580000.00,
                'availableBalance' => 1450000.00,
                'frozenBalance' => 80000.00,
                'pendingBalance' => 50000.00
            ];

            return response()->json([
                'code' => 200,
                'message' => '获取余额数据成功',
                'data' => $balanceData
            ]);

        } catch (\Exception $e) {
            Log::error('盛付通API: 获取余额数据失败 - ' . $e->getMessage());
            return response()->json([
                'code' => 500,
                'message' => '获取余额数据失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 获取资金流水
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getBalanceFlow(Request $request)
    {
        try {
            $page = $request->get('page', 1);
            $size = $request->get('size', 20);
            $type = $request->get('type', '');
            $dateRange = $request->get('dateRange', []);

            Log::info('盛付通API: 获取资金流水', [
                'page' => $page,
                'size' => $size,
                'type' => $type,
                'dateRange' => $dateRange
            ]);

            // 模拟流水数据，实际应从数据库获取
            $flowData = [];
            for ($i = 0; $i < $size; $i++) {
                $flowData[] = [
                    'id' => 'F' . str_pad($i + 1, 6, '0', STR_PAD_LEFT),
                    'type' => ['income', 'withdraw', 'freeze', 'unfreeze'][rand(0, 3)],
                    'amount' => rand(1000, 50000),
                    'balance' => rand(1000000, 2000000),
                    'description' => '交易分润',
                    'time' => date('Y-m-d H:i:s', time() - rand(0, 86400 * 30))
                ];
            }

            return response()->json([
                'code' => 200,
                'message' => '获取资金流水成功',
                'data' => [
                    'list' => $flowData,
                    'total' => 1000,
                    'page' => $page,
                    'size' => $size
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('盛付通API: 获取资金流水失败 - ' . $e->getMessage());
            return response()->json([
                'code' => 500,
                'message' => '获取资金流水失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 提现申请
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function withdrawApply(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'amount' => 'required|numeric|min:100',
                'bankCard' => 'required|string',
                'password' => 'required|string'
            ], [
                'amount.required' => '请输入提现金额',
                'amount.numeric' => '提现金额必须为数字',
                'amount.min' => '提现金额不能少于100元',
                'bankCard.required' => '请选择银行卡',
                'password.required' => '请输入支付密码'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'code' => 400,
                    'message' => '参数验证失败',
                    'errors' => $validator->errors()
                ]);
            }

            $amount = $request->get('amount');
            $bankCard = $request->get('bankCard');
            $password = $request->get('password');

            Log::info('盛付通API: 提现申请', [
                'amount' => $amount,
                'bankCard' => $bankCard
            ]);

            // 这里应该添加提现逻辑
            // 暂时返回成功响应
            return response()->json([
                'code' => 200,
                'message' => '提现申请提交成功，请等待审核',
                'data' => [
                    'withdrawId' => 'W' . date('YmdHis') . rand(1000, 9999),
                    'amount' => $amount,
                    'status' => 'pending'
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('盛付通API: 提现申请失败 - ' . $e->getMessage());
            return response()->json([
                'code' => 500,
                'message' => '提现申请失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 充值
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function recharge(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'amount' => 'required|numeric|min:1',
                'paymentMethod' => 'required|string'
            ], [
                'amount.required' => '请输入充值金额',
                'amount.numeric' => '充值金额必须为数字',
                'amount.min' => '充值金额不能少于1元',
                'paymentMethod.required' => '请选择支付方式'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'code' => 400,
                    'message' => '参数验证失败',
                    'errors' => $validator->errors()
                ]);
            }

            $amount = $request->get('amount');
            $paymentMethod = $request->get('paymentMethod');

            Log::info('盛付通API: 充值', [
                'amount' => $amount,
                'paymentMethod' => $paymentMethod
            ]);

            // 这里应该添加充值逻辑
            // 暂时返回成功响应
            return response()->json([
                'code' => 200,
                'message' => '充值成功',
                'data' => [
                    'rechargeId' => 'R' . date('YmdHis') . rand(1000, 9999),
                    'amount' => $amount,
                    'status' => 'success'
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('盛付通API: 充值失败 - ' . $e->getMessage());
            return response()->json([
                'code' => 500,
                'message' => '充值失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 获取提现列表
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getWithdrawalList(Request $request)
    {
        try {
            $page = $request->get('page', 1);
            $limit = $request->get('limit', 10);
            $search = $request->get('search', '');
            $status = $request->get('status', '');
            $date = $request->get('date', '');

            Log::info('盛付通API: 获取提现列表', [
                'page' => $page,
                'limit' => $limit,
                'search' => $search,
                'status' => $status,
                'date' => $date
            ]);

            // 模拟提现数据，实际应从数据库获取
            $withdrawals = [];
            for ($i = 0; $i < $limit; $i++) {
                $withdrawals[] = [
                    'withdrawal_id' => 'W' . date('Ymd') . str_pad($i + 1, 6, '0', STR_PAD_LEFT),
                    'institution_id' => 1000 + $i,
                    'institution_name' => '测试机构' . ($i + 1),
                    'withdrawal_amount' => rand(1000, 50000),
                    'actual_amount' => rand(900, 49000),
                    'status' => ['1', '2', '3'][rand(0, 2)],
                    'update_time' => date('Y-m-d H:i:s', time() - rand(0, 86400 * 7)),
                    'created_at' => date('Y-m-d H:i:s', time() - rand(86400, 86400 * 30)),
                    'withdrawal_time' => date('Y-m-d H:i:s', time() - rand(0, 86400 * 3)),
                    'alipay_openid' => rand(0, 1) ? 'alipay_' . uniqid() : null
                ];
            }

            return response()->json([
                'code' => 200,
                'message' => '获取提现列表成功',
                'data' => [
                    'items' => $withdrawals,
                    'total' => 1000
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('盛付通API: 获取提现列表失败 - ' . $e->getMessage());
            return response()->json([
                'code' => 500,
                'message' => '获取提现列表失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 获取提现统计数据
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getWithdrawalStatistics(Request $request)
    {
        try {
            Log::info('盛付通API: 获取提现统计数据');

            // 模拟统计数据，实际应从数据库获取
            $statistics = [
                'pending_amount' => 158000.00,
                'paid_amount' => 2680000.00,
                'rejected_amount' => 25000.00,
                'alipay_balance' => 1580000.00
            ];

            return response()->json([
                'code' => 200,
                'message' => '获取提现统计成功',
                'data' => $statistics
            ]);

        } catch (\Exception $e) {
            Log::error('盛付通API: 获取提现统计失败 - ' . $e->getMessage());
            return response()->json([
                'code' => 500,
                'message' => '获取提现统计失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 审核提现申请
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function auditWithdrawal(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'withdrawal_id' => 'required|string',
                'status' => 'required|in:1,2',
                'password' => 'required|string'
            ], [
                'withdrawal_id.required' => '提现ID不能为空',
                'status.required' => '审核状态不能为空',
                'status.in' => '审核状态无效',
                'password.required' => '管理密码不能为空'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'code' => 400,
                    'message' => '参数验证失败',
                    'errors' => $validator->errors()
                ]);
            }

            $withdrawalId = $request->get('withdrawal_id');
            $status = $request->get('status');
            $password = $request->get('password');

            Log::info('盛付通API: 审核提现申请', [
                'withdrawal_id' => $withdrawalId,
                'status' => $status
            ]);

            // 验证管理密码（这里应该从配置或数据库获取）
            if ($password !== 'admin123') {
                return response()->json([
                    'code' => 40001,
                    'message' => '管理密码错误'
                ]);
            }

            // 这里应该添加实际的审核逻辑
            // 包括更新数据库状态、调用支付宝转账接口等

            return response()->json([
                'code' => 200,
                'message' => '审核成功',
                'data' => [
                    'withdrawal_id' => $withdrawalId,
                    'status' => $status,
                    'audit_time' => date('Y-m-d H:i:s')
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('盛付通API: 审核提现失败 - ' . $e->getMessage());
            return response()->json([
                'code' => 500,
                'message' => '审核提现失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 获取支付宝余额
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getAlipayBalance(Request $request)
    {
        try {
            Log::info('盛付通API: 获取支付宝余额');

            // 模拟支付宝余额，实际应调用支付宝API获取
            $balance = 1580000.00;

            return response()->json([
                'code' => 200,
                'message' => '获取支付宝余额成功',
                'data' => [
                    'balance' => $balance
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('盛付通API: 获取支付宝余额失败 - ' . $e->getMessage());
            return response()->json([
                'code' => 500,
                'message' => '获取支付宝余额失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 验证文件名格式
     */
    private function validateFilename($filename)
    {
        // 支持两种格式：
        // 1. 42083878_YYYY-MM-DD_表名_YYYY-MM-DD.csv
        // 2. 42083878_YYYY-MM-DD_表名_YYYY-MM-DD_数字.csv (DETAIL表的新格式)
        $pattern = '/^42083878_\d{4}-\d{2}-\d{2}_(AGENT_MCH_SUM|DETAIL|RESELLER_SUM)_\d{4}-\d{2}-\d{2}(_\d+)?\.(csv|xlsx|xls)$/i';
        return preg_match($pattern, $filename);
    }

    /**
     * 处理上传的文件
     */
    private function processUploadedFile($filePath, $tableName, $dataTime, $uploadLog)
    {
        try {
            Log::info('盛付通API: 开始处理文件数据', [
                'file_path' => $filePath,
                'table_name' => $tableName,
                'data_time' => $dataTime
            ]);

            // 读取CSV文件
            $data = $this->readCsvFile($filePath);
            if (empty($data)) {
                return [
                    'success' => false,
                    'message' => '文件为空或格式错误',
                    'count' => 0
                ];
            }

            // 获取字段映射
            $fieldMapping = $this->getFieldMapping($tableName);
            if (empty($fieldMapping)) {
                return [
                    'success' => false,
                    'message' => '不支持的表名: ' . $tableName,
                    'count' => 0
                ];
            }

            // 处理数据
            $processedData = $this->processDataForTable($data, $fieldMapping, $tableName, $dataTime);
            if (empty($processedData)) {
                return [
                    'success' => false,
                    'message' => '没有有效的数据可以插入，请检查文件格式和表头是否正确',
                    'count' => 0
                ];
            }

            // 批量插入数据
            $insertedCount = $this->batchInsertData($tableName, $processedData);

            Log::info('盛付通API: 文件处理完成', [
                'table_name' => $tableName,
                'inserted_count' => $insertedCount
            ]);

            return [
                'success' => true,
                'message' => '数据处理成功',
                'count' => $insertedCount
            ];

        } catch (\Exception $e) {
            Log::error('盛付通API: 文件处理失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'message' => '文件处理失败: ' . $e->getMessage(),
                'count' => 0
            ];
        }
    }

    /**
     * 读取CSV文件
     */
    private function readCsvFile($filePath)
    {
        $data = [];
        $handle = fopen($filePath, 'r');
        
        if ($handle !== false) {
            // 读取表头
            $headers = fgetcsv($handle);
            if ($headers === false) {
                fclose($handle);
                return [];
            }

            // 读取数据行
            while (($row = fgetcsv($handle)) !== false) {
                if (count($row) === count($headers)) {
                    $data[] = array_combine($headers, $row);
                }
            }
            fclose($handle);
        }

        return $data;
    }



    /**
     * 处理表数据
     */
    private function processDataForTable($data, $fieldMapping, $tableName, $dataTime)
    {
        $processedData = [];

        foreach ($data as $row) {
            $processedRow = [];
            $hasValidData = false;

            foreach ($fieldMapping as $csvField => $dbField) {
                if (isset($row[$csvField])) {
                    $value = trim($row[$csvField]);
                    
                    // 处理特殊字段
                    if ($dbField === 'transaction_time' && !empty($value)) {
                        // 转换时间格式
                        try {
                            $processedRow[$dbField] = date('Y-m-d H:i:s', strtotime($value));
                        } catch (\Exception $e) {
                            $processedRow[$dbField] = null;
                        }
                    } elseif (in_array($dbField, ['success_amount', 'refund_amount', 'actual_amount', 'agent_commission', 'reseller_commission', 'should_receive_commission', 'transaction_amount', 'handling_fee'])) {
                        // 处理金额字段
                        $processedRow[$dbField] = !empty($value) ? floatval($value) : 0.00;
                    } elseif (in_array($dbField, ['success_count', 'refund_count'])) {
                        // 处理数量字段
                        $processedRow[$dbField] = !empty($value) ? intval($value) : 0;
                    } else {
                        $processedRow[$dbField] = $value;
                    }

                    if (!empty($value)) {
                        $hasValidData = true;
                    }
                }
            }

            // 设置默认值
            if (in_array($tableName, ['ddg_sft_agent_mch_sum', 'ddg_sft_reseller_sum'])) {
                if (empty($processedRow['summary_period'])) {
                    $processedRow['summary_period'] = $dataTime;
                }
            }

            // 设置默认的渠道商信息
            if (empty($processedRow['reseller_id'])) {
                $processedRow['reseller_id'] = '42083878';
            }
            if (empty($processedRow['reseller_name'])) {
                $processedRow['reseller_name'] = '点点够';
            }

            if ($hasValidData) {
                $processedData[] = $processedRow;
            }
        }

        return $processedData;
    }

    /**
     * 批量插入数据
     */
    private function batchInsertData($tableName, $data)
    {
        $batchSize = 100;
        $totalInserted = 0;
        $chunks = array_chunk($data, $batchSize);

        foreach ($chunks as $chunk) {
            try {
                DB::connection('payment_db')->table($tableName)->insert($chunk);
                $totalInserted += count($chunk);
            } catch (\Exception $e) {
                Log::error('盛付通API: 批量插入数据失败', [
                    'table_name' => $tableName,
                    'chunk_size' => count($chunk),
                    'error' => $e->getMessage()
                ]);
                throw $e;
            }
        }

        return $totalInserted;
    }

    /**
     * 解析文件名
     */
    private function parseFilename($filename)
    {
        // 支持两种格式：
        // 1. 42083878_YYYY-MM-DD_表名_YYYY-MM-DD.csv
        // 2. 42083878_YYYY-MM-DD_表名_YYYY-MM-DD_数字.csv
        $pattern = '/^(\d{8})_(\d{4}-\d{2}-\d{2})_(\w+)_(\d{4}-\d{2}-\d{2})(_\d+)?\.(csv|xlsx|xls)$/i';
        if (preg_match($pattern, $filename, $matches)) {
            $tableMapping = [
                'AGENT_MCH_SUM' => 'ddg_sft_agent_mch_sum',
                'DETAIL' => 'ddg_sft_detail',
                'RESELLER_SUM' => 'ddg_sft_reseller_sum'
            ];

            $tableName = $tableMapping[strtoupper($matches[3])] ?? null;
            
            return [
                'merchant_id' => $matches[1],
                'data_time' => $matches[2],
                'table_type' => strtoupper($matches[3]),
                'table_name' => $tableName,
                'download_time' => $matches[4],
                'suffix' => isset($matches[5]) ? ltrim($matches[5], '_') : null, // 去掉前导下划线
                'extension' => strtolower($matches[6]) // 注意索引变化
            ];
        }
        
        return null;
    }

    /**
     * 处理文件并插入数据
     */
    private function processFile($filePath, $tableName, $dataTime, $uploadLog)
    {
        try {
            Log::info('盛付通API: 开始处理文件', [
                'file_path' => $filePath,
                'table_name' => $tableName,
                'data_time' => $dataTime
            ]);

            // 读取文件数据
            $data = $this->readFileData($filePath);
            if (!$data) {
                return ['success' => false, 'error_message' => '无法读取文件数据'];
            }

            Log::info('盛付通API: 文件读取成功', ['rows_count' => count($data)]);

            // 获取字段映射
            $fieldMapping = $this->getFieldMapping($tableName);
            if (!$fieldMapping) {
                return ['success' => false, 'error_message' => "未找到表 {$tableName} 的字段映射"];
            }

            // 处理数据
            $processedData = $this->processData($data, $fieldMapping, $tableName, $dataTime);
            if (empty($processedData)) {
                return ['success' => false, 'error_message' => '没有有效的数据可以插入，请检查文件格式和表头是否正确'];
            }

            Log::info('盛付通API: 数据处理完成', ['processed_rows' => count($processedData)]);

            // 检查重复数据
            $filteredData = $this->filterDuplicateData($processedData, $tableName, $dataTime);
            if (empty($filteredData)) {
                return ['success' => false, 'error_message' => '所有数据已存在，无法插入新的数据'];
            }

            Log::info('盛付通API: 重复数据过滤完成', ['final_rows' => count($filteredData)]);

            // 批量插入数据
            $insertedCount = $this->insertDataInBatches($filteredData, $tableName);

            Log::info('盛付通API: 数据插入完成', ['inserted_count' => $insertedCount]);

            return ['success' => true, 'inserted_count' => $insertedCount];

        } catch (\Exception $e) {
            Log::error('盛付通API: 处理文件失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return ['success' => false, 'error_message' => '处理文件失败: ' . $e->getMessage()];
        }
    }

    /**
     * 读取文件数据
     */
    private function readFileData($filePath)
    {
        $extension = pathinfo($filePath, PATHINFO_EXTENSION);
        
        try {
            if (in_array(strtolower($extension), ['xlsx', 'xls'])) {
                // 读取Excel文件
                $spreadsheet = IOFactory::load($filePath);
                $worksheet = $spreadsheet->getActiveSheet();
                $data = $worksheet->toArray();
                
                // 移除空行
                $data = array_filter($data, function($row) {
                    return !empty(array_filter($row));
                });
                
                return array_values($data);
            } else {
                // 读取CSV文件，处理编码问题
                $data = [];
                $handle = fopen($filePath, 'r');
                
                if ($handle === false) {
                    throw new \Exception('无法打开CSV文件');
                }
                
                while (($row = fgetcsv($handle)) !== false) {
                    // 检测并转换编码
                    $convertedRow = [];
                    foreach ($row as $cell) {
                        // 检测编码
                        $encoding = mb_detect_encoding($cell, ['UTF-8', 'GBK', 'GB2312', 'CP936'], true);
                        
                        if ($encoding && $encoding !== 'UTF-8') {
                            // 转换为UTF-8
                            $convertedCell = mb_convert_encoding($cell, 'UTF-8', $encoding);
                        } else {
                            $convertedCell = $cell;
                        }
                        
                        $convertedRow[] = $convertedCell;
                    }
                    $data[] = $convertedRow;
                }
                
                fclose($handle);
                
                // 移除空行
                $data = array_filter($data, function($row) {
                    return !empty(array_filter($row, function($cell) {
                        return trim($cell) !== '';
                    }));
                });
                
                return array_values($data);
            }
        } catch (\Exception $e) {
            Log::error('盛付通API: 读取文件失败', ['error' => $e->getMessage()]);
            return null;
        }
    }

    /**
     * 获取字段映射
     */
    private function getFieldMapping($tableName)
    {
        $mappings = [
            'ddg_sft_agent_mch_sum' => [
                '汇总周期' => 'summary_period',
                '商户号' => 'merchant_id',
                '商户名称' => 'merchant_name',
                '商户归属渠道商id' => 'reseller_id',
                '商户归属渠道商ID' => 'reseller_id',
                '商户归属渠道商名称' => 'reseller_name',
                '支付方式' => 'payment_method',
                '成功金额' => 'success_amount',
                '成功笔数' => 'success_count',
                '退货金额' => 'refund_amount',
                '退货笔数' => 'refund_count',
                '实际金额' => 'actual_amount',
                '本级实收分润' => 'agent_commission',
                '渠道商分润' => 'reseller_commission',
                '本级应收分润' => 'should_receive_commission',
                '商户操作员' => 'operator',
                '分润类型' => 'commission_type'
            ],
            'ddg_sft_detail' => [
                '交易时间' => 'transaction_time',
                '商户号' => 'merchant_id',
                '商户名称' => 'merchant_name',
                '商户归属渠道商id' => 'reseller_id',
                '商户归属渠道商ID' => 'reseller_id',
                '商户归属渠道商名称' => 'reseller_name',
                '订单类型' => 'order_type',
                '支付方式' => 'payment_method',
                '交易金额' => 'transaction_amount',
                '收单手续费' => 'handling_fee',
                '本级实收分润' => 'agent_commission',
                '渠道商分润' => 'reseller_commission',
                '本级应收分润' => 'should_receive_commission',
                '盛付通订单号' => 'order_id',
                '商户订单号' => 'merchant_order_id',
                '商户操作员' => 'operator',
                '分润类型' => 'commission_type'
            ],
            'ddg_sft_reseller_sum' => [
                '汇总周期' => 'summary_period',
                '渠道商id' => 'reseller_id',
                '渠道商名称' => 'reseller_name',
                '支付渠道' => 'payment_channel',
                '成功金额' => 'success_amount',
                '成功笔数' => 'success_count',
                '退货金额' => 'refund_amount',
                '退货笔数' => 'refund_count',
                '实际金额' => 'actual_amount',
                '本级实收分润' => 'agent_commission',
                '渠道商分润' => 'reseller_commission',
                '本级应收分润' => 'should_receive_commission',
                '分润类型' => 'commission_type'
            ]
        ];

        return $mappings[$tableName] ?? null;
    }

    /**
     * 获取必需字段（与Python项目保持一致）
     */
    private function getRequiredFields($tableName)
    {
        // 根据源项目逻辑，只有非空的关键字段才是必需的
        $requiredFields = [
            'ddg_sft_agent_mch_sum' => ['merchant_id'],  // 商户号是关键字段
            'ddg_sft_detail' => ['merchant_id'],         // 商户号是关键字段
            'ddg_sft_reseller_sum' => ['reseller_id']    // 渠道商ID是关键字段
        ];

        return $requiredFields[$tableName] ?? [];
    }

    /**
     * 处理数据
     */
    private function processData($data, $fieldMapping, $tableName, $dataTime)
    {
        if (empty($data)) {
            return [];
        }

        // 获取表头
        $headers = array_shift($data);
        
        // 清理表头，保持原始大小写
        $headers = array_map(function($header) {
            return trim($header);
        }, $headers);

        Log::info('盛付通API: 文件表头', ['headers' => $headers]);
        Log::info('盛付通API: 字段映射', ['field_mapping' => $fieldMapping]);

        // 创建小写字段映射（与Python项目保持一致）
        $lowercaseFieldMapping = [];
        foreach ($fieldMapping as $chineseField => $englishField) {
            $lowercaseFieldMapping[strtolower($chineseField)] = $englishField;
        }

        $processedData = [];
        $mappedFieldsCount = 0;
        
        foreach ($data as $rowIndex => $row) {
            $rowData = [];
            
            // 映射字段
            foreach ($headers as $index => $header) {
                // 将表头转换为小写进行匹配（与Python项目保持一致）
                $lowercaseHeader = strtolower($header);
                $mappedField = null;
                
                if (isset($lowercaseFieldMapping[$lowercaseHeader])) {
                    $mappedField = $lowercaseFieldMapping[$lowercaseHeader];
                }
                
                if ($mappedField) {
                    $value = isset($row[$index]) ? $this->cleanCellValue($row[$index]) : '';
                    $rowData[$mappedField] = $value;
                    $mappedFieldsCount++;
                }
            }

            // 如果汇总周期字段为空，则使用文件名中的日期
            if (in_array($tableName, ['ddg_sft_agent_mch_sum', 'ddg_sft_reseller_sum'])) {
                if (empty($rowData['summary_period'])) {
                    $rowData['summary_period'] = $dataTime;
                }
            }

            // 验证必需字段
            $requiredFields = $this->getRequiredFields($tableName);
            $missingFields = [];
            foreach ($requiredFields as $field) {
                if (empty($rowData[$field])) {
                    $missingFields[] = $field;
                }
            }

            if (!empty($missingFields)) {
                Log::warning('盛付通API: 数据行缺少必需字段', [
                    'row_index' => $rowIndex,
                    'missing_fields' => $missingFields,
                    'row_data' => $rowData
                ]);
                continue; // 跳过这一行
            }

            if (!empty($rowData)) {
                $processedData[] = $rowData;
            }
        }

        Log::info('盛付通API: 数据处理完成', [
            'total_rows' => count($data),
            'processed_rows' => count($processedData),
            'mapped_fields_count' => $mappedFieldsCount
        ]);

        return $processedData;
    }

    /**
     * 过滤重复数据
     */
    private function filterDuplicateData($data, $tableName, $dataTime)
    {
        if (empty($data)) {
            return [];
        }

        if (in_array($tableName, ['ddg_sft_agent_mch_sum', 'ddg_sft_reseller_sum'])) {
            // 检查汇总周期是否已存在
            $existingCount = DB::connection('payment_db')
                ->table($tableName)
                ->where('summary_period', $dataTime)
                ->count();
            
            if ($existingCount > 0) {
                Log::info('盛付通API: 数据时间已存在', ['data_time' => $dataTime, 'table' => $tableName]);
                return [];
            }
        } elseif ($tableName === 'ddg_sft_detail') {
            // 检查订单号是否已存在
            $orderIds = array_column($data, 'order_id');
            $orderIds = array_filter($orderIds); // 移除空值
            
            if (!empty($orderIds)) {
                $existingOrderIds = DB::connection('payment_db')
                    ->table($tableName)
                    ->whereIn('order_id', $orderIds)
                    ->pluck('order_id')
                    ->toArray();
                
                // 过滤掉已存在的订单
                $data = array_filter($data, function($row) use ($existingOrderIds) {
                    return !in_array($row['order_id'], $existingOrderIds);
                });
                
                Log::info('盛付通API: 过滤重复订单', [
                    'total_orders' => count($orderIds),
                    'existing_orders' => count($existingOrderIds),
                    'remaining_orders' => count($data)
                ]);
            }
        }

        return array_values($data);
    }

    /**
     * 批量插入数据
     */
    private function insertDataInBatches($data, $tableName, $batchSize = 100)
    {
        $totalInserted = 0;
        $chunks = array_chunk($data, $batchSize);

        foreach ($chunks as $chunk) {
            try {
                DB::connection('payment_db')->table($tableName)->insert($chunk);
                $totalInserted += count($chunk);
                
                Log::info('盛付通API: 批量插入成功', [
                    'table' => $tableName,
                    'batch_size' => count($chunk),
                    'total_inserted' => $totalInserted
                ]);
            } catch (\Exception $e) {
                Log::error('盛付通API: 批量插入失败', [
                    'table' => $tableName,
                    'error' => $e->getMessage(),
                    'batch_data' => $chunk
                ]);
                throw $e;
            }
        }

        return $totalInserted;
    }

    /**
     * 文件上传处理
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function uploadFile(Request $request)
    {
        try {
            // 验证请求
            if (!$request->hasFile('file')) {
                return response()->json([
                    'code' => 400,
                    'message' => '没有选择文件'
                ]);
            }

            $file = $request->file('file');
            $originalFilename = $file->getClientOriginalName();

            Log::info('盛付通API: 开始处理文件上传', ['filename' => $originalFilename]);

            // 验证文件名格式
            if (!$this->validateFilename($originalFilename)) {
                return response()->json([
                    'code' => 400,
                    'message' => '文件名格式不正确！请按照规定格式命名文件：42083878_YYYY-MM-DD_表名_YYYY-MM-DD.csv 或 42083878_YYYY-MM-DD_DETAIL_YYYY-MM-DD_数字.csv'
                ]);
            }

            // 解析文件名
            $parsed = $this->parseFilename($originalFilename);
            if (!$parsed) {
                return response()->json([
                    'code' => 400,
                    'message' => '文件名解析失败'
                ]);
            }

            $dataTime = $parsed['data_time'];
            $tableName = $parsed['table_name'];
            $downloadTime = $parsed['download_time'];

            // 检查文件是否已经上传过
            $existingLog = SftUploadLog::where('original_filename', $originalFilename)->first();
            if ($existingLog) {
                return response()->json([
                    'code' => 400,
                    'message' => '文件已经上传，请勿重复上传'
                ]);
            }

            // 检查数据时间是否已存在
            $existingDataTime = SftUploadLog::where('data_time', $dataTime)
                ->where('table_name', $tableName)
                ->first();
            if ($existingDataTime) {
                return response()->json([
                    'code' => 400,
                    'message' => "数据时间 {$dataTime} 的数据已存在"
                ]);
            }

            // 生成唯一文件名
            $timestamp = now()->format('Ymd_His');
            $randomStr = substr(md5(uniqid()), 0, 8);
            $extension = $file->getClientOriginalExtension();
            $newFilename = "{$timestamp}_{$randomStr}.{$extension}";

            // 确保上传目录存在
            $uploadDir = storage_path('app/uploads/' . now()->format('Ym'));
            if (!file_exists($uploadDir)) {
                mkdir($uploadDir, 0755, true);
            }

            // 保存文件
            $filePath = $uploadDir . '/' . $newFilename;
            $file->move($uploadDir, $newFilename);

            Log::info('盛付通API: 文件保存成功', ['path' => $filePath]);

            // 创建上传日志记录
            $uploadLog = SftUploadLog::create([
                'filename' => $newFilename,
                'original_filename' => $originalFilename,
                'data_time' => $dataTime,
                'table_name' => $tableName,
                'file_path' => $filePath,
                'file_size' => filesize($filePath),
                'status' => SftUploadLog::STATUS_PROCESSING,
                'message' => '文件上传成功，开始处理数据',
                'uploader_id' => auth()->id(),
                'upload_type' => SftUploadLog::TYPE_MANUAL,
                'processed_rows' => 0,
                'upload_time' => now()
            ]);

            // 处理文件数据
            $result = $this->processUploadedFile($filePath, $tableName, $dataTime, $uploadLog);

            if ($result['success']) {
                $uploadLog->update([
                    'status' => SftUploadLog::STATUS_SUCCESS,
                    'message' => "成功插入 {$result['count']} 条数据到 {$tableName} 表",
                    'processed_rows' => $result['count'],
                    'insert_success_time' => now()
                ]);

                return response()->json([
                    'code' => 200,
                    'message' => "文件上传成功，共处理 {$result['count']} 条数据",
                    'data' => [
                        'upload_id' => $uploadLog->id,
                        'processed_rows' => $result['count'],
                        'table_name' => $tableName,
                        'data_time' => $dataTime
                    ]
                ]);
            } else {
                $uploadLog->update([
                    'status' => SftUploadLog::STATUS_FAILED,
                    'message' => $result['message']
                ]);

                return response()->json([
                    'code' => 500,
                    'message' => '上传失败: ' . $result['message']
                ]);
            }

        } catch (\Exception $e) {
            Log::error('盛付通API: 文件上传失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'code' => 500,
                'message' => '文件上传失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 获取上传日志列表
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getUploadLogs(Request $request)
    {
        try {
            $page = $request->get('page', 1);
            $size = $request->get('size', 20);
            $search = $request->get('search', '');
            $status = $request->get('status', '');
            $tableName = $request->get('table_name', '');

            $query = SftUploadLog::query();

            // 搜索条件
            if ($search) {
                $query->where('original_filename', 'like', "%{$search}%");
            }

            if ($status) {
                $query->where('status', $status);
            }

            if ($tableName) {
                $query->where('table_name', $tableName);
            }

            // 排序：按创建时间倒序（最新上传的记录在前面）
            $query->orderBy('created_at', 'desc');

            $logs = $query->paginate($size, ['*'], 'page', $page);

            $data = $logs->items();
            $formattedData = array_map(function($log) {
                return [
                    'id' => $log->id,
                    'filename' => $log->original_filename,
                    'data_time' => $log->data_time,
                    'table_name' => $log->table_name,
                    'table_name_text' => $log->table_name_text,
                    'file_size' => $log->file_size,
                    'file_size_formatted' => $log->file_size_formatted,
                    'status' => $log->status,
                    'status_text' => $log->status_text,
                    'message' => $log->message,
                    'processed_rows' => $log->processed_rows,
                    'uploader_name' => $log->uploader ? $log->uploader->name : '未知',
                    'created_at' => $log->created_at ? $log->created_at->format('Y-m-d H:i:s') : null,
                    'upload_time' => $log->upload_time ? $log->upload_time->format('Y-m-d H:i:s') : null
                ];
            }, $data);

            return response()->json([
                'code' => 200,
                'message' => '获取上传日志成功',
                'data' => [
                    'list' => $formattedData,
                    'total' => $logs->total(),
                    'page' => $logs->currentPage(),
                    'size' => $logs->perPage(),
                    'pages' => $logs->lastPage()
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('盛付通API: 获取上传日志失败', ['error' => $e->getMessage()]);
            return response()->json([
                'code' => 500,
                'message' => '获取上传日志失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 删除上传日志
     *
     * @param Request $request
     * @param int $logId
     * @return \Illuminate\Http\JsonResponse
     */
    public function deleteUploadLog(Request $request, $logId)
    {
        try {
            $log = SftUploadLog::find($logId);
            if (!$log) {
                return response()->json([
                    'code' => 404,
                    'message' => '上传日志不存在'
                ]);
            }

            // 删除文件
            if (file_exists($log->file_path)) {
                unlink($log->file_path);
            }

            // 删除日志记录
            $log->delete();

            return response()->json([
                'code' => 200,
                'message' => '删除成功'
            ]);

        } catch (\Exception $e) {
            Log::error('盛付通API: 删除上传日志失败', ['error' => $e->getMessage()]);
            return response()->json([
                'code' => 500,
                'message' => '删除失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 清理单元格数据（与Python项目的clean_dataframe功能对应）
     */
    private function cleanCellValue($value)
    {
        if (is_null($value)) {
            return '';
        }

        // 转换为字符串
        $value = (string) $value;
        
        // 移除制表符、前后空格，并替换连续空格为单个空格
        $value = preg_replace('/\s+/', ' ', trim($value));
        
        // 移除特殊字符
        $value = str_replace(["\r", "\n", "\t"], '', $value);
        
        // 将 'null'、'none'、'nan' 等值转换为空字符串
        $nullValues = ['null', 'none', 'nan', 'NULL', 'None', 'NaN'];
        if (in_array($value, $nullValues)) {
            return '';
        }
        
        return trim($value);
    }

    /**
     * 获取日数据列表 (V1 API)
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getDailyList(Request $request)
    {
        try {
            // 获取查询参数
            $page = (int) $request->get('page', 1);
            $size = (int) $request->get('size', 15);
            $date = $request->get('date', '');
            $search = $request->get('search', '');
            $lv_filter = $request->get('lv', '');
            $super_institution = $request->get('super_institution', '');
            $sort_by = $request->get('sort_by', 'total_transaction');
            $order = $request->get('order', 'desc');

            Log::info('盛付通API: 获取日数据列表', [
                'page' => $page,
                'size' => $size,
                'date' => $date,
                'search' => $search,
                'lv' => $lv_filter,
                'super_institution' => $super_institution,
                'sort_by' => $sort_by,
                'order' => $order
            ]);

            // 构建基础查询
            $query = DB::connection('payment_db')
                ->table('ddg_institution as si')
                ->leftJoin('sft_institution_count_history as sh', 'si.id', '=', 'sh.institution_id')
                ->leftJoin('ddg_institution as parent', 'si.institution_id', '=', 'parent.id')
                ->select([
                    'si.id as institution_id',
                    'si.name as institution_name',
                    'si.xs_number',
                    'parent.name as super_institution_name',
                    'si.lv as institution_lv',
                    DB::raw('COALESCE(sh.total_transaction, 0) as total_transaction'),
                    DB::raw('COALESCE(sh.direct_transaction, 0) as direct_transaction'),
                    DB::raw('COALESCE(sh.direct_commission, 0) as direct_commission'),
                    DB::raw('COALESCE(sh.commission_difference, 0) as commission_difference'),
                    DB::raw('COALESCE(sh.total_commission, 0) as total_commission'),
                    'sh.summary_day'
                ]);

            // 添加日期过滤
            if (!empty($date)) {
                $query->where('sh.summary_day', $date);
            }

            // 添加搜索条件
            if (!empty($search)) {
                $query->where(function($q) use ($search) {
                    $q->where('si.name', 'like', "%{$search}%")
                      ->orWhere('si.xs_number', 'like', "%{$search}%");
                });
            }

            // 添加等级过滤
            if (!empty($lv_filter)) {
                $query->where('si.lv', $lv_filter);
            }

            // 添加上级机构过滤
            if (!empty($super_institution)) {
                $query->where('parent.name', 'like', "%{$super_institution}%");
            }

            // 获取总记录数
            $total = $query->count();

            // 添加排序
            $sort_fields = [
                'total_transaction' => 'sh.total_transaction',
                'direct_transaction' => 'sh.direct_transaction',
                'direct_commission' => 'sh.direct_commission',
                'commission_difference' => 'sh.commission_difference',
                'total_commission' => 'sh.total_commission',
                'institution_id' => 'si.id'
            ];
            $sort_field = $sort_fields[$sort_by] ?? 'sh.total_transaction';

            // 执行查询
            $results = $query->orderBy($sort_field, $order)
                ->offset(($page - 1) * $size)
                ->limit($size)
                ->get();

            // 处理数据
            $daily_list = [];
            foreach ($results as $row) {
                $daily_list[] = [
                    'institution_id' => $row->institution_id,
                    'institution_name' => $row->institution_name ?: '--',
                    'xs_number' => $row->xs_number ?: '--',
                    'super_institution_name' => $row->super_institution_name ?: '--',
                    'institution_lv' => $row->institution_lv,
                    'total_transaction' => (float) $row->total_transaction,
                    'direct_transaction' => (float) $row->direct_transaction,
                    'direct_commission' => (float) $row->direct_commission,
                    'commission_difference' => (float) $row->commission_difference,
                    'total_commission' => (float) $row->total_commission
                ];
            }

            return response()->json([
                'code' => 0,
                'data' => [
                    'list' => $daily_list,
                    'total' => $total
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('盛付通API: 获取日数据列表失败 - ' . $e->getMessage());
            return response()->json([
                'code' => 1,
                'message' => $e->getMessage()
            ]);
        }
    }

    /**
     * 计算日数据 (V1 API)
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function calculateDaily(Request $request)
    {
        try {
            $data = $request->json()->all();
            $date = $data['date'] ?? null;

            if (!$date) {
                return response()->json([
                    'code' => 1,
                    'message' => '请选择日期'
                ]);
            }

            Log::info('盛付通API: 开始计算日数据', ['date' => $date]);

            // 删除已有数据
            DB::connection('payment_db')
                ->table('sft_institution_count_history')
                ->where('summary_day', $date)
                ->delete();

            // 计算并插入数据
            $sql = "
                INSERT INTO sft_institution_count_history (
                    institution_id, summary_day, total_transaction,
                    direct_transaction, direct_commission, total_commission,
                    commission_difference, summary_period
                )
                SELECT 
                    i.id as institution_id,
                    ? as summary_day,
                    COALESCE(SUM(r.actual_amount), 0) as total_transaction,
                    COALESCE(SUM(CASE WHEN i.xs_number = r.reseller_id THEN r.actual_amount ELSE 0 END), 0) as direct_transaction,
                    COALESCE(SUM(CASE WHEN i.xs_number = r.reseller_id THEN r.reseller_commission ELSE 0 END), 0) as direct_commission,
                    COALESCE(SUM(r.reseller_commission), 0) as total_commission,
                    COALESCE(SUM(r.reseller_commission), 0) - COALESCE(SUM(CASE WHEN i.xs_number = r.reseller_id THEN r.reseller_commission ELSE 0 END), 0) as commission_difference,
                    DATE_FORMAT(?, '%Y-%m') as summary_period
                FROM ddg_institution i
                LEFT JOIN ddg_sft_reseller_sum r ON i.xs_number = r.reseller_id 
                    AND DATE(r.summary_period) = ?
                GROUP BY i.id
            ";

            DB::connection('payment_db')->insert($sql, [$date, $date, $date]);

            Log::info('盛付通API: 日数据计算完成', ['date' => $date]);

            return response()->json([
                'code' => 0,
                'message' => '计算成功'
            ]);

        } catch (\Exception $e) {
            Log::error('盛付通API: 计算日数据失败 - ' . $e->getMessage());
            return response()->json([
                'code' => 1,
                'message' => $e->getMessage()
            ]);
        }
    }

    /**
     * 批量计算日数据 (V1 API)
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function batchCalculateDaily(Request $request)
    {
        try {
            $data = $request->json()->all();
            $start_date = $data['start_date'] ?? null;
            $end_date = $data['end_date'] ?? null;

            if (!$start_date || !$end_date) {
                return response()->json([
                    'code' => 1,
                    'message' => '请选择开始和结束日期'
                ]);
            }

            Log::info('盛付通API: 开始批量计算日数据', [
                'start_date' => $start_date,
                'end_date' => $end_date
            ]);

            $start = Carbon::parse($start_date);
            $end = Carbon::parse($end_date);
            $calculated_count = 0;

            // 逐日计算
            while ($start->lte($end)) {
                $current_date = $start->format('Y-m-d');
                
                // 删除已有数据
                DB::connection('payment_db')
                    ->table('sft_institution_count_history')
                    ->where('summary_day', $current_date)
                    ->delete();

                // 计算并插入数据
                $sql = "
                    INSERT INTO sft_institution_count_history (
                        institution_id, summary_day, total_transaction,
                        direct_transaction, direct_commission, total_commission,
                        commission_difference, summary_period
                    )
                    SELECT 
                        i.id as institution_id,
                        ? as summary_day,
                        COALESCE(SUM(r.actual_amount), 0) as total_transaction,
                        COALESCE(SUM(CASE WHEN i.xs_number = r.reseller_id THEN r.actual_amount ELSE 0 END), 0) as direct_transaction,
                        COALESCE(SUM(CASE WHEN i.xs_number = r.reseller_id THEN r.reseller_commission ELSE 0 END), 0) as direct_commission,
                        COALESCE(SUM(r.reseller_commission), 0) as total_commission,
                        COALESCE(SUM(r.reseller_commission), 0) - COALESCE(SUM(CASE WHEN i.xs_number = r.reseller_id THEN r.reseller_commission ELSE 0 END), 0) as commission_difference,
                        DATE_FORMAT(?, '%Y-%m') as summary_period
                    FROM ddg_institution i
                    LEFT JOIN ddg_sft_reseller_sum r ON i.xs_number = r.reseller_id 
                        AND DATE(r.summary_period) = ?
                    GROUP BY i.id
                ";

                DB::connection('payment_db')->insert($sql, [$current_date, $current_date, $current_date]);
                $calculated_count++;

                $start->addDay();
            }

            Log::info('盛付通API: 批量日数据计算完成', [
                'start_date' => $start_date,
                'end_date' => $end_date,
                'calculated_count' => $calculated_count
            ]);

            return response()->json([
                'code' => 0,
                'message' => "批量计算成功，共计算了 {$calculated_count} 天的数据"
            ]);

        } catch (\Exception $e) {
            Log::error('盛付通API: 批量计算日数据失败 - ' . $e->getMessage());
            return response()->json([
                'code' => 1,
                'message' => $e->getMessage()
            ]);
        }
    }

    /**
     * 检查数据 (V1 API)
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function checkData(Request $request)
    {
        try {
            $data = $request->json()->all();
            $date = $data['date'] ?? null;

            if (!$date) {
                return response()->json([
                    'code' => 1,
                    'message' => '请选择日期'
                ]);
            }

            Log::info('盛付通API: 开始检查数据', ['date' => $date]);

            // 检查基础数据是否存在
            $reseller_sum_count = DB::connection('payment_db')
                ->table('ddg_sft_reseller_sum')
                ->whereDate('summary_period', $date)
                ->count();

            $detail_count = DB::connection('payment_db')
                ->table('ddg_sft_detail')
                ->whereDate('transaction_time', $date)
                ->count();

            $agent_mch_sum_count = DB::connection('payment_db')
                ->table('ddg_sft_agent_mch_sum')
                ->whereDate('summary_period', $date)
                ->count();

            // 检查计算结果是否存在
            $calculated_count = DB::connection('payment_db')
                ->table('sft_institution_count_history')
                ->where('summary_day', $date)
                ->count();

            // 检查数据一致性
            $inconsistencies = [];
            
            // 检查总交易金额是否一致
            $detail_total = DB::connection('payment_db')
                ->table('ddg_sft_detail')
                ->whereDate('transaction_time', $date)
                ->sum('transaction_amount');

            $calculated_total = DB::connection('payment_db')
                ->table('sft_institution_count_history')
                ->where('summary_day', $date)
                ->sum('total_transaction');

            if (abs($detail_total - $calculated_total) > 0.01) {
                $inconsistencies[] = [
                    'type' => '交易金额不一致',
                    'detail_amount' => $detail_total,
                    'calculated_amount' => $calculated_total,
                    'difference' => $detail_total - $calculated_total
                ];
            }

            $check_result = [
                'date' => $date,
                'data_status' => [
                    'reseller_sum_count' => $reseller_sum_count,
                    'detail_count' => $detail_count,
                    'agent_mch_sum_count' => $agent_mch_sum_count,
                    'calculated_count' => $calculated_count
                ],
                'data_consistency' => [
                    'detail_total_amount' => (float) $detail_total,
                    'calculated_total_amount' => (float) $calculated_total,
                    'inconsistencies' => $inconsistencies
                ],
                'status' => empty($inconsistencies) ? 'consistent' : 'inconsistent'
            ];

            Log::info('盛付通API: 数据检查完成', $check_result);

            return response()->json([
                'code' => 0,
                'data' => $check_result,
                'message' => empty($inconsistencies) ? '数据检查通过' : '发现数据不一致'
            ]);

        } catch (\Exception $e) {
            Log::error('盛付通API: 检查数据失败 - ' . $e->getMessage());
            return response()->json([
                'code' => 1,
                'message' => $e->getMessage()
            ]);
        }
    }

    /**
     * 获取机构汇总列表
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getInstitutionSummary(Request $request)
    {
        try {
            $page = $request->get('page', 1);
            $size = $request->get('size', 15);
            $search = $request->get('search', '');
            $parentId = $request->get('parent_id', null);
            $sortBy = $request->get('sort_by', 'sub_count');
            $order = $request->get('order', 'desc');

            Log::info('盛付通API: 获取机构汇总列表', [
                'page' => $page,
                'size' => $size,
                'search' => $search,
                'parent_id' => $parentId,
                'sort_by' => $sortBy,
                'order' => $order
            ]);

            $query = DB::connection('payment_db')->table('ddg_institution_relation as r')
                ->leftJoin('ddg_institution as i', 'r.institution_id', '=', 'i.id')
                ->leftJoin('ddg_institution as parent', 'r.parent_id', '=', 'parent.id')
                ->leftJoin('ddg_sft_merchant_count as m', 'i.id', '=', 'm.institution_id')
                ->select([
                    'r.institution_id',
                    'r.name',
                    'r.xs_number',
                    'i.lv',
                    'parent.name as super_institution_name',
                    'r.current_transaction',
                    'r.total_transaction',
                    'm.direct_merchant_count',
                    'm.total_merchant_count',
                    'm.team_merchant_count',
                    'r.direct_sub_count',
                    'r.sub_count'
                ]);

            // 如果指定了parent_id，则查询该机构的直属下级
            if ($parentId) {
                $query->where('r.parent_id', $parentId);
            }

            // 搜索条件
            if (!empty($search)) {
                $query->where(function($q) use ($search) {
                    $q->where('r.name', 'like', "%{$search}%")
                      ->orWhere('r.xs_number', 'like', "%{$search}%");
                });
            }

            // 排序
            $allowedSortFields = [
                'sub_count' => 'r.sub_count',
                'direct_sub_count' => 'r.direct_sub_count',
                'current_transaction' => 'r.current_transaction',
                'total_transaction' => 'r.total_transaction',
                'direct_merchant_count' => 'm.direct_merchant_count',
                'total_merchant_count' => 'm.total_merchant_count',
                'team_merchant_count' => 'm.team_merchant_count'
            ];

            $sortField = $allowedSortFields[$sortBy] ?? 'r.sub_count';
            $query->orderBy($sortField, $order);

            // 获取总数
            $total = $query->count();

            // 分页
            $offset = ($page - 1) * $size;
            $list = $query->offset($offset)->limit($size)->get();

            // 处理数据
            $processedList = $list->map(function ($item) {
                return [
                    'institution_id' => $item->institution_id,
                    'name' => $item->name ?: '--',
                    'xs_number' => $item->xs_number ?: '--',
                    'lv' => $item->lv ?: 0,
                    'super_institution_name' => $item->super_institution_name ?: '--',
                    'current_transaction' => (float) ($item->current_transaction ?: 0),
                    'total_transaction' => (float) ($item->total_transaction ?: 0),
                    'direct_merchant_count' => (int) ($item->direct_merchant_count ?: 0),
                    'total_merchant_count' => (int) ($item->total_merchant_count ?: 0),
                    'team_merchant_count' => (int) ($item->team_merchant_count ?: 0),
                    'direct_sub_count' => (int) ($item->direct_sub_count ?: 0),
                    'sub_count' => (int) ($item->sub_count ?: 0)
                ];
            });

            return response()->json([
                'code' => 0,
                'message' => '获取机构汇总列表成功',
                'data' => [
                    'list' => $processedList,
                    'total' => $total,
                    'page' => $page,
                    'size' => $size
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('盛付通API: 获取机构汇总列表失败 - ' . $e->getMessage());
            return response()->json([
                'code' => 1,
                'message' => '获取机构汇总列表失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 同步机构数据
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function syncInstitutionData(Request $request)
    {
        try {
            Log::info('盛付通API: 开始同步机构数据');

            // 设置同步状态
            cache()->put('institution_sync_status', [
                'status' => 'syncing',
                'progress' => 0,
                'message' => '准备同步...',
                'current_step' => '第1步：初始化数据表'
            ], 600); // 10分钟过期

            // 异步执行同步任务
            dispatch(function () {
                $this->performInstitutionSync();
            });

            return response()->json([
                'code' => 0,
                'message' => '同步任务已启动'
            ]);

        } catch (\Exception $e) {
            Log::error('盛付通API: 启动机构数据同步失败 - ' . $e->getMessage());
            return response()->json([
                'code' => 1,
                'message' => '启动同步失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 获取同步状态
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getInstitutionSyncStatus(Request $request)
    {
        try {
            $status = cache()->get('institution_sync_status', [
                'status' => 'idle',
                'progress' => 0,
                'message' => '未开始同步',
                'current_step' => ''
            ]);

            return response()->json([
                'code' => 0,
                'message' => '获取同步状态成功',
                'data' => $status
            ]);

        } catch (\Exception $e) {
            Log::error('盛付通API: 获取同步状态失败 - ' . $e->getMessage());
            return response()->json([
                'code' => 1,
                'message' => '获取同步状态失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 执行机构数据同步
     */
    private function performInstitutionSync()
    {
        try {
            // 第一步：创建或更新机构关系表
            cache()->put('institution_sync_status', [
                'status' => 'syncing',
                'progress' => 10,
                'message' => '正在创建机构关系表...',
                'current_step' => '第1步：创建机构关系表'
            ], 600);

            $this->createInstitutionRelationTable();

            // 第二步：同步机构基础数据
            cache()->put('institution_sync_status', [
                'status' => 'syncing',
                'progress' => 30,
                'message' => '正在同步机构基础数据...',
                'current_step' => '第2步：同步机构基础数据'
            ], 600);

            $this->syncInstitutionBasicData();

            // 第三步：更新交易数据
            cache()->put('institution_sync_status', [
                'status' => 'syncing',
                'progress' => 50,
                'message' => '正在更新交易数据...',
                'current_step' => '第3步：更新交易数据'
            ], 600);

            $this->updateInstitutionTransactionData();

            // 第四步：计算下级机构数量
            cache()->put('institution_sync_status', [
                'status' => 'syncing',
                'progress' => 70,
                'message' => '正在计算下级机构数量...',
                'current_step' => '第4步：计算下级机构数量'
            ], 600);

            $this->calculateSubInstitutionCounts();

            // 第五步：更新商户统计数据
            cache()->put('institution_sync_status', [
                'status' => 'syncing',
                'progress' => 90,
                'message' => '正在更新商户统计数据...',
                'current_step' => '第5步：更新商户统计数据'
            ], 600);

            $this->updateMerchantCounts();

            // 完成
            cache()->put('institution_sync_status', [
                'status' => 'completed',
                'progress' => 100,
                'message' => '同步完成',
                'current_step' => '同步完成'
            ], 600);

            Log::info('盛付通API: 机构数据同步完成');

        } catch (\Exception $e) {
            Log::error('盛付通API: 机构数据同步失败 - ' . $e->getMessage());
            cache()->put('institution_sync_status', [
                'status' => 'failed',
                'progress' => 0,
                'message' => '同步失败: ' . $e->getMessage(),
                'current_step' => '同步失败'
            ], 600);
        }
    }

    /**
     * 创建机构关系表
     */
    private function createInstitutionRelationTable()
    {
        // 创建机构关系表
        DB::connection('payment_db')->statement("
            CREATE TABLE IF NOT EXISTS ddg_institution_relation (
                institution_id INT PRIMARY KEY,
                name VARCHAR(255),
                number VARCHAR(100),
                xs_number VARCHAR(100),
                level INT,
                parent_id INT,
                current_transaction DECIMAL(15,2) DEFAULT 0,
                total_transaction DECIMAL(15,2) DEFAULT 0,
                direct_sub_count INT DEFAULT 0,
                sub_count INT DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_parent_id (parent_id),
                INDEX idx_xs_number (xs_number)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");

        // 创建层级关系表
        DB::connection('payment_db')->statement("
            CREATE TABLE IF NOT EXISTS institution_hierarchy (
                institution_id INT,
                parent_id INT,
                level INT,
                PRIMARY KEY (institution_id, parent_id),
                INDEX idx_parent_level (parent_id, level)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
    }

    /**
     * 同步机构基础数据
     */
    private function syncInstitutionBasicData()
    {
        DB::connection('payment_db')->statement("
            INSERT INTO ddg_institution_relation (
                institution_id, name, number, xs_number, level, parent_id,
                created_at, updated_at
            )
            SELECT 
                i.id as institution_id,
                i.name,
                i.number,
                i.xs_number,
                i.lv as level,
                CASE 
                    WHEN i.institution_id IS NULL THEN NULL
                    WHEN EXISTS (SELECT 1 FROM ddg_institution p WHERE p.id = i.institution_id) 
                    THEN i.institution_id 
                    ELSE NULL 
                END as parent_id,
                CURRENT_TIMESTAMP,
                CURRENT_TIMESTAMP
            FROM ddg_institution i
            ON DUPLICATE KEY UPDATE
                name = VALUES(name),
                number = VALUES(number),
                xs_number = VALUES(xs_number),
                level = VALUES(level),
                parent_id = VALUES(parent_id),
                updated_at = CURRENT_TIMESTAMP
        ");
    }

    /**
     * 更新交易数据
     */
    private function updateInstitutionTransactionData()
    {
        DB::connection('payment_db')->statement("
            UPDATE ddg_institution_relation r
            LEFT JOIN (
                SELECT 
                    institution_id,
                    SUM(current_month_total_transaction) as total_trans,
                    MAX(CASE 
                        WHEN summary_period = DATE_FORMAT(CURRENT_DATE, '%Y-%m')
                        THEN current_month_total_transaction 
                        ELSE 0 
                    END) as current_trans
                FROM sft_institution_count
                GROUP BY institution_id
            ) s ON r.institution_id = s.institution_id
            SET 
                r.total_transaction = COALESCE(s.total_trans, 0),
                r.current_transaction = COALESCE(s.current_trans, 0)
        ");
    }

    /**
     * 计算下级机构数量
     */
    private function calculateSubInstitutionCounts()
    {
        // 更新直属下级数量
        DB::connection('payment_db')->statement("
            UPDATE ddg_institution_relation r
            LEFT JOIN (
                SELECT parent_id, COUNT(*) as direct_count
                FROM ddg_institution_relation
                WHERE parent_id IS NOT NULL
                GROUP BY parent_id
            ) d ON r.institution_id = d.parent_id
            SET r.direct_sub_count = COALESCE(d.direct_count, 0)
        ");

        // 清空层级关系表
        DB::connection('payment_db')->statement("TRUNCATE TABLE institution_hierarchy");

        // 插入直接下级关系
        DB::connection('payment_db')->statement("
            INSERT INTO institution_hierarchy
            SELECT institution_id, parent_id, 1
            FROM ddg_institution_relation
            WHERE parent_id IS NOT NULL
        ");

        // 循环插入更深层级的关系
        $level = 1;
        while (true) {
            $affected = DB::connection('payment_db')->statement("
                INSERT IGNORE INTO institution_hierarchy
                SELECT DISTINCT h.institution_id, r.parent_id, ?
                FROM institution_hierarchy h
                JOIN ddg_institution_relation r ON h.parent_id = r.institution_id
                WHERE r.parent_id IS NOT NULL AND h.level = ?
            ", [$level + 1, $level]);

            if (!$affected) {
                break;
            }
            $level++;
        }

        // 更新总下级数量
        DB::connection('payment_db')->statement("
            UPDATE ddg_institution_relation r
            LEFT JOIN (
                SELECT parent_id, COUNT(DISTINCT institution_id) as total_count
                FROM institution_hierarchy
                GROUP BY parent_id
            ) t ON r.institution_id = t.parent_id
            SET r.sub_count = COALESCE(t.total_count, 0)
        ");
    }

    /**
     * 更新商户统计数据
     */
    private function updateMerchantCounts()
    {
        // 创建商户统计表（如果不存在）
        DB::connection('payment_db')->statement("
            CREATE TABLE IF NOT EXISTS ddg_sft_merchant_count (
                institution_id INT PRIMARY KEY,
                direct_merchant_count INT DEFAULT 0,
                total_merchant_count INT DEFAULT 0,
                team_merchant_count INT DEFAULT 0,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");

        // 这里可以根据实际的商户数据表来计算商户数量
        // 暂时设置为0，后续可以根据实际业务需求调整
        DB::connection('payment_db')->statement("
            INSERT INTO ddg_sft_merchant_count (institution_id, direct_merchant_count, total_merchant_count, team_merchant_count)
            SELECT institution_id, 0, 0, 0
            FROM ddg_institution_relation
            ON DUPLICATE KEY UPDATE
                direct_merchant_count = 0,
                total_merchant_count = 0,
                team_merchant_count = 0,
                updated_at = CURRENT_TIMESTAMP
        ");
    }

    /**
     * 获取余额管理列表
     */
    public function getBalanceList(Request $request)
    {
        try {
            $page = $request->get('page', 1);
            $size = $request->get('size', 15);
            $search = $request->get('search', '');
            $sort = $request->get('sort', 'withdrawable_balance');
            $order = $request->get('order', 'descending');

            // 获取上个月的年月
            $lastMonth = Carbon::now()->subMonth()->format('Y-m');

            // 构建查询 - 使用正确的数据库连接
            $query = DB::connection('payment_db')->table('sft_institution_balance as b')
                ->leftJoin('ddg_institution_relation as ir', 'b.institution_id', '=', 'ir.institution_id')
                ->leftJoin(DB::raw("(
                    SELECT institution_id, 
                           current_month_direct_commission as last_month_direct_commission,
                           commission_difference as last_month_commission_difference,
                           current_month_total_commission as last_month_commission,
                           0 as last_month_manage_commission
                    FROM sft_institution_count 
                    WHERE summary_period = '{$lastMonth}'
                ) as ic"), 'b.institution_id', '=', 'ic.institution_id')
                ->select([
                    'b.institution_id',
                    'ir.name',
                    'ic.last_month_direct_commission',
                    'ic.last_month_commission_difference', 
                    'ic.last_month_commission',
                    'ic.last_month_manage_commission',
                    'b.pending_balance',
                    'b.withdrawable_balance',
                    'b.non_withdrawable_balance',
                    'b.withdrawn_amount',
                    'b.last_synced_period'
                ]);

            // 搜索条件
            if ($search) {
                $query->where(function($q) use ($search) {
                    $q->where('ir.name', 'like', "%{$search}%")
                      ->orWhere('b.institution_id', 'like', "%{$search}%");
                });
            }

            // 排序
            $orderDirection = $order === 'ascending' ? 'asc' : 'desc';
            $query->orderBy($sort, $orderDirection);

            // 分页
            $total = $query->count();
            $offset = ($page - 1) * $size;
            $list = $query->offset($offset)->limit($size)->get();

            // 计算总计
            $totalBalances = DB::connection('payment_db')->table('sft_institution_balance as b')
                ->leftJoin(DB::raw("(
                    SELECT institution_id, 
                           SUM(current_month_total_commission) as last_month_commission_sum
                    FROM sft_institution_count 
                    WHERE summary_period = '{$lastMonth}'
                    GROUP BY institution_id
                ) as ic"), 'b.institution_id', '=', 'ic.institution_id')
                ->selectRaw('
                    SUM(ic.last_month_commission_sum) as last_month_commission_sum,
                    SUM(b.withdrawable_balance) as withdrawable_balance_sum,
                    SUM(b.withdrawn_amount) as withdrawn_amount_sum
                ')
                ->first();

            return response()->json([
                'code' => 0,
                'message' => '获取余额管理列表成功',
                'data' => [
                    'list' => $list,
                    'total' => $total,
                    'totalBalances' => $totalBalances
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('获取余额管理列表失败: ' . $e->getMessage());
            return response()->json([
                'code' => 500,
                'message' => '获取余额管理列表失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 同步分润到待结算
     */
    public function syncBalanceData(Request $request)
    {
        try {
            $month = $request->get('month', Carbon::now()->subMonth()->format('Y-m'));

            // 同步分润数据到余额表
            DB::connection('payment_db')->transaction(function() use ($month) {
                // 从机构统计表获取分润数据
                $commissionData = DB::connection('payment_db')->table('sft_institution_count')
                    ->where('summary_period', $month)
                    ->get();

                foreach ($commissionData as $data) {
                    // 更新或插入余额记录
                    DB::connection('payment_db')->table('sft_institution_balance')
                        ->updateOrInsert(
                            ['institution_id' => $data->institution_id],
                            [
                                'pending_balance' => DB::raw('pending_balance + ' . ($data->current_month_total_commission ?? 0)),
                                'last_synced_period' => $month,
                                'updated_at' => now()
                            ]
                        );
                }
            });

            return response()->json([
                'code' => 0,
                'message' => '同步分润数据成功'
            ]);

        } catch (\Exception $e) {
            Log::error('同步分润数据失败: ' . $e->getMessage());
            return response()->json([
                'code' => 500,
                'message' => '同步分润数据失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 批量转入不可提现余额
     */
    public function batchToNonWithdrawable(Request $request)
    {
        try {
            DB::connection('payment_db')->transaction(function() {
                DB::connection('payment_db')->table('sft_institution_balance')
                    ->where('pending_balance', '>', 0)
                    ->update([
                        'non_withdrawable_balance' => DB::raw('non_withdrawable_balance + pending_balance'),
                        'pending_balance' => 0,
                        'updated_at' => now()
                    ]);
            });

            return response()->json([
                'code' => 0,
                'message' => '批量转入不可提现余额成功'
            ]);

        } catch (\Exception $e) {
            Log::error('批量转入不可提现余额失败: ' . $e->getMessage());
            return response()->json([
                'code' => 500,
                'message' => '批量转入不可提现余额失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 批量转入可提现余额
     */
    public function batchToWithdrawable(Request $request)
    {
        try {
            DB::connection('payment_db')->transaction(function() {
                DB::connection('payment_db')->table('sft_institution_balance')
                    ->where('pending_balance', '>', 0)
                    ->update([
                        'withdrawable_balance' => DB::raw('withdrawable_balance + pending_balance'),
                        'pending_balance' => 0,
                        'updated_at' => now()
                    ]);
            });

            return response()->json([
                'code' => 0,
                'message' => '批量转入可提现余额成功'
            ]);

        } catch (\Exception $e) {
            Log::error('批量转入可提现余额失败: ' . $e->getMessage());
            return response()->json([
                'code' => 500,
                'message' => '批量转入可提现余额失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 管理奖加到可提现余额
     */
    public function manageCommissionToWithdrawable(Request $request)
    {
        try {
            $lastMonth = Carbon::now()->subMonth()->format('Y-m');

            DB::connection('payment_db')->transaction(function() use ($lastMonth) {
                // 获取上月管理奖数据
                $manageCommissions = DB::connection('payment_db')->table('sft_institution_count')
                    ->where('summary_period', $lastMonth)
                    ->where('current_month_manage_commission', '>', 0)
                    ->get();

                foreach ($manageCommissions as $commission) {
                    DB::connection('payment_db')->table('sft_institution_balance')
                        ->updateOrInsert(
                            ['institution_id' => $commission->institution_id],
                            [
                                'withdrawable_balance' => DB::raw('withdrawable_balance + ' . $commission->current_month_manage_commission),
                                'updated_at' => now()
                            ]
                        );
                }
            });

            return response()->json([
                'code' => 0,
                'message' => '管理奖已成功加入可提现余额'
            ]);

        } catch (\Exception $e) {
            Log::error('管理奖加入可提现余额失败: ' . $e->getMessage());
            return response()->json([
                'code' => 500,
                'message' => '管理奖加入可提现余额失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 调整余额
     */
    public function adjustBalance(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'institution_id' => 'required|integer',
                'changeType' => 'required|in:increase,decrease,final',
                'amount' => 'required|numeric|min:0',
                'password' => 'required|string'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'code' => 400,
                    'message' => '参数验证失败: ' . $validator->errors()->first()
                ]);
            }

            // 验证管理密码（这里需要根据实际情况实现）
            $password = $request->get('password');
            if ($password !== 'admin123') { // 这里应该从配置或数据库获取正确的密码
                return response()->json([
                    'code' => 401,
                    'message' => '管理密码错误'
                ]);
            }

            $institutionId = $request->get('institution_id');
            $changeType = $request->get('changeType');
            $amount = $request->get('amount');

            DB::connection('payment_db')->transaction(function() use ($institutionId, $changeType, $amount) {
                $balance = DB::connection('payment_db')->table('sft_institution_balance')
                    ->where('institution_id', $institutionId)
                    ->first();

                $currentBalance = $balance ? $balance->withdrawable_balance : 0;

                switch ($changeType) {
                    case 'increase':
                        $newBalance = $currentBalance + $amount;
                        break;
                    case 'decrease':
                        $newBalance = max(0, $currentBalance - $amount);
                        break;
                    case 'final':
                        $newBalance = $amount;
                        break;
                    default:
                        throw new \Exception('无效的调整类型');
                }

                DB::connection('payment_db')->table('sft_institution_balance')
                    ->updateOrInsert(
                        ['institution_id' => $institutionId],
                        [
                            'withdrawable_balance' => $newBalance,
                            'updated_at' => now()
                        ]
                    );
            });

            return response()->json([
                'code' => 0,
                'message' => '余额调整成功'
            ]);

        } catch (\Exception $e) {
            Log::error('余额调整失败: ' . $e->getMessage());
            return response()->json([
                'code' => 500,
                'message' => '余额调整失败: ' . $e->getMessage()
            ]);
        }
    }
} 