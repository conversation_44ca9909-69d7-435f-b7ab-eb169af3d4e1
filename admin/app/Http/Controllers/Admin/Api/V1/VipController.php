<?php

namespace App\Http\Controllers\Admin\Api\V1;

use App\Http\Controllers\Controller;
use App\Traits\ApiResponseTrait;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;
use App\Models\Vip;
use App\Models\VipLevel;
use App\Models\VipTransaction;
use App\Models\User;

class VipController extends Controller
{
    use ApiResponseTrait;

    /**
     * 获取VIP会员列表
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        $query = Vip::query();
        
        // 搜索条件
        if ($request->has('keyword') && !empty($request->keyword)) {
            $keyword = $request->keyword;
            $query->whereHas('user', function($q) use ($keyword) {
                $q->where('name', 'like', "%{$keyword}%")
                  ->orWhere('phone', 'like', "%{$keyword}%");
            });
        }
        
        // 状态筛选
        if ($request->has('status') && $request->status !== '') {
            $query->where('status', $request->status);
        }
        
        // 等级筛选
        if ($request->has('level_id') && !empty($request->level_id)) {
            $query->where('level_id', $request->level_id);
        }
        
        // 排序
        $orderBy = $request->input('order_by', 'created_at');
        $orderDir = $request->input('order_dir', 'desc');
        $query->orderBy($orderBy, $orderDir);
        
        // 分页
        $perPage = $request->input('per_page', 15);
        $vips = $query->paginate($perPage);
        
        // 加载关联数据
        $vips->load(['user', 'level']);
        
        return $this->paginate($vips);
    }

    /**
     * 创建VIP会员
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'user_id' => 'required|integer|exists:users,id',
            'level_id' => 'required|integer|exists:vip_levels,id',
            'expire_time' => 'required|date',
            'status' => 'required|integer|in:0,1,2',
            'points' => 'nullable|integer|min:0',
            'remark' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return $this->error('验证失败', 422, $validator->errors());
        }

        // 检查用户是否已经是VIP
        $existingVip = Vip::where('user_id', $request->user_id)->first();
        if ($existingVip) {
            return $this->error('该用户已经是VIP会员', 400);
        }

        try {
            DB::beginTransaction();
            
            // 创建VIP会员
            $vip = new Vip();
            $vip->user_id = $request->user_id;
            $vip->level_id = $request->level_id;
            $vip->expire_time = $request->expire_time;
            $vip->status = $request->status;
            $vip->points = $request->points ?? 0;
            $vip->remark = $request->remark;
            $vip->save();
            
            // 更新用户VIP状态
            User::where('id', $request->user_id)->update(['is_vip' => 1]);
            
            // 记录交易记录
            $transaction = new VipTransaction();
            $transaction->user_id = $request->user_id;
            $transaction->vip_id = $vip->id;
            $transaction->type = 'activate';
            $transaction->level_id = $request->level_id;
            $transaction->points = $request->points ?? 0;
            $transaction->remark = '管理员创建VIP会员';
            $transaction->admin_id = auth()->id();
            $transaction->save();
            
            DB::commit();
            
            // 加载关联数据
            $vip->load(['user', 'level']);
            
            return $this->success($vip, 'VIP会员创建成功');
        } catch (\Exception $e) {
            DB::rollBack();
            return $this->error('VIP会员创建失败: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 获取单个VIP会员详情
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        $vip = Vip::with(['user', 'level'])->find($id);
        
        if (!$vip) {
            return $this->error('VIP会员不存在', 404);
        }
        
        // 获取交易记录
        $transactions = VipTransaction::where('vip_id', $id)
            ->orderBy('created_at', 'desc')
            ->get();
        
        $vip->transactions = $transactions;
        
        return $this->success($vip);
    }

    /**
     * 更新VIP会员
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id)
    {
        $vip = Vip::find($id);
        
        if (!$vip) {
            return $this->error('VIP会员不存在', 404);
        }
        
        $validator = Validator::make($request->all(), [
            'level_id' => 'required|integer|exists:vip_levels,id',
            'expire_time' => 'required|date',
            'status' => 'required|integer|in:0,1,2',
            'points' => 'nullable|integer|min:0',
            'remark' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return $this->error('验证失败', 422, $validator->errors());
        }
        
        try {
            DB::beginTransaction();
            
            // 记录原始数据
            $oldLevelId = $vip->level_id;
            $oldStatus = $vip->status;
            
            // 更新VIP会员
            $vip->level_id = $request->level_id;
            $vip->expire_time = $request->expire_time;
            $vip->status = $request->status;
            
            // 如果提供了积分，更新积分
            if ($request->has('points')) {
                $pointsDiff = $request->points - $vip->points;
                $vip->points = $request->points;
                
                // 记录积分变动
                if ($pointsDiff != 0) {
                    $transaction = new VipTransaction();
                    $transaction->user_id = $vip->user_id;
                    $transaction->vip_id = $vip->id;
                    $transaction->type = $pointsDiff > 0 ? 'points_add' : 'points_deduct';
                    $transaction->level_id = $vip->level_id;
                    $transaction->points = abs($pointsDiff);
                    $transaction->remark = '管理员调整积分';
                    $transaction->admin_id = auth()->id();
                    $transaction->save();
                }
            }
            
            $vip->remark = $request->remark;
            $vip->save();
            
            // 如果等级变更，记录等级变更
            if ($oldLevelId != $request->level_id) {
                $transaction = new VipTransaction();
                $transaction->user_id = $vip->user_id;
                $transaction->vip_id = $vip->id;
                $transaction->type = 'level_change';
                $transaction->level_id = $request->level_id;
                $transaction->old_level_id = $oldLevelId;
                $transaction->remark = '管理员调整会员等级';
                $transaction->admin_id = auth()->id();
                $transaction->save();
            }
            
            // 如果状态变更为已过期或已禁用，更新用户VIP状态
            if (($oldStatus == 1 && $request->status != 1) || $request->status == 2) {
                User::where('id', $vip->user_id)->update(['is_vip' => 0]);
            } else if ($oldStatus != 1 && $request->status == 1) {
                User::where('id', $vip->user_id)->update(['is_vip' => 1]);
            }
            
            DB::commit();
            
            // 加载关联数据
            $vip->load(['user', 'level']);
            
            return $this->success($vip, 'VIP会员更新成功');
        } catch (\Exception $e) {
            DB::rollBack();
            return $this->error('VIP会员更新失败: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 删除VIP会员
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy($id)
    {
        $vip = Vip::find($id);
        
        if (!$vip) {
            return $this->error('VIP会员不存在', 404);
        }
        
        try {
            DB::beginTransaction();
            
            // 删除VIP会员
            $vip->delete();
            
            // 更新用户VIP状态
            User::where('id', $vip->user_id)->update(['is_vip' => 0]);
            
            // 删除交易记录
            VipTransaction::where('vip_id', $id)->delete();
            
            DB::commit();
            
            return $this->success(null, 'VIP会员删除成功');
        } catch (\Exception $e) {
            DB::rollBack();
            return $this->error('VIP会员删除失败: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * 更新VIP会员状态
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateStatus(Request $request, $id)
    {
        $vip = Vip::find($id);
        
        if (!$vip) {
            return $this->error('VIP会员不存在', 404);
        }
        
        $validator = Validator::make($request->all(), [
            'status' => 'required|integer|in:0,1,2',
        ]);

        if ($validator->fails()) {
            return $this->error('验证失败', 422, $validator->errors());
        }
        
        try {
            DB::beginTransaction();
            
            // 记录原始状态
            $oldStatus = $vip->status;
            
            // 更新VIP会员状态
            $vip->status = $request->status;
            $vip->save();
            
            // 如果状态变更为已过期或已禁用，更新用户VIP状态
            if (($oldStatus == 1 && $request->status != 1) || $request->status == 2) {
                User::where('id', $vip->user_id)->update(['is_vip' => 0]);
            } else if ($oldStatus != 1 && $request->status == 1) {
                User::where('id', $vip->user_id)->update(['is_vip' => 1]);
            }
            
            DB::commit();
            
            // 加载关联数据
            $vip->load(['user', 'level']);
            
            return $this->success($vip, 'VIP会员状态更新成功');
        } catch (\Exception $e) {
            DB::rollBack();
            return $this->error('VIP会员状态更新失败: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * 调整VIP会员积分
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function adjustPoints(Request $request, $id)
    {
        $vip = Vip::find($id);
        
        if (!$vip) {
            return $this->error('VIP会员不存在', 404);
        }
        
        $validator = Validator::make($request->all(), [
            'points' => 'required|integer|not_in:0',
            'remark' => 'required|string',
        ]);

        if ($validator->fails()) {
            return $this->error('验证失败', 422, $validator->errors());
        }
        
        try {
            DB::beginTransaction();
            
            // 调整积分
            $points = $request->points;
            $type = $points > 0 ? 'points_add' : 'points_deduct';
            
            // 如果是扣减积分，确保不会扣成负数
            if ($points < 0 && abs($points) > $vip->points) {
                return $this->error('积分不足，无法扣减', 400);
            }
            
            $vip->points += $points;
            $vip->save();
            
            // 记录交易记录
            $transaction = new VipTransaction();
            $transaction->user_id = $vip->user_id;
            $transaction->vip_id = $vip->id;
            $transaction->type = $type;
            $transaction->level_id = $vip->level_id;
            $transaction->points = abs($points);
            $transaction->remark = $request->remark;
            $transaction->admin_id = auth()->id();
            $transaction->save();
            
            DB::commit();
            
            // 加载关联数据
            $vip->load(['user', 'level']);
            
            return $this->success($vip, 'VIP会员积分调整成功');
        } catch (\Exception $e) {
            DB::rollBack();
            return $this->error('VIP会员积分调整失败: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * 延长VIP会员有效期
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function extendExpiration(Request $request, $id)
    {
        $vip = Vip::find($id);
        
        if (!$vip) {
            return $this->error('VIP会员不存在', 404);
        }
        
        $validator = Validator::make($request->all(), [
            'days' => 'required|integer|min:1',
            'remark' => 'required|string',
        ]);

        if ($validator->fails()) {
            return $this->error('验证失败', 422, $validator->errors());
        }
        
        try {
            DB::beginTransaction();
            
            // 记录原始过期时间
            $oldExpireTime = $vip->expire_time;
            
            // 延长有效期
            $days = $request->days;
            $vip->expire_time = date('Y-m-d H:i:s', strtotime($vip->expire_time . " +{$days} days"));
            
            // 如果已过期或禁用，恢复为正常状态
            if ($vip->status != 1) {
                $vip->status = 1;
                User::where('id', $vip->user_id)->update(['is_vip' => 1]);
            }
            
            $vip->save();
            
            // 记录交易记录
            $transaction = new VipTransaction();
            $transaction->user_id = $vip->user_id;
            $transaction->vip_id = $vip->id;
            $transaction->type = 'extend';
            $transaction->level_id = $vip->level_id;
            $transaction->remark = $request->remark;
            $transaction->admin_id = auth()->id();
            $transaction->save();
            
            DB::commit();
            
            // 加载关联数据
            $vip->load(['user', 'level']);
            
            return $this->success($vip, 'VIP会员有效期延长成功');
        } catch (\Exception $e) {
            DB::rollBack();
            return $this->error('VIP会员有效期延长失败: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * 获取VIP会员交易记录
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function transactions(Request $request, $id)
    {
        $vip = Vip::find($id);
        
        if (!$vip) {
            return $this->error('VIP会员不存在', 404);
        }
        
        $query = VipTransaction::where('vip_id', $id);
        
        // 交易类型筛选
        if ($request->has('type') && !empty($request->type)) {
            $query->where('type', $request->type);
        }
        
        // 日期范围筛选
        if ($request->has('start_date') && !empty($request->start_date)) {
            $query->where('created_at', '>=', $request->start_date);
        }
        
        if ($request->has('end_date') && !empty($request->end_date)) {
            $query->where('created_at', '<=', $request->end_date . ' 23:59:59');
        }
        
        // 排序
        $orderBy = $request->input('order_by', 'created_at');
        $orderDir = $request->input('order_dir', 'desc');
        $query->orderBy($orderBy, $orderDir);
        
        // 分页
        $perPage = $request->input('per_page', 15);
        $transactions = $query->paginate($perPage);
        
        return $this->paginate($transactions);
    }
    
    /**
     * 获取VIP等级列表
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function levels()
    {
        $levels = VipLevel::orderBy('level', 'asc')->get();
        
        return $this->success($levels);
    }
    
    /**
     * 创建VIP等级
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function storeLevel(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:50',
            'level' => 'required|integer|min:1|unique:vip_levels,level',
            'price' => 'required|numeric|min:0',
            'duration' => 'required|integer|min:1',
            'benefits' => 'nullable|array',
            'icon' => 'nullable|string|max:200',
            'description' => 'nullable|string',
            'status' => 'required|integer|in:0,1',
        ]);

        if ($validator->fails()) {
            return $this->error('验证失败', 422, $validator->errors());
        }

        try {
            $level = new VipLevel();
            $level->name = $request->name;
            $level->level = $request->level;
            $level->price = $request->price;
            $level->duration = $request->duration;
            $level->benefits = $request->benefits ? json_encode($request->benefits) : null;
            $level->icon = $request->icon;
            $level->description = $request->description;
            $level->status = $request->status;
            $level->save();
            
            return $this->success($level, 'VIP等级创建成功');
        } catch (\Exception $e) {
            return $this->error('VIP等级创建失败: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * 更新VIP等级
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateLevel(Request $request, $id)
    {
        $level = VipLevel::find($id);
        
        if (!$level) {
            return $this->error('VIP等级不存在', 404);
        }
        
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:50',
            'level' => 'required|integer|min:1|unique:vip_levels,level,'.$id,
            'price' => 'required|numeric|min:0',
            'duration' => 'required|integer|min:1',
            'benefits' => 'nullable|array',
            'icon' => 'nullable|string|max:200',
            'description' => 'nullable|string',
            'status' => 'required|integer|in:0,1',
        ]);

        if ($validator->fails()) {
            return $this->error('验证失败', 422, $validator->errors());
        }
        
        try {
            $level->name = $request->name;
            $level->level = $request->level;
            $level->price = $request->price;
            $level->duration = $request->duration;
            $level->benefits = $request->benefits ? json_encode($request->benefits) : null;
            $level->icon = $request->icon;
            $level->description = $request->description;
            $level->status = $request->status;
            $level->save();
            
            return $this->success($level, 'VIP等级更新成功');
        } catch (\Exception $e) {
            return $this->error('VIP等级更新失败: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * 删除VIP等级
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroyLevel($id)
    {
        $level = VipLevel::find($id);
        
        if (!$level) {
            return $this->error('VIP等级不存在', 404);
        }
        
        // 检查是否有关联的VIP会员
        $vipCount = Vip::where('level_id', $id)->count();
        if ($vipCount > 0) {
            return $this->error('该VIP等级有关联的会员，无法删除', 400);
        }
        
        try {
            $level->delete();
            
            return $this->success(null, 'VIP等级删除成功');
        } catch (\Exception $e) {
            return $this->error('VIP等级删除失败: ' . $e->getMessage(), 500);
        }
    }
}
