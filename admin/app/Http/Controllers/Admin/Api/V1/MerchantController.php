<?php

namespace App\Http\Controllers\Admin\Api\V1;

use App\Http\Controllers\Controller;
use App\Traits\ApiResponseTrait;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;
use App\Models\Merchant;
use App\Models\User;

class MerchantController extends Controller
{
    use ApiResponseTrait;

    /**
     * 获取商户列表
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        $query = Merchant::query();
        
        // 搜索条件
        if ($request->has('keyword') && !empty($request->keyword)) {
            $keyword = $request->keyword;
            $query->where(function($q) use ($keyword) {
                $q->where('name', 'like', "%{$keyword}%")
                  ->orWhere('contact_name', 'like', "%{$keyword}%")
                  ->orWhere('contact_phone', 'like', "%{$keyword}%")
                  ->orWhere('merchant_code', 'like', "%{$keyword}%");
            });
        }
        
        // 状态筛选
        if ($request->has('status') && $request->status !== '') {
            $query->where('status', $request->status);
        }
        
        // 类型筛选
        if ($request->has('type') && !empty($request->type)) {
            $query->where('type', $request->type);
        }
        
        // 区域筛选
        if ($request->has('region') && !empty($request->region)) {
            $query->where('region', $request->region);
        }
        
        // 业务员筛选
        if ($request->has('salesman_id') && !empty($request->salesman_id)) {
            $query->where('salesman_id', $request->salesman_id);
        }
        
        // 排序
        $orderBy = $request->input('order_by', 'created_at');
        $orderDir = $request->input('order_dir', 'desc');
        $query->orderBy($orderBy, $orderDir);
        
        // 分页
        $perPage = $request->input('per_page', 15);
        $merchants = $query->paginate($perPage);
        
        // 加载关联数据
        $merchants->load(['salesman', 'user']);
        
        return $this->paginate($merchants);
    }

    /**
     * 创建商户
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:100',
            'merchant_code' => 'required|string|max:50|unique:merchants,merchant_code',
            'contact_name' => 'required|string|max:50',
            'contact_phone' => 'required|string|regex:/^1[3-9]\d{9}$/',
            'address' => 'nullable|string|max:200',
            'region' => 'nullable|string|max:100',
            'type' => 'required|string|max:50',
            'business_license' => 'nullable|string|max:100',
            'logo' => 'nullable|string|max:200',
            'salesman_id' => 'nullable|integer|exists:salesmen,id',
            'user_id' => 'nullable|integer|exists:users,id',
            'status' => 'required|in:0,1,2',
            'remark' => 'nullable|string',
            'create_user' => 'boolean',
            'password' => 'required_if:create_user,true|nullable|string|min:6',
        ]);

        if ($validator->fails()) {
            return $this->error('验证失败', 422, $validator->errors());
        }

        try {
            DB::beginTransaction();
            
            // 如果需要创建用户账号
            $userId = $request->user_id;
            if ($request->input('create_user', false) && empty($userId)) {
                // 创建用户账号
                $user = new User();
                $user->name = $request->contact_name;
                $user->phone = $request->contact_phone;
                $user->password = Hash::make($request->password);
                $user->status = $request->status == 1 ? 1 : 0;
                $user->save();
                
                // 分配商户角色
                if (class_exists('Spatie\Permission\Models\Role')) {
                    $role = \Spatie\Permission\Models\Role::where('name', 'merchant')->first();
                    if ($role) {
                        $user->assignRole($role);
                    }
                }
                
                $userId = $user->id;
            }
            
            // 创建商户记录
            $merchant = new Merchant();
            $merchant->name = $request->name;
            $merchant->merchant_code = $request->merchant_code;
            $merchant->contact_name = $request->contact_name;
            $merchant->contact_phone = $request->contact_phone;
            $merchant->address = $request->address;
            $merchant->region = $request->region;
            $merchant->type = $request->type;
            $merchant->business_license = $request->business_license;
            $merchant->logo = $request->logo;
            $merchant->salesman_id = $request->salesman_id;
            $merchant->user_id = $userId;
            $merchant->status = $request->status;
            $merchant->remark = $request->remark;
            $merchant->save();
            
            DB::commit();
            
            // 加载关联数据
            $merchant->load(['salesman', 'user']);
            
            return $this->success($merchant, '商户创建成功');
        } catch (\Exception $e) {
            DB::rollBack();
            return $this->error('商户创建失败: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 获取单个商户详情
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        $merchant = Merchant::with(['salesman', 'user'])->find($id);
        
        if (!$merchant) {
            return $this->error('商户不存在', 404);
        }
        
        // 获取商户统计数据
        $stats = $this->getMerchantStats($id);
        $merchant->stats = $stats;
        
        return $this->success($merchant);
    }

    /**
     * 更新商户
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id)
    {
        $merchant = Merchant::find($id);
        
        if (!$merchant) {
            return $this->error('商户不存在', 404);
        }
        
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:100',
            'merchant_code' => 'required|string|max:50|unique:merchants,merchant_code,'.$id,
            'contact_name' => 'required|string|max:50',
            'contact_phone' => 'required|string|regex:/^1[3-9]\d{9}$/',
            'address' => 'nullable|string|max:200',
            'region' => 'nullable|string|max:100',
            'type' => 'required|string|max:50',
            'business_license' => 'nullable|string|max:100',
            'logo' => 'nullable|string|max:200',
            'salesman_id' => 'nullable|integer|exists:salesmen,id',
            'user_id' => 'nullable|integer|exists:users,id',
            'status' => 'required|in:0,1,2',
            'remark' => 'nullable|string',
            'create_user' => 'boolean',
            'password' => 'required_if:create_user,true|nullable|string|min:6',
        ]);

        if ($validator->fails()) {
            return $this->error('验证失败', 422, $validator->errors());
        }
        
        try {
            DB::beginTransaction();
            
            // 处理用户账号
            $userId = $request->user_id;
            if ($request->input('create_user', false) && empty($userId) && empty($merchant->user_id)) {
                // 创建用户账号
                $user = new User();
                $user->name = $request->contact_name;
                $user->phone = $request->contact_phone;
                $user->password = Hash::make($request->password);
                $user->status = $request->status == 1 ? 1 : 0;
                $user->save();
                
                // 分配商户角色
                if (class_exists('Spatie\Permission\Models\Role')) {
                    $role = \Spatie\Permission\Models\Role::where('name', 'merchant')->first();
                    if ($role) {
                        $user->assignRole($role);
                    }
                }
                
                $userId = $user->id;
            } elseif ($merchant->user_id) {
                // 更新现有用户账号
                $user = User::find($merchant->user_id);
                if ($user) {
                    $user->name = $request->contact_name;
                    $user->phone = $request->contact_phone;
                    if ($request->has('password') && !empty($request->password)) {
                        $user->password = Hash::make($request->password);
                    }
                    $user->status = $request->status == 1 ? 1 : 0;
                    $user->save();
                }
                
                $userId = $merchant->user_id;
            }
            
            // 更新商户记录
            $merchant->name = $request->name;
            $merchant->merchant_code = $request->merchant_code;
            $merchant->contact_name = $request->contact_name;
            $merchant->contact_phone = $request->contact_phone;
            $merchant->address = $request->address;
            $merchant->region = $request->region;
            $merchant->type = $request->type;
            $merchant->business_license = $request->business_license;
            $merchant->logo = $request->logo;
            $merchant->salesman_id = $request->salesman_id;
            $merchant->user_id = $userId;
            $merchant->status = $request->status;
            $merchant->remark = $request->remark;
            $merchant->save();
            
            DB::commit();
            
            // 加载关联数据
            $merchant->load(['salesman', 'user']);
            
            return $this->success($merchant, '商户更新成功');
        } catch (\Exception $e) {
            DB::rollBack();
            return $this->error('商户更新失败: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 删除商户
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy($id)
    {
        $merchant = Merchant::find($id);
        
        if (!$merchant) {
            return $this->error('商户不存在', 404);
        }
        
        // 检查是否有关联设备
        $deviceCount = 0;
        if (class_exists('App\Models\Device')) {
            $deviceCount = \App\Models\Device::where('merchant_id', $id)->count();
            if ($deviceCount > 0) {
                return $this->error('该商户下有关联设备，无法删除', 400);
            }
        }
        
        // 检查是否有关联订单
        $orderCount = 0;
        if (class_exists('App\Models\Order')) {
            $orderCount = \App\Models\Order::where('merchant_id', $id)->count();
            if ($orderCount > 0) {
                return $this->error('该商户有关联订单，无法删除', 400);
            }
        }
        
        try {
            DB::beginTransaction();
            
            // 删除商户记录
            $merchant->delete();
            
            // 注意：不删除关联的用户账号，避免数据丢失
            
            DB::commit();
            
            return $this->success(null, '商户删除成功');
        } catch (\Exception $e) {
            DB::rollBack();
            return $this->error('商户删除失败: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * 获取商户统计数据
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function stats($id)
    {
        $merchant = Merchant::find($id);
        
        if (!$merchant) {
            return $this->error('商户不存在', 404);
        }
        
        $stats = $this->getMerchantStats($id);
        
        return $this->success($stats);
    }
    
    /**
     * 获取商户设备列表
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function devices(Request $request, $id)
    {
        $merchant = Merchant::find($id);
        
        if (!$merchant) {
            return $this->error('商户不存在', 404);
        }
        
        if (!class_exists('App\Models\Device')) {
            return $this->error('设备模型不存在', 500);
        }
        
        $query = \App\Models\Device::where('merchant_id', $id);
        
        // 设备类型筛选
        if ($request->has('device_type') && !empty($request->device_type)) {
            $query->where('device_type', $request->device_type);
        }
        
        // 状态筛选
        if ($request->has('status') && $request->status !== '') {
            $query->where('status', $request->status);
        }
        
        // 排序
        $orderBy = $request->input('order_by', 'created_at');
        $orderDir = $request->input('order_dir', 'desc');
        $query->orderBy($orderBy, $orderDir);
        
        // 分页
        $perPage = $request->input('per_page', 15);
        $devices = $query->paginate($perPage);
        
        return $this->paginate($devices);
    }
    
    /**
     * 获取商户订单列表
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function orders(Request $request, $id)
    {
        $merchant = Merchant::find($id);
        
        if (!$merchant) {
            return $this->error('商户不存在', 404);
        }
        
        if (!class_exists('App\Models\Order')) {
            return $this->error('订单模型不存在', 500);
        }
        
        $query = \App\Models\Order::where('merchant_id', $id);
        
        // 订单状态筛选
        if ($request->has('status') && $request->status !== '') {
            $query->where('status', $request->status);
        }
        
        // 支付状态筛选
        if ($request->has('pay_status') && $request->pay_status !== '') {
            $query->where('pay_status', $request->pay_status);
        }
        
        // 时间范围筛选
        if ($request->has('start_time') && !empty($request->start_time)) {
            $query->where('created_at', '>=', $request->start_time);
        }
        
        if ($request->has('end_time') && !empty($request->end_time)) {
            $query->where('created_at', '<=', $request->end_time . ' 23:59:59');
        }
        
        // 排序
        $orderBy = $request->input('order_by', 'created_at');
        $orderDir = $request->input('order_dir', 'desc');
        $query->orderBy($orderBy, $orderDir);
        
        // 分页
        $perPage = $request->input('per_page', 15);
        $orders = $query->paginate($perPage);
        
        // 加载关联数据
        $orders->load(['user', 'items.product']);
        
        return $this->paginate($orders);
    }
    
    /**
     * 获取商户类型列表
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function types()
    {
        $types = Merchant::select('type')
            ->distinct()
            ->whereNotNull('type')
            ->pluck('type');
            
        return $this->success($types);
    }
    
    /**
     * 获取商户区域列表
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function regions()
    {
        $regions = Merchant::select('region')
            ->distinct()
            ->whereNotNull('region')
            ->pluck('region');
            
        return $this->success($regions);
    }
    
    /**
     * 获取商户统计数据
     *
     * @param int $merchantId
     * @return array
     */
    private function getMerchantStats($merchantId)
    {
        // 设备统计
        $deviceCount = 0;
        $onlineDeviceCount = 0;
        
        if (class_exists('App\Models\Device')) {
            $deviceCount = \App\Models\Device::where('merchant_id', $merchantId)->count();
            $onlineDeviceCount = \App\Models\Device::where('merchant_id', $merchantId)
                ->where('is_online', 1)
                ->count();
        }
        
        // 订单统计
        $orderCount = 0;
        $orderAmount = 0;
        
        if (class_exists('App\Models\Order')) {
            $orderStats = \App\Models\Order::where('merchant_id', $merchantId)
                ->selectRaw('COUNT(*) as count, SUM(CASE WHEN pay_status = 1 THEN total_amount ELSE 0 END) as amount')
                ->first();
                
            $orderCount = $orderStats->count ?? 0;
            $orderAmount = $orderStats->amount ?? 0;
        }
        
        return [
            'device_count' => $deviceCount,
            'online_device_count' => $onlineDeviceCount,
            'device_online_rate' => $deviceCount > 0 ? round($onlineDeviceCount / $deviceCount * 100, 2) : 0,
            'order_count' => $orderCount,
            'order_amount' => $orderAmount,
        ];
    }
}
