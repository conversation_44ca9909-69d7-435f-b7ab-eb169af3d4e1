<?php

namespace App\Http\Controllers\Admin\Api\V1;

use App\Http\Controllers\Controller;
use App\Traits\ApiResponseTrait;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;
use App\Models\Admin;

class AdminController extends Controller
{
    use ApiResponseTrait;

    /**
     * 获取后台管理员列表
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        $query = Admin::query();
        
        // 搜索条件
        if ($request->has('keyword') && !empty($request->keyword)) {
            $keyword = $request->keyword;
            $query->where(function($q) use ($keyword) {
                $q->where('name', 'like', "%{$keyword}%")
                  ->orWhere('username', 'like', "%{$keyword}%")
                  ->orWhere('phone', 'like', "%{$keyword}%")
                  ->orWhere('email', 'like', "%{$keyword}%");
            });
        }
        
        // 状态筛选
        if ($request->has('status') && $request->status !== '') {
            $query->where('status', $request->status);
        }
        
        // 角色筛选
        if ($request->has('role') && $request->role !== '') {
            $query->where('role', $request->role);
        }
        
        // 排序
        $orderBy = $request->input('order_by', 'created_at');
        $orderDir = $request->input('order_dir', 'desc');
        $query->orderBy($orderBy, $orderDir);
        
        // 分页
        $perPage = $request->input('per_page', 15);
        $admins = $query->paginate($perPage);
        
        return $this->paginate($admins);
    }

    /**
     * 创建后台管理员
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'username' => 'required|string|max:50|unique:admin_users,username',
            'name' => 'required|string|max:50',
            'phone' => 'nullable|string|regex:/^1[3-9]\d{9}$/|unique:admin_users,phone',
            'email' => 'nullable|email|max:100|unique:admin_users,email',
            'password' => 'required|string|min:6',
            'avatar' => 'nullable|string|max:200',
            'role' => 'required|in:super_admin,admin',
            'status' => 'required|in:active,disabled',
        ]);

        if ($validator->fails()) {
            return $this->error('验证失败', 422, $validator->errors());
        }

        try {
            DB::beginTransaction();
            
            $admin = new Admin();
            $admin->username = $request->username;
            $admin->name = $request->name;
            $admin->phone = $request->phone;
            $admin->email = $request->email;
            $admin->password = Hash::make($request->password);
            $admin->avatar = $request->avatar;
            $admin->role = $request->role;
            $admin->status = $request->status;
            $admin->save();
            
            DB::commit();
            
            return $this->success($admin, '管理员创建成功');
        } catch (\Exception $e) {
            DB::rollBack();
            return $this->error('管理员创建失败: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 获取单个管理员详情
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        $admin = Admin::find($id);
        
        if (!$admin) {
            return $this->error('管理员不存在', 404);
        }
        
        return $this->success($admin);
    }

    /**
     * 更新管理员
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id)
    {
        $admin = Admin::find($id);
        
        if (!$admin) {
            return $this->error('管理员不存在', 404);
        }
        
        $validator = Validator::make($request->all(), [
            'username' => 'required|string|max:50|unique:admin_users,username,'.$id,
            'name' => 'required|string|max:50',
            'phone' => 'nullable|string|regex:/^1[3-9]\d{9}$/|unique:admin_users,phone,'.$id,
            'email' => 'nullable|email|max:100|unique:admin_users,email,'.$id,
            'password' => 'nullable|string|min:6',
            'avatar' => 'nullable|string|max:200',
            'role' => 'required|in:super_admin,admin',
            'status' => 'required|in:active,disabled',
        ]);

        if ($validator->fails()) {
            return $this->error('验证失败', 422, $validator->errors());
        }
        
        try {
            DB::beginTransaction();
            
            $admin->username = $request->username;
            $admin->name = $request->name;
            $admin->phone = $request->phone;
            $admin->email = $request->email;
            
            // 如果提供了密码，则更新密码
            if ($request->has('password') && !empty($request->password)) {
                $admin->password = Hash::make($request->password);
            }
            
            $admin->avatar = $request->avatar;
            $admin->role = $request->role;
            $admin->status = $request->status;
            $admin->save();
            
            DB::commit();
            
            return $this->success($admin, '管理员更新成功');
        } catch (\Exception $e) {
            DB::rollBack();
            return $this->error('管理员更新失败: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 删除管理员
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy($id)
    {
        $admin = Admin::find($id);
        
        if (!$admin) {
            return $this->error('管理员不存在', 404);
        }
        
        // 检查是否为超级管理员
        if ($admin->role === 'super_admin') {
            return $this->error('超级管理员不能删除', 400);
        }
        
        // 检查是否为当前登录用户
        if ($admin->id == auth()->id()) {
            return $this->error('不能删除当前登录用户', 400);
        }
        
        try {
            DB::beginTransaction();
            
            // 删除管理员
            $admin->delete();
            
            DB::commit();
            
            return $this->success(null, '管理员删除成功');
        } catch (\Exception $e) {
            DB::rollBack();
            return $this->error('管理员删除失败: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * 更新管理员状态
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateStatus(Request $request, $id)
    {
        $admin = Admin::find($id);
        
        if (!$admin) {
            return $this->error('管理员不存在', 404);
        }
        
        // 检查是否为超级管理员
        if ($admin->role === 'super_admin') {
            return $this->error('超级管理员状态不能修改', 400);
        }
        
        // 检查是否为当前登录用户
        if ($admin->id == auth()->id()) {
            return $this->error('不能修改当前登录用户的状态', 400);
        }
        
        $validator = Validator::make($request->all(), [
            'status' => 'required|in:active,disabled',
        ]);

        if ($validator->fails()) {
            return $this->error('验证失败', 422, $validator->errors());
        }
        
        try {
            $admin->status = $request->status;
            $admin->save();
            
            return $this->success($admin, '管理员状态更新成功');
        } catch (\Exception $e) {
            return $this->error('管理员状态更新失败: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * 重置管理员密码
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function resetPassword(Request $request, $id)
    {
        $admin = Admin::find($id);
        
        if (!$admin) {
            return $this->error('管理员不存在', 404);
        }
        
        $validator = Validator::make($request->all(), [
            'password' => 'required|string|min:6',
        ]);

        if ($validator->fails()) {
            return $this->error('验证失败', 422, $validator->errors());
        }
        
        try {
            $admin->password = Hash::make($request->password);
            $admin->save();
            
            return $this->success(null, '管理员密码重置成功');
        } catch (\Exception $e) {
            return $this->error('管理员密码重置失败: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * 获取所有角色列表
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function roles()
    {
        $roles = [
            ['value' => 'super_admin', 'label' => '超级管理员'],
            ['value' => 'admin', 'label' => '管理员']
        ];
        
        return $this->success($roles);
    }
}
