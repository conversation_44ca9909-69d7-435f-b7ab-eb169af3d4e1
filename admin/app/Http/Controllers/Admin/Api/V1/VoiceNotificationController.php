<?php

namespace App\Http\Controllers\Admin\Api\V1;

use App\Http\Controllers\Controller;
use App\Traits\ApiResponseTrait;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;
use App\Models\AdminNotification;

class VoiceNotificationController extends Controller
{
    use ApiResponseTrait;

    /**
     * 获取需要语音播报的通知列表
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function list(Request $request)
    {
        try {
            // 获取所有未读通知，然后在PHP中过滤
            $allNotifications = AdminNotification::where('is_read', false)
                ->orderBy('created_at', 'desc')
                ->get();

            // 在PHP中过滤需要语音播报且未播放的通知
            $notifications = $allNotifications->filter(function ($notification) {
                $extraData = $notification->extra_data ?: [];
                return isset($extraData['voice_enabled']) && 
                       $extraData['voice_enabled'] === true &&
                       isset($extraData['voice_played']) && 
                       $extraData['voice_played'] === false;
            });

            // 格式化数据
            $formattedNotifications = $notifications->map(function ($notification) {
                $extraData = $notification->extra_data ?: [];
                
                return [
                    'id' => $notification->id,
                    'title' => $notification->title,
                    'content' => $notification->content,
                    'type' => $notification->type,
                    'priority' => $notification->priority,
                    'created_at' => $notification->created_at,
                    'voice_text' => $extraData['voice_text'] ?? $notification->title,
                    'voice_speed' => $extraData['voice_speed'] ?? 1.0,
                    'voice_volume' => $extraData['voice_volume'] ?? 1.0
                ];
            })->values(); // 使用values()方法确保返回数组而不是对象

            return response()->json([
                'code' => 200,
                'message' => '获取成功',
                'data' => $formattedNotifications
            ]);
        } catch (\Exception $e) {
            \Log::error('获取语音通知列表失败: ' . $e->getMessage());
            return response()->json([
                'code' => 500,
                'message' => '获取失败: ' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

    /**
     * 标记语音通知已播放
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function markPlayed(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'notification_id' => 'required|integer|exists:admin_notifications,id'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'code' => 422,
                'message' => '验证失败',
                'data' => $validator->errors()
            ]);
        }

        try {
            $notification = AdminNotification::find($request->notification_id);
            
            if (!$notification) {
                return response()->json([
                    'code' => 404,
                    'message' => '通知不存在',
                    'data' => null
                ]);
            }

            // 更新extra_data中的voice_played字段
            $extraData = $notification->extra_data ?: [];
            $extraData['voice_played'] = true;
            $extraData['voice_played_at'] = now()->toISOString();
            
            $notification->extra_data = $extraData;
            $notification->save();

            return response()->json([
                'code' => 200,
                'message' => '标记成功',
                'data' => null
            ]);
        } catch (\Exception $e) {
            \Log::error('标记语音通知已播放失败: ' . $e->getMessage());
            return response()->json([
                'code' => 500,
                'message' => '标记失败: ' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

    /**
     * 创建测试语音通知
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function test(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'text' => 'required|string|max:500',
            'speed' => 'nullable|numeric|min:0.1|max:3.0',
            'volume' => 'nullable|numeric|min:0.1|max:1.0'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'code' => 422,
                'message' => '验证失败',
                'data' => $validator->errors()
            ]);
        }

        try {
            // 创建测试通知
            $notification = new AdminNotification();
            $notification->type = 'info';
            $notification->title = '语音播报测试';
            $notification->content = $request->text;
            $notification->admin_id = 1; // 默认管理员
            $notification->priority = 'normal';
            $notification->is_read = false;
            
            // 设置语音相关参数
            $extraData = [
                'voice_enabled' => true,
                'voice_played' => false,
                'voice_text' => $request->text,
                'voice_speed' => $request->speed ?? 1.0,
                'voice_volume' => $request->volume ?? 1.0,
                'is_test' => true
            ];
            
            $notification->extra_data = $extraData;
            $notification->save();

            return response()->json([
                'code' => 200,
                'message' => '测试通知创建成功',
                'data' => [
                    'notification_id' => $notification->id,
                    'text' => $request->text,
                    'speed' => $request->speed ?? 1.0,
                    'volume' => $request->volume ?? 1.0
                ]
            ]);
        } catch (\Exception $e) {
            \Log::error('创建测试语音通知失败: ' . $e->getMessage());
            return response()->json([
                'code' => 500,
                'message' => '创建失败: ' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

    /**
     * 批量标记语音通知已播放
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function batchMarkPlayed(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'notification_ids' => 'required|array',
            'notification_ids.*' => 'integer|exists:admin_notifications,id'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'code' => 422,
                'message' => '验证失败',
                'data' => $validator->errors()
            ]);
        }

        try {
            $updatedCount = 0;
            
            foreach ($request->notification_ids as $notificationId) {
                $notification = AdminNotification::find($notificationId);
                
                if ($notification) {
                    $extraData = json_decode($notification->extra_data, true) ?: [];
                    $extraData['voice_played'] = true;
                    $extraData['voice_played_at'] = now()->toISOString();
                    
                    $notification->extra_data = json_encode($extraData);
                    $notification->save();
                    
                    $updatedCount++;
                }
            }

            return response()->json([
                'code' => 200,
                'message' => "批量标记成功，共更新 {$updatedCount} 条通知",
                'data' => ['updated_count' => $updatedCount]
            ]);
        } catch (\Exception $e) {
            \Log::error('批量标记语音通知已播放失败: ' . $e->getMessage());
            return response()->json([
                'code' => 500,
                'message' => '批量标记失败: ' . $e->getMessage(),
                'data' => null
            ]);
        }
    }
}