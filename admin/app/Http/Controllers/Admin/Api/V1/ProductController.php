<?php

namespace App\Http\Controllers\Admin\Api\V1;

use App\Http\Controllers\Controller;
use App\Traits\ApiResponseTrait;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Storage;
use App\Models\Product;
use App\Models\ProductCategory;

class ProductController extends Controller
{
    use ApiResponseTrait;

    /**
     * 获取产品列表
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        $query = Product::query();
        
        // 搜索条件
        if ($request->has('keyword') && !empty($request->keyword)) {
            $keyword = $request->keyword;
            $query->where(function($q) use ($keyword) {
                $q->where('name', 'like', "%{$keyword}%")
                  ->orWhere('product_code', 'like', "%{$keyword}%");
            });
        }
        
        // 分类筛选
        if ($request->has('category_id') && !empty($request->category_id)) {
            $categoryId = $request->category_id;
            
            // 获取所有子分类ID
            $childCategoryIds = [];
            if ($request->input('include_child_categories', false)) {
                $childCategoryIds = $this->getAllChildCategoryIds($categoryId);
            }
            
            $categoryIds = array_merge([$categoryId], $childCategoryIds);
            $query->whereIn('category_id', $categoryIds);
        }
        
        // 状态筛选
        if ($request->has('status') && $request->status !== '') {
            $query->where('status', $request->status);
        }
        
        // 价格区间筛选
        if ($request->has('min_price') && is_numeric($request->min_price)) {
            $query->where('price', '>=', $request->min_price);
        }
        
        if ($request->has('max_price') && is_numeric($request->max_price)) {
            $query->where('price', '<=', $request->max_price);
        }
        
        // 排序
        $orderBy = $request->input('order_by', 'created_at');
        $orderDir = $request->input('order_dir', 'desc');
        $query->orderBy($orderBy, $orderDir);
        
        // 分页
        $perPage = $request->input('per_page', 15);
        $products = $query->paginate($perPage);
        
        // 加载分类关系
        $products->load('category');
        
        return $this->paginate($products);
    }

    /**
     * 创建产品
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:100',
            'product_code' => 'nullable|string|max:50|unique:products,product_code',
            'category_id' => 'required|integer|exists:product_categories,id',
            'price' => 'required|numeric|min:0',
            'original_price' => 'nullable|numeric|min:0',
            'stock' => 'nullable|integer|min:0',
            'images' => 'nullable|array',
            'main_image' => 'required|string',
            'description' => 'nullable|string',
            'content' => 'nullable|string',
            'status' => 'required|in:0,1',
            'is_recommend' => 'nullable|in:0,1',
            'is_hot' => 'nullable|in:0,1',
            'is_new' => 'nullable|in:0,1',
            'tags' => 'nullable|array',
            'sort' => 'nullable|integer',
        ]);

        if ($validator->fails()) {
            return $this->error('验证失败', 422, $validator->errors());
        }

        $product = new Product();
        $product->name = $request->name;
        $product->product_code = $request->product_code;
        $product->category_id = $request->category_id;
        $product->price = $request->price;
        $product->original_price = $request->original_price;
        $product->stock = $request->stock ?? 0;
        $product->images = json_encode($request->images ?? []);
        $product->main_image = $request->main_image;
        $product->description = $request->description;
        $product->content = $request->content;
        $product->status = $request->status;
        $product->is_recommend = $request->is_recommend ?? 0;
        $product->is_hot = $request->is_hot ?? 0;
        $product->is_new = $request->is_new ?? 0;
        $product->tags = json_encode($request->tags ?? []);
        $product->sort = $request->sort ?? 0;
        $product->save();
        
        return $this->success($product, '产品创建成功');
    }

    /**
     * 获取单个产品详情
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        $product = Product::with('category')->find($id);
        
        if (!$product) {
            return $this->error('产品不存在', 404);
        }
        
        // 处理JSON字段
        $product->images = json_decode($product->images);
        $product->tags = json_decode($product->tags);
        
        return $this->success($product);
    }

    /**
     * 更新产品
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id)
    {
        $product = Product::find($id);
        
        if (!$product) {
            return $this->error('产品不存在', 404);
        }
        
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:100',
            'product_code' => 'nullable|string|max:50|unique:products,product_code,'.$id,
            'category_id' => 'required|integer|exists:product_categories,id',
            'price' => 'required|numeric|min:0',
            'original_price' => 'nullable|numeric|min:0',
            'stock' => 'nullable|integer|min:0',
            'images' => 'nullable|array',
            'main_image' => 'required|string',
            'description' => 'nullable|string',
            'content' => 'nullable|string',
            'status' => 'required|in:0,1',
            'is_recommend' => 'nullable|in:0,1',
            'is_hot' => 'nullable|in:0,1',
            'is_new' => 'nullable|in:0,1',
            'tags' => 'nullable|array',
            'sort' => 'nullable|integer',
        ]);

        if ($validator->fails()) {
            return $this->error('验证失败', 422, $validator->errors());
        }
        
        $product->name = $request->name;
        $product->product_code = $request->product_code;
        $product->category_id = $request->category_id;
        $product->price = $request->price;
        $product->original_price = $request->original_price;
        $product->stock = $request->stock ?? $product->stock;
        $product->images = json_encode($request->images ?? []);
        $product->main_image = $request->main_image;
        $product->description = $request->description;
        $product->content = $request->content;
        $product->status = $request->status;
        $product->is_recommend = $request->is_recommend ?? $product->is_recommend;
        $product->is_hot = $request->is_hot ?? $product->is_hot;
        $product->is_new = $request->is_new ?? $product->is_new;
        $product->tags = json_encode($request->tags ?? []);
        $product->sort = $request->sort ?? $product->sort;
        $product->save();
        
        return $this->success($product, '产品更新成功');
    }

    /**
     * 删除产品
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy($id)
    {
        $product = Product::find($id);
        
        if (!$product) {
            return $this->error('产品不存在', 404);
        }
        
        // 检查是否有关联订单
        $orderCount = 0;
        if (class_exists('App\Models\OrderItem')) {
            $orderCount = \App\Models\OrderItem::where('product_id', $id)->count();
            if ($orderCount > 0) {
                return $this->error('该产品已有订单，无法删除', 400);
            }
        }
        
        $product->delete();
        
        return $this->success(null, '产品删除成功');
    }
    
    /**
     * 更新产品状态
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateStatus(Request $request, $id)
    {
        $product = Product::find($id);
        
        if (!$product) {
            return $this->error('产品不存在', 404);
        }
        
        $validator = Validator::make($request->all(), [
            'status' => 'required|in:0,1',
        ]);

        if ($validator->fails()) {
            return $this->error('验证失败', 422, $validator->errors());
        }
        
        $product->status = $request->status;
        $product->save();
        
        return $this->success($product, '状态更新成功');
    }
    
    /**
     * 更新产品库存
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateStock(Request $request, $id)
    {
        $product = Product::find($id);
        
        if (!$product) {
            return $this->error('产品不存在', 404);
        }
        
        $validator = Validator::make($request->all(), [
            'stock' => 'required|integer|min:0',
        ]);

        if ($validator->fails()) {
            return $this->error('验证失败', 422, $validator->errors());
        }
        
        $product->stock = $request->stock;
        $product->save();
        
        return $this->success($product, '库存更新成功');
    }
    
    /**
     * 批量更新产品状态
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function batchUpdateStatus(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'ids' => 'required|array',
            'ids.*' => 'required|integer|exists:products,id',
            'status' => 'required|in:0,1',
        ]);

        if ($validator->fails()) {
            return $this->error('验证失败', 422, $validator->errors());
        }
        
        Product::whereIn('id', $request->ids)->update(['status' => $request->status]);
        
        return $this->success(null, '批量更新状态成功');
    }
    
    /**
     * 获取所有子分类ID
     *
     * @param int $categoryId
     * @return array
     */
    private function getAllChildCategoryIds($categoryId)
    {
        $childIds = [];
        
        $children = ProductCategory::where('parent_id', $categoryId)->get();
        
        foreach ($children as $child) {
            $childIds[] = $child->id;
            $childIds = array_merge($childIds, $this->getAllChildCategoryIds($child->id));
        }
        
        return $childIds;
    }
}
