<?php

namespace App\Http\Controllers\Admin\Api\V1;

use App\Http\Controllers\Controller;
use App\Traits\ApiResponseTrait;
use App\Services\SalesmanCommissionService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;
use App\Models\Salesman;
use App\Models\SalesmanSale;
use App\Models\SalesmanCustomer;
use App\Models\TappDevice;
use Carbon\Carbon;

class SalesmanSaleController extends Controller
{
    use ApiResponseTrait;

    protected $commissionService;

    public function __construct(SalesmanCommissionService $commissionService)
    {
        $this->commissionService = $commissionService;
    }

    /**
     * 获取业务员销售记录列表
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        try {
            $query = SalesmanSale::with(['salesman.user']);
            
            // 业务员筛选
            if ($request->filled('salesman_id')) {
                $query->where('salesman_id', $request->input('salesman_id'));
            }
            
            // 状态筛选
            if ($request->filled('status') && $request->input('status') !== '') {
                $query->where('status', $request->input('status'));
            }
            
            // 产品筛选
            if ($request->filled('product_name')) {
                $query->where('product_name', 'like', '%' . $request->input('product_name') . '%');
            }
            
            // 客户筛选
            if ($request->filled('customer_name')) {
                $query->where('customer_name', 'like', '%' . $request->input('customer_name') . '%');
            }
            
            // 日期范围筛选
            if ($request->filled('start_date')) {
                $query->where('sale_date', '>=', $request->input('start_date'));
            }
            if ($request->filled('end_date')) {
                $query->where('sale_date', '<=', $request->input('end_date'));
            }
            
            // 排序
            $orderBy = $request->input('order_by', 'sale_date');
            $orderDir = $request->input('order_dir', 'desc');
            $query->orderBy($orderBy, $orderDir);
            
            // 分页
            $perPage = $request->input('per_page', 15);
            $page = $request->input('page', 1);
            
            $sales = $query->paginate($perPage, ['*'], 'page', $page);
            
            return $this->paginate($sales);
            
        } catch (\Exception $e) {
            return $this->error('获取销售记录失败：'.$e->getMessage(), 500);
        }
    }

    /**
     * 创建销售记录
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'salesman_id' => 'required|exists:salesmen,id',
            'product_name' => 'required|string|max:255',
            'product_id' => 'nullable|string|max:100',
            'quantity' => 'required|integer|min:1',
            'amount' => 'required|numeric|min:0',
            'commission_rate' => 'required|numeric|min:0|max:100',
            'customer_name' => 'required|string|max:100',
            'customer_phone' => 'nullable|string|max:20',
            'sale_date' => 'required|date',
            'order_id' => 'nullable|string|max:100',
            'remarks' => 'nullable|string|max:500'
        ]);

        if ($validator->fails()) {
            return $this->error('验证失败', 422, $validator->errors());
        }

        DB::beginTransaction();
        
        try {
            // 计算提成金额
            $commissionAmount = ($request->input('amount') * $request->input('commission_rate')) / 100;
            
            // 创建销售记录
            $sale = SalesmanSale::create([
                'salesman_id' => $request->input('salesman_id'),
                'product_name' => $request->input('product_name'),
                'product_id' => $request->input('product_id'),
                'quantity' => $request->input('quantity'),
                'amount' => $request->input('amount'),
                'commission_rate' => $request->input('commission_rate'),
                'commission_amount' => $commissionAmount,
                'customer_name' => $request->input('customer_name'),
                'customer_phone' => $request->input('customer_phone'),
                'sale_date' => $request->input('sale_date'),
                'order_id' => $request->input('order_id'),
                'status' => 'completed',
                'remarks' => $request->input('remarks')
            ]);
            
            // 更新或创建客户记录
            $this->updateCustomerRecord($sale);
            
            DB::commit();
            
            // 加载关联数据
            $sale->load('salesman.user');
            
            return $this->success($sale, '销售记录创建成功');
                
        } catch (\Exception $e) {
            DB::rollBack();
            
            return $this->error('销售记录创建失败：'.$e->getMessage(), 500);
        }
    }

    /**
     * 获取销售记录详情
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        $sale = SalesmanSale::with(['salesman.user'])->find($id);
        
        if (!$sale) {
            return $this->error('销售记录不存在', 404);
        }
        
        return $this->success($sale);
    }

    /**
     * 更新销售记录
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id)
    {
        $sale = SalesmanSale::find($id);
        
        if (!$sale) {
            return $this->error('销售记录不存在', 404);
        }
        
        $validator = Validator::make($request->all(), [
            'product_name' => 'required|string|max:255',
            'product_id' => 'nullable|string|max:100',
            'quantity' => 'required|integer|min:1',
            'amount' => 'required|numeric|min:0',
            'commission_rate' => 'required|numeric|min:0|max:100',
            'customer_name' => 'required|string|max:100',
            'customer_phone' => 'nullable|string|max:20',
            'sale_date' => 'required|date',
            'order_id' => 'nullable|string|max:100',
            'status' => 'required|in:pending,completed,cancelled',
            'remarks' => 'nullable|string|max:500'
        ]);

        if ($validator->fails()) {
            return $this->error('验证失败', 422, $validator->errors());
        }
        
        DB::beginTransaction();
        
        try {
            // 计算提成金额
            $commissionAmount = ($request->input('amount') * $request->input('commission_rate')) / 100;
            
            // 更新销售记录
            $sale->update([
                'product_name' => $request->input('product_name'),
                'product_id' => $request->input('product_id'),
                'quantity' => $request->input('quantity'),
                'amount' => $request->input('amount'),
                'commission_rate' => $request->input('commission_rate'),
                'commission_amount' => $commissionAmount,
                'customer_name' => $request->input('customer_name'),
                'customer_phone' => $request->input('customer_phone'),
                'sale_date' => $request->input('sale_date'),
                'order_id' => $request->input('order_id'),
                'status' => $request->input('status'),
                'remarks' => $request->input('remarks')
            ]);
            
            // 更新客户记录
            $this->updateCustomerRecord($sale);
            
            DB::commit();
            
            // 加载关联数据
            $sale->load('salesman.user');
            
            return $this->success($sale, '销售记录更新成功');
                
        } catch (\Exception $e) {
            DB::rollBack();
            
            return $this->error('销售记录更新失败：'.$e->getMessage(), 500);
        }
    }

    /**
     * 删除销售记录
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy($id)
    {
        $sale = SalesmanSale::find($id);
        
        if (!$sale) {
            return $this->error('销售记录不存在', 404);
        }
        
        try {
            $sale->delete();
            
            return $this->success(null, '销售记录删除成功');
                
        } catch (\Exception $e) {
            return $this->error('销售记录删除失败：'.$e->getMessage(), 500);
        }
    }

    /**
     * 获取设备销售统计
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function deviceSalesStats(Request $request)
    {
        try {
            $salesmanId = $request->input('salesman_id');
            $timeRange = $request->input('time_range', 'month');
            
            if (!$salesmanId) {
                return $this->error('业务员ID不能为空', 400);
            }
            
            $stats = $this->commissionService->getDeviceSalesStats($salesmanId, $timeRange);
            
            return $this->success($stats);
            
        } catch (\Exception $e) {
            return $this->error('获取设备销售统计失败：'.$e->getMessage(), 500);
        }
    }

    /**
     * 同步设备销售数据
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function syncDeviceSales(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'salesman_id' => 'required|exists:salesmen,id',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after_or_equal:start_date'
        ]);

        if ($validator->fails()) {
            return $this->error('验证失败', 422, $validator->errors());
        }

        try {
            $salesmanId = $request->input('salesman_id');
            $startDate = $request->input('start_date');
            $endDate = $request->input('end_date');
            
            // 计算设备提成数据
            $commissionData = $this->commissionService->calculateDeviceCommissions($salesmanId, $startDate, $endDate);
            
            if (empty($commissionData['commissions'])) {
                return $this->error('该期间没有设备销售数据', 400);
            }
            
            // 创建销售记录
            $this->commissionService->createSalesRecords($salesmanId, $commissionData);
            
            return $this->success([
                'synced_count' => count($commissionData['commissions']),
                'total_commission' => $commissionData['total_commission'],
                'devices_count' => $commissionData['devices_count']
            ], '设备销售数据同步成功');
            
        } catch (\Exception $e) {
            return $this->error('同步设备销售数据失败：'.$e->getMessage(), 500);
        }
    }

    /**
     * 获取销售统计数据
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function stats(Request $request)
    {
        try {
            $salesmanId = $request->input('salesman_id');
            $timeRange = $request->input('time_range', 'year');
            $startDate = $request->input('start_date');
            $endDate = $request->input('end_date');
            $type = $request->input('type', 'overview');
            $limit = $request->input('limit', 10);
            
            // 根据时间范围设置默认日期
            if (!$startDate || !$endDate) {
                switch ($timeRange) {
                    case 'today':
                        $startDate = Carbon::today()->toDateString();
                        $endDate = Carbon::today()->toDateString();
                        break;
                    case 'week':
                        $startDate = Carbon::now()->startOfWeek()->toDateString();
                        $endDate = Carbon::now()->endOfWeek()->toDateString();
                        break;
                    case 'month':
                        $startDate = Carbon::now()->startOfMonth()->toDateString();
                        $endDate = Carbon::now()->endOfMonth()->toDateString();
                        break;
                    case 'quarter':
                        $startDate = Carbon::now()->startOfQuarter()->toDateString();
                        $endDate = Carbon::now()->endOfQuarter()->toDateString();
                        break;
                    case 'year':
                        $startDate = Carbon::now()->startOfYear()->toDateString();
                        $endDate = Carbon::now()->endOfYear()->toDateString();
                        break;
                    default:
                        $startDate = Carbon::now()->startOfYear()->toDateString();
                        $endDate = Carbon::now()->endOfYear()->toDateString();
                }
            }
            
            $query = SalesmanSale::query();
            
            if ($salesmanId) {
                $query->where('salesman_id', $salesmanId);
            }
            
            $query->whereBetween('sale_date', [$startDate, $endDate]);
            
            switch ($type) {
                case 'overview':
                    // 基础统计概览
                    $stats = [
                        'total_sales' => $query->count(),
                        'total_amount' => $query->sum('amount'),
                        'total_commission' => $query->sum('commission_amount'),
                        'total_quantity' => $query->sum('quantity'),
                        'avg_amount' => $query->avg('amount'),
                        'avg_commission' => $query->avg('commission_amount'),
                        'completed_sales' => $query->where('status', 'completed')->count(),
                        'pending_sales' => $query->where('status', 'pending')->count(),
                        'cancelled_sales' => $query->where('status', 'cancelled')->count(),
                        'sales_change' => 0, // 可以后续计算同比变化
                    ];
                    
                    // 如果指定了业务员，获取设备销售统计
                    if ($salesmanId) {
                        $deviceStats = $this->commissionService->getDeviceSalesStats($salesmanId, $timeRange);
                        $stats['device_sales'] = $deviceStats['stats'];
                    }
                    break;
                    
                case 'sales_ranking':
                    // 销售排行榜
                    $stats = DB::table('salesman_sales')
                        ->join('salesmen', 'salesman_sales.salesman_id', '=', 'salesmen.id')
                        ->join('app_users', 'salesmen.user_id', '=', 'app_users.id')
                        ->whereBetween('salesman_sales.sale_date', [$startDate, $endDate])
                        ->where('salesman_sales.status', 'completed')
                        ->groupBy('salesman_sales.salesman_id', 'app_users.name', 'salesmen.employee_id')
                        ->select(
                            'salesman_sales.salesman_id',
                            'app_users.name as salesman_name',
                            'salesmen.employee_id',
                            DB::raw('COUNT(salesman_sales.id) as sales_count'),
                            DB::raw('SUM(salesman_sales.quantity) as total_quantity'),
                            DB::raw('SUM(salesman_sales.amount) as total_amount'),
                            DB::raw('SUM(salesman_sales.commission_amount) as total_commission'),
                            DB::raw('AVG(salesman_sales.amount) as avg_amount')
                        )
                        ->orderBy('total_commission', 'desc')
                        ->limit($limit)
                        ->get();
                    break;
                    
                case 'sales_trend':
                    // 销售趋势图表数据
                    $stats = DB::table('salesman_sales')
                        ->select(
                            DB::raw('DATE(sale_date) as date'),
                            DB::raw('COUNT(*) as sales_count'),
                            DB::raw('SUM(amount) as total_amount'),
                            DB::raw('SUM(commission_amount) as total_commission')
                        )
                        ->when($salesmanId, function($query) use ($salesmanId) {
                            return $query->where('salesman_id', $salesmanId);
                        })
                        ->whereBetween('sale_date', [$startDate, $endDate])
                        ->where('status', 'completed')
                        ->groupBy(DB::raw('DATE(sale_date)'))
                        ->orderBy('date', 'asc')
                        ->get();
                    break;
                    
                case 'detailed':
                    // 详细统计表格数据
                    $stats = $query->with(['salesman.user'])
                        ->orderBy('sale_date', 'desc')
                        ->limit($limit)
                        ->get();
                    break;
                    
                default:
                    $stats = [];
            }
            
            return $this->success($stats);
            
        } catch (\Exception $e) {
            return $this->error('获取销售统计失败：'.$e->getMessage(), 500);
        }
    }

    /**
     * 更新或创建客户记录
     *
     * @param SalesmanSale $sale
     * @return void
     */
    private function updateCustomerRecord(SalesmanSale $sale)
    {
        if (empty($sale->customer_name)) {
            return;
        }
        
        // 查找或创建客户记录
        $customer = SalesmanCustomer::where('salesman_id', $sale->salesman_id)
            ->where('name', $sale->customer_name)
            ->first();
            
        if (!$customer) {
            $customer = SalesmanCustomer::create([
                'salesman_id' => $sale->salesman_id,
                'name' => $sale->customer_name,
                'phone' => $sale->customer_phone,
                'source' => 'sales_record',
                'status' => 'active',
                'first_contact_date' => $sale->sale_date,
                'last_contact_date' => $sale->sale_date,
                'deal_count' => 1,
                'total_amount' => $sale->amount,
                'remarks' => '来自销售记录'
            ]);
        } else {
            // 更新客户信息
            $customer->update([
                'phone' => $sale->customer_phone ?: $customer->phone,
                'last_contact_date' => $sale->sale_date,
                'deal_count' => $customer->deal_count + 1,
                'total_amount' => $customer->total_amount + $sale->amount
            ]);
        }
    }
} 