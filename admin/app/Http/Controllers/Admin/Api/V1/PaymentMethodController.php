<?php

namespace App\Http\Controllers\Admin\Api\V1;

use App\Http\Controllers\Controller;
use App\Traits\ApiResponseTrait;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use App\Models\PaymentMethod;

class PaymentMethodController extends Controller
{
    use ApiResponseTrait;

    /**
     * 获取支付方式列表
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        $query = PaymentMethod::query();
        
        // 搜索条件
        if ($request->has('keyword') && !empty($request->keyword)) {
            $keyword = $request->keyword;
            $query->where(function($q) use ($keyword) {
                $q->where('name', 'like', "%{$keyword}%")
                  ->orWhere('code', 'like', "%{$keyword}%");
            });
        }
        
        // 状态筛选
        if ($request->has('status') && $request->status !== '') {
            $query->where('status', $request->status);
        }
        
        // 类型筛选
        if ($request->has('type') && !empty($request->type)) {
            $query->where('type', $request->type);
        }
        
        // 排序
        $orderBy = $request->input('order_by', 'sort');
        $orderDir = $request->input('order_dir', 'asc');
        $query->orderBy($orderBy, $orderDir);
        
        // 分页
        $perPage = $request->input('per_page', 15);
        $paymentMethods = $query->paginate($perPage);
        
        // 处理配置数据
        foreach ($paymentMethods as $method) {
            if ($method->config) {
                $method->config = json_decode($method->config, true);
            }
        }
        
        return $this->paginate($paymentMethods);
    }

    /**
     * 创建支付方式
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:50',
            'code' => 'required|string|max:50|unique:payment_methods,code',
            'type' => 'required|string|max:20',
            'icon' => 'nullable|string|max:200',
            'description' => 'nullable|string',
            'config' => 'nullable|array',
            'sort' => 'nullable|integer',
            'status' => 'required|integer|in:0,1',
            'is_default' => 'required|boolean',
        ]);

        if ($validator->fails()) {
            return $this->error('验证失败', 422, $validator->errors());
        }

        try {
            // 如果设置为默认，将其他支付方式设置为非默认
            if ($request->is_default) {
                PaymentMethod::where('is_default', 1)->update(['is_default' => 0]);
            }
            
            $paymentMethod = new PaymentMethod();
            $paymentMethod->name = $request->name;
            $paymentMethod->code = $request->code;
            $paymentMethod->type = $request->type;
            $paymentMethod->icon = $request->icon;
            $paymentMethod->description = $request->description;
            $paymentMethod->config = $request->config ? json_encode($request->config) : null;
            $paymentMethod->sort = $request->sort ?? 0;
            $paymentMethod->status = $request->status;
            $paymentMethod->is_default = $request->is_default;
            $paymentMethod->save();
            
            // 处理配置数据
            if ($paymentMethod->config) {
                $paymentMethod->config = json_decode($paymentMethod->config, true);
            }
            
            return $this->success($paymentMethod, '支付方式创建成功');
        } catch (\Exception $e) {
            return $this->error('支付方式创建失败: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 获取单个支付方式详情
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        $paymentMethod = PaymentMethod::find($id);
        
        if (!$paymentMethod) {
            return $this->error('支付方式不存在', 404);
        }
        
        // 处理配置数据
        if ($paymentMethod->config) {
            $paymentMethod->config = json_decode($paymentMethod->config, true);
        }
        
        return $this->success($paymentMethod);
    }

    /**
     * 更新支付方式
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id)
    {
        $paymentMethod = PaymentMethod::find($id);
        
        if (!$paymentMethod) {
            return $this->error('支付方式不存在', 404);
        }
        
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:50',
            'code' => 'required|string|max:50|unique:payment_methods,code,'.$id,
            'type' => 'required|string|max:20',
            'icon' => 'nullable|string|max:200',
            'description' => 'nullable|string',
            'config' => 'nullable|array',
            'sort' => 'nullable|integer',
            'status' => 'required|integer|in:0,1',
            'is_default' => 'required|boolean',
        ]);

        if ($validator->fails()) {
            return $this->error('验证失败', 422, $validator->errors());
        }
        
        try {
            // 如果设置为默认，将其他支付方式设置为非默认
            if ($request->is_default && !$paymentMethod->is_default) {
                PaymentMethod::where('is_default', 1)->update(['is_default' => 0]);
            }
            
            $paymentMethod->name = $request->name;
            $paymentMethod->code = $request->code;
            $paymentMethod->type = $request->type;
            $paymentMethod->icon = $request->icon;
            $paymentMethod->description = $request->description;
            $paymentMethod->config = $request->config ? json_encode($request->config) : null;
            $paymentMethod->sort = $request->sort ?? 0;
            $paymentMethod->status = $request->status;
            $paymentMethod->is_default = $request->is_default;
            $paymentMethod->save();
            
            // 处理配置数据
            if ($paymentMethod->config) {
                $paymentMethod->config = json_decode($paymentMethod->config, true);
            }
            
            return $this->success($paymentMethod, '支付方式更新成功');
        } catch (\Exception $e) {
            return $this->error('支付方式更新失败: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 删除支付方式
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy($id)
    {
        $paymentMethod = PaymentMethod::find($id);
        
        if (!$paymentMethod) {
            return $this->error('支付方式不存在', 404);
        }
        
        // 检查是否为默认支付方式
        if ($paymentMethod->is_default) {
            return $this->error('默认支付方式不能删除', 400);
        }
        
        // 检查是否有关联的支付订单
        if (class_exists('App\Models\PaymentOrder')) {
            $orderCount = \App\Models\PaymentOrder::where('payment_method', $paymentMethod->code)->count();
            if ($orderCount > 0) {
                return $this->error('该支付方式有关联的支付订单，无法删除', 400);
            }
        }
        
        try {
            $paymentMethod->delete();
            
            return $this->success(null, '支付方式删除成功');
        } catch (\Exception $e) {
            return $this->error('支付方式删除失败: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * 更新支付方式状态
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateStatus(Request $request, $id)
    {
        $paymentMethod = PaymentMethod::find($id);
        
        if (!$paymentMethod) {
            return $this->error('支付方式不存在', 404);
        }
        
        $validator = Validator::make($request->all(), [
            'status' => 'required|integer|in:0,1',
        ]);

        if ($validator->fails()) {
            return $this->error('验证失败', 422, $validator->errors());
        }
        
        try {
            $paymentMethod->status = $request->status;
            $paymentMethod->save();
            
            return $this->success($paymentMethod, '支付方式状态更新成功');
        } catch (\Exception $e) {
            return $this->error('支付方式状态更新失败: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * 更新支付方式排序
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateSort(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'items' => 'required|array',
            'items.*.id' => 'required|integer|exists:payment_methods,id',
            'items.*.sort' => 'required|integer',
        ]);

        if ($validator->fails()) {
            return $this->error('验证失败', 422, $validator->errors());
        }
        
        try {
            foreach ($request->items as $item) {
                PaymentMethod::where('id', $item['id'])->update(['sort' => $item['sort']]);
            }
            
            return $this->success(null, '支付方式排序更新成功');
        } catch (\Exception $e) {
            return $this->error('支付方式排序更新失败: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * 设置默认支付方式
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function setDefault(Request $request, $id)
    {
        $paymentMethod = PaymentMethod::find($id);
        
        if (!$paymentMethod) {
            return $this->error('支付方式不存在', 404);
        }
        
        try {
            // 将所有支付方式设置为非默认
            PaymentMethod::where('is_default', 1)->update(['is_default' => 0]);
            
            // 将当前支付方式设置为默认
            $paymentMethod->is_default = 1;
            $paymentMethod->save();
            
            return $this->success($paymentMethod, '默认支付方式设置成功');
        } catch (\Exception $e) {
            return $this->error('默认支付方式设置失败: ' . $e->getMessage(), 500);
        }
    }
}
