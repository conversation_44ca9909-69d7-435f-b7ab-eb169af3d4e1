<?php

namespace App\Http\Controllers\Admin\Api\V1;

use App\Http\Controllers\Controller;
use App\Traits\ApiResponseTrait;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Artisan;
use Carbon\Carbon;

class BackupController extends Controller
{
    use ApiResponseTrait;
    
    /**
     * 备份目录
     */
    protected $backupPath = 'backups';
    
    /**
     * 获取备份列表
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        // 确保备份目录存在
        if (!Storage::exists($this->backupPath)) {
            Storage::makeDirectory($this->backupPath);
        }
        
        // 获取备份文件列表
        $files = Storage::files($this->backupPath);
        
        $backups = [];
        foreach ($files as $file) {
            // 只处理.sql和.zip文件
            if (pathinfo($file, PATHINFO_EXTENSION) === 'sql' || pathinfo($file, PATHINFO_EXTENSION) === 'zip') {
                $backups[] = [
                    'name' => basename($file),
                    'path' => $file,
                    'size' => Storage::size($file),
                    'date' => Carbon::createFromTimestamp(Storage::lastModified($file))->format('Y-m-d H:i:s'),
                    'type' => pathinfo($file, PATHINFO_EXTENSION),
                ];
            }
        }
        
        // 按日期排序
        usort($backups, function($a, $b) {
            return strcmp($b['date'], $a['date']);
        });
        
        // 关键词搜索
        if ($request->has('keyword') && !empty($request->keyword)) {
            $keyword = $request->keyword;
            $backups = array_filter($backups, function($backup) use ($keyword) {
                return stripos($backup['name'], $keyword) !== false;
            });
        }
        
        // 类型筛选
        if ($request->has('type') && !empty($request->type)) {
            $type = $request->type;
            $backups = array_filter($backups, function($backup) use ($type) {
                return $backup['type'] === $type;
            });
        }
        
        // 日期范围筛选
        if ($request->has('start_date') && !empty($request->start_date)) {
            $startDate = $request->start_date;
            $backups = array_filter($backups, function($backup) use ($startDate) {
                return $backup['date'] >= $startDate;
            });
        }
        
        if ($request->has('end_date') && !empty($request->end_date)) {
            $endDate = $request->end_date . ' 23:59:59';
            $backups = array_filter($backups, function($backup) use ($endDate) {
                return $backup['date'] <= $endDate;
            });
        }
        
        // 重新索引数组
        $backups = array_values($backups);
        
        // 分页
        $page = $request->input('page', 1);
        $perPage = $request->input('per_page', 15);
        $total = count($backups);
        $backups = array_slice($backups, ($page - 1) * $perPage, $perPage);
        
        return $this->success([
            'data' => $backups,
            'meta' => [
                'current_page' => (int)$page,
                'per_page' => (int)$perPage,
                'total' => $total,
                'last_page' => ceil($total / $perPage),
            ]
        ]);
    }
    
    /**
     * 创建数据库备份
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function createDatabaseBackup(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'tables' => 'nullable|array',
            'tables.*' => 'string',
            'filename' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return $this->error('验证失败', 422, $validator->errors());
        }
        
        try {
            // 确保备份目录存在
            if (!Storage::exists($this->backupPath)) {
                Storage::makeDirectory($this->backupPath);
            }
            
            // 生成备份文件名
            $filename = $request->input('filename');
            if (empty($filename)) {
                $filename = 'db_backup_' . date('YmdHis') . '.sql';
            } else {
                $filename = $filename . '.sql';
            }
            
            // 备份文件路径
            $backupFile = storage_path('app/' . $this->backupPath . '/' . $filename);
            
            // 获取数据库配置
            $database = config('database.connections.mysql.database');
            $username = config('database.connections.mysql.username');
            $password = config('database.connections.mysql.password');
            $host = config('database.connections.mysql.host');
            
            // 构建命令
            $command = "mysqldump -h {$host} -u {$username} -p{$password} {$database}";
            
            // 如果指定了表，则只备份指定的表
            if ($request->has('tables') && !empty($request->tables)) {
                $tables = implode(' ', $request->tables);
                $command .= " {$tables}";
            }
            
            // 执行备份命令
            $command .= " > {$backupFile}";
            exec($command, $output, $returnVar);
            
            if ($returnVar !== 0) {
                return $this->error('数据库备份失败', 500);
            }
            
            // 获取备份文件信息
            $backup = [
                'name' => $filename,
                'path' => $this->backupPath . '/' . $filename,
                'size' => Storage::size($this->backupPath . '/' . $filename),
                'date' => Carbon::now()->format('Y-m-d H:i:s'),
                'type' => 'sql',
            ];
            
            return $this->success($backup, '数据库备份创建成功');
        } catch (\Exception $e) {
            return $this->error('数据库备份失败: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * 创建文件备份
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function createFileBackup(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'directories' => 'nullable|array',
            'directories.*' => 'string',
            'filename' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return $this->error('验证失败', 422, $validator->errors());
        }
        
        try {
            // 确保备份目录存在
            if (!Storage::exists($this->backupPath)) {
                Storage::makeDirectory($this->backupPath);
            }
            
            // 生成备份文件名
            $filename = $request->input('filename');
            if (empty($filename)) {
                $filename = 'file_backup_' . date('YmdHis') . '.zip';
            } else {
                $filename = $filename . '.zip';
            }
            
            // 备份文件路径
            $backupFile = storage_path('app/' . $this->backupPath . '/' . $filename);
            
            // 创建 ZIP 文件
            $zip = new \ZipArchive();
            if ($zip->open($backupFile, \ZipArchive::CREATE | \ZipArchive::OVERWRITE) !== true) {
                return $this->error('无法创建 ZIP 文件', 500);
            }
            
            // 要备份的目录
            $directories = $request->input('directories', [
                'app',
                'config',
                'database',
                'public',
                'resources',
                'routes',
            ]);
            
            // 项目根目录
            $rootPath = base_path();
            
            // 添加文件到 ZIP
            foreach ($directories as $directory) {
                $dirPath = $rootPath . '/' . $directory;
                
                if (!is_dir($dirPath)) {
                    continue;
                }
                
                // 递归添加目录下的所有文件
                $this->addFilesToZip($zip, $dirPath, $rootPath);
            }
            
            // 关闭 ZIP 文件
            $zip->close();
            
            // 获取备份文件信息
            $backup = [
                'name' => $filename,
                'path' => $this->backupPath . '/' . $filename,
                'size' => Storage::size($this->backupPath . '/' . $filename),
                'date' => Carbon::now()->format('Y-m-d H:i:s'),
                'type' => 'zip',
            ];
            
            return $this->success($backup, '文件备份创建成功');
        } catch (\Exception $e) {
            return $this->error('文件备份失败: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * 递归添加文件到 ZIP
     *
     * @param \ZipArchive $zip
     * @param string $directory
     * @param string $rootPath
     */
    protected function addFilesToZip($zip, $directory, $rootPath)
    {
        $files = new \RecursiveIteratorIterator(
            new \RecursiveDirectoryIterator($directory),
            \RecursiveIteratorIterator::LEAVES_ONLY
        );
        
        foreach ($files as $name => $file) {
            // 跳过目录和特殊文件
            if (!$file->isDir() && $file->getFilename() !== '.' && $file->getFilename() !== '..') {
                // 获取相对路径
                $filePath = $file->getRealPath();
                $relativePath = substr($filePath, strlen($rootPath) + 1);
                
                // 添加文件到 ZIP
                $zip->addFile($filePath, $relativePath);
            }
        }
    }
    
    /**
     * 恢复数据库备份
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function restoreDatabase(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'backup' => 'required|string',
        ]);

        if ($validator->fails()) {
            return $this->error('验证失败', 422, $validator->errors());
        }
        
        try {
            $backupFile = $request->input('backup');
            $backupPath = storage_path('app/' . $backupFile);
            
            // 检查文件是否存在
            if (!file_exists($backupPath)) {
                return $this->error('备份文件不存在', 404);
            }
            
            // 检查文件类型
            if (pathinfo($backupPath, PATHINFO_EXTENSION) !== 'sql') {
                return $this->error('无效的备份文件类型，只能恢复 SQL 文件', 400);
            }
            
            // 获取数据库配置
            $database = config('database.connections.mysql.database');
            $username = config('database.connections.mysql.username');
            $password = config('database.connections.mysql.password');
            $host = config('database.connections.mysql.host');
            
            // 执行恢复命令
            $command = "mysql -h {$host} -u {$username} -p{$password} {$database} < {$backupPath}";
            exec($command, $output, $returnVar);
            
            if ($returnVar !== 0) {
                return $this->error('数据库恢复失败', 500);
            }
            
            return $this->success(null, '数据库恢复成功');
        } catch (\Exception $e) {
            return $this->error('数据库恢复失败: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * 下载备份文件
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function download(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'backup' => 'required|string',
        ]);

        if ($validator->fails()) {
            return $this->error('验证失败', 422, $validator->errors());
        }
        
        try {
            $backupFile = $request->input('backup');
            $backupPath = storage_path('app/' . $backupFile);
            
            // 检查文件是否存在
            if (!file_exists($backupPath)) {
                return $this->error('备份文件不存在', 404);
            }
            
            // 获取文件名
            $filename = basename($backupPath);
            
            // 创建下载链接
            $downloadUrl = url('api/admin/v1/backups/download-file?file=' . urlencode($backupFile));
            
            return $this->success([
                'url' => $downloadUrl,
                'filename' => $filename,
            ], '下载链接生成成功');
        } catch (\Exception $e) {
            return $this->error('生成下载链接失败: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * 下载文件（直接输出文件）
     *
     * @param Request $request
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    public function downloadFile(Request $request)
    {
        $file = $request->input('file');
        $backupPath = storage_path('app/' . $file);
        
        // 检查文件是否存在
        if (!file_exists($backupPath)) {
            abort(404, '备份文件不存在');
        }
        
        // 获取文件名
        $filename = basename($backupPath);
        
        // 返回文件下载响应
        return response()->download($backupPath, $filename);
    }
    
    /**
     * 删除备份文件
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'backup' => 'required|string',
        ]);

        if ($validator->fails()) {
            return $this->error('验证失败', 422, $validator->errors());
        }
        
        try {
            $backupFile = $request->input('backup');
            
            // 检查文件是否存在
            if (!Storage::exists($backupFile)) {
                return $this->error('备份文件不存在', 404);
            }
            
            // 删除文件
            Storage::delete($backupFile);
            
            return $this->success(null, '备份文件删除成功');
        } catch (\Exception $e) {
            return $this->error('备份文件删除失败: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * 批量删除备份文件
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function batchDelete(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'backups' => 'required|array',
            'backups.*' => 'required|string',
        ]);

        if ($validator->fails()) {
            return $this->error('验证失败', 422, $validator->errors());
        }
        
        try {
            $backupFiles = $request->input('backups');
            $deletedCount = 0;
            
            foreach ($backupFiles as $backupFile) {
                // 检查文件是否存在
                if (Storage::exists($backupFile)) {
                    // 删除文件
                    Storage::delete($backupFile);
                    $deletedCount++;
                }
            }
            
            return $this->success(['deleted_count' => $deletedCount], '批量删除备份文件成功');
        } catch (\Exception $e) {
            return $this->error('批量删除备份文件失败: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * 获取数据库表列表
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getTables()
    {
        try {
            // 获取数据库名
            $database = config('database.connections.mysql.database');
            
            // 查询所有表
            $tables = DB::select("SHOW TABLES FROM `{$database}`");
            
            // 提取表名
            $tableNames = [];
            $tableKey = "Tables_in_{$database}";
            
            foreach ($tables as $table) {
                $tableName = $table->$tableKey;
                
                // 获取表信息
                $tableInfo = DB::select("SHOW TABLE STATUS FROM `{$database}` WHERE Name = '{$tableName}'")[0];
                
                $tableNames[] = [
                    'name' => $tableName,
                    'rows' => $tableInfo->Rows,
                    'size' => $tableInfo->Data_length + $tableInfo->Index_length,
                    'engine' => $tableInfo->Engine,
                    'collation' => $tableInfo->Collation,
                    'comment' => $tableInfo->Comment,
                ];
            }
            
            // 按表名排序
            usort($tableNames, function($a, $b) {
                return strcmp($a['name'], $b['name']);
            });
            
            return $this->success($tableNames);
        } catch (\Exception $e) {
            return $this->error('获取数据库表列表失败: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * 获取目录列表
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getDirectories()
    {
        try {
            $rootPath = base_path();
            $directories = [];
            
            // 获取根目录下的所有目录
            $items = File::directories($rootPath);
            
            foreach ($items as $item) {
                $dirName = basename($item);
                
                // 排除一些不需要备份的目录
                if (in_array($dirName, ['.git', 'node_modules', 'vendor', 'storage', 'bootstrap/cache'])) {
                    continue;
                }
                
                $directories[] = [
                    'name' => $dirName,
                    'path' => $dirName,
                    'size' => $this->getDirSize($item),
                ];
            }
            
            // 按目录名排序
            usort($directories, function($a, $b) {
                return strcmp($a['name'], $b['name']);
            });
            
            return $this->success($directories);
        } catch (\Exception $e) {
            return $this->error('获取目录列表失败: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * 获取目录大小
     *
     * @param string $directory
     * @return int
     */
    protected function getDirSize($directory)
    {
        $size = 0;
        
        foreach (File::allFiles($directory) as $file) {
            $size += $file->getSize();
        }
        
        return $size;
    }
    
    /**
     * 清理缓存
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function clearCache(Request $request)
    {
        try {
            // 清理应用缓存
            Artisan::call('cache:clear');
            
            // 清理配置缓存
            Artisan::call('config:clear');
            
            // 清理路由缓存
            Artisan::call('route:clear');
            
            // 清理视图缓存
            Artisan::call('view:clear');
            
            return $this->success(null, '缓存清理成功');
        } catch (\Exception $e) {
            return $this->error('缓存清理失败: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * 优化数据库
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function optimizeDatabase(Request $request)
    {
        try {
            // 获取数据库名
            $database = config('database.connections.mysql.database');
            
            // 查询所有表
            $tables = DB::select("SHOW TABLES FROM `{$database}`");
            
            // 提取表名
            $tableKey = "Tables_in_{$database}";
            $tableNames = [];
            
            foreach ($tables as $table) {
                $tableNames[] = $table->$tableKey;
            }
            
            // 优化表
            foreach ($tableNames as $tableName) {
                DB::statement("OPTIMIZE TABLE `{$tableName}`");
            }
            
            return $this->success(null, '数据库优化成功');
        } catch (\Exception $e) {
            return $this->error('数据库优化失败: ' . $e->getMessage(), 500);
        }
    }
}
