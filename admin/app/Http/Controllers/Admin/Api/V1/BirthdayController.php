<?php

namespace App\Http\Controllers\Admin\Api\V1;

use App\Http\Controllers\Controller;
use App\Models\Institution;
use App\Models\BirthdaySmsRecord;
use App\Services\SmsService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class BirthdayController extends Controller
{
    protected $smsService;

    public function __construct(SmsService $smsService)
    {
        $this->smsService = $smsService;
    }

    /**
     * 获取生日列表
     */
    public function index(Request $request)
    {
        try {
            $page = $request->get('page', 1);
            $size = $request->get('size', 15);
            $search = $request->get('search', '');

            // 使用b.tapgo.cn数据库连接
            $query = DB::connection('payment_db')
                ->table('ddg_institution')
                ->select([
                    'id',
                    'name',
                    'number',
                    'phone',
                    'xs_number',
                    'lv',
                    'sfz',
                    'institution_id'
                ])
                ->whereNotNull('sfz')
                ->where('sfz', '!=', '')
                ->whereRaw('LENGTH(sfz) = 18');

            // 如果没有搜索条件，则只显示今天生日的数据
            if (empty($search)) {
                $query->whereRaw("CONCAT(
                    SUBSTRING(sfz, 11, 2),
                    SUBSTRING(sfz, 13, 2)
                ) = DATE_FORMAT(CURDATE(), '%m%d')");
            } else {
                // 如果有搜索条件，则不限制生日日期
                $query->where(function($q) use ($search) {
                    $q->where('name', 'like', "%{$search}%")
                      ->orWhere('phone', 'like', "%{$search}%");
                });
            }

            // 获取总数
            $total = $query->count();

            // 分页查询
            $results = $query->orderBy('name')
                ->offset(($page - 1) * $size)
                ->limit($size)
                ->get();

            // 处理数据
            $birthdayList = [];
            foreach ($results as $row) {
                try {
                    $birthdayInfo = $this->parseIdCard($row->sfz);
                    if ($birthdayInfo) {
                        $birthdayList[] = [
                            'id' => $row->id,
                            'name' => $row->name,
                            'number' => $row->number,
                            'phone' => $row->phone,
                            'xs_number' => $row->xs_number,
                            'lv' => $row->lv,
                            'parent_name' => $this->getParentName($row->institution_id),
                            'birth_date' => $birthdayInfo['birth_date'],
                            'gender' => $birthdayInfo['gender'],
                            'age' => $birthdayInfo['age']
                        ];
                    }
                } catch (\Exception $e) {
                    continue;
                }
            }

            return response()->json([
                'code' => 200,
                'data' => [
                    'list' => $birthdayList,
                    'total' => $total,
                    'page' => $page,
                    'size' => $size
                ],
                'message' => '获取成功'
            ]);

        } catch (\Exception $e) {
            Log::error('获取生日列表失败', ['error' => $e->getMessage()]);
            return response()->json([
                'code' => 500,
                'message' => '获取生日列表失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 发送单个生日祝福短信
     */
    public function sendSms(Request $request)
    {
        try {
            $institutionId = $request->get('id');
            $phone = $request->get('phone');
            $name = $request->get('name');
            $age = $request->get('age');
            $gender = $request->get('gender');
            $birthday = $request->get('birth_date');

            if (!$phone || !$name || !$age) {
                return response()->json([
                    'code' => 400,
                    'message' => '参数不完整'
                ]);
            }

            // 发送短信
            $result = $this->smsService->sendBirthdaySms($phone, $name, $age);

            // 记录发送日志
            $status = ($result && $result['Code'] === 'OK') ? 'success' : 'failed';
            $smsContent = "亲爱的{$name}，今天是你的{$age}岁生日，点点够祝您生日快乐！祝您每一天都是美好的一天！";

            DB::connection('payment_db')->table('sft_bsms_record')->insert([
                'institution_id' => $institutionId,
                'name' => $name,
                'phone' => $phone,
                'sms_content' => $smsContent,
                'birthday' => $birthday ? Carbon::parse($birthday)->format('Y-m-d') : null,
                'gender' => $gender,
                'age' => $age,
                'send_time' => now(),
                'status' => $status
            ]);

            if ($status === 'success') {
                return response()->json([
                    'code' => 200,
                    'message' => '发送成功'
                ]);
            } else {
                return response()->json([
                    'code' => 500,
                    'message' => '发送失败: ' . ($result['Message'] ?? '未知错误')
                ]);
            }

        } catch (\Exception $e) {
            Log::error('发送生日短信失败', ['error' => $e->getMessage()]);
            return response()->json([
                'code' => 500,
                'message' => '发送失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 批量发送生日祝福短信
     */
    public function batchSendSms(Request $request)
    {
        try {
            $recipients = $request->get('recipients', []);

            if (empty($recipients)) {
                return response()->json([
                    'code' => 400,
                    'message' => '没有选择发送对象'
                ]);
            }

            $successCount = 0;
            $failCount = 0;

            foreach ($recipients as $recipient) {
                try {
                    // 发送短信
                    $result = $this->smsService->sendBirthdaySms(
                        $recipient['phone'],
                        $recipient['name'],
                        $recipient['age']
                    );

                    $status = ($result && $result['Code'] === 'OK') ? 'success' : 'failed';
                    $smsContent = "亲爱的{$recipient['name']}，今天是你的{$recipient['age']}岁生日，点点够祝您生日快乐！祝您每一天都是美好的一天！";

                    // 记录发送日志
                    DB::connection('payment_db')->table('sft_bsms_record')->insert([
                        'institution_id' => $recipient['id'],
                        'name' => $recipient['name'],
                        'phone' => $recipient['phone'],
                        'sms_content' => $smsContent,
                        'birthday' => Carbon::parse($recipient['birth_date'])->format('Y-m-d'),
                        'gender' => $recipient['gender'],
                        'age' => $recipient['age'],
                        'send_time' => now(),
                        'status' => $status
                    ]);

                    if ($status === 'success') {
                        $successCount++;
                    } else {
                        $failCount++;
                    }

                } catch (\Exception $e) {
                    Log::error('批量发送短信失败', ['recipient' => $recipient, 'error' => $e->getMessage()]);
                    $failCount++;
                }
            }

            return response()->json([
                'code' => 200,
                'message' => "批量发送完成，成功: {$successCount}，失败: {$failCount}"
            ]);

        } catch (\Exception $e) {
            Log::error('批量发送生日祝福短信失败', ['error' => $e->getMessage()]);
            return response()->json([
                'code' => 500,
                'message' => '批量发送失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 获取短信发送记录
     */
    public function getSmsLogs(Request $request)
    {
        try {
            $page = $request->get('page', 1);
            $size = $request->get('size', 15);
            $search = $request->get('search', '');
            $status = $request->get('status', '');
            $startDate = $request->get('start_date', '');
            $endDate = $request->get('end_date', '');

            $query = DB::connection('payment_db')
                ->table('sft_bsms_record')
                ->select('*');

            if (!empty($search)) {
                $query->where(function($q) use ($search) {
                    $q->where('name', 'like', "%{$search}%")
                      ->orWhere('phone', 'like', "%{$search}%");
                });
            }

            if (!empty($status)) {
                $query->where('status', $status);
            }

            if (!empty($startDate)) {
                $query->whereDate('send_time', '>=', $startDate);
            }

            if (!empty($endDate)) {
                $query->whereDate('send_time', '<=', $endDate);
            }

            $total = $query->count();

            $logs = $query->orderBy('send_time', 'desc')
                ->offset(($page - 1) * $size)
                ->limit($size)
                ->get();

            return response()->json([
                'code' => 200,
                'data' => [
                    'list' => $logs,
                    'total' => $total,
                    'page' => $page,
                    'size' => $size
                ],
                'message' => '获取成功'
            ]);

        } catch (\Exception $e) {
            Log::error('获取短信记录失败', ['error' => $e->getMessage()]);
            return response()->json([
                'code' => 500,
                'message' => '获取短信记录失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 获取生日祝福统计数据
     */
    public function getStatistics()
    {
        try {
            // 今日生日人数
            $todayBirthday = DB::connection('payment_db')
                ->table('ddg_institution')
                ->whereNotNull('sfz')
                ->where('sfz', '!=', '')
                ->whereRaw('LENGTH(sfz) = 18')
                ->whereRaw("CONCAT(
                    SUBSTRING(sfz, 11, 2),
                    SUBSTRING(sfz, 13, 2)
                ) = DATE_FORMAT(CURDATE(), '%m%d')")
                ->count();

            // 今日已发送数量
            $sentToday = DB::connection('payment_db')
                ->table('sft_bsms_record')
                ->whereDate('send_time', Carbon::today())
                ->count();

            // 今日发送成功数量
            $successToday = DB::connection('payment_db')
                ->table('sft_bsms_record')
                ->whereDate('send_time', Carbon::today())
                ->where('status', 'success')
                ->count();

            // 计算成功率
            $successRate = $sentToday > 0 ? round(($successToday / $sentToday) * 100, 1) : 0;

            // 累计发送总数
            $totalSent = DB::connection('payment_db')
                ->table('sft_bsms_record')
                ->count();

            return response()->json([
                'code' => 200,
                'data' => [
                    'todayBirthday' => $todayBirthday,
                    'sentToday' => $sentToday,
                    'successRate' => $successRate,
                    'totalSent' => $totalSent
                ],
                'message' => '获取统计数据成功'
            ]);

        } catch (\Exception $e) {
            Log::error('获取统计数据失败', ['error' => $e->getMessage()]);
            return response()->json([
                'code' => 500,
                'message' => '获取统计数据失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 获取分析数据
     */
    public function getAnalytics(Request $request)
    {
        try {
            $days = $request->get('days', 7); // 默认7天

            // 发送趋势数据（最近N天）
            $sendTrend = DB::connection('payment_db')
                ->table('sft_bsms_record')
                ->select(
                    DB::raw('DATE(send_time) as date'),
                    DB::raw('COUNT(*) as total'),
                    DB::raw('SUM(CASE WHEN status = "success" THEN 1 ELSE 0 END) as success')
                )
                ->where('send_time', '>=', Carbon::now()->subDays($days))
                ->groupBy(DB::raw('DATE(send_time)'))
                ->orderBy('date')
                ->get();

            // 年龄分布
            $ageDistribution = DB::connection('payment_db')
                ->table('ddg_institution')
                ->select(
                    DB::raw('CASE 
                        WHEN YEAR(CURDATE()) - YEAR(STR_TO_DATE(SUBSTRING(sfz, 7, 8), "%Y%m%d")) < 30 THEN "20-30岁"
                        WHEN YEAR(CURDATE()) - YEAR(STR_TO_DATE(SUBSTRING(sfz, 7, 8), "%Y%m%d")) < 40 THEN "30-40岁"
                        WHEN YEAR(CURDATE()) - YEAR(STR_TO_DATE(SUBSTRING(sfz, 7, 8), "%Y%m%d")) < 50 THEN "40-50岁"
                        WHEN YEAR(CURDATE()) - YEAR(STR_TO_DATE(SUBSTRING(sfz, 7, 8), "%Y%m%d")) < 60 THEN "50-60岁"
                        ELSE "60岁以上"
                    END as age_group'),
                    DB::raw('COUNT(*) as count')
                )
                ->whereNotNull('sfz')
                ->where('sfz', '!=', '')
                ->whereRaw('LENGTH(sfz) = 18')
                ->groupBy('age_group')
                ->get();

            // 性别分布
            $genderDistribution = DB::connection('payment_db')
                ->table('ddg_institution')
                ->select(
                    DB::raw('CASE WHEN CAST(SUBSTRING(sfz, 17, 1) AS UNSIGNED) % 2 = 0 THEN "女" ELSE "男" END as gender'),
                    DB::raw('COUNT(*) as count')
                )
                ->whereNotNull('sfz')
                ->where('sfz', '!=', '')
                ->whereRaw('LENGTH(sfz) = 18')
                ->groupBy('gender')
                ->get();

            // 等级分布
            $levelDistribution = DB::connection('payment_db')
                ->table('ddg_institution')
                ->select('lv as level', DB::raw('COUNT(*) as count'))
                ->whereNotNull('lv')
                ->groupBy('lv')
                ->orderBy('lv')
                ->get();

            return response()->json([
                'code' => 200,
                'data' => [
                    'sendTrend' => $sendTrend,
                    'ageDistribution' => $ageDistribution,
                    'genderDistribution' => $genderDistribution,
                    'levelDistribution' => $levelDistribution
                ],
                'message' => '获取分析数据成功'
            ]);

        } catch (\Exception $e) {
            Log::error('获取分析数据失败', ['error' => $e->getMessage()]);
            return response()->json([
                'code' => 500,
                'message' => '获取分析数据失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 导出发送记录
     */
    public function exportLogs(Request $request)
    {
        try {
            $search = $request->get('search', '');
            $status = $request->get('status', '');
            $startDate = $request->get('start_date', '');
            $endDate = $request->get('end_date', '');

            $query = DB::connection('payment_db')
                ->table('sft_bsms_record')
                ->select([
                    'id',
                    'institution_id',
                    'name',
                    'phone',
                    'age',
                    'gender',
                    'birthday',
                    'sms_content',
                    'send_time',
                    'status'
                ]);

            if (!empty($search)) {
                $query->where(function($q) use ($search) {
                    $q->where('name', 'like', "%{$search}%")
                      ->orWhere('phone', 'like', "%{$search}%");
                });
            }

            if (!empty($status)) {
                $query->where('status', $status);
            }

            if (!empty($startDate)) {
                $query->whereDate('send_time', '>=', $startDate);
            }

            if (!empty($endDate)) {
                $query->whereDate('send_time', '<=', $endDate);
            }

            $logs = $query->orderBy('send_time', 'desc')->get();

            // 生成CSV内容
            $csvContent = "记录ID,机构ID,姓名,手机号,年龄,性别,生日,短信内容,发送时间,发送状态\n";
            
            foreach ($logs as $log) {
                $csvContent .= sprintf(
                    "%s,%s,%s,%s,%s,%s,%s,\"%s\",%s,%s\n",
                    $log->id,
                    $log->institution_id,
                    $log->name,
                    $log->phone,
                    $log->age,
                    $log->gender,
                    $log->birthday,
                    str_replace('"', '""', $log->sms_content),
                    $log->send_time,
                    $log->status === 'success' ? '成功' : '失败'
                );
            }

            $filename = '生日祝福发送记录_' . date('Y-m-d_H-i-s') . '.csv';

            return response($csvContent, 200, [
                'Content-Type' => 'text/csv; charset=UTF-8',
                'Content-Disposition' => 'attachment; filename="' . $filename . '"',
                'Cache-Control' => 'no-cache, must-revalidate',
                'Pragma' => 'no-cache'
            ]);

        } catch (\Exception $e) {
            Log::error('导出发送记录失败', ['error' => $e->getMessage()]);
            return response()->json([
                'code' => 500,
                'message' => '导出失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 获取生日提醒设置
     */
    public function getReminderSettings()
    {
        try {
            // 从配置文件或数据库获取提醒设置
            $settings = [
                'auto_send_enabled' => env('BIRTHDAY_AUTO_SEND_ENABLED', false),
                'send_time' => env('BIRTHDAY_SEND_TIME', '09:00'),
                'advance_days' => env('BIRTHDAY_ADVANCE_DAYS', 0),
                'template_content' => env('ALIYUN_SMS_BIRTHDAY_TEMPLATE_CONTENT', '亲爱的{name}，今天是你的{age}岁生日，点点够祝您生日快乐！祝您每一天都是美好的一天！'),
                'max_daily_send' => env('BIRTHDAY_MAX_DAILY_SEND', 1000),
                'retry_failed' => env('BIRTHDAY_RETRY_FAILED', true),
                'retry_times' => env('BIRTHDAY_RETRY_TIMES', 3)
            ];

            return response()->json([
                'code' => 200,
                'data' => $settings,
                'message' => '获取设置成功'
            ]);

        } catch (\Exception $e) {
            Log::error('获取生日提醒设置失败', ['error' => $e->getMessage()]);
            return response()->json([
                'code' => 500,
                'message' => '获取设置失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 更新生日提醒设置
     */
    public function updateReminderSettings(Request $request)
    {
        try {
            $settings = $request->all();
            
            // 这里可以将设置保存到数据库或配置文件
            // 暂时返回成功响应
            
            return response()->json([
                'code' => 200,
                'message' => '设置更新成功'
            ]);

        } catch (\Exception $e) {
            Log::error('更新生日提醒设置失败', ['error' => $e->getMessage()]);
            return response()->json([
                'code' => 500,
                'message' => '设置更新失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 获取发送历史统计
     */
    public function getSendHistory(Request $request)
    {
        try {
            $days = $request->get('days', 30);
            $startDate = Carbon::now()->subDays($days);

            // 按日期统计发送数据
            $dailyStats = DB::connection('payment_db')
                ->table('sft_bsms_record')
                ->select(
                    DB::raw('DATE(send_time) as date'),
                    DB::raw('COUNT(*) as total_sent'),
                    DB::raw('SUM(CASE WHEN status = "success" THEN 1 ELSE 0 END) as success_count'),
                    DB::raw('SUM(CASE WHEN status = "failed" THEN 1 ELSE 0 END) as failed_count')
                )
                ->where('send_time', '>=', $startDate)
                ->groupBy(DB::raw('DATE(send_time)'))
                ->orderBy('date')
                ->get();

            // 按小时统计今日发送数据
            $hourlyStats = DB::connection('payment_db')
                ->table('sft_bsms_record')
                ->select(
                    DB::raw('HOUR(send_time) as hour'),
                    DB::raw('COUNT(*) as total_sent'),
                    DB::raw('SUM(CASE WHEN status = "success" THEN 1 ELSE 0 END) as success_count')
                )
                ->whereDate('send_time', Carbon::today())
                ->groupBy(DB::raw('HOUR(send_time)'))
                ->orderBy('hour')
                ->get();

            // 失败原因统计（模拟数据，实际可以从短信服务商获取）
            $failureReasons = [
                ['reason' => '手机号无效', 'count' => 15],
                ['reason' => '网络超时', 'count' => 8],
                ['reason' => '余额不足', 'count' => 3],
                ['reason' => '其他原因', 'count' => 5]
            ];

            return response()->json([
                'code' => 200,
                'data' => [
                    'daily_stats' => $dailyStats,
                    'hourly_stats' => $hourlyStats,
                    'failure_reasons' => $failureReasons
                ],
                'message' => '获取发送历史成功'
            ]);

        } catch (\Exception $e) {
            Log::error('获取发送历史失败', ['error' => $e->getMessage()]);
            return response()->json([
                'code' => 500,
                'message' => '获取发送历史失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 获取即将生日的用户
     */
    public function getUpcomingBirthdays(Request $request)
    {
        try {
            $days = $request->get('days', 7); // 未来7天内生日的用户

            $upcomingBirthdays = [];
            
            for ($i = 0; $i <= $days; $i++) {
                $targetDate = Carbon::now()->addDays($i);
                $monthDay = $targetDate->format('md');
                
                $users = DB::connection('payment_db')
                    ->table('ddg_institution')
                    ->select([
                        'id',
                        'name',
                        'phone',
                        'sfz',
                        'lv'
                    ])
                    ->whereNotNull('sfz')
                    ->where('sfz', '!=', '')
                    ->whereRaw('LENGTH(sfz) = 18')
                    ->whereRaw("CONCAT(
                        SUBSTRING(sfz, 11, 2),
                        SUBSTRING(sfz, 13, 2)
                    ) = ?", [$monthDay])
                    ->get();

                foreach ($users as $user) {
                    $birthdayInfo = $this->parseIdCard($user->sfz);
                    if ($birthdayInfo) {
                        $upcomingBirthdays[] = [
                            'id' => $user->id,
                            'name' => $user->name,
                            'phone' => $user->phone,
                            'lv' => $user->lv,
                            'birth_date' => $birthdayInfo['birth_date'],
                            'age' => $birthdayInfo['age'],
                            'gender' => $birthdayInfo['gender'],
                            'days_until_birthday' => $i,
                            'birthday_date' => $targetDate->format('Y-m-d')
                        ];
                    }
                }
            }

            return response()->json([
                'code' => 200,
                'data' => [
                    'upcoming_birthdays' => $upcomingBirthdays,
                    'total_count' => count($upcomingBirthdays)
                ],
                'message' => '获取即将生日用户成功'
            ]);

        } catch (\Exception $e) {
            Log::error('获取即将生日用户失败', ['error' => $e->getMessage()]);
            return response()->json([
                'code' => 500,
                'message' => '获取即将生日用户失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 测试短信发送
     */
    public function testSms(Request $request)
    {
        try {
            $phone = $request->get('phone');
            $name = $request->get('name', '测试用户');
            $age = $request->get('age', 25);

            if (!$phone) {
                return response()->json([
                    'code' => 400,
                    'message' => '手机号不能为空'
                ]);
            }

            // 发送测试短信
            $result = $this->smsService->sendBirthdaySms($phone, $name, $age);

            if ($result && $result['Code'] === 'OK') {
                return response()->json([
                    'code' => 200,
                    'message' => '测试短信发送成功',
                    'data' => $result
                ]);
            } else {
                return response()->json([
                    'code' => 500,
                    'message' => '测试短信发送失败: ' . ($result['Message'] ?? '未知错误'),
                    'data' => $result
                ]);
            }

        } catch (\Exception $e) {
            Log::error('测试短信发送失败', ['error' => $e->getMessage()]);
            return response()->json([
                'code' => 500,
                'message' => '测试短信发送失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 解析身份证信息
     */
    private function parseIdCard($idCard)
    {
        if (!$idCard || strlen($idCard) !== 18) {
            return null;
        }

        try {
            $birthDate = Carbon::createFromFormat('Ymd', substr($idCard, 6, 8));
            $age = $birthDate->diffInYears(Carbon::now());
            $gender = (int)substr($idCard, -2, 1) % 2 === 0 ? '女' : '男';

            return [
                'birth_date' => $birthDate->format('Y-m-d'),
                'age' => $age,
                'gender' => $gender
            ];
        } catch (\Exception $e) {
            return null;
        }
    }

    /**
     * 获取上级机构名称
     */
    private function getParentName($institutionId)
    {
        if (!$institutionId) {
            return '';
        }

        try {
            $parent = DB::connection('payment_db')
                ->table('ddg_institution')
                ->where('id', $institutionId)
                ->first();

            return $parent ? $parent->name : '';
        } catch (\Exception $e) {
            return '';
        }
    }
} 