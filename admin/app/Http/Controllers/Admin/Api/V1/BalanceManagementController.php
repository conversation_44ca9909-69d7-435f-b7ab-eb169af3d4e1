<?php

namespace App\Http\Controllers\Admin\Api\V1;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Exception;

class BalanceManagementController extends Controller
{
    /**
     * 获取余额列表
     */
    public function getBalanceList(Request $request)
    {
        try {
            $page = $request->get('page', 1);
            $limit = $request->get('limit', 10);
            $search = $request->get('search', '');
            
            $offset = ($page - 1) * $limit;
            
            // 构建查询条件
            $whereConditions = [];
            $params = [];
            
            if ($search) {
                $whereConditions[] = "(r.name LIKE ? OR b.institution_id LIKE ?)";
                $params[] = "%{$search}%";
                $params[] = "%{$search}%";
            }
            
            $whereClause = '';
            if (!empty($whereConditions)) {
                $whereClause = 'WHERE ' . implode(' AND ', $whereConditions);
            }
            
            // 获取总数
            $countSql = "
                SELECT COUNT(*) as total
                FROM sft_institution_balance b
                LEFT JOIN ddg_institution_relation r ON b.institution_id = r.institution_id
                {$whereClause}
            ";
            
            $totalResult = DB::connection('payment_db')->select($countSql, $params);
            $total = $totalResult[0]->total ?? 0;
            
            // 获取统计数据
            $statsSql = "
                SELECT 
                    SUM(COALESCE(pending_balance, 0)) as pending_balance_sum,
                    SUM(COALESCE(withdrawable_balance, 0)) as withdrawable_balance_sum,
                    SUM(COALESCE(non_withdrawable_balance, 0)) as non_withdrawable_balance_sum,
                    SUM(COALESCE(withdrawn_amount, 0)) as withdrawn_amount_sum
                FROM sft_institution_balance b
                LEFT JOIN ddg_institution_relation r ON b.institution_id = r.institution_id
                {$whereClause}
            ";
            
            $statsResult = DB::connection('payment_db')->select($statsSql, $params);
            $stats = $statsResult[0] ?? null;
            
            // 获取列表数据
            $listSql = "
                SELECT 
                    b.institution_id,
                    r.name,
                    COALESCE(b.pending_balance, 0) as pending_balance,
                    COALESCE(b.withdrawable_balance, 0) as withdrawable_balance,
                    COALESCE(b.non_withdrawable_balance, 0) as non_withdrawable_balance,
                    COALESCE(b.withdrawn_amount, 0) as withdrawn_amount,
                    b.can_withdraw,
                    b.last_synced_period,
                    b.created_at,
                    b.updated_at
                FROM sft_institution_balance b
                LEFT JOIN ddg_institution_relation r ON b.institution_id = r.institution_id
                {$whereClause}
                ORDER BY b.institution_id
                LIMIT ? OFFSET ?
            ";
            
            $params[] = $limit;
            $params[] = $offset;
            
            $list = DB::connection('payment_db')->select($listSql, $params);
            
            return response()->json([
                'code' => 20000,
                'message' => 'success',
                'data' => [
                    'items' => $list,
                    'total' => $total,
                    'totalBalances' => [
                        'pending_balance_sum' => number_format($stats->pending_balance_sum ?? 0, 2),
                        'withdrawable_balance_sum' => number_format($stats->withdrawable_balance_sum ?? 0, 2),
                        'non_withdrawable_balance_sum' => number_format($stats->non_withdrawable_balance_sum ?? 0, 2),
                        'withdrawn_amount_sum' => number_format($stats->withdrawn_amount_sum ?? 0, 2)
                    ]
                ]
            ]);
            
        } catch (Exception $e) {
            Log::error('获取余额列表失败: ' . $e->getMessage());
            return response()->json([
                'code' => 50000,
                'message' => '获取数据失败: ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * 同步分润到待结算余额
     */
    public function addBalance(Request $request)
    {
        try {
            $month = $request->input('month');
            
            if (!$month) {
                return response()->json([
                    'code' => 40000,
                    'message' => '未选择月份'
                ], 400);
            }
            
            // 检查是否已经同步过
            $alreadySyncedSql = "
                SELECT COUNT(*) as count
                FROM sft_institution_balance 
                WHERE last_synced_period = ?
            ";
            
            $syncedResult = DB::connection('payment_db')->select($alreadySyncedSql, [$month]);
            $alreadySynced = $syncedResult[0]->count ?? 0;
            
            if ($alreadySynced > 0) {
                return response()->json([
                    'code' => 40001,
                    'message' => '该月份数据已同步'
                ], 400);
            }
            
            DB::connection('payment_db')->beginTransaction();
            
            try {
                // 1. 更新已存在的记录
                $updateSql = "
                    UPDATE sft_institution_balance b
                    INNER JOIN sft_institution_count ic ON b.institution_id = ic.institution_id
                    SET 
                        b.pending_balance = b.pending_balance + COALESCE(ic.current_month_total_commission, 0),
                        b.last_synced_period = ?,
                        b.updated_at = NOW()
                    WHERE ic.summary_period = ?
                ";
                
                DB::connection('payment_db')->update($updateSql, [$month, $month]);
                
                // 2. 插入新记录
                $insertSql = "
                    INSERT INTO sft_institution_balance (
                        institution_id,
                        pending_balance,
                        withdrawable_balance,
                        non_withdrawable_balance,
                        withdrawn_amount,
                        can_withdraw,
                        last_synced_period,
                        created_at,
                        updated_at
                    )
                    SELECT 
                        ic.institution_id,
                        COALESCE(ic.current_month_total_commission, 0),
                        0,
                        0,
                        0,
                        1,
                        ?,
                        NOW(),
                        NOW()
                    FROM sft_institution_count ic
                    LEFT JOIN sft_institution_balance b ON ic.institution_id = b.institution_id
                    WHERE ic.summary_period = ?
                    AND b.institution_id IS NULL
                ";
                
                DB::connection('payment_db')->insert($insertSql, [$month, $month]);
                
                DB::connection('payment_db')->commit();
                
                return response()->json([
                    'code' => 20000,
                    'message' => '同步成功'
                ]);
                
            } catch (Exception $e) {
                DB::connection('payment_db')->rollback();
                throw $e;
            }
            
        } catch (Exception $e) {
            Log::error('同步分润失败: ' . $e->getMessage());
            return response()->json([
                'code' => 50000,
                'message' => '同步失败: ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * 批量转入不可提现余额
     */
    public function batchToNonWithdrawable(Request $request)
    {
        try {
            DB::connection('payment_db')->beginTransaction();
            
            try {
                $updateSql = "
                    UPDATE sft_institution_balance 
                    SET 
                        non_withdrawable_balance = COALESCE(non_withdrawable_balance, 0) + COALESCE(pending_balance, 0),
                        pending_balance = 0,
                        updated_at = NOW()
                    WHERE pending_balance > 0
                ";
                
                DB::connection('payment_db')->update($updateSql);
                
                DB::connection('payment_db')->commit();
                
                return response()->json([
                    'code' => 20000,
                    'message' => '批量转入不可提现余额成功'
                ]);
                
            } catch (Exception $e) {
                DB::connection('payment_db')->rollback();
                throw $e;
            }
            
        } catch (Exception $e) {
            Log::error('批量转入不可提现余额失败: ' . $e->getMessage());
            return response()->json([
                'code' => 50000,
                'message' => '操作失败: ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * 批量转入可提现余额
     */
    public function batchToWithdrawable(Request $request)
    {
        try {
            DB::connection('payment_db')->beginTransaction();
            
            try {
                $updateSql = "
                    UPDATE sft_institution_balance 
                    SET 
                        withdrawable_balance = COALESCE(withdrawable_balance, 0) + COALESCE(pending_balance, 0),
                        pending_balance = 0,
                        updated_at = NOW()
                    WHERE pending_balance > 0
                ";
                
                DB::connection('payment_db')->update($updateSql);
                
                DB::connection('payment_db')->commit();
                
                return response()->json([
                    'code' => 20000,
                    'message' => '批量转入可提现余额成功'
                ]);
                
            } catch (Exception $e) {
                DB::connection('payment_db')->rollback();
                throw $e;
            }
            
        } catch (Exception $e) {
            Log::error('批量转入可提现余额失败: ' . $e->getMessage());
            return response()->json([
                'code' => 50000,
                'message' => '操作失败: ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * 调整余额
     */
    public function updateBalance(Request $request)
    {
        try {
            $institutionId = $request->input('institution_id');
            $changeType = $request->input('change_type'); // increase, decrease, final
            $amount = $request->input('amount');
            $balanceType = $request->input('balance_type', 'withdrawable'); // withdrawable, non_withdrawable, pending
            
            if (!$institutionId || !$amount || $amount < 0) {
                return response()->json([
                    'code' => 40000,
                    'message' => '参数错误'
                ], 400);
            }
            
            DB::connection('payment_db')->beginTransaction();
            
            try {
                $field = $balanceType . '_balance';
                
                if ($changeType === 'increase') {
                    $updateSql = "
                        UPDATE sft_institution_balance 
                        SET {$field} = COALESCE({$field}, 0) + ?,
                            updated_at = NOW()
                        WHERE institution_id = ?
                    ";
                    DB::connection('payment_db')->update($updateSql, [$amount, $institutionId]);
                } elseif ($changeType === 'decrease') {
                    $updateSql = "
                        UPDATE sft_institution_balance 
                        SET {$field} = GREATEST(COALESCE({$field}, 0) - ?, 0),
                            updated_at = NOW()
                        WHERE institution_id = ?
                    ";
                    DB::connection('payment_db')->update($updateSql, [$amount, $institutionId]);
                } elseif ($changeType === 'final') {
                    $updateSql = "
                        UPDATE sft_institution_balance 
                        SET {$field} = ?,
                            updated_at = NOW()
                        WHERE institution_id = ?
                    ";
                    DB::connection('payment_db')->update($updateSql, [$amount, $institutionId]);
                }
                
                DB::connection('payment_db')->commit();
                
                return response()->json([
                    'code' => 20000,
                    'message' => '调整成功'
                ]);
                
            } catch (Exception $e) {
                DB::connection('payment_db')->rollback();
                throw $e;
            }
            
        } catch (Exception $e) {
            Log::error('调整余额失败: ' . $e->getMessage());
            return response()->json([
                'code' => 50000,
                'message' => '调整失败: ' . $e->getMessage()
            ], 500);
        }
    }
} 