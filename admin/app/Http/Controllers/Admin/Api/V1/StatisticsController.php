<?php

namespace App\Http\Controllers\Admin\Api\V1;

use App\Http\Controllers\Controller;
use App\Traits\ApiResponseTrait;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Models\User;
use App\Models\Order;
use App\Models\Device;
use App\Models\Merchant;
use App\Models\Salesman;

class StatisticsController extends Controller
{
    use ApiResponseTrait;

    /**
     * 获取用户统计数据
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function userStats(Request $request)
    {
        // 时间范围筛选
        $startTime = $request->input('start_time', date('Y-m-d', strtotime('-30 days')));
        $endTime = $request->input('end_time', date('Y-m-d'));
        
        // 用户总数
        $totalUsers = User::count();
        
        // 新增用户数
        $newUsers = User::where('created_at', '>=', $startTime)
            ->where('created_at', '<=', $endTime . ' 23:59:59')
            ->count();
        
        // 活跃用户数（有订单的用户）
        $activeUsers = 0;
        if (class_exists('App\Models\Order')) {
            $activeUsers = Order::where('created_at', '>=', $startTime)
                ->where('created_at', '<=', $endTime . ' 23:59:59')
                ->distinct('user_id')
                ->count('user_id');
        }
        
        // 用户增长趋势
        $userTrend = User::where('created_at', '>=', $startTime)
            ->where('created_at', '<=', $endTime . ' 23:59:59')
            ->selectRaw('DATE(created_at) as date, COUNT(*) as count')
            ->groupBy('date')
            ->orderBy('date', 'asc')
            ->get()
            ->map(function ($item) {
                return [
                    'date' => $item->date,
                    'count' => $item->count
                ];
            });
        
        // 用户来源分布
        $userSourceDistribution = User::where('created_at', '>=', $startTime)
            ->where('created_at', '<=', $endTime . ' 23:59:59')
            ->selectRaw('source, COUNT(*) as count')
            ->groupBy('source')
            ->orderByRaw('COUNT(*) DESC')
            ->get()
            ->map(function ($item) {
                return [
                    'source' => $item->source ?: '未知',
                    'count' => $item->count
                ];
            });
        
        $data = [
            'total_users' => $totalUsers,
            'new_users' => $newUsers,
            'active_users' => $activeUsers,
            'user_trend' => $userTrend,
            'user_source_distribution' => $userSourceDistribution
        ];
        
        return $this->success($data);
    }

    /**
     * 获取订单统计数据
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function orderStats(Request $request)
    {
        if (!class_exists('App\Models\Order')) {
            return $this->error('订单模型不存在', 500);
        }
        
        // 时间范围筛选
        $startTime = $request->input('start_time', date('Y-m-d', strtotime('-30 days')));
        $endTime = $request->input('end_time', date('Y-m-d'));
        
        // 订单总数
        $totalOrders = Order::count();
        
        // 时间范围内订单数
        $periodOrders = Order::where('created_at', '>=', $startTime)
            ->where('created_at', '<=', $endTime . ' 23:59:59')
            ->count();
        
        // 订单总金额
        $totalAmount = Order::sum('total_amount');
        
        // 时间范围内订单金额
        $periodAmount = Order::where('created_at', '>=', $startTime)
            ->where('created_at', '<=', $endTime . ' 23:59:59')
            ->sum('total_amount');
        
        // 已支付订单数
        $paidOrders = Order::where('pay_status', 1)->count();
        
        // 时间范围内已支付订单数
        $periodPaidOrders = Order::where('created_at', '>=', $startTime)
            ->where('created_at', '<=', $endTime . ' 23:59:59')
            ->where('pay_status', 1)
            ->count();
        
        // 已支付金额
        $paidAmount = Order::where('pay_status', 1)->sum('total_amount');
        
        // 时间范围内已支付金额
        $periodPaidAmount = Order::where('created_at', '>=', $startTime)
            ->where('created_at', '<=', $endTime . ' 23:59:59')
            ->where('pay_status', 1)
            ->sum('total_amount');
        
        // 订单趋势
        $orderTrend = Order::where('created_at', '>=', $startTime)
            ->where('created_at', '<=', $endTime . ' 23:59:59')
            ->selectRaw('DATE(created_at) as date, COUNT(*) as count, SUM(total_amount) as amount, SUM(CASE WHEN pay_status = 1 THEN total_amount ELSE 0 END) as paid_amount')
            ->groupBy('date')
            ->orderBy('date', 'asc')
            ->get()
            ->map(function ($item) {
                return [
                    'date' => $item->date,
                    'count' => $item->count,
                    'amount' => $item->amount,
                    'paid_amount' => $item->paid_amount
                ];
            });
        
        // 支付方式分布
        $paymentMethodDistribution = Order::where('created_at', '>=', $startTime)
            ->where('created_at', '<=', $endTime . ' 23:59:59')
            ->where('pay_status', 1)
            ->selectRaw('payment_method, COUNT(*) as count, SUM(total_amount) as amount')
            ->groupBy('payment_method')
            ->orderByRaw('COUNT(*) DESC')
            ->get()
            ->map(function ($item) {
                return [
                    'payment_method' => $item->payment_method ?: '未知',
                    'count' => $item->count,
                    'amount' => $item->amount
                ];
            });
        
        $data = [
            'total_orders' => $totalOrders,
            'period_orders' => $periodOrders,
            'total_amount' => $totalAmount,
            'period_amount' => $periodAmount,
            'paid_orders' => $paidOrders,
            'period_paid_orders' => $periodPaidOrders,
            'paid_amount' => $paidAmount,
            'period_paid_amount' => $periodPaidAmount,
            'order_trend' => $orderTrend,
            'payment_method_distribution' => $paymentMethodDistribution
        ];
        
        return $this->success($data);
    }

    /**
     * 获取设备统计数据
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function deviceStats(Request $request)
    {
        if (!class_exists('App\Models\Device')) {
            return $this->error('设备模型不存在', 500);
        }
        
        // 设备总数
        $totalDevices = Device::count();
        
        // 在线设备数
        $onlineDevices = Device::where('is_online', 1)->count();
        
        // 离线设备数
        $offlineDevices = Device::where('is_online', 0)->count();
        
        // 在线率
        $onlineRate = $totalDevices > 0 ? round($onlineDevices / $totalDevices * 100, 2) : 0;
        
        // 设备类型分布
        $deviceTypeDistribution = Device::selectRaw('device_type, COUNT(*) as count')
            ->groupBy('device_type')
            ->orderByRaw('COUNT(*) DESC')
            ->get()
            ->map(function ($item) {
                return [
                    'device_type' => $item->device_type ?: '未知',
                    'count' => $item->count
                ];
            });
        
        // 设备状态分布
        $deviceStatusDistribution = Device::selectRaw('status, COUNT(*) as count')
            ->groupBy('status')
            ->orderByRaw('COUNT(*) DESC')
            ->get()
            ->map(function ($item) {
                return [
                    'status' => $item->status,
                    'count' => $item->count
                ];
            });
        
        // 设备区域分布
        $deviceRegionDistribution = Device::join('merchants', 'devices.merchant_id', '=', 'merchants.id')
            ->selectRaw('merchants.region, COUNT(devices.id) as count')
            ->groupBy('merchants.region')
            ->orderByRaw('COUNT(devices.id) DESC')
            ->get()
            ->map(function ($item) {
                return [
                    'region' => $item->region ?: '未知',
                    'count' => $item->count
                ];
            });
        
        $data = [
            'total_devices' => $totalDevices,
            'online_devices' => $onlineDevices,
            'offline_devices' => $offlineDevices,
            'online_rate' => $onlineRate,
            'device_type_distribution' => $deviceTypeDistribution,
            'device_status_distribution' => $deviceStatusDistribution,
            'device_region_distribution' => $deviceRegionDistribution
        ];
        
        return $this->success($data);
    }

    /**
     * 获取商户统计数据
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function merchantStats(Request $request)
    {
        if (!class_exists('App\Models\Merchant')) {
            return $this->error('商户模型不存在', 500);
        }
        
        // 时间范围筛选
        $startTime = $request->input('start_time', date('Y-m-d', strtotime('-30 days')));
        $endTime = $request->input('end_time', date('Y-m-d'));
        
        // 商户总数
        $totalMerchants = Merchant::count();
        
        // 新增商户数
        $newMerchants = Merchant::where('created_at', '>=', $startTime)
            ->where('created_at', '<=', $endTime . ' 23:59:59')
            ->count();
        
        // 活跃商户数（有订单的商户）
        $activeMerchants = 0;
        if (class_exists('App\Models\Order')) {
            $activeMerchants = Order::where('created_at', '>=', $startTime)
                ->where('created_at', '<=', $endTime . ' 23:59:59')
                ->distinct('merchant_id')
                ->count('merchant_id');
        }
        
        // 商户类型分布
        $merchantTypeDistribution = Merchant::selectRaw('type, COUNT(*) as count')
            ->groupBy('type')
            ->orderByRaw('COUNT(*) DESC')
            ->get()
            ->map(function ($item) {
                return [
                    'type' => $item->type ?: '未知',
                    'count' => $item->count
                ];
            });
        
        // 商户区域分布
        $merchantRegionDistribution = Merchant::selectRaw('region, COUNT(*) as count')
            ->groupBy('region')
            ->orderByRaw('COUNT(*) DESC')
            ->get()
            ->map(function ($item) {
                return [
                    'region' => $item->region ?: '未知',
                    'count' => $item->count
                ];
            });
        
        // 商户增长趋势
        $merchantTrend = Merchant::where('created_at', '>=', $startTime)
            ->where('created_at', '<=', $endTime . ' 23:59:59')
            ->selectRaw('DATE(created_at) as date, COUNT(*) as count')
            ->groupBy('date')
            ->orderBy('date', 'asc')
            ->get()
            ->map(function ($item) {
                return [
                    'date' => $item->date,
                    'count' => $item->count
                ];
            });
        
        $data = [
            'total_merchants' => $totalMerchants,
            'new_merchants' => $newMerchants,
            'active_merchants' => $activeMerchants,
            'merchant_type_distribution' => $merchantTypeDistribution,
            'merchant_region_distribution' => $merchantRegionDistribution,
            'merchant_trend' => $merchantTrend
        ];
        
        return $this->success($data);
    }

    /**
     * 获取业务员统计数据
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function salesmanStats(Request $request)
    {
        if (!class_exists('App\Models\Salesman')) {
            return $this->error('业务员模型不存在', 500);
        }
        
        // 时间范围筛选
        $startTime = $request->input('start_time', date('Y-m-d', strtotime('-30 days')));
        $endTime = $request->input('end_time', date('Y-m-d'));
        
        // 业务员总数
        $totalSalesmen = Salesman::count();
        
        // 业务员业绩排行
        $salesmanRanking = Salesman::select('salesmen.*')
            ->selectRaw('COUNT(DISTINCT merchants.id) as merchant_count')
            ->selectRaw('COUNT(DISTINCT devices.id) as device_count')
            ->leftJoin('merchants', 'salesmen.id', '=', 'merchants.salesman_id')
            ->leftJoin('devices', 'merchants.id', '=', 'devices.merchant_id')
            ->groupBy('salesmen.id')
            ->orderByRaw('COUNT(DISTINCT merchants.id) DESC')
            ->limit(10)
            ->get()
            ->map(function ($item) {
                return [
                    'id' => $item->id,
                    'name' => $item->name,
                    'phone' => $item->phone,
                    'merchant_count' => $item->merchant_count,
                    'device_count' => $item->device_count
                ];
            });
        
        // 业务员区域分布
        $salesmanRegionDistribution = Salesman::selectRaw('region, COUNT(*) as count')
            ->groupBy('region')
            ->orderByRaw('COUNT(*) DESC')
            ->get()
            ->map(function ($item) {
                return [
                    'region' => $item->region ?: '未知',
                    'count' => $item->count
                ];
            });
        
        // 业务员新增商户趋势
        $salesmanMerchantTrend = Merchant::where('created_at', '>=', $startTime)
            ->where('created_at', '<=', $endTime . ' 23:59:59')
            ->whereNotNull('salesman_id')
            ->selectRaw('DATE(created_at) as date, COUNT(*) as count')
            ->groupBy('date')
            ->orderBy('date', 'asc')
            ->get()
            ->map(function ($item) {
                return [
                    'date' => $item->date,
                    'count' => $item->count
                ];
            });
        
        $data = [
            'total_salesmen' => $totalSalesmen,
            'salesman_ranking' => $salesmanRanking,
            'salesman_region_distribution' => $salesmanRegionDistribution,
            'salesman_merchant_trend' => $salesmanMerchantTrend
        ];
        
        return $this->success($data);
    }

    /**
     * 获取产品统计数据
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function productStats(Request $request)
    {
        if (!class_exists('App\Models\Product')) {
            return $this->error('产品模型不存在', 500);
        }
        
        // 时间范围筛选
        $startTime = $request->input('start_time', date('Y-m-d', strtotime('-30 days')));
        $endTime = $request->input('end_time', date('Y-m-d'));
        
        // 产品总数
        $totalProducts = \App\Models\Product::count();
        
        // 热销产品排行
        $hotProducts = [];
        if (class_exists('App\Models\OrderItem')) {
            $hotProducts = \App\Models\OrderItem::select('order_items.product_id')
                ->selectRaw('products.name as product_name')
                ->selectRaw('SUM(order_items.quantity) as total_quantity')
                ->selectRaw('SUM(order_items.price * order_items.quantity) as total_amount')
                ->join('products', 'order_items.product_id', '=', 'products.id')
                ->join('orders', 'order_items.order_id', '=', 'orders.id')
                ->where('orders.created_at', '>=', $startTime)
                ->where('orders.created_at', '<=', $endTime . ' 23:59:59')
                ->where('orders.pay_status', 1)
                ->groupBy('order_items.product_id', 'products.name')
                ->orderByRaw('SUM(order_items.quantity) DESC')
                ->limit(10)
                ->get()
                ->map(function ($item) {
                    return [
                        'product_id' => $item->product_id,
                        'product_name' => $item->product_name,
                        'total_quantity' => $item->total_quantity,
                        'total_amount' => $item->total_amount
                    ];
                });
        }
        
        // 产品分类销量分布
        $categoryDistribution = [];
        if (class_exists('App\Models\OrderItem') && class_exists('App\Models\ProductCategory')) {
            $categoryDistribution = \App\Models\OrderItem::select('product_categories.id as category_id')
                ->selectRaw('product_categories.name as category_name')
                ->selectRaw('SUM(order_items.quantity) as total_quantity')
                ->selectRaw('SUM(order_items.price * order_items.quantity) as total_amount')
                ->join('products', 'order_items.product_id', '=', 'products.id')
                ->join('product_categories', 'products.category_id', '=', 'product_categories.id')
                ->join('orders', 'order_items.order_id', '=', 'orders.id')
                ->where('orders.created_at', '>=', $startTime)
                ->where('orders.created_at', '<=', $endTime . ' 23:59:59')
                ->where('orders.pay_status', 1)
                ->groupBy('product_categories.id', 'product_categories.name')
                ->orderByRaw('SUM(order_items.quantity) DESC')
                ->get()
                ->map(function ($item) {
                    return [
                        'category_id' => $item->category_id,
                        'category_name' => $item->category_name,
                        'total_quantity' => $item->total_quantity,
                        'total_amount' => $item->total_amount
                    ];
                });
        }
        
        $data = [
            'total_products' => $totalProducts,
            'hot_products' => $hotProducts,
            'category_distribution' => $categoryDistribution
        ];
        
        return $this->success($data);
    }
}
