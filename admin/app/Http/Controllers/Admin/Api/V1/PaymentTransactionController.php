<?php

namespace App\Http\Controllers\Admin\Api\V1;

use App\Http\Controllers\Controller;
use App\Traits\ApiResponseTrait;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use App\Models\PaymentTransaction;
use App\Models\PaymentOrder;

class PaymentTransactionController extends Controller
{
    use ApiResponseTrait;

    /**
     * 获取支付交易记录列表
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        $query = PaymentTransaction::query();
        
        // 搜索条件
        if ($request->has('keyword') && !empty($request->keyword)) {
            $keyword = $request->keyword;
            $query->where(function($q) use ($keyword) {
                $q->where('order_no', 'like', "%{$keyword}%")
                  ->orWhere('transaction_no', 'like', "%{$keyword}%");
            });
        }
        
        // 订单号筛选
        if ($request->has('order_no') && !empty($request->order_no)) {
            $query->where('order_no', $request->order_no);
        }
        
        // 状态筛选
        if ($request->has('status') && $request->status !== '') {
            $query->where('status', $request->status);
        }
        
        // 交易类型筛选
        if ($request->has('transaction_type') && !empty($request->transaction_type)) {
            $query->where('transaction_type', $request->transaction_type);
        }
        
        // 支付方式筛选
        if ($request->has('payment_method') && !empty($request->payment_method)) {
            $query->where('payment_method', $request->payment_method);
        }
        
        // 金额范围筛选
        if ($request->has('min_amount') && !empty($request->min_amount)) {
            $query->where('amount', '>=', $request->min_amount);
        }
        
        if ($request->has('max_amount') && !empty($request->max_amount)) {
            $query->where('amount', '<=', $request->max_amount);
        }
        
        // 日期范围筛选
        if ($request->has('start_date') && !empty($request->start_date)) {
            $query->where('transaction_time', '>=', $request->start_date);
        }
        
        if ($request->has('end_date') && !empty($request->end_date)) {
            $query->where('transaction_time', '<=', $request->end_date . ' 23:59:59');
        }
        
        // 排序
        $orderBy = $request->input('order_by', 'transaction_time');
        $orderDir = $request->input('order_dir', 'desc');
        $query->orderBy($orderBy, $orderDir);
        
        // 分页
        $perPage = $request->input('per_page', 15);
        $transactions = $query->paginate($perPage);
        
        // 加载关联数据
        $transactions->load('order');
        
        return $this->paginate($transactions);
    }

    /**
     * 获取单个交易记录详情
     *
     * @param string $transactionNo
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($transactionNo)
    {
        $transaction = PaymentTransaction::with('order')->where('transaction_no', $transactionNo)->first();
        
        if (!$transaction) {
            return $this->error('交易记录不存在', 404);
        }
        
        return $this->success($transaction);
    }
    
    /**
     * 获取交易记录统计数据
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function statistics(Request $request)
    {
        // 时间范围筛选
        $startDate = $request->input('start_date', date('Y-m-d', strtotime('-30 days')));
        $endDate = $request->input('end_date', date('Y-m-d'));
        
        // 支付方式筛选
        $paymentMethod = $request->input('payment_method');
        $query = PaymentTransaction::query();
        
        if ($paymentMethod) {
            $query->where('payment_method', $paymentMethod);
        }
        
        // 总交易数
        $totalCount = $query->count();
        
        // 时间范围内交易数
        $periodCount = $query->clone()
            ->where('transaction_time', '>=', $startDate)
            ->where('transaction_time', '<=', $endDate . ' 23:59:59')
            ->count();
        
        // 支付交易数
        $paymentCount = $query->clone()
            ->where('transaction_type', 'payment')
            ->where('status', 1)
            ->count();
        
        // 退款交易数
        $refundCount = $query->clone()
            ->where('transaction_type', 'refund')
            ->where('status', 1)
            ->count();
        
        // 总支付金额
        $totalPaymentAmount = $query->clone()
            ->where('transaction_type', 'payment')
            ->where('status', 1)
            ->sum('amount');
        
        // 总退款金额
        $totalRefundAmount = $query->clone()
            ->where('transaction_type', 'refund')
            ->where('status', 1)
            ->sum('amount');
        
        // 时间范围内支付金额
        $periodPaymentAmount = $query->clone()
            ->where('transaction_type', 'payment')
            ->where('status', 1)
            ->where('transaction_time', '>=', $startDate)
            ->where('transaction_time', '<=', $endDate . ' 23:59:59')
            ->sum('amount');
        
        // 时间范围内退款金额
        $periodRefundAmount = $query->clone()
            ->where('transaction_type', 'refund')
            ->where('status', 1)
            ->where('transaction_time', '>=', $startDate)
            ->where('transaction_time', '<=', $endDate . ' 23:59:59')
            ->sum('amount');
        
        // 各状态交易数
        $statusCounts = $query->clone()
            ->selectRaw('status, COUNT(*) as count')
            ->groupBy('status')
            ->pluck('count', 'status')
            ->toArray();
        
        // 成功交易数
        $successCount = $statusCounts[1] ?? 0;
        
        // 失败交易数
        $failCount = $statusCounts[0] ?? 0;
        
        // 支付方式分布
        $paymentMethodDistribution = $query->clone()
            ->where('status', 1)
            ->selectRaw('payment_method, COUNT(*) as count, SUM(amount) as amount')
            ->groupBy('payment_method')
            ->orderByRaw('COUNT(*) DESC')
            ->get();
        
        // 每日交易趋势
        $dailyTrend = $query->clone()
            ->where('status', 1)
            ->where('transaction_time', '>=', $startDate)
            ->where('transaction_time', '<=', $endDate . ' 23:59:59')
            ->selectRaw('DATE(transaction_time) as date, COUNT(*) as count, SUM(CASE WHEN transaction_type = "payment" THEN amount ELSE 0 END) as payment_amount, SUM(CASE WHEN transaction_type = "refund" THEN amount ELSE 0 END) as refund_amount')
            ->groupBy('date')
            ->orderBy('date', 'asc')
            ->get();
        
        $data = [
            'total_count' => $totalCount,
            'period_count' => $periodCount,
            'payment_count' => $paymentCount,
            'refund_count' => $refundCount,
            'total_payment_amount' => $totalPaymentAmount,
            'total_refund_amount' => $totalRefundAmount,
            'period_payment_amount' => $periodPaymentAmount,
            'period_refund_amount' => $periodRefundAmount,
            'success_count' => $successCount,
            'fail_count' => $failCount,
            'payment_method_distribution' => $paymentMethodDistribution,
            'daily_trend' => $dailyTrend
        ];
        
        return $this->success($data);
    }
    
    /**
     * 导出交易记录
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function export(Request $request)
    {
        // 时间范围筛选
        $startDate = $request->input('start_date', date('Y-m-d', strtotime('-30 days')));
        $endDate = $request->input('end_date', date('Y-m-d'));
        
        $query = PaymentTransaction::query();
        
        // 订单号筛选
        if ($request->has('order_no') && !empty($request->order_no)) {
            $query->where('order_no', $request->order_no);
        }
        
        // 状态筛选
        if ($request->has('status') && $request->status !== '') {
            $query->where('status', $request->status);
        }
        
        // 交易类型筛选
        if ($request->has('transaction_type') && !empty($request->transaction_type)) {
            $query->where('transaction_type', $request->transaction_type);
        }
        
        // 支付方式筛选
        if ($request->has('payment_method') && !empty($request->payment_method)) {
            $query->where('payment_method', $request->payment_method);
        }
        
        // 日期范围筛选
        $query->where('transaction_time', '>=', $startDate)
            ->where('transaction_time', '<=', $endDate . ' 23:59:59');
        
        // 排序
        $query->orderBy('transaction_time', 'desc');
        
        // 获取数据
        $transactions = $query->with('order')->get();
        
        // 准备表头
        $headers = [
            '交易号',
            '订单号',
            '交易类型',
            '支付方式',
            '金额',
            '状态',
            '交易时间',
            '备注'
        ];
        
        // 准备数据
        $data = [];
        foreach ($transactions as $transaction) {
            $row = [
                $transaction->transaction_no,
                $transaction->order_no,
                $this->getTransactionTypeText($transaction->transaction_type),
                $transaction->payment_method,
                $transaction->amount,
                $this->getStatusText($transaction->status),
                $transaction->transaction_time,
                $transaction->remark
            ];
            
            $data[] = $row;
        }
        
        return $this->success([
            'headers' => $headers,
            'data' => $data,
            'filename' => '交易记录_' . date('YmdHis') . '.xlsx'
        ]);
    }
    
    /**
     * 获取交易类型文本
     *
     * @param string $type
     * @return string
     */
    private function getTransactionTypeText($type)
    {
        $typeMap = [
            'payment' => '支付',
            'refund' => '退款'
        ];
        
        return $typeMap[$type] ?? '未知类型';
    }
    
    /**
     * 获取状态文本
     *
     * @param int $status
     * @return string
     */
    private function getStatusText($status)
    {
        $statusMap = [
            0 => '失败',
            1 => '成功'
        ];
        
        return $statusMap[$status] ?? '未知状态';
    }
}
