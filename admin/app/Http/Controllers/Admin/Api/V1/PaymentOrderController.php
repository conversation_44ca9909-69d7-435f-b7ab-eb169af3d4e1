<?php

namespace App\Http\Controllers\Admin\Api\V1;

use App\Http\Controllers\Controller;
use App\Traits\ApiResponseTrait;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use App\Models\PaymentOrder;
use App\Models\PaymentTransaction;

class PaymentOrderController extends Controller
{
    use ApiResponseTrait;

    /**
     * 获取支付订单列表
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        $query = PaymentOrder::query();
        
        // 搜索条件
        if ($request->has('keyword') && !empty($request->keyword)) {
            $keyword = $request->keyword;
            $query->where(function($q) use ($keyword) {
                $q->where('order_no', 'like', "%{$keyword}%")
                  ->orWhere('out_trade_no', 'like', "%{$keyword}%")
                  ->orWhere('subject', 'like', "%{$keyword}%");
            });
        }
        
        // 状态筛选
        if ($request->has('status') && $request->status !== '') {
            $query->where('status', $request->status);
        }
        
        // 支付方式筛选
        if ($request->has('payment_method') && !empty($request->payment_method)) {
            $query->where('payment_method', $request->payment_method);
        }
        
        // 商户筛选
        if ($request->has('merchant_id') && !empty($request->merchant_id)) {
            $query->where('merchant_id', $request->merchant_id);
        }
        
        // 用户筛选
        if ($request->has('user_id') && !empty($request->user_id)) {
            $query->where('user_id', $request->user_id);
        }
        
        // 金额范围筛选
        if ($request->has('min_amount') && !empty($request->min_amount)) {
            $query->where('amount', '>=', $request->min_amount);
        }
        
        if ($request->has('max_amount') && !empty($request->max_amount)) {
            $query->where('amount', '<=', $request->max_amount);
        }
        
        // 日期范围筛选
        if ($request->has('start_date') && !empty($request->start_date)) {
            $query->where('created_at', '>=', $request->start_date);
        }
        
        if ($request->has('end_date') && !empty($request->end_date)) {
            $query->where('created_at', '<=', $request->end_date . ' 23:59:59');
        }
        
        // 排序
        $orderBy = $request->input('order_by', 'created_at');
        $orderDir = $request->input('order_dir', 'desc');
        $query->orderBy($orderBy, $orderDir);
        
        // 分页
        $perPage = $request->input('per_page', 15);
        $paymentOrders = $query->paginate($perPage);
        
        // 加载关联数据
        $paymentOrders->load(['merchant', 'user']);
        
        return $this->paginate($paymentOrders);
    }

    /**
     * 获取单个支付订单详情
     *
     * @param string $orderNo
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($orderNo)
    {
        $paymentOrder = PaymentOrder::with(['merchant', 'user', 'transactions'])->where('order_no', $orderNo)->first();
        
        if (!$paymentOrder) {
            return $this->error('支付订单不存在', 404);
        }
        
        return $this->success($paymentOrder);
    }

    /**
     * 更新支付订单状态
     *
     * @param Request $request
     * @param string $orderNo
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateStatus(Request $request, $orderNo)
    {
        $paymentOrder = PaymentOrder::where('order_no', $orderNo)->first();
        
        if (!$paymentOrder) {
            return $this->error('支付订单不存在', 404);
        }
        
        $validator = Validator::make($request->all(), [
            'status' => 'required|integer|in:0,1,2,3',
            'remark' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return $this->error('验证失败', 422, $validator->errors());
        }
        
        try {
            // 如果订单已完成或已关闭，不允许修改状态
            if ($paymentOrder->status == 2 || $paymentOrder->status == 3) {
                return $this->error('订单已完成或已关闭，不能修改状态', 400);
            }
            
            $paymentOrder->status = $request->status;
            
            // 如果有备注，更新备注
            if ($request->has('remark')) {
                $paymentOrder->remark = $request->remark;
            }
            
            // 如果状态是已支付，更新支付时间
            if ($request->status == 2 && !$paymentOrder->pay_time) {
                $paymentOrder->pay_time = now();
                
                // 创建支付交易记录
                $transaction = new PaymentTransaction();
                $transaction->order_no = $paymentOrder->order_no;
                $transaction->transaction_no = 'MANUAL_' . date('YmdHis') . rand(1000, 9999);
                $transaction->payment_method = $paymentOrder->payment_method;
                $transaction->amount = $paymentOrder->amount;
                $transaction->status = 1; // 成功
                $transaction->transaction_type = 'payment';
                $transaction->transaction_time = now();
                $transaction->remark = '管理员手动确认支付';
                $transaction->save();
            }
            
            // 如果状态是已关闭，更新关闭时间
            if ($request->status == 3 && !$paymentOrder->close_time) {
                $paymentOrder->close_time = now();
            }
            
            $paymentOrder->save();
            
            // 加载关联数据
            $paymentOrder->load(['merchant', 'user', 'transactions']);
            
            return $this->success($paymentOrder, '支付订单状态更新成功');
        } catch (\Exception $e) {
            return $this->error('支付订单状态更新失败: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * 关闭支付订单
     *
     * @param string $orderNo
     * @return \Illuminate\Http\JsonResponse
     */
    public function close($orderNo)
    {
        $paymentOrder = PaymentOrder::where('order_no', $orderNo)->first();
        
        if (!$paymentOrder) {
            return $this->error('支付订单不存在', 404);
        }
        
        // 如果订单已完成或已关闭，不允许关闭
        if ($paymentOrder->status == 2 || $paymentOrder->status == 3) {
            return $this->error('订单已完成或已关闭，不能再次关闭', 400);
        }
        
        try {
            $paymentOrder->status = 3; // 已关闭
            $paymentOrder->close_time = now();
            $paymentOrder->save();
            
            // 加载关联数据
            $paymentOrder->load(['merchant', 'user', 'transactions']);
            
            return $this->success($paymentOrder, '支付订单关闭成功');
        } catch (\Exception $e) {
            return $this->error('支付订单关闭失败: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * 退款
     *
     * @param Request $request
     * @param string $orderNo
     * @return \Illuminate\Http\JsonResponse
     */
    public function refund(Request $request, $orderNo)
    {
        $paymentOrder = PaymentOrder::where('order_no', $orderNo)->first();
        
        if (!$paymentOrder) {
            return $this->error('支付订单不存在', 404);
        }
        
        // 如果订单未支付或已关闭，不允许退款
        if ($paymentOrder->status != 2) {
            return $this->error('订单未支付或已关闭，不能退款', 400);
        }
        
        $validator = Validator::make($request->all(), [
            'refund_amount' => 'required|numeric|min:0.01|max:' . $paymentOrder->amount,
            'refund_reason' => 'required|string|max:200',
        ]);

        if ($validator->fails()) {
            return $this->error('验证失败', 422, $validator->errors());
        }
        
        try {
            // 创建退款交易记录
            $transaction = new PaymentTransaction();
            $transaction->order_no = $paymentOrder->order_no;
            $transaction->transaction_no = 'REFUND_' . date('YmdHis') . rand(1000, 9999);
            $transaction->payment_method = $paymentOrder->payment_method;
            $transaction->amount = $request->refund_amount;
            $transaction->status = 1; // 成功
            $transaction->transaction_type = 'refund';
            $transaction->transaction_time = now();
            $transaction->remark = $request->refund_reason;
            $transaction->save();
            
            // 更新订单退款金额
            $paymentOrder->refund_amount = ($paymentOrder->refund_amount ?? 0) + $request->refund_amount;
            
            // 如果退款金额等于订单金额，标记为已退款
            if ($paymentOrder->refund_amount >= $paymentOrder->amount) {
                $paymentOrder->refund_status = 2; // 已全额退款
            } else {
                $paymentOrder->refund_status = 1; // 部分退款
            }
            
            $paymentOrder->save();
            
            // 加载关联数据
            $paymentOrder->load(['merchant', 'user', 'transactions']);
            
            return $this->success($paymentOrder, '退款成功');
        } catch (\Exception $e) {
            return $this->error('退款失败: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * 获取支付订单统计数据
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function statistics(Request $request)
    {
        // 时间范围筛选
        $startDate = $request->input('start_date', date('Y-m-d', strtotime('-30 days')));
        $endDate = $request->input('end_date', date('Y-m-d'));
        
        // 商户筛选
        $merchantId = $request->input('merchant_id');
        $query = PaymentOrder::query();
        
        if ($merchantId) {
            $query->where('merchant_id', $merchantId);
        }
        
        // 总订单数
        $totalCount = $query->count();
        
        // 时间范围内订单数
        $periodCount = $query->clone()
            ->where('created_at', '>=', $startDate)
            ->where('created_at', '<=', $endDate . ' 23:59:59')
            ->count();
        
        // 总支付金额
        $totalAmount = $query->clone()
            ->where('status', 2)
            ->sum('amount');
        
        // 时间范围内支付金额
        $periodAmount = $query->clone()
            ->where('status', 2)
            ->where('created_at', '>=', $startDate)
            ->where('created_at', '<=', $endDate . ' 23:59:59')
            ->sum('amount');
        
        // 总退款金额
        $totalRefundAmount = $query->clone()
            ->where('refund_status', '>', 0)
            ->sum('refund_amount');
        
        // 时间范围内退款金额
        $periodRefundAmount = $query->clone()
            ->where('refund_status', '>', 0)
            ->where('created_at', '>=', $startDate)
            ->where('created_at', '<=', $endDate . ' 23:59:59')
            ->sum('refund_amount');
        
        // 各状态订单数
        $statusCounts = $query->clone()
            ->selectRaw('status, COUNT(*) as count')
            ->groupBy('status')
            ->pluck('count', 'status')
            ->toArray();
        
        // 待支付订单数
        $pendingCount = $statusCounts[0] ?? 0;
        
        // 支付中订单数
        $processingCount = $statusCounts[1] ?? 0;
        
        // 已支付订单数
        $paidCount = $statusCounts[2] ?? 0;
        
        // 已关闭订单数
        $closedCount = $statusCounts[3] ?? 0;
        
        // 支付方式分布
        $paymentMethodDistribution = $query->clone()
            ->where('status', 2)
            ->selectRaw('payment_method, COUNT(*) as count, SUM(amount) as amount')
            ->groupBy('payment_method')
            ->orderByRaw('COUNT(*) DESC')
            ->get();
        
        // 每日支付趋势
        $dailyTrend = $query->clone()
            ->where('status', 2)
            ->where('created_at', '>=', $startDate)
            ->where('created_at', '<=', $endDate . ' 23:59:59')
            ->selectRaw('DATE(created_at) as date, COUNT(*) as count, SUM(amount) as amount')
            ->groupBy('date')
            ->orderBy('date', 'asc')
            ->get();
        
        $data = [
            'total_count' => $totalCount,
            'period_count' => $periodCount,
            'total_amount' => $totalAmount,
            'period_amount' => $periodAmount,
            'total_refund_amount' => $totalRefundAmount,
            'period_refund_amount' => $periodRefundAmount,
            'pending_count' => $pendingCount,
            'processing_count' => $processingCount,
            'paid_count' => $paidCount,
            'closed_count' => $closedCount,
            'payment_method_distribution' => $paymentMethodDistribution,
            'daily_trend' => $dailyTrend
        ];
        
        return $this->success($data);
    }
}
