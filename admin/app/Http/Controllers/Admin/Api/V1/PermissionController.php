<?php

namespace App\Http\Controllers\Admin\Api\V1;

use App\Http\Controllers\Controller;
use App\Traits\ApiResponseTrait;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use App\Models\Permission;

class PermissionController extends Controller
{
    use ApiResponseTrait;

    /**
     * 获取权限列表
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        $query = Permission::query();
        
        // 搜索条件
        if ($request->has('keyword') && !empty($request->keyword)) {
            $keyword = $request->keyword;
            $query->where(function($q) use ($keyword) {
                $q->where('name', 'like', "%{$keyword}%")
                  ->orWhere('display_name', 'like', "%{$keyword}%");
            });
        }
        
        // 模块筛选
        if ($request->has('module') && !empty($request->module)) {
            $query->where('module', $request->module);
        }
        
        // 排序
        $orderBy = $request->input('order_by', 'id');
        $orderDir = $request->input('order_dir', 'asc');
        $query->orderBy($orderBy, $orderDir);
        
        // 分页
        $perPage = $request->input('per_page', 15);
        $permissions = $query->paginate($perPage);
        
        return $this->paginate($permissions);
    }

    /**
     * 创建权限
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|unique:admin_permissions,name',
            'display_name' => 'required|string',
            'description' => 'nullable|string',
            'module' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return $this->error('验证失败', 422, $validator->errors());
        }

        $permission = Permission::create([
            'name' => $request->name,
            'display_name' => $request->display_name,
            'description' => $request->description,
            'module' => $request->module
        ]);
        
        return $this->success($permission, '权限创建成功');
    }

    /**
     * 获取单个权限详情
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        $permission = Permission::find($id);
        
        if (!$permission) {
            return $this->error('权限不存在', 404);
        }
        
        return $this->success($permission);
    }

    /**
     * 更新权限
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id)
    {
        $permission = Permission::find($id);
        
        if (!$permission) {
            return $this->error('权限不存在', 404);
        }
        
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|unique:admin_permissions,name,'.$id,
            'display_name' => 'required|string',
            'description' => 'nullable|string',
            'module' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return $this->error('验证失败', 422, $validator->errors());
        }
        
        $permission->name = $request->name;
        $permission->display_name = $request->display_name;
        $permission->description = $request->description;
        $permission->module = $request->module;
        $permission->save();
        
        return $this->success($permission, '权限更新成功');
    }

    /**
     * 删除权限
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy($id)
    {
        $permission = Permission::find($id);
        
        if (!$permission) {
            return $this->error('权限不存在', 404);
        }
        
        // 检查是否有角色使用此权限
        $roleCount = $permission->roles()->count();
        if ($roleCount > 0) {
            return $this->error('该权限被角色使用，无法删除', 400);
        }
        
        $permission->delete();
        
        return $this->success(null, '权限删除成功');
    }
    
    /**
     * 获取所有权限模块
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function groups()
    {
        $modules = Permission::select('module')
            ->distinct()
            ->whereNotNull('module')
            ->pluck('module');
            
        return $this->success($modules);
    }
}
