<?php

namespace App\Http\Controllers\Admin\Api\V1;

use App\Http\Controllers\Controller;
use App\Models\ScheduledTask;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class ScheduledTaskController extends Controller
{
    /**
     * 获取任务列表
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        try {
            $query = ScheduledTask::query();
            
            // 搜索条件
            if ($request->filled('name')) {
                $query->where('name', 'like', '%' . $request->name . '%');
            }
            
            if ($request->filled('is_enabled')) {
                $query->where('is_enabled', $request->is_enabled);
            }
            
            if ($request->filled('last_run_status')) {
                $query->where('last_run_status', $request->last_run_status);
            }
            
            // 排序
            $sortBy = $request->get('sort_by', 'created_at');
            $sortOrder = $request->get('sort_order', 'desc');
            $query->orderBy($sortBy, $sortOrder);
            
            // 分页
            $perPage = $request->get('per_page', 15);
            $tasks = $query->paginate($perPage);
            
            // 添加计算属性
            $tasks->getCollection()->transform(function ($task) {
                $task->status_text = $task->status_text;
                $task->success_rate = $task->success_rate;
                $task->next_run_human = $task->next_run_human;
                $task->last_run_human = $task->last_run_human;
                return $task;
            });
            
            return response()->json([
                'code' => 200,
                'message' => '获取任务列表成功',
                'data' => $tasks
            ]);
            
        } catch (\Exception $e) {
            Log::error('获取任务列表失败: ' . $e->getMessage());
            return response()->json([
                'code' => 500,
                'message' => '获取任务列表失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 创建新任务
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'name' => 'required|string|max:100|unique:scheduled_tasks,name',
                'command' => 'required|string|max:255',
                'description' => 'nullable|string|max:500',
                'schedule_expression' => 'required|string|max:50',
                'schedule_description' => 'required|string|max:100',
                'is_enabled' => 'boolean',
                'without_overlapping' => 'boolean',
                'log_file' => 'nullable|string|max:255'
            ], [
                'name.required' => '任务名称不能为空',
                'name.unique' => '任务名称已存在',
                'command.required' => '执行命令不能为空',
                'schedule_expression.required' => '调度表达式不能为空',
                'schedule_description.required' => '调度描述不能为空'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'code' => 400,
                    'message' => '数据验证失败',
                    'errors' => $validator->errors()
                ]);
            }

            $data = $request->only([
                'name', 'command', 'description', 'schedule_expression', 
                'schedule_description', 'is_enabled', 'without_overlapping', 'log_file'
            ]);
            
            $data['is_enabled'] = $data['is_enabled'] ?? true;
            $data['without_overlapping'] = $data['without_overlapping'] ?? false;

            $task = ScheduledTask::create($data);
            
            // 如果任务启用，计算下次执行时间
            if ($task->is_enabled) {
                $task->next_run_at = $task->calculateNextRunTime();
                $task->save();
            }

            Log::info('创建定时任务成功', ['task_id' => $task->id, 'name' => $task->name]);

            return response()->json([
                'code' => 200,
                'message' => '任务创建成功',
                'data' => $task
            ]);

        } catch (\Exception $e) {
            Log::error('创建任务失败: ' . $e->getMessage());
            return response()->json([
                'code' => 500,
                'message' => '创建任务失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 获取任务详情
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        try {
            $task = ScheduledTask::findOrFail($id);
            
            // 添加计算属性
            $task->status_text = $task->status_text;
            $task->success_rate = $task->success_rate;
            $task->next_run_human = $task->next_run_human;
            $task->last_run_human = $task->last_run_human;
            
            return response()->json([
                'code' => 200,
                'message' => '获取任务详情成功',
                'data' => $task
            ]);
            
        } catch (\Exception $e) {
            Log::error('获取任务详情失败: ' . $e->getMessage());
            return response()->json([
                'code' => 404,
                'message' => '任务不存在'
            ]);
        }
    }

    /**
     * 更新任务
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id)
    {
        try {
            $task = ScheduledTask::findOrFail($id);
            
            $validator = Validator::make($request->all(), [
                'name' => 'required|string|max:100|unique:scheduled_tasks,name,' . $id,
                'command' => 'required|string|max:255',
                'description' => 'nullable|string|max:500',
                'schedule_expression' => 'required|string|max:50',
                'schedule_description' => 'required|string|max:100',
                'is_enabled' => 'boolean',
                'without_overlapping' => 'boolean',
                'log_file' => 'nullable|string|max:255'
            ], [
                'name.required' => '任务名称不能为空',
                'name.unique' => '任务名称已存在',
                'command.required' => '执行命令不能为空',
                'schedule_expression.required' => '调度表达式不能为空',
                'schedule_description.required' => '调度描述不能为空'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'code' => 400,
                    'message' => '数据验证失败',
                    'errors' => $validator->errors()
                ]);
            }

            $data = $request->only([
                'name', 'command', 'description', 'schedule_expression', 
                'schedule_description', 'is_enabled', 'without_overlapping', 'log_file'
            ]);

            $task->update($data);
            
            // 重新计算下次执行时间
            if ($task->is_enabled) {
                $task->next_run_at = $task->calculateNextRunTime();
                $task->save();
            } else {
                $task->next_run_at = null;
                $task->save();
            }

            Log::info('更新定时任务成功', ['task_id' => $task->id, 'name' => $task->name]);

            return response()->json([
                'code' => 200,
                'message' => '任务更新成功',
                'data' => $task
            ]);

        } catch (\Exception $e) {
            Log::error('更新任务失败: ' . $e->getMessage());
            return response()->json([
                'code' => 500,
                'message' => '更新任务失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 删除任务
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy($id)
    {
        try {
            $task = ScheduledTask::findOrFail($id);
            $taskName = $task->name;
            
            $task->delete();

            Log::info('删除定时任务成功', ['task_id' => $id, 'name' => $taskName]);

            return response()->json([
                'code' => 200,
                'message' => '任务删除成功'
            ]);

        } catch (\Exception $e) {
            Log::error('删除任务失败: ' . $e->getMessage());
            return response()->json([
                'code' => 500,
                'message' => '删除任务失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 启用/禁用任务
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function toggleStatus(Request $request, $id)
    {
        try {
            $task = ScheduledTask::findOrFail($id);
            
            $isEnabled = $request->get('is_enabled', !$task->is_enabled);
            
            if ($isEnabled) {
                $task->enable();
                $message = '任务已启用';
            } else {
                $task->disable();
                $message = '任务已禁用';
            }

            Log::info('切换任务状态成功', [
                'task_id' => $task->id, 
                'name' => $task->name, 
                'is_enabled' => $isEnabled
            ]);

            return response()->json([
                'code' => 200,
                'message' => $message,
                'data' => $task
            ]);

        } catch (\Exception $e) {
            Log::error('切换任务状态失败: ' . $e->getMessage());
            return response()->json([
                'code' => 500,
                'message' => '操作失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 手动执行任务
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function runTask($id)
    {
        try {
            $task = ScheduledTask::findOrFail($id);
            
            Log::info('手动执行任务开始', ['task_id' => $task->id, 'command' => $task->command]);
            
            // 更新任务状态为运行中
            $task->update(['last_run_status' => 'running']);
            
            // 执行命令
            $exitCode = Artisan::call($task->command);
            $output = Artisan::output();
            
            // 更新执行结果
            $status = $exitCode === 0 ? 'success' : 'failed';
            $task->updateRunStats($status, $output);
            
            // 更新下次执行时间
            if ($task->is_enabled) {
                $task->next_run_at = $task->calculateNextRunTime();
                $task->save();
            }

            Log::info('手动执行任务完成', [
                'task_id' => $task->id,
                'exit_code' => $exitCode,
                'status' => $status
            ]);

            return response()->json([
                'code' => 200,
                'message' => $status === 'success' ? '任务执行成功' : '任务执行失败',
                'data' => [
                    'exit_code' => $exitCode,
                    'output' => $output,
                    'status' => $status
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('手动执行任务失败: ' . $e->getMessage());
            return response()->json([
                'code' => 500,
                'message' => '执行任务失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 获取任务执行日志
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function getLogs($id)
    {
        try {
            $task = ScheduledTask::findOrFail($id);
            
            if (!$task->log_file) {
                return response()->json([
                    'code' => 404,
                    'message' => '该任务未配置日志文件'
                ]);
            }
            
            $logPath = storage_path($task->log_file);
            
            if (!file_exists($logPath)) {
                return response()->json([
                    'code' => 404,
                    'message' => '日志文件不存在'
                ]);
            }
            
            // 读取最后100行日志
            $lines = [];
            $file = new \SplFileObject($logPath);
            $file->seek(PHP_INT_MAX);
            $totalLines = $file->key();
            
            $startLine = max(0, $totalLines - 100);
            $file->seek($startLine);
            
            while (!$file->eof()) {
                $line = trim($file->current());
                if (!empty($line)) {
                    $lines[] = $line;
                }
                $file->next();
            }
            
            return response()->json([
                'code' => 200,
                'message' => '获取日志成功',
                'data' => [
                    'logs' => $lines,
                    'total_lines' => $totalLines,
                    'log_file' => $task->log_file
                ]
            ]);
            
        } catch (\Exception $e) {
            Log::error('获取任务日志失败: ' . $e->getMessage());
            return response()->json([
                'code' => 500,
                'message' => '获取日志失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 获取任务统计信息
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getStats()
    {
        try {
            $stats = [
                'total_tasks' => ScheduledTask::count(),
                'enabled_tasks' => ScheduledTask::where('is_enabled', true)->count(),
                'disabled_tasks' => ScheduledTask::where('is_enabled', false)->count(),
                'success_tasks' => ScheduledTask::where('last_run_status', 'success')->count(),
                'failed_tasks' => ScheduledTask::where('last_run_status', 'failed')->count(),
                'never_run_tasks' => ScheduledTask::whereNull('last_run_at')->count(),
                'total_runs' => ScheduledTask::sum('run_count'),
                'total_success' => ScheduledTask::sum('success_count'),
                'total_failures' => ScheduledTask::sum('failure_count')
            ];
            
            // 计算总体成功率
            $stats['overall_success_rate'] = $stats['total_runs'] > 0 
                ? round(($stats['total_success'] / $stats['total_runs']) * 100, 2) 
                : 0;
            
            return response()->json([
                'code' => 200,
                'message' => '获取统计信息成功',
                'data' => $stats
            ]);
            
        } catch (\Exception $e) {
            Log::error('获取任务统计失败: ' . $e->getMessage());
            return response()->json([
                'code' => 500,
                'message' => '获取统计信息失败: ' . $e->getMessage()
            ]);
        }
    }
}
