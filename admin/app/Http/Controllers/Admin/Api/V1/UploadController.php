<?php

namespace App\Http\Controllers\Admin\Api\V1;

use App\Http\Controllers\Controller;
use App\Traits\ApiResponseTrait;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class UploadController extends Controller
{
    use ApiResponseTrait;

    /**
     * 上传图片
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function upload(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'file' => 'required|file|max:10240', // 最大10MB
            'type' => 'nullable|string|in:image,file,video',
            'folder' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return $this->error('验证失败', 422, $validator->errors());
        }

        $file = $request->file('file');
        $type = $request->input('type', 'image');
        $folder = $request->input('folder', 'uploads');
        
        // 检查文件类型
        if ($type == 'image') {
            $validator = Validator::make($request->all(), [
                'file' => 'image|mimes:jpeg,png,jpg,gif|max:5120', // 最大5MB
            ]);
            
            if ($validator->fails()) {
                return $this->error('文件类型不正确，只允许上传图片文件', 422, $validator->errors());
            }
        } elseif ($type == 'video') {
            $validator = Validator::make($request->all(), [
                'file' => 'mimes:mp4,mov,avi,wmv|max:102400', // 最大100MB
            ]);
            
            if ($validator->fails()) {
                return $this->error('文件类型不正确，只允许上传视频文件', 422, $validator->errors());
            }
        }
        
        // 生成唯一文件名
        $extension = $file->getClientOriginalExtension();
        $fileName = Str::random(40) . '.' . $extension;
        
        // 存储路径
        $path = $folder . '/' . date('Ymd');
        
        // 存储文件
        $filePath = $file->storeAs('public/' . $path, $fileName);
        
        if (!$filePath) {
            return $this->error('文件上传失败', 500);
        }
        
        // 获取文件URL
        $url = Storage::url($filePath);
        
        // 获取文件信息
        $fileInfo = [
            'name' => $file->getClientOriginalName(),
            'size' => $file->getSize(),
            'type' => $file->getMimeType(),
            'extension' => $extension,
            'path' => $filePath,
            'url' => $url,
        ];
        
        // 记录上传日志
        $this->logFileUpload($fileInfo, auth()->id());
        
        return $this->success($fileInfo, '文件上传成功');
    }
    
    /**
     * 删除文件
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function delete(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'path' => 'required|string',
        ]);

        if ($validator->fails()) {
            return $this->error('验证失败', 422, $validator->errors());
        }
        
        $path = $request->path;
        
        // 安全检查：确保路径以public/开头
        if (strpos($path, 'public/') !== 0) {
            $path = 'public/' . ltrim($path, '/');
        }
        
        // 检查文件是否存在
        if (!Storage::exists($path)) {
            return $this->error('文件不存在', 404);
        }
        
        // 删除文件
        $result = Storage::delete($path);
        
        if (!$result) {
            return $this->error('文件删除失败', 500);
        }
        
        return $this->success(null, '文件删除成功');
    }
    
    /**
     * 获取上传配置
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function config()
    {
        $config = [
            'max_file_size' => config('filesystems.max_file_size', 10240), // 默认10MB
            'allowed_extensions' => config('filesystems.allowed_extensions', [
                'jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg',
                'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'pdf', 'txt',
                'mp4', 'mov', 'avi', 'wmv',
                'zip', 'rar', '7z'
            ]),
            'image_extensions' => ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg'],
            'document_extensions' => ['doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'pdf', 'txt'],
            'video_extensions' => ['mp4', 'mov', 'avi', 'wmv'],
            'archive_extensions' => ['zip', 'rar', '7z'],
            'upload_url' => '/api/admin/v1/upload',
            'storage_url' => Storage::url(''),
        ];
        
        return $this->success($config);
    }
    
    /**
     * 记录文件上传日志
     *
     * @param array $fileInfo
     * @param int $adminId
     * @return void
     */
    private function logFileUpload($fileInfo, $adminId)
    {
        if (class_exists('App\Models\UploadLog')) {
            \App\Models\UploadLog::create([
                'file_name' => $fileInfo['name'],
                'file_path' => $fileInfo['path'],
                'file_url' => $fileInfo['url'],
                'file_size' => $fileInfo['size'],
                'file_type' => $fileInfo['type'],
                'admin_id' => $adminId,
                'ip' => request()->ip(),
                'user_agent' => request()->userAgent(),
            ]);
        }
    }
}
