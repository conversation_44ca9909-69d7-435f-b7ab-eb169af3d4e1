<?php

namespace App\Http\Controllers\Admin\Api\V1;

use App\Http\Controllers\Controller;
use App\Traits\ApiResponseTrait;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Hash;
use App\Models\InstallationEngineer;
use App\Models\Installation;

class InstallationEngineerController extends Controller
{
    use ApiResponseTrait;

    /**
     * 获取工程师列表
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        $query = InstallationEngineer::query();
        
        // 搜索条件
        if ($request->has('keyword') && !empty($request->keyword)) {
            $keyword = $request->keyword;
            $query->where(function($q) use ($keyword) {
                $q->where('name', 'like', "%{$keyword}%")
                  ->orWhere('phone', 'like', "%{$keyword}%")
                  ->orWhere('region', 'like', "%{$keyword}%");
            });
        }
        
        // 状态筛选
        if ($request->has('status') && $request->status !== '') {
            $query->where('status', $request->status);
        }
        
        // 区域筛选
        if ($request->has('region') && !empty($request->region)) {
            $query->where('region', $request->region);
        }
        
        // 排序
        $orderBy = $request->input('order_by', 'created_at');
        $orderDir = $request->input('order_dir', 'desc');
        $query->orderBy($orderBy, $orderDir);
        
        // 分页
        $perPage = $request->input('per_page', 15);
        $engineers = $query->paginate($perPage);
        
        return $this->paginate($engineers);
    }

    /**
     * 创建工程师
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:50',
            'phone' => 'required|string|regex:/^1[3-9]\d{9}$/|unique:installation_engineers,phone',
            'password' => 'required|string|min:6',
            'region' => 'required|string|max:100',
            'address' => 'nullable|string|max:200',
            'id_card' => 'nullable|string|max:18',
            'avatar' => 'nullable|string|max:200',
            'status' => 'required|integer|in:0,1',
            'remark' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return $this->error('验证失败', 422, $validator->errors());
        }

        try {
            $engineer = new InstallationEngineer();
            $engineer->name = $request->name;
            $engineer->phone = $request->phone;
            $engineer->password = Hash::make($request->password);
            $engineer->region = $request->region;
            $engineer->address = $request->address;
            $engineer->id_card = $request->id_card;
            $engineer->avatar = $request->avatar;
            $engineer->status = $request->status;
            $engineer->remark = $request->remark;
            $engineer->save();
            
            return $this->success($engineer, '工程师创建成功');
        } catch (\Exception $e) {
            return $this->error('工程师创建失败: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 获取单个工程师详情
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        $engineer = InstallationEngineer::find($id);
        
        if (!$engineer) {
            return $this->error('工程师不存在', 404);
        }
        
        // 获取工程师统计数据
        $stats = $this->getEngineerStats($id);
        $engineer->stats = $stats;
        
        return $this->success($engineer);
    }

    /**
     * 更新工程师
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id)
    {
        $engineer = InstallationEngineer::find($id);
        
        if (!$engineer) {
            return $this->error('工程师不存在', 404);
        }
        
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:50',
            'phone' => 'required|string|regex:/^1[3-9]\d{9}$/|unique:installation_engineers,phone,'.$id,
            'password' => 'nullable|string|min:6',
            'region' => 'required|string|max:100',
            'address' => 'nullable|string|max:200',
            'id_card' => 'nullable|string|max:18',
            'avatar' => 'nullable|string|max:200',
            'status' => 'required|integer|in:0,1',
            'remark' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return $this->error('验证失败', 422, $validator->errors());
        }
        
        try {
            $engineer->name = $request->name;
            $engineer->phone = $request->phone;
            
            // 如果提供了密码，则更新密码
            if ($request->has('password') && !empty($request->password)) {
                $engineer->password = Hash::make($request->password);
            }
            
            $engineer->region = $request->region;
            $engineer->address = $request->address;
            $engineer->id_card = $request->id_card;
            $engineer->avatar = $request->avatar;
            $engineer->status = $request->status;
            $engineer->remark = $request->remark;
            $engineer->save();
            
            return $this->success($engineer, '工程师更新成功');
        } catch (\Exception $e) {
            return $this->error('工程师更新失败: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 删除工程师
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy($id)
    {
        $engineer = InstallationEngineer::find($id);
        
        if (!$engineer) {
            return $this->error('工程师不存在', 404);
        }
        
        // 检查是否有关联的安装预约
        $installationCount = Installation::where('engineer_id', $id)->count();
        if ($installationCount > 0) {
            return $this->error('该工程师有关联的安装预约，无法删除', 400);
        }
        
        try {
            $engineer->delete();
            
            return $this->success(null, '工程师删除成功');
        } catch (\Exception $e) {
            return $this->error('工程师删除失败: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * 更新工程师状态
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateStatus(Request $request, $id)
    {
        $engineer = InstallationEngineer::find($id);
        
        if (!$engineer) {
            return $this->error('工程师不存在', 404);
        }
        
        $validator = Validator::make($request->all(), [
            'status' => 'required|integer|in:0,1',
        ]);

        if ($validator->fails()) {
            return $this->error('验证失败', 422, $validator->errors());
        }
        
        try {
            $engineer->status = $request->status;
            $engineer->save();
            
            return $this->success($engineer, '工程师状态更新成功');
        } catch (\Exception $e) {
            return $this->error('工程师状态更新失败: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * 获取工程师安装任务列表
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function installations(Request $request, $id)
    {
        $engineer = InstallationEngineer::find($id);
        
        if (!$engineer) {
            return $this->error('工程师不存在', 404);
        }
        
        $query = Installation::where('engineer_id', $id);
        
        // 状态筛选
        if ($request->has('status') && $request->status !== '') {
            $query->where('status', $request->status);
        }
        
        // 日期范围筛选
        if ($request->has('start_date') && !empty($request->start_date)) {
            $query->where('installation_date', '>=', $request->start_date);
        }
        
        if ($request->has('end_date') && !empty($request->end_date)) {
            $query->where('installation_date', '<=', $request->end_date);
        }
        
        // 排序
        $orderBy = $request->input('order_by', 'installation_date');
        $orderDir = $request->input('order_dir', 'desc');
        $query->orderBy($orderBy, $orderDir);
        
        // 分页
        $perPage = $request->input('per_page', 15);
        $installations = $query->paginate($perPage);
        
        // 加载关联数据
        $installations->load(['device']);
        
        return $this->paginate($installations);
    }
    
    /**
     * 获取工程师统计数据
     *
     * @param int $id
     * @return array
     */
    public function stats($id)
    {
        $engineer = InstallationEngineer::find($id);
        
        if (!$engineer) {
            return $this->error('工程师不存在', 404);
        }
        
        $stats = $this->getEngineerStats($id);
        
        return $this->success($stats);
    }
    
    /**
     * 获取工程师区域列表
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function regions()
    {
        $regions = InstallationEngineer::select('region')
            ->distinct()
            ->whereNotNull('region')
            ->pluck('region');
            
        return $this->success($regions);
    }
    
    /**
     * 获取工程师统计数据
     *
     * @param int $engineerId
     * @return array
     */
    private function getEngineerStats($engineerId)
    {
        // 总安装任务数
        $totalCount = Installation::where('engineer_id', $engineerId)->count();
        
        // 各状态安装数
        $statusCounts = Installation::where('engineer_id', $engineerId)
            ->selectRaw('status, COUNT(*) as count')
            ->groupBy('status')
            ->pluck('count', 'status')
            ->toArray();
        
        // 待处理数量
        $pendingCount = ($statusCounts[1] ?? 0);
        
        // 安装中数量
        $installingCount = ($statusCounts[2] ?? 0);
        
        // 已完成数量
        $completedCount = ($statusCounts[3] ?? 0);
        
        // 已取消数量
        $cancelledCount = ($statusCounts[4] ?? 0);
        
        // 完成率
        $completionRate = $totalCount > 0 ? round($completedCount / $totalCount * 100, 2) : 0;
        
        // 本月安装数
        $currentMonthCount = Installation::where('engineer_id', $engineerId)
            ->whereRaw('MONTH(created_at) = MONTH(CURRENT_DATE())')
            ->whereRaw('YEAR(created_at) = YEAR(CURRENT_DATE())')
            ->count();
        
        // 本月完成数
        $currentMonthCompletedCount = Installation::where('engineer_id', $engineerId)
            ->where('status', 3)
            ->whereRaw('MONTH(created_at) = MONTH(CURRENT_DATE())')
            ->whereRaw('YEAR(created_at) = YEAR(CURRENT_DATE())')
            ->count();
        
        return [
            'total_count' => $totalCount,
            'pending_count' => $pendingCount,
            'installing_count' => $installingCount,
            'completed_count' => $completedCount,
            'cancelled_count' => $cancelledCount,
            'completion_rate' => $completionRate,
            'current_month_count' => $currentMonthCount,
            'current_month_completed_count' => $currentMonthCompletedCount
        ];
    }
    
    /**
     * 获取可用工程师列表
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function available(Request $request)
    {
        $query = InstallationEngineer::where('status', 1); // 只获取状态为1（在职）的工程师
        
        // 区域筛选
        if ($request->has('region') && !empty($request->region)) {
            $query->where('region', $request->region);
        }
        
        $engineers = $query->select('id', 'name', 'phone', 'region')
            ->orderBy('name')
            ->get();
            
        return $this->success($engineers);
    }
    
    /**
     * 同步净水器数据库工程师
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function syncFromWaterDb()
    {
        try {
            // 从净水器数据库获取工程师数据
            $waterMasters = \DB::connection('water_db')
                ->table('wb_master')
                ->select([
                    'id',
                    'master_name',
                    'phone',
                    'province',
                    'city',
                    'area',
                    'address',
                    'status',
                    'remark',
                    'dealer_id',
                    'password',
                    'create_date'
                ])
                ->get();

            $syncCount = 0;
            $skipCount = 0;
            $errorCount = 0;
            $errors = [];

            foreach ($waterMasters as $master) {
                try {
                    // 检查是否已存在（根据手机号）
                    $existingEngineer = InstallationEngineer::where('phone', $master->phone)->first();
                    
                    if ($existingEngineer) {
                        $skipCount++;
                        continue;
                    }

                    // 构建区域信息
                    $region = '';
                    if ($master->province) {
                        $region .= $master->province;
                    }
                    if ($master->city && $master->city !== $master->province) {
                        $region .= $master->city;
                    }
                    if ($master->area && $master->area !== $master->city) {
                        $region .= $master->area;
                    }

                    // 转换状态：E=启用(1), D=禁用(0)
                    $status = ($master->status === 'E') ? 1 : 0;

                    // 创建新工程师
                    InstallationEngineer::create([
                        'name' => $master->master_name ?: '未知工程师',
                        'phone' => $master->phone ?: '',
                        'password' => $master->password ? bcrypt($master->password) : bcrypt('123456'),
                        'region' => $region,
                        'address' => $master->address ?: '',
                        'id_card' => null,
                        'avatar' => null,
                        'status' => $status,
                        'remark' => $master->remark ?: "从净水器系统同步，原ID: {$master->id}",
                    ]);

                    $syncCount++;
                } catch (\Exception $e) {
                    $errorCount++;
                    $errors[] = "工程师 {$master->master_name}({$master->phone}) 同步失败: " . $e->getMessage();
                }
            }

            $result = [
                'total' => $waterMasters->count(),
                'sync_count' => $syncCount,
                'skip_count' => $skipCount,
                'error_count' => $errorCount,
                'errors' => $errors
            ];

            $message = "同步完成！共处理 {$result['total']} 条数据，成功同步 {$syncCount} 条，跳过 {$skipCount} 条，失败 {$errorCount} 条";

            return $this->success($result, $message);
        } catch (\Exception $e) {
            return $this->error('同步失败: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * 更新所有工程师安装数量
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateInstallationCount()
    {
        try {
            // 获取所有工程师
            $engineers = InstallationEngineer::all();
            $updateCount = 0;
            
            foreach ($engineers as $engineer) {
                // 计算该工程师的安装数量（这里假设有installation表）
                // 如果没有installation表，可以从其他地方获取数据
                $installationCount = 0;
                
                // 尝试从installation表获取
                try {
                    $installationCount = \DB::table('installations')
                        ->where('engineer_id', $engineer->id)
                        ->where('status', 3) // 假设3是已完成状态
                        ->count();
                } catch (\Exception $e) {
                    // 如果installation表不存在，设置为0
                    $installationCount = 0;
                }
                
                // 更新工程师的安装数量
                $engineer->update([
                    'completed_installations' => $installationCount
                ]);
                
                $updateCount++;
            }
            
            $result = [
                'updated_engineers' => $updateCount,
                'total_engineers' => $engineers->count()
            ];
            
            return $this->success($result, "成功更新 {$updateCount} 个工程师的安装数量");
        } catch (\Exception $e) {
            return $this->error('更新失败: ' . $e->getMessage(), 500);
        }
    }
    

    
    /**
     * 批量操作工程师
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function batchOperate(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'ids' => 'required|array',
            'ids.*' => 'integer|exists:installation_engineers,id',
            'action' => 'required|string|in:delete,enable,disable',
        ]);

        if ($validator->fails()) {
            return $this->error('验证失败', 422, $validator->errors());
        }
        
        try {
            $ids = $request->ids;
            $action = $request->action;
            
            switch ($action) {
                case 'delete':
                    InstallationEngineer::whereIn('id', $ids)->delete();
                    break;
                case 'enable':
                    InstallationEngineer::whereIn('id', $ids)->update(['status' => 1]);
                    break;
                case 'disable':
                    InstallationEngineer::whereIn('id', $ids)->update(['status' => 0]);
                    break;
            }
            
            return $this->success(null, '批量操作成功');
        } catch (\Exception $e) {
            return $this->error('批量操作失败: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * 导出工程师数据
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function export(Request $request)
    {
        try {
            // 这里需要实现导出功能
            // 暂时返回成功消息
            return $this->success(null, '导出功能开发中');
        } catch (\Exception $e) {
            return $this->error('导出失败: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * 分配工作给工程师
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function assignWork(Request $request, $id)
    {
        $engineer = InstallationEngineer::find($id);
        
        if (!$engineer) {
            return $this->error('工程师不存在', 404);
        }
        
        try {
            // 这里需要实现分配工作的逻辑
            // 暂时返回成功消息
            return $this->success(null, '分配工作功能开发中');
        } catch (\Exception $e) {
            return $this->error('分配工作失败: ' . $e->getMessage(), 500);
        }
    }
}
