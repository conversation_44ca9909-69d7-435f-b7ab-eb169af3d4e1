<?php

namespace App\Http\Controllers\Admin\Api\V1;

use App\Http\Controllers\Controller;
use App\Traits\ApiResponseTrait;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;
use App\Models\Invitation;
use App\Models\InvitationRegistration;

class InvitationController extends Controller
{
    use ApiResponseTrait;

    /**
     * 获取邀请函配置列表
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        $query = Invitation::query();
        
        // 搜索条件
        if ($request->has('keyword') && !empty($request->keyword)) {
            $keyword = $request->keyword;
            $query->where(function($q) use ($keyword) {
                $q->where('title', 'like', "%{$keyword}%")
                  ->orWhere('description', 'like', "%{$keyword}%");
            });
        }
        
        // 状态筛选
        if ($request->has('status') && $request->status !== '') {
            $query->where('status', $request->status);
        }
        
        // 排序
        $orderBy = $request->input('order_by', 'created_at');
        $orderDir = $request->input('order_dir', 'desc');
        $query->orderBy($orderBy, $orderDir);
        
        // 分页
        $perPage = $request->input('per_page', 15);
        $invitations = $query->paginate($perPage);
        
        return $this->paginate($invitations);
    }

    /**
     * 创建邀请函配置
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:100',
            'description' => 'nullable|string',
            'cover_image' => 'required|string|max:200',
            'background_image' => 'nullable|string|max:200',
            'content' => 'required|string',
            'start_time' => 'required|date',
            'end_time' => 'required|date|after:start_time',
            'location' => 'required|string|max:200',
            'contact_person' => 'nullable|string|max:50',
            'contact_phone' => 'nullable|string|max:20',
            'form_fields' => 'nullable|array',
            'max_registrations' => 'nullable|integer',
            'status' => 'required|integer|in:0,1,2',
            'is_public' => 'required|boolean',
            'qr_code' => 'nullable|string|max:200',
            'share_image' => 'nullable|string|max:200',
        ]);

        if ($validator->fails()) {
            return $this->error('验证失败', 422, $validator->errors());
        }

        try {
            $invitation = new Invitation();
            $invitation->title = $request->title;
            $invitation->description = $request->description;
            $invitation->cover_image = $request->cover_image;
            $invitation->background_image = $request->background_image;
            $invitation->content = $request->content;
            $invitation->start_time = $request->start_time;
            $invitation->end_time = $request->end_time;
            $invitation->location = $request->location;
            $invitation->contact_person = $request->contact_person;
            $invitation->contact_phone = $request->contact_phone;
            $invitation->form_fields = $request->form_fields ? json_encode($request->form_fields) : null;
            $invitation->max_registrations = $request->max_registrations;
            $invitation->status = $request->status;
            $invitation->is_public = $request->is_public;
            $invitation->qr_code = $request->qr_code;
            $invitation->share_image = $request->share_image;
            $invitation->save();
            
            return $this->success($invitation, '邀请函创建成功');
        } catch (\Exception $e) {
            return $this->error('邀请函创建失败: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 获取单个邀请函详情
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        $invitation = Invitation::find($id);
        
        if (!$invitation) {
            return $this->error('邀请函不存在', 404);
        }
        
        // 处理表单字段数据
        if ($invitation->form_fields) {
            $invitation->form_fields = json_decode($invitation->form_fields, true);
        }
        
        // 获取报名统计数据
        $registrationCount = InvitationRegistration::where('invitation_id', $id)->count();
        $invitation->registration_count = $registrationCount;
        
        return $this->success($invitation);
    }

    /**
     * 更新邀请函配置
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id)
    {
        $invitation = Invitation::find($id);
        
        if (!$invitation) {
            return $this->error('邀请函不存在', 404);
        }
        
        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:100',
            'description' => 'nullable|string',
            'cover_image' => 'required|string|max:200',
            'background_image' => 'nullable|string|max:200',
            'content' => 'required|string',
            'start_time' => 'required|date',
            'end_time' => 'required|date|after:start_time',
            'location' => 'required|string|max:200',
            'contact_person' => 'nullable|string|max:50',
            'contact_phone' => 'nullable|string|max:20',
            'form_fields' => 'nullable|array',
            'max_registrations' => 'nullable|integer',
            'status' => 'required|integer|in:0,1,2',
            'is_public' => 'required|boolean',
            'qr_code' => 'nullable|string|max:200',
            'share_image' => 'nullable|string|max:200',
        ]);

        if ($validator->fails()) {
            return $this->error('验证失败', 422, $validator->errors());
        }
        
        try {
            $invitation->title = $request->title;
            $invitation->description = $request->description;
            $invitation->cover_image = $request->cover_image;
            $invitation->background_image = $request->background_image;
            $invitation->content = $request->content;
            $invitation->start_time = $request->start_time;
            $invitation->end_time = $request->end_time;
            $invitation->location = $request->location;
            $invitation->contact_person = $request->contact_person;
            $invitation->contact_phone = $request->contact_phone;
            $invitation->form_fields = $request->form_fields ? json_encode($request->form_fields) : null;
            $invitation->max_registrations = $request->max_registrations;
            $invitation->status = $request->status;
            $invitation->is_public = $request->is_public;
            $invitation->qr_code = $request->qr_code;
            $invitation->share_image = $request->share_image;
            $invitation->save();
            
            // 处理表单字段数据
            if ($invitation->form_fields) {
                $invitation->form_fields = json_decode($invitation->form_fields, true);
            }
            
            return $this->success($invitation, '邀请函更新成功');
        } catch (\Exception $e) {
            return $this->error('邀请函更新失败: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 删除邀请函配置
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy($id)
    {
        $invitation = Invitation::find($id);
        
        if (!$invitation) {
            return $this->error('邀请函不存在', 404);
        }
        
        // 检查是否有关联的报名记录
        $registrationCount = InvitationRegistration::where('invitation_id', $id)->count();
        if ($registrationCount > 0) {
            return $this->error('该邀请函有关联的报名记录，无法删除', 400);
        }
        
        try {
            $invitation->delete();
            
            return $this->success(null, '邀请函删除成功');
        } catch (\Exception $e) {
            return $this->error('邀请函删除失败: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * 更新邀请函状态
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateStatus(Request $request, $id)
    {
        $invitation = Invitation::find($id);
        
        if (!$invitation) {
            return $this->error('邀请函不存在', 404);
        }
        
        $validator = Validator::make($request->all(), [
            'status' => 'required|integer|in:0,1,2',
        ]);

        if ($validator->fails()) {
            return $this->error('验证失败', 422, $validator->errors());
        }
        
        try {
            $invitation->status = $request->status;
            $invitation->save();
            
            return $this->success($invitation, '邀请函状态更新成功');
        } catch (\Exception $e) {
            return $this->error('邀请函状态更新失败: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * 获取邀请函统计数据
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function statistics($id)
    {
        $invitation = Invitation::find($id);
        
        if (!$invitation) {
            return $this->error('邀请函不存在', 404);
        }
        
        // 报名总数
        $totalCount = InvitationRegistration::where('invitation_id', $id)->count();
        
        // 每日报名趋势
        $dailyTrend = InvitationRegistration::where('invitation_id', $id)
            ->selectRaw('DATE(created_at) as date, COUNT(*) as count')
            ->groupBy('date')
            ->orderBy('date', 'asc')
            ->get();
        
        // 表单字段统计
        $formFieldStats = [];
        
        // 如果有表单字段配置
        if ($invitation->form_fields) {
            $formFields = json_decode($invitation->form_fields, true);
            
            foreach ($formFields as $field) {
                if ($field['type'] == 'select' || $field['type'] == 'radio') {
                    $fieldName = $field['name'];
                    
                    // 获取该字段的选项分布
                    $fieldStats = InvitationRegistration::where('invitation_id', $id)
                        ->whereNotNull('form_data->'.$fieldName)
                        ->selectRaw('JSON_UNQUOTE(form_data->"$.'.$fieldName.'") as value, COUNT(*) as count')
                        ->groupBy('value')
                        ->orderByRaw('COUNT(*) DESC')
                        ->get();
                    
                    $formFieldStats[$fieldName] = [
                        'label' => $field['label'],
                        'stats' => $fieldStats
                    ];
                }
            }
        }
        
        $data = [
            'total_count' => $totalCount,
            'daily_trend' => $dailyTrend,
            'form_field_stats' => $formFieldStats
        ];
        
        return $this->success($data);
    }
}
