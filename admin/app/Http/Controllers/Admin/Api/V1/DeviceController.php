<?php

namespace App\Http\Controllers\Admin\Api\V1;

use App\Http\Controllers\Controller;
use App\Traits\ApiResponseTrait;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;
use App\Models\WaterDevice;
use App\Models\DeviceLog;

class DeviceController extends Controller
{
    use ApiResponseTrait;

    /**
     * 获取设备列表
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        try {
            $query = WaterDevice::query();
            
            // 🔥 重要：只显示已激活的设备
            // 激活条件：activate_date不为空且不为'0000-00-00 00:00:00'
            $query->whereNotNull('activate_date')
                  ->where('activate_date', '!=', '')
                  ->where('activate_date', '!=', '0000-00-00 00:00:00');
            
            // 预加载关联数据，提高查询效率
            $query->with(['client', 'dealer', 'saleDealer']);
        
            // 搜索条件 - 根据数据库字段优化
        if ($request->has('keyword') && !empty($request->keyword)) {
            $keyword = $request->keyword;
            $query->where(function($q) use ($keyword) {
                    $q->where('device_number', 'like', "%{$keyword}%")  // 设备编号
                      ->orWhere('imei', 'like', "%{$keyword}%")          // 芯片编码
                      ->orWhere('address', 'like', "%{$keyword}%")       // 安装地址
                      ->orWhere('iccid', 'like', "%{$keyword}%")         // SIM卡号
                      ->orWhereHas('client', function($clientQuery) use ($keyword) {
                          $clientQuery->where('name', 'like', "%{$keyword}%")
                                     ->orWhere('wx_nickname', 'like', "%{$keyword}%")
                                     ->orWhere('phone', 'like', "%{$keyword}%");
                      })
                      ->orWhereHas('dealer', function($dealerQuery) use ($keyword) {
                          $dealerQuery->where('dealer_name', 'like', "%{$keyword}%")
                                     ->orWhere('dealer_number', 'like', "%{$keyword}%");
                      });
            });
        }
        
        // 设备类型筛选
        if ($request->has('device_type') && !empty($request->device_type)) {
            $query->where('device_type', $request->device_type);
        }
        
        // 状态筛选
        if ($request->has('status') && $request->status !== '') {
            $query->where('status', $request->status);
        }
        
            // 网络状态筛选（1在线0离线）
            if ($request->has('network_status') && $request->network_status !== '') {
                $query->where('network_status', $request->network_status);
            }
            
            // 计费模式筛选（1流量0包年）
            if ($request->has('billing_mode') && $request->billing_mode !== '') {
                $query->where('billing_mode', $request->billing_mode);
        }
        
            // 客户ID筛选
            if ($request->has('client_id') && !empty($request->client_id)) {
                $query->where('client_id', $request->client_id);
            }
            
            // 渠道商ID筛选
            if ($request->has('dealer_id') && !empty($request->dealer_id)) {
                $query->where('dealer_id', $request->dealer_id);
        }
        
            // 销售渠道商ID筛选
            if ($request->has('dealer_id_sale') && !empty($request->dealer_id_sale)) {
                $query->where('dealer_id_sale', $request->dealer_id_sale);
            }
            
            // 剩余水量筛选（低水量预警）
            if ($request->has('low_water_alert') && $request->low_water_alert == '1') {
                $query->where('surplus_flow', '<', 50); // 剩余水量少于50L
            }
            
            // 剩余天数筛选（即将到期预警）
            if ($request->has('expire_alert') && $request->expire_alert == '1') {
                $query->where('remaining_days', '<', 30); // 剩余天数少于30天
            }
            
            // 滤芯预警筛选（滤芯使用量超过90%）
            if ($request->has('filter_alert') && $request->filter_alert == '1') {
                $query->where(function($q) {
                    $q->whereRaw('(f1_flux / NULLIF(f1_flux_max, 0)) * 100 >= 90')
                      ->orWhereRaw('(f2_flux / NULLIF(f2_flux_max, 0)) * 100 >= 90')
                      ->orWhereRaw('(f3_flux / NULLIF(f3_flux_max, 0)) * 100 >= 90')
                      ->orWhereRaw('(f4_flux / NULLIF(f4_flux_max, 0)) * 100 >= 90')
                      ->orWhereRaw('(f5_flux / NULLIF(f5_flux_max, 0)) * 100 >= 90');
                });
            }
            
            // 地理位置筛选（按经纬度范围）
            if ($request->has('longitude_min') && $request->has('longitude_max')) {
                $query->whereBetween('longitude', [$request->longitude_min, $request->longitude_max]);
            }
            if ($request->has('latitude_min') && $request->has('latitude_max')) {
                $query->whereBetween('latitude', [$request->latitude_min, $request->latitude_max]);
            }
            
            // 激活时间范围筛选
            if ($request->has('activate_start') && !empty($request->activate_start)) {
                $query->where('activate_date', '>=', $request->activate_start);
            }
            if ($request->has('activate_end') && !empty($request->activate_end)) {
                $query->where('activate_date', '<=', $request->activate_end . ' 23:59:59');
        }
        
            // 排序：优先按激活时间倒序，最近激活的排在前面
            $orderBy = $request->input('order_by', 'activate_date');
        $orderDir = $request->input('order_dir', 'desc');
            
            // 支持多种排序字段
            $allowedOrderFields = [
                'activate_date',        // 激活时间
                'filter_date',          // 数据更新时间
                'surplus_flow',         // 剩余水量
                'remaining_days',       // 剩余天数
                'cumulative_filtration_flow', // 累计过滤水量
                'network_status',       // 网络状态
                'device_number'         // 设备编号
            ];
            
            if (in_array($orderBy, $allowedOrderFields)) {
        $query->orderBy($orderBy, $orderDir);
            } else {
                $query->orderBy('activate_date', 'desc');
            }
        
        // 分页
        $perPage = $request->input('per_page', 15);
            $page = $request->input('page', 1);
            
            // 获取分页数据
            $devices = $query->paginate($perPage, ['*'], 'page', $page);
        
            // 格式化返回数据，增加计算字段和状态文本
            $formattedDevices = $devices->getCollection()->map(function ($device) {
                return [
                    // 基础设备信息
                    'id' => $device->id,
                    'device_number' => $device->device_number,
                    'device_type' => $device->device_type,
                    'device_status' => $device->device_status,
                    'imei' => $device->imei,
                    'iccid' => $device->iccid,
                    'address' => $device->address,
                    'longitude' => $device->longitude,
                    'latitude' => $device->latitude,
                    
                    // 水质和流量信息
                    'raw_water_value' => $device->raw_water_value,              // 原水值
                    'purification_water_value' => $device->purification_water_value, // 净水值
                    'tds_in' => $device->tds_in,                                // 进水TDS
                    'tds_out' => $device->tds_out,                              // 出水TDS
                    'cumulative_filtration_flow' => $device->cumulative_filtration_flow, // 累计过滤水量L
                    'surplus_flow' => $device->surplus_flow,                    // 剩余水量L
                    'water_quality_grade' => $device->water_quality_grade,      // 水质等级
                    
                    // 计费和服务信息
                    'billing_mode' => $device->billing_mode,                    // 1流量0包年
                    'billing_mode_text' => $device->billing_mode == '1' ? '流量计费' : '包年计费',
                    'remaining_days' => $device->remaining_days,                // 剩余天数
                    'service_end_time' => $device->service_end_time,            // 服务到期时间
                    'cash_pledge' => $device->cash_pledge,                      // 押金
                    
                    // 状态信息
                    'network_status' => $device->network_status,                // 1在线0离线
                    'network_status_text' => $device->network_status == '1' ? '在线' : '离线',
                    'status' => $device->status,
                    'status_text' => $device->status === 'E' ? '启用' : '禁用',
                    'bind_status' => $device->bind_status,
                    'bind_status_text' => $device->bind_status == '1' ? '已绑定' : '未绑定',
                    
                    // 时间信息
                    'activate_date' => $device->activate_date,                  // 激活时间
                    'filter_date' => $device->filter_date,                      // 数据更新时间
                    'create_date' => $device->create_date,
                    'update_date' => $device->update_date,
                    
                    // 关联信息
                    'client_id' => $device->client_id,
                    'client_name' => $device->client ? $device->client->name : '未关联客户',
                    'client_phone' => $device->client ? $device->client->phone : '',
                    'client_wx_nickname' => $device->client ? $device->client->wx_nickname : '',
                    'client_device_name' => $device->client ? $device->client->client_device_name : '',
                    
                    'dealer_id' => $device->dealer_id,
                    'dealer_name' => $device->dealer ? $device->dealer->dealer_name : '未关联渠道商',
                    'dealer_number' => $device->dealer ? $device->dealer->dealer_number : '',
                    
                    'dealer_id_sale' => $device->dealer_id_sale,               // 销售渠道商id
                    'sale_dealer_name' => $device->saleDealer ? $device->saleDealer->dealer_name : '未关联销售商',
                    
                    // 计算字段和预警状态
                    'water_level_percentage' => $device->surplus_flow > 0 ? 
                        min(100, ($device->surplus_flow / 1000) * 100) : 0,    // 水量百分比
                    'days_percentage' => $device->remaining_days > 0 ? 
                        min(100, ($device->remaining_days / 365) * 100) : 0,   // 剩余天数百分比
                    'is_low_water' => $device->surplus_flow < 50,               // 低水量预警
                    'is_expire_soon' => $device->remaining_days < 30,           // 即将到期预警
                    'is_online' => $device->network_status == '1',              // 是否在线
                    
                    // 滤芯信息
                    'f1_flux' => $device->f1_flux,                             // PP棉滤芯使用量
                    'f1_flux_max' => $device->f1_flux_max,                     // PP棉滤芯最大值
                    'f1_life_percent' => $device->f1_life_percent,             // PP棉滤芯寿命百分比
                    'f2_flux' => $device->f2_flux,                             // 活性炭滤芯使用量
                    'f2_flux_max' => $device->f2_flux_max,                     // 活性炭滤芯最大值
                    'f2_life_percent' => $device->f2_life_percent,             // 活性炭滤芯寿命百分比
                    'f3_flux' => $device->f3_flux,                             // RO反渗透滤芯使用量
                    'f3_flux_max' => $device->f3_flux_max,                     // RO反渗透滤芯最大值
                    'f3_life_percent' => $device->f3_life_percent,             // RO反渗透滤芯寿命百分比
                    'f4_flux' => $device->f4_flux,                             // 第四级滤芯使用量
                    'f4_flux_max' => $device->f4_flux_max,                     // 第四级滤芯最大值
                    'f4_life_percent' => $device->f4_life_percent,             // 第四级滤芯寿命百分比
                    'f5_flux' => $device->f5_flux,                             // 第五级滤芯使用量
                    'f5_flux_max' => $device->f5_flux_max,                     // 第五级滤芯最大值
                    'f5_life_percent' => $device->f5_life_percent,             // 第五级滤芯寿命百分比
                    'filter_alert' => $device->filter_alert,                   // 滤芯预警详情
                    'has_filter_alert' => $device->has_filter_alert,           // 是否有滤芯预警
                    
                    // 其他信息
                    'remark' => $device->remark,
                    'product_id' => $device->product_id,
                ];
            });
            
            // 格式化返回数据，与原生PHP API保持一致
            $formattedData = [
                'data' => $formattedDevices,
                'total' => $devices->total(),
                'current_page' => $devices->currentPage(),
                'per_page' => $devices->perPage(),
                'last_page' => $devices->lastPage(),
                
                // 添加统计信息
                'statistics' => [
                    'total_devices' => $devices->total(),
                    'online_devices' => $formattedDevices->where('is_online', true)->count(),
                    'offline_devices' => $formattedDevices->where('is_online', false)->count(),
                    'low_water_devices' => $formattedDevices->where('is_low_water', true)->count(),
                    'expire_soon_devices' => $formattedDevices->where('is_expire_soon', true)->count(),
                    'filter_alert_devices' => $formattedDevices->where('has_filter_alert', true)->count(),
                    'flow_billing_devices' => $formattedDevices->where('billing_mode', '1')->count(),
                    'annual_billing_devices' => $formattedDevices->where('billing_mode', '0')->count(),
                ]
            ];
            
            return $this->success($formattedData, '获取成功');
        } catch (\Exception $e) {
            return $this->error('获取设备列表失败: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 创建设备
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'device_code' => 'required|string|max:50|unique:devices,device_code',
            'device_name' => 'required|string|max:100',
            'device_sn' => 'required|string|max:50|unique:devices,device_sn',
            'device_type' => 'required|string|max:50',
            'model' => 'nullable|string|max:50',
            'firmware_version' => 'nullable|string|max:50',
            'hardware_version' => 'nullable|string|max:50',
            'user_id' => 'nullable|integer',
            'merchant_id' => 'nullable|integer',
            'status' => 'required|in:0,1,2',
            'remark' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return $this->error('验证失败', 422, $validator->errors());
        }

        $device = new WaterDevice();
        $device->device_code = $request->device_code;
        $device->device_name = $request->device_name;
        $device->device_sn = $request->device_sn;
        $device->device_type = $request->device_type;
        $device->model = $request->model;
        $device->firmware_version = $request->firmware_version;
        $device->hardware_version = $request->hardware_version;
        $device->user_id = $request->user_id;
        $device->merchant_id = $request->merchant_id;
        $device->status = $request->status;
        $device->is_online = 0;
        $device->remark = $request->remark;
        $device->save();
        
        // 记录设备创建日志
        $this->logDeviceOperation($device->id, '创建设备', auth()->id(), $request->remark ?? '');
        
        return $this->success($device, '设备创建成功');
    }

    /**
     * 获取单个设备详情
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        $device = WaterDevice::with(['user', 'merchant'])->find($id);
        
        if (!$device) {
            return $this->error('设备不存在', 404);
        }
        
        return $this->success($device);
    }

    /**
     * 更新设备
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id)
    {
        $device = WaterDevice::find($id);
        
        if (!$device) {
            return $this->error('设备不存在', 404);
        }
        
        $validator = Validator::make($request->all(), [
            'device_code' => 'required|string|max:50|unique:devices,device_code,'.$id,
            'device_name' => 'required|string|max:100',
            'device_sn' => 'required|string|max:50|unique:devices,device_sn,'.$id,
            'device_type' => 'required|string|max:50',
            'model' => 'nullable|string|max:50',
            'firmware_version' => 'nullable|string|max:50',
            'hardware_version' => 'nullable|string|max:50',
            'user_id' => 'nullable|integer',
            'merchant_id' => 'nullable|integer',
            'status' => 'required|in:0,1,2',
            'remark' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return $this->error('验证失败', 422, $validator->errors());
        }
        
        $device->device_code = $request->device_code;
        $device->device_name = $request->device_name;
        $device->device_sn = $request->device_sn;
        $device->device_type = $request->device_type;
        $device->model = $request->model;
        $device->firmware_version = $request->firmware_version;
        $device->hardware_version = $request->hardware_version;
        $device->user_id = $request->user_id;
        $device->merchant_id = $request->merchant_id;
        $device->status = $request->status;
        $device->remark = $request->remark;
        $device->save();
        
        // 记录设备更新日志
        $this->logDeviceOperation($device->id, '更新设备', auth()->id(), $request->remark ?? '');
        
        return $this->success($device, '设备更新成功');
    }

    /**
     * 删除设备
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy($id)
    {
        $device = WaterDevice::find($id);
        
        if (!$device) {
            return $this->error('设备不存在', 404);
        }
        
        // 检查设备是否可以删除
        if ($device->status == 1) {
            return $this->error('设备处于使用中状态，无法删除', 400);
        }
        
        // 记录设备删除日志
        $this->logDeviceOperation($device->id, '删除设备', auth()->id(), '管理员删除');
        
        $device->delete();
        
        return $this->success(null, '设备删除成功');
    }
    
    /**
     * 获取设备状态
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function status($id)
    {
        $device = WaterDevice::find($id);
        
        if (!$device) {
            return $this->error('设备不存在', 404);
        }
        
        // 获取设备最新状态数据
        $statusData = DB::table('device_status')
            ->where('device_id', $id)
            ->orderBy('created_at', 'desc')
            ->first();
        
        // 获取设备最近7天的运行数据
        $sevenDaysAgo = date('Y-m-d H:i:s', strtotime('-7 days'));
        $runningData = DB::table('device_running_data')
            ->where('device_id', $id)
            ->where('created_at', '>=', $sevenDaysAgo)
            ->orderBy('created_at', 'asc')
            ->get();
        
        // 获取设备最近10条报警记录
        $alarmLogs = DB::table('device_alarm_logs')
            ->where('device_id', $id)
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get();
        
        $data = [
            'device' => $device,
            'status' => $statusData,
            'running_data' => $runningData,
            'alarm_logs' => $alarmLogs,
        ];
        
        return $this->success($data);
    }
    
    /**
     * 绑定设备到用户
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function bindUser(Request $request, $id)
    {
        $device = WaterDevice::find($id);
        
        if (!$device) {
            return $this->error('设备不存在', 404);
        }
        
        $validator = Validator::make($request->all(), [
            'user_id' => 'required|integer|exists:users,id',
            'remark' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return $this->error('验证失败', 422, $validator->errors());
        }
        
        // 检查设备是否已绑定
        if ($device->user_id && $device->user_id != $request->user_id) {
            return $this->error('设备已绑定其他用户，请先解绑', 400);
        }
        
        $device->user_id = $request->user_id;
        $device->status = 1; // 设置为使用中
        $device->save();
        
        // 记录设备绑定日志
        $this->logDeviceOperation($device->id, '绑定用户', auth()->id(), $request->remark ?? '');
        
        return $this->success($device, '设备绑定成功');
    }
    
    /**
     * 解绑设备
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function unbindUser(Request $request, $id)
    {
        $device = WaterDevice::find($id);
        
        if (!$device) {
            return $this->error('设备不存在', 404);
        }
        
        $validator = Validator::make($request->all(), [
            'remark' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return $this->error('验证失败', 422, $validator->errors());
        }
        
        // 检查设备是否已绑定
        if (!$device->user_id) {
            return $this->error('设备未绑定用户', 400);
        }
        
        $oldUserId = $device->user_id;
        $device->user_id = null;
        $device->status = 0; // 设置为闲置
        $device->save();
        
        // 记录设备解绑日志
        $this->logDeviceOperation($device->id, '解绑用户', auth()->id(), $request->remark ?? '');
        
        return $this->success($device, '设备解绑成功');
    }
    
    /**
     * 远程控制设备
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function control(Request $request, $id)
    {
        $device = WaterDevice::find($id);
        
        if (!$device) {
            return $this->error('设备不存在', 404);
        }
        
        // 检查设备是否在线
        if ($device->is_online != 1) {
            return $this->error('设备不在线，无法控制', 400);
        }
        
        $validator = Validator::make($request->all(), [
            'command' => 'required|string',
            'params' => 'nullable|array',
            'remark' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return $this->error('验证失败', 422, $validator->errors());
        }
        
        // 发送控制命令到设备
        // 这里需要根据实际情况实现设备控制逻辑
        $result = $this->sendCommandToDevice($device, $request->command, $request->params ?? []);
        
        if (!$result['success']) {
            return $this->error($result['message'], 500);
        }
        
        // 记录设备控制日志
        $this->logDeviceOperation(
            $device->id, 
            '远程控制', 
            auth()->id(), 
            '命令: ' . $request->command . ', 参数: ' . json_encode($request->params ?? []) . ', 备注: ' . ($request->remark ?? '')
        );
        
        return $this->success($result['data'], '控制命令发送成功');
    }
    
    /**
     * 获取设备操作日志
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function logs(Request $request, $id)
    {
        $device = WaterDevice::find($id);
        
        if (!$device) {
            return $this->error('设备不存在', 404);
        }
        
        $query = DeviceLog::where('device_id', $id);
        
        // 操作类型筛选
        if ($request->has('operation_type') && !empty($request->operation_type)) {
            $query->where('operation_type', $request->operation_type);
        }
        
        // 时间范围筛选
        if ($request->has('start_time') && !empty($request->start_time)) {
            $query->where('created_at', '>=', $request->start_time);
        }
        
        if ($request->has('end_time') && !empty($request->end_time)) {
            $query->where('created_at', '<=', $request->end_time . ' 23:59:59');
        }
        
        // 排序
        $query->orderBy('created_at', 'desc');
        
        // 分页
        $perPage = $request->input('per_page', 15);
        $logs = $query->paginate($perPage);
        
        // 加载关联数据
        $logs->load(['user', 'admin']);
        
        return $this->paginate($logs);
    }
    
    /**
     * 获取设备类型列表
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function deviceTypes()
    {
        $types = WaterDevice::select('device_type')
            ->distinct()
            ->pluck('device_type');
            
        return $this->success($types);
    }
    
    /**
     * 记录设备操作日志
     *
     * @param int $deviceId
     * @param string $operationType
     * @param int $adminId
     * @param string $remark
     * @return void
     */
    private function logDeviceOperation($deviceId, $operationType, $adminId, $remark = '')
    {
        if (class_exists('App\Models\DeviceLog')) {
            DeviceLog::create([
                'device_id' => $deviceId,
                'operation_type' => $operationType,
                'admin_id' => $adminId,
                'remark' => $remark,
            ]);
        }
    }
    
    /**
     * 发送命令到设备
     * 
     * 注意：这是一个示例方法，需要根据实际情况实现
     *
     * @param WaterDevice $device
     * @param string $command
     * @param array $params
     * @return array
     */
    private function sendCommandToDevice($device, $command, $params = [])
    {
        // 这里需要根据实际情况实现设备控制逻辑
        // 例如通过MQTT、WebSocket等方式发送命令到设备
        
        // 模拟发送命令
        try {
            // 记录命令
            DB::table('device_commands')->insert([
                'device_id' => $device->id,
                'command' => $command,
                'params' => json_encode($params),
                'status' => 0, // 待处理
                'created_at' => now(),
                'updated_at' => now(),
            ]);
            
            return [
                'success' => true,
                'message' => '命令发送成功',
                'data' => [
                    'command_id' => DB::getPdo()->lastInsertId(),
                    'device_id' => $device->id,
                    'command' => $command,
                    'params' => $params,
                    'send_time' => now()->toDateTimeString(),
                ]
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => '命令发送失败: ' . $e->getMessage(),
                'data' => null
            ];
        }
    }
}
