<?php

namespace App\Http\Controllers\Admin\Api\V1;

use App\Http\Controllers\Controller;
use App\Traits\ApiResponseTrait;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Product;

class OrderController extends Controller
{
    use ApiResponseTrait;

    /**
     * 获取订单列表
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        $query = Order::query();
        
        // 搜索条件
        if ($request->has('keyword') && !empty($request->keyword)) {
            $keyword = $request->keyword;
            $query->where(function($q) use ($keyword) {
                $q->where('order_no', 'like', "%{$keyword}%")
                  ->orWhere('receiver_name', 'like', "%{$keyword}%")
                  ->orWhere('receiver_phone', 'like', "%{$keyword}%");
            });
        }
        
        // 订单状态筛选
        if ($request->has('status') && $request->status !== '') {
            $query->where('status', $request->status);
        }
        
        // 支付状态筛选
        if ($request->has('pay_status') && $request->pay_status !== '') {
            $query->where('pay_status', $request->pay_status);
        }
        
        // 配送状态筛选
        if ($request->has('shipping_status') && $request->shipping_status !== '') {
            $query->where('shipping_status', $request->shipping_status);
        }
        
        // 用户ID筛选
        if ($request->has('user_id') && !empty($request->user_id)) {
            $query->where('user_id', $request->user_id);
        }
        
        // 时间范围筛选
        if ($request->has('start_time') && !empty($request->start_time)) {
            $query->where('created_at', '>=', $request->start_time);
        }
        
        if ($request->has('end_time') && !empty($request->end_time)) {
            $query->where('created_at', '<=', $request->end_time . ' 23:59:59');
        }
        
        // 排序
        $orderBy = $request->input('order_by', 'created_at');
        $orderDir = $request->input('order_dir', 'desc');
        $query->orderBy($orderBy, $orderDir);
        
        // 分页
        $perPage = $request->input('per_page', 15);
        $orders = $query->paginate($perPage);
        
        // 加载关联数据
        $orders->load(['user', 'items.product']);
        
        return $this->paginate($orders);
    }

    /**
     * 获取单个订单详情
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        $order = Order::with(['user', 'items.product'])->find($id);
        
        if (!$order) {
            return $this->error('订单不存在', 404);
        }
        
        return $this->success($order);
    }

    /**
     * 更新订单状态
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateStatus(Request $request, $id)
    {
        $order = Order::find($id);
        
        if (!$order) {
            return $this->error('订单不存在', 404);
        }
        
        $validator = Validator::make($request->all(), [
            'status' => 'required|in:0,1,2,3,4,5',
            'remark' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return $this->error('验证失败', 422, $validator->errors());
        }
        
        $order->status = $request->status;
        
        if ($request->has('remark')) {
            $order->admin_remark = $request->remark;
        }
        
        $order->save();
        
        // 记录订单状态变更日志
        $this->logOrderStatusChange($order, $request->status, auth()->id(), $request->remark ?? '');
        
        return $this->success($order, '订单状态更新成功');
    }

    /**
     * 更新订单支付状态
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function updatePayStatus(Request $request, $id)
    {
        $order = Order::find($id);
        
        if (!$order) {
            return $this->error('订单不存在', 404);
        }
        
        $validator = Validator::make($request->all(), [
            'pay_status' => 'required|in:0,1',
            'pay_time' => 'nullable|date',
            'transaction_id' => 'nullable|string',
            'remark' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return $this->error('验证失败', 422, $validator->errors());
        }
        
        $order->pay_status = $request->pay_status;
        
        if ($request->pay_status == 1) {
            $order->pay_time = $request->pay_time ?? now();
            $order->transaction_id = $request->transaction_id;
        }
        
        if ($request->has('remark')) {
            $order->admin_remark = $request->remark;
        }
        
        $order->save();
        
        // 记录订单支付状态变更日志
        $this->logOrderPayStatusChange($order, $request->pay_status, auth()->id(), $request->remark ?? '');
        
        return $this->success($order, '支付状态更新成功');
    }

    /**
     * 更新订单配送状态
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateShippingStatus(Request $request, $id)
    {
        $order = Order::find($id);
        
        if (!$order) {
            return $this->error('订单不存在', 404);
        }
        
        $validator = Validator::make($request->all(), [
            'shipping_status' => 'required|in:0,1,2,3',
            'shipping_company' => 'nullable|string',
            'shipping_no' => 'nullable|string',
            'remark' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return $this->error('验证失败', 422, $validator->errors());
        }
        
        $order->shipping_status = $request->shipping_status;
        
        if ($request->shipping_status == 1) {
            $order->shipping_time = now();
            $order->shipping_company = $request->shipping_company;
            $order->shipping_no = $request->shipping_no;
        }
        
        if ($request->has('remark')) {
            $order->admin_remark = $request->remark;
        }
        
        $order->save();
        
        // 记录订单配送状态变更日志
        $this->logOrderShippingStatusChange($order, $request->shipping_status, auth()->id(), $request->remark ?? '');
        
        return $this->success($order, '配送状态更新成功');
    }

    /**
     * 订单退款
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function refund(Request $request, $id)
    {
        $order = Order::find($id);
        
        if (!$order) {
            return $this->error('订单不存在', 404);
        }
        
        // 检查订单是否已支付
        if ($order->pay_status != 1) {
            return $this->error('订单未支付，无法退款', 400);
        }
        
        $validator = Validator::make($request->all(), [
            'refund_amount' => 'required|numeric|min:0.01|max:' . $order->total_amount,
            'refund_reason' => 'required|string',
            'remark' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return $this->error('验证失败', 422, $validator->errors());
        }
        
        try {
            DB::beginTransaction();
            
            // 更新订单状态
            $order->status = 5; // 已退款
            $order->refund_status = 2; // 已退款
            $order->refund_time = now();
            $order->refund_amount = $request->refund_amount;
            $order->refund_reason = $request->refund_reason;
            
            if ($request->has('remark')) {
                $order->admin_remark = $request->remark;
            }
            
            $order->save();
            
            // 记录订单退款日志
            $this->logOrderRefund($order, $request->refund_amount, auth()->id(), $request->refund_reason);
            
            // 恢复商品库存
            foreach ($order->items as $item) {
                $product = Product::find($item->product_id);
                if ($product) {
                    $product->stock += $item->quantity;
                    $product->save();
                }
            }
            
            DB::commit();
            
            return $this->success($order, '订单退款成功');
        } catch (\Exception $e) {
            DB::rollBack();
            return $this->error('退款失败: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * 获取订单统计数据
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function statistics(Request $request)
    {
        // 时间范围筛选
        $startTime = $request->input('start_time', date('Y-m-d', strtotime('-30 days')));
        $endTime = $request->input('end_time', date('Y-m-d'));
        
        // 订单总数
        $totalOrders = Order::whereBetween('created_at', [$startTime, $endTime . ' 23:59:59'])->count();
        
        // 已支付订单数
        $paidOrders = Order::where('pay_status', 1)
            ->whereBetween('created_at', [$startTime, $endTime . ' 23:59:59'])
            ->count();
        
        // 订单总金额
        $totalAmount = Order::where('pay_status', 1)
            ->whereBetween('created_at', [$startTime, $endTime . ' 23:59:59'])
            ->sum('total_amount');
        
        // 待发货订单数
        $pendingShipOrders = Order::where('pay_status', 1)
            ->where('shipping_status', 0)
            ->where('status', 1)
            ->count();
        
        // 已发货订单数
        $shippedOrders = Order::where('shipping_status', 1)
            ->whereBetween('created_at', [$startTime, $endTime . ' 23:59:59'])
            ->count();
        
        // 已完成订单数
        $completedOrders = Order::where('status', 3)
            ->whereBetween('created_at', [$startTime, $endTime . ' 23:59:59'])
            ->count();
        
        // 已退款订单数
        $refundedOrders = Order::where('refund_status', 2)
            ->whereBetween('created_at', [$startTime, $endTime . ' 23:59:59'])
            ->count();
        
        // 退款金额
        $refundAmount = Order::where('refund_status', 2)
            ->whereBetween('created_at', [$startTime, $endTime . ' 23:59:59'])
            ->sum('refund_amount');
        
        $data = [
            'total_orders' => $totalOrders,
            'paid_orders' => $paidOrders,
            'total_amount' => $totalAmount,
            'pending_ship_orders' => $pendingShipOrders,
            'shipped_orders' => $shippedOrders,
            'completed_orders' => $completedOrders,
            'refunded_orders' => $refundedOrders,
            'refund_amount' => $refundAmount,
        ];
        
        return $this->success($data);
    }
    
    /**
     * 记录订单状态变更日志
     *
     * @param Order $order
     * @param int $status
     * @param int $adminId
     * @param string $remark
     * @return void
     */
    private function logOrderStatusChange($order, $status, $adminId, $remark = '')
    {
        if (class_exists('App\Models\OrderLog')) {
            \App\Models\OrderLog::create([
                'order_id' => $order->id,
                'order_no' => $order->order_no,
                'user_id' => $order->user_id,
                'admin_id' => $adminId,
                'action' => 'update_status',
                'old_value' => $order->getOriginal('status'),
                'new_value' => $status,
                'remark' => $remark,
            ]);
        }
    }
    
    /**
     * 记录订单支付状态变更日志
     *
     * @param Order $order
     * @param int $payStatus
     * @param int $adminId
     * @param string $remark
     * @return void
     */
    private function logOrderPayStatusChange($order, $payStatus, $adminId, $remark = '')
    {
        if (class_exists('App\Models\OrderLog')) {
            \App\Models\OrderLog::create([
                'order_id' => $order->id,
                'order_no' => $order->order_no,
                'user_id' => $order->user_id,
                'admin_id' => $adminId,
                'action' => 'update_pay_status',
                'old_value' => $order->getOriginal('pay_status'),
                'new_value' => $payStatus,
                'remark' => $remark,
            ]);
        }
    }
    
    /**
     * 记录订单配送状态变更日志
     *
     * @param Order $order
     * @param int $shippingStatus
     * @param int $adminId
     * @param string $remark
     * @return void
     */
    private function logOrderShippingStatusChange($order, $shippingStatus, $adminId, $remark = '')
    {
        if (class_exists('App\Models\OrderLog')) {
            \App\Models\OrderLog::create([
                'order_id' => $order->id,
                'order_no' => $order->order_no,
                'user_id' => $order->user_id,
                'admin_id' => $adminId,
                'action' => 'update_shipping_status',
                'old_value' => $order->getOriginal('shipping_status'),
                'new_value' => $shippingStatus,
                'remark' => $remark,
            ]);
        }
    }
    
    /**
     * 记录订单退款日志
     *
     * @param Order $order
     * @param float $refundAmount
     * @param int $adminId
     * @param string $reason
     * @return void
     */
    private function logOrderRefund($order, $refundAmount, $adminId, $reason = '')
    {
        if (class_exists('App\Models\OrderLog')) {
            \App\Models\OrderLog::create([
                'order_id' => $order->id,
                'order_no' => $order->order_no,
                'user_id' => $order->user_id,
                'admin_id' => $adminId,
                'action' => 'refund',
                'old_value' => 0,
                'new_value' => $refundAmount,
                'remark' => $reason,
            ]);
        }
    }
}
