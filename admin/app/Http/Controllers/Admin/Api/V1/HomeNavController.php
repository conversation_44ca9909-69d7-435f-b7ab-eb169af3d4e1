<?php

namespace App\Http\Controllers\Admin\Api\V1;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class HomeNavController extends Controller
{
    /**
     * 获取首页导航列表
     */
    public function index()
    {
        return response()->json([
            'code' => 0,
            'message' => '获取成功',
            'data' => [
                [
                    'id' => 1,
                    'title' => '分类',
                    'icon' => 'apps-o',
                    'path' => '/category',
                    'url' => '/category',
                    'status' => 1,
                    'is_active' => 1,
                    'sort_order' => 1,
                    'sort' => 1
                ],
                [
                    'id' => 2,
                    'title' => '每日特惠',
                    'icon' => 'gift-o',
                    'path' => '/daily-special',
                    'url' => '/daily-special',
                    'status' => 1,
                    'is_active' => 1,
                    'sort_order' => 2,
                    'sort' => 2
                ],
                [
                    'id' => 3,
                    'title' => '净水充值',
                    'icon' => 'balance-o',
                    'path' => '/water-recharge',
                    'url' => '/water-recharge',
                    'status' => 1,
                    'is_active' => 1,
                    'sort_order' => 3,
                    'sort' => 3
                ],
                [
                    'id' => 4,
                    'title' => '取水点',
                    'icon' => 'location-o',
                    'path' => '/water-point',
                    'url' => '/water-point',
                    'status' => 1,
                    'is_active' => 1,
                    'sort_order' => 4,
                    'sort' => 4
                ],
                [
                    'id' => 5,
                    'title' => '客户服务',
                    'icon' => 'service-o',
                    'path' => '/user/service',
                    'url' => '/user/service',
                    'status' => 1,
                    'is_active' => 1,
                    'sort_order' => 5,
                    'sort' => 5
                ]
            ]
        ]);
    }

    /**
     * 创建导航项
     */
    public function store(Request $request)
    {
        try {
            $data = $request->validate([
                'title' => 'required|string|max:50',
                'icon' => 'required|string|max:50',
                'path' => 'required|string|max:255',
                'sort_order' => 'required|integer|min:1',
                'status' => 'required|boolean'
            ]);

            // 兼容不同字段名
            $data['url'] = $data['path'];
            $data['sort'] = $data['sort_order'];
            $data['is_active'] = $data['status'];

            // 这里可以实现保存导航项的逻辑
            // 例如保存到数据库

            return response()->json([
                'code' => 0,
                'message' => '创建成功',
                'data' => array_merge(['id' => rand(100, 999)], $data)
            ]);
        } catch (\Exception $e) {
            Log::error('创建导航项失败', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'code' => 500,
                'message' => '创建失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取单个导航项
     */
    public function show($id)
    {
        return response()->json([
            'code' => 0,
            'message' => '获取成功',
            'data' => [
                'id' => (int)$id,
                'title' => '导航项' . $id,
                'icon' => 'home-o',
                'path' => '/page/' . $id,
                'url' => '/page/' . $id,
                'sort_order' => (int)$id,
                'sort' => (int)$id,
                'status' => 1,
                'is_active' => 1
            ]
        ]);
    }

    /**
     * 更新导航项
     */
    public function update(Request $request, $id)
    {
        try {
            $data = $request->validate([
                'title' => 'required|string|max:50',
                'icon' => 'required|string|max:50',
                'path' => 'required|string|max:255',
                'sort_order' => 'required|integer|min:1',
                'status' => 'required|boolean'
            ]);

            // 兼容不同字段名
            $data['url'] = $data['path'];
            $data['sort'] = $data['sort_order'];
            $data['is_active'] = $data['status'];

            // 这里可以实现更新导航项的逻辑
            // 例如更新数据库中的记录

            return response()->json([
                'code' => 0,
                'message' => '更新成功',
                'data' => array_merge(['id' => (int)$id], $data)
            ]);
        } catch (\Exception $e) {
            Log::error('更新导航项失败', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'code' => 500,
                'message' => '更新失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 删除导航项
     */
    public function destroy($id)
    {
        // 这里可以实现删除导航项的逻辑
        // 例如从数据库中删除记录

        return response()->json([
            'code' => 0,
            'message' => '删除成功'
        ]);
    }

    /**
     * 获取激活的导航项
     */
    public function getActiveNavItems()
    {
        return response()->json([
            'code' => 0,
            'message' => '获取成功',
            'data' => [
                [
                    'id' => 1,
                    'title' => '分类',
                    'icon' => 'apps-o',
                    'path' => '/category',
                    'url' => '/category',
                    'sort_order' => 1,
                    'sort' => 1
                ],
                [
                    'id' => 2,
                    'title' => '每日特惠',
                    'icon' => 'gift-o',
                    'path' => '/daily-special',
                    'url' => '/daily-special',
                    'sort_order' => 2,
                    'sort' => 2
                ],
                [
                    'id' => 3,
                    'title' => '净水充值',
                    'icon' => 'balance-o',
                    'path' => '/water-recharge',
                    'url' => '/water-recharge',
                    'sort_order' => 3,
                    'sort' => 3
                ],
                [
                    'id' => 4,
                    'title' => '取水点',
                    'icon' => 'location-o',
                    'path' => '/water-point',
                    'url' => '/water-point',
                    'sort_order' => 4,
                    'sort' => 4
                ],
                [
                    'id' => 5,
                    'title' => '客户服务',
                    'icon' => 'service-o',
                    'path' => '/user/service',
                    'url' => '/user/service',
                    'sort_order' => 5,
                    'sort' => 5
                ]
            ]
        ]);
    }

    /**
     * 获取Vant图标列表
     */
    public function getVantIcons()
    {
        return response()->json([
            'code' => 0,
            'message' => '获取成功',
            'data' => [
                'home-o', 'search', 'friends-o', 'setting-o', 'clock-o',
                'gold-coin-o', 'chat-o', 'smile-o', 'music-o', 'star-o',
                'phone-o', 'point-gift-o', 'service-o', 'goods-collect-o',
                'shop-o', 'cart-o', 'user-o', 'orders-o', 'coupon-o',
                'apps-o', 'gift-o', 'balance-o', 'location-o', 'filter-o',
                'diamond-o', 'fire-o', 'flower-o', 'gem-o', 'gift-card-o',
                'hot-o', 'like-o', 'medal-o', 'new-o', 'trophy-o',
                'umbrella-o', 'warning-o', 'wechat-o', 'weibo', 'wifi-o'
            ]
        ]);
    }
} 