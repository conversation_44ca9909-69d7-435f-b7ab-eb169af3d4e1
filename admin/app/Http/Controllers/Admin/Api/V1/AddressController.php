<?php

namespace App\Http\Controllers\Admin\Api\V1;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class AddressController extends Controller
{
    // 腾讯地图API配置
    private $tencentMapKey = 'KBXBZ-QLM67-MEHX4-P2QC2-552E2-W3FH6';
    private $baseUrl = 'https://apis.map.qq.com/ws/district/v1';
    
    /**
     * 获取省份列表
     */
    public function getProvinces()
    {
        try {
            // 使用缓存，省份数据相对稳定，缓存24小时
            $provinces = Cache::remember('tencent_provinces', 24 * 60 * 60, function () {
                $response = Http::timeout(10)->get($this->baseUrl . '/list', [
                    'key' => $this->tencentMapKey
                ]);
                
                if ($response->successful()) {
                    $data = $response->json();
                    if ($data['status'] === 0 && isset($data['result'])) {
                        return $data['result'][0] ?? [];
                    } else {
                        Log::warning('腾讯地图API返回错误: ' . ($data['message'] ?? '未知错误'));
                        return $this->getBackupProvinces();
                    }
                } else {
                    Log::error('腾讯地图API请求失败: ' . $response->status());
                    return $this->getBackupProvinces();
                }
            });
            
            return response()->json([
                'code' => 0,
                'message' => '获取成功',
                'data' => $provinces
            ]);
        } catch (\Exception $e) {
            Log::error('获取省份数据失败: ' . $e->getMessage());
            return response()->json([
                'code' => 0,
                'message' => '获取成功',
                'data' => $this->getBackupProvinces()
            ]);
        }
    }
    
    /**
     * 获取城市列表
     */
    public function getCities(Request $request)
    {
        $provinceId = $request->input('province_id');
        
        if (!$provinceId) {
            return response()->json([
                'code' => 1,
                'message' => '省份ID不能为空',
                'data' => []
            ]);
        }
        
        try {
            // 使用缓存，城市数据相对稳定，缓存12小时
            $cities = Cache::remember("tencent_cities_{$provinceId}", 12 * 60 * 60, function () use ($provinceId) {
                $response = Http::timeout(10)->get($this->baseUrl . '/getchildren', [
                    'id' => $provinceId,
                    'key' => $this->tencentMapKey
                ]);
                
                if ($response->successful()) {
                    $data = $response->json();
                    if ($data['status'] === 0 && isset($data['result'][0])) {
                        return $data['result'][0];
                    } else {
                        Log::warning('腾讯地图API返回错误: ' . ($data['message'] ?? '未知错误'));
                        return $this->getBackupCities($provinceId);
                    }
                } else {
                    Log::error('腾讯地图API请求失败: ' . $response->status());
                    return $this->getBackupCities($provinceId);
                }
            });
            
            return response()->json([
                'code' => 0,
                'message' => '获取成功',
                'data' => $cities
            ]);
        } catch (\Exception $e) {
            Log::error('获取城市数据失败: ' . $e->getMessage());
            return response()->json([
                'code' => 0,
                'message' => '获取成功',
                'data' => $this->getBackupCities($provinceId)
            ]);
        }
    }
    
    /**
     * 获取区县列表
     */
    public function getDistricts(Request $request)
    {
        $cityId = $request->input('city_id');
        
        if (!$cityId) {
            return response()->json([
                'code' => 1,
                'message' => '城市ID不能为空',
                'data' => []
            ]);
        }
        
        try {
            // 使用缓存，区县数据相对稳定，缓存12小时
            $districts = Cache::remember("tencent_districts_{$cityId}", 12 * 60 * 60, function () use ($cityId) {
                $response = Http::timeout(10)->get($this->baseUrl . '/getchildren', [
                    'id' => $cityId,
                    'key' => $this->tencentMapKey
                ]);
                
                if ($response->successful()) {
                    $data = $response->json();
                    if ($data['status'] === 0 && isset($data['result'][0])) {
                        return $data['result'][0];
                    } else {
                        Log::warning('腾讯地图API返回错误: ' . ($data['message'] ?? '未知错误'));
                        return $this->getBackupDistricts($cityId);
                    }
                } else {
                    Log::error('腾讯地图API请求失败: ' . $response->status());
                    return $this->getBackupDistricts($cityId);
                }
            });
            
            return response()->json([
                'code' => 0,
                'message' => '获取成功',
                'data' => $districts
            ]);
        } catch (\Exception $e) {
            Log::error('获取区县数据失败: ' . $e->getMessage());
            return response()->json([
                'code' => 0,
                'message' => '获取成功',
                'data' => $this->getBackupDistricts($cityId)
            ]);
        }
    }
    
    /**
     * 地址解析（地址转坐标）
     */
    public function geocode(Request $request)
    {
        $address = $request->input('address');
        
        if (!$address) {
            return response()->json([
                'code' => 1,
                'message' => '地址不能为空',
                'data' => null
            ]);
        }
        
        try {
            $response = Http::timeout(10)->get('https://apis.map.qq.com/ws/geocoder/v1/', [
                'address' => $address,
                'key' => $this->tencentMapKey
            ]);
            
            if ($response->successful()) {
                $data = $response->json();
                if ($data['status'] === 0 && isset($data['result']['location'])) {
                    return response()->json([
                        'code' => 0,
                        'message' => '解析成功',
                        'data' => [
                            'lng' => $data['result']['location']['lng'],
                            'lat' => $data['result']['location']['lat']
                        ]
                    ]);
                } else {
                    // 使用模拟坐标
                    $coordinates = $this->generateSimulateCoordinates($address);
                    return response()->json([
                        'code' => 0,
                        'message' => '使用模拟坐标',
                        'data' => $coordinates
                    ]);
                }
            } else {
                // 使用模拟坐标
                $coordinates = $this->generateSimulateCoordinates($address);
                return response()->json([
                    'code' => 0,
                    'message' => '使用模拟坐标',
                    'data' => $coordinates
                ]);
            }
        } catch (\Exception $e) {
            Log::error('地址解析失败: ' . $e->getMessage());
            // 使用模拟坐标
            $coordinates = $this->generateSimulateCoordinates($address);
            return response()->json([
                'code' => 0,
                'message' => '使用模拟坐标',
                'data' => $coordinates
            ]);
        }
    }
    
    /**
     * 备用省份数据
     */
    private function getBackupProvinces()
    {
        return [
            ['id' => '110000', 'name' => '北京市', 'fullname' => '北京市'],
            ['id' => '120000', 'name' => '天津市', 'fullname' => '天津市'],
            ['id' => '130000', 'name' => '河北省', 'fullname' => '河北省'],
            ['id' => '140000', 'name' => '山西省', 'fullname' => '山西省'],
            ['id' => '150000', 'name' => '内蒙古自治区', 'fullname' => '内蒙古自治区'],
            ['id' => '210000', 'name' => '辽宁省', 'fullname' => '辽宁省'],
            ['id' => '220000', 'name' => '吉林省', 'fullname' => '吉林省'],
            ['id' => '230000', 'name' => '黑龙江省', 'fullname' => '黑龙江省'],
            ['id' => '310000', 'name' => '上海市', 'fullname' => '上海市'],
            ['id' => '320000', 'name' => '江苏省', 'fullname' => '江苏省'],
            ['id' => '330000', 'name' => '浙江省', 'fullname' => '浙江省'],
            ['id' => '340000', 'name' => '安徽省', 'fullname' => '安徽省'],
            ['id' => '350000', 'name' => '福建省', 'fullname' => '福建省'],
            ['id' => '360000', 'name' => '江西省', 'fullname' => '江西省'],
            ['id' => '370000', 'name' => '山东省', 'fullname' => '山东省'],
            ['id' => '410000', 'name' => '河南省', 'fullname' => '河南省'],
            ['id' => '420000', 'name' => '湖北省', 'fullname' => '湖北省'],
            ['id' => '430000', 'name' => '湖南省', 'fullname' => '湖南省'],
            ['id' => '440000', 'name' => '广东省', 'fullname' => '广东省'],
            ['id' => '450000', 'name' => '广西壮族自治区', 'fullname' => '广西壮族自治区'],
            ['id' => '460000', 'name' => '海南省', 'fullname' => '海南省'],
            ['id' => '500000', 'name' => '重庆市', 'fullname' => '重庆市'],
            ['id' => '510000', 'name' => '四川省', 'fullname' => '四川省'],
            ['id' => '520000', 'name' => '贵州省', 'fullname' => '贵州省'],
            ['id' => '530000', 'name' => '云南省', 'fullname' => '云南省'],
            ['id' => '540000', 'name' => '西藏自治区', 'fullname' => '西藏自治区'],
            ['id' => '610000', 'name' => '陕西省', 'fullname' => '陕西省'],
            ['id' => '620000', 'name' => '甘肃省', 'fullname' => '甘肃省'],
            ['id' => '630000', 'name' => '青海省', 'fullname' => '青海省'],
            ['id' => '640000', 'name' => '宁夏回族自治区', 'fullname' => '宁夏回族自治区'],
            ['id' => '650000', 'name' => '新疆维吾尔自治区', 'fullname' => '新疆维吾尔自治区'],
            ['id' => '710000', 'name' => '台湾省', 'fullname' => '台湾省'],
            ['id' => '810000', 'name' => '香港特别行政区', 'fullname' => '香港特别行政区'],
            ['id' => '820000', 'name' => '澳门特别行政区', 'fullname' => '澳门特别行政区']
        ];
    }
    
    /**
     * 备用城市数据
     */
    private function getBackupCities($provinceId)
    {
        $cities = [
            '350000' => [ // 福建省
                ['id' => '350100', 'name' => '福州市', 'fullname' => '福州市'],
                ['id' => '350200', 'name' => '厦门市', 'fullname' => '厦门市'],
                ['id' => '350300', 'name' => '莆田市', 'fullname' => '莆田市'],
                ['id' => '350400', 'name' => '三明市', 'fullname' => '三明市'],
                ['id' => '350500', 'name' => '泉州市', 'fullname' => '泉州市'],
                ['id' => '350600', 'name' => '漳州市', 'fullname' => '漳州市'],
                ['id' => '350700', 'name' => '南平市', 'fullname' => '南平市'],
                ['id' => '350800', 'name' => '龙岩市', 'fullname' => '龙岩市'],
                ['id' => '350900', 'name' => '宁德市', 'fullname' => '宁德市']
            ],
            '440000' => [ // 广东省
                ['id' => '440100', 'name' => '广州市', 'fullname' => '广州市'],
                ['id' => '440200', 'name' => '韶关市', 'fullname' => '韶关市'],
                ['id' => '440300', 'name' => '深圳市', 'fullname' => '深圳市'],
                ['id' => '440400', 'name' => '珠海市', 'fullname' => '珠海市'],
                ['id' => '440500', 'name' => '汕头市', 'fullname' => '汕头市'],
                ['id' => '440600', 'name' => '佛山市', 'fullname' => '佛山市'],
                ['id' => '440700', 'name' => '江门市', 'fullname' => '江门市'],
                ['id' => '440800', 'name' => '湛江市', 'fullname' => '湛江市'],
                ['id' => '440900', 'name' => '茂名市', 'fullname' => '茂名市'],
                ['id' => '441200', 'name' => '肇庆市', 'fullname' => '肇庆市'],
                ['id' => '441300', 'name' => '惠州市', 'fullname' => '惠州市'],
                ['id' => '441400', 'name' => '梅州市', 'fullname' => '梅州市'],
                ['id' => '441500', 'name' => '汕尾市', 'fullname' => '汕尾市'],
                ['id' => '441600', 'name' => '河源市', 'fullname' => '河源市'],
                ['id' => '441700', 'name' => '阳江市', 'fullname' => '阳江市'],
                ['id' => '441800', 'name' => '清远市', 'fullname' => '清远市'],
                ['id' => '441900', 'name' => '东莞市', 'fullname' => '东莞市'],
                ['id' => '442000', 'name' => '中山市', 'fullname' => '中山市'],
                ['id' => '445100', 'name' => '潮州市', 'fullname' => '潮州市'],
                ['id' => '445200', 'name' => '揭阳市', 'fullname' => '揭阳市'],
                ['id' => '445300', 'name' => '云浮市', 'fullname' => '云浮市']
            ]
        ];
        
        return $cities[$provinceId] ?? [];
    }
    
    /**
     * 备用区县数据
     */
    private function getBackupDistricts($cityId)
    {
        $districts = [
            '350200' => [ // 厦门市
                ['id' => '350203', 'name' => '思明区', 'fullname' => '思明区'],
                ['id' => '350205', 'name' => '海沧区', 'fullname' => '海沧区'],
                ['id' => '350206', 'name' => '湖里区', 'fullname' => '湖里区'],
                ['id' => '350211', 'name' => '集美区', 'fullname' => '集美区'],
                ['id' => '350212', 'name' => '同安区', 'fullname' => '同安区'],
                ['id' => '350213', 'name' => '翔安区', 'fullname' => '翔安区']
            ],
            '350100' => [ // 福州市
                ['id' => '350102', 'name' => '鼓楼区', 'fullname' => '鼓楼区'],
                ['id' => '350103', 'name' => '台江区', 'fullname' => '台江区'],
                ['id' => '350104', 'name' => '仓山区', 'fullname' => '仓山区'],
                ['id' => '350105', 'name' => '马尾区', 'fullname' => '马尾区'],
                ['id' => '350111', 'name' => '晋安区', 'fullname' => '晋安区'],
                ['id' => '350112', 'name' => '长乐区', 'fullname' => '长乐区'],
                ['id' => '350121', 'name' => '闽侯县', 'fullname' => '闽侯县'],
                ['id' => '350122', 'name' => '连江县', 'fullname' => '连江县'],
                ['id' => '350123', 'name' => '罗源县', 'fullname' => '罗源县'],
                ['id' => '350124', 'name' => '闽清县', 'fullname' => '闽清县'],
                ['id' => '350125', 'name' => '永泰县', 'fullname' => '永泰县'],
                ['id' => '350128', 'name' => '平潭县', 'fullname' => '平潭县'],
                ['id' => '350181', 'name' => '福清市', 'fullname' => '福清市']
            ]
        ];
        
        return $districts[$cityId] ?? [];
    }
    
    /**
     * 生成模拟坐标
     */
    private function generateSimulateCoordinates($address)
    {
        // 基于地址生成模拟坐标
        $hash = crc32($address);
        $lng = 116.4074 + (($hash % 10000) / 10000 - 0.5) * 0.1; // 北京附近
        $lat = 39.9042 + (($hash % 7000) / 7000 - 0.5) * 0.1;
        
        return [
            'lng' => round($lng, 6),
            'lat' => round($lat, 6)
        ];
    }
} 