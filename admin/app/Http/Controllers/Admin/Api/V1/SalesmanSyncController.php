<?php

namespace App\Http\Controllers\Admin\Api\V1;

use App\Http\Controllers\Controller;
use App\Traits\ApiResponseTrait;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;
use App\Models\Salesman;
use App\Models\AppUser;
use Carbon\Carbon;

class SalesmanSyncController extends Controller
{
    use ApiResponseTrait;

    /**
     * 同步APP用户到业务员
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function syncAppUsers(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'user_ids' => 'nullable|array',
            'user_ids.*' => 'exists:app_users,id',
            'default_title' => 'nullable|string|max:50',
            'default_department' => 'nullable|string|max:100',
            'default_region' => 'nullable|string|max:100',
            'force' => 'nullable|boolean'
        ]);

        if ($validator->fails()) {
            return $this->error('验证失败', 422, $validator->errors());
        }

        DB::beginTransaction();
        
        try {
            $defaultTitle = $request->input('default_title', '业务员');
            $defaultDepartment = $request->input('default_department', '销售部');
            $defaultRegion = $request->input('default_region', '');
            $force = $request->input('force', false);
            
            // 如果没有指定user_ids，则自动获取所有需要同步的用户
            $userIds = $request->input('user_ids');
            if (empty($userIds)) {
                // 获取所有is_salesman=1的用户
                $userIds = AppUser::where('is_salesman', 1)
                    ->pluck('id')
                    ->toArray();
                    
                if (empty($userIds)) {
                    return $this->success([
                        'success_count' => 0,
                        'failed_count' => 0,
                        'synced_users' => [],
                        'errors' => []
                    ], "没有找到需要同步的用户");
                }
            }
            
            $successCount = 0;
            $failedCount = 0;
            $errors = [];
            $syncedUsers = [];
            
            foreach ($userIds as $userId) {
                try {
                    // 检查用户是否存在
                    $user = AppUser::find($userId);
                    if (!$user) {
                        $failedCount++;
                        $errors[] = "用户ID {$userId} 不存在";
                        continue;
                    }
                    
                    // 检查是否已经是业务员
                    $existingSalesman = Salesman::where('user_id', $userId)->first();
                    if ($existingSalesman && !$force) {
                        $failedCount++;
                        $errors[] = "用户 {$user->name} (ID: {$userId}) 已经是业务员";
                        continue;
                    }
                    
                    // 如果强制同步且已存在，则跳过创建但更新标记
                    if ($existingSalesman && $force) {
                        if (!$user->is_salesman) {
                            $user->is_salesman = 1;
                            $user->save();
                        }
                        $successCount++;
                        $syncedUsers[] = [
                            'user_id' => $userId,
                            'salesman_id' => $existingSalesman->id,
                            'employee_id' => $existingSalesman->employee_id,
                            'name' => $user->name,
                            'phone' => $user->phone,
                            'action' => 'updated'
                        ];
                        continue;
                    }
                    
                    // 生成员工编号
                    $employeeId = 'S' . str_pad($userId, 6, '0', STR_PAD_LEFT);
                    
                    // 检查员工编号是否重复
                    $existingEmployee = Salesman::where('employee_id', $employeeId)->first();
                    if ($existingEmployee) {
                        $employeeId = 'S' . str_pad($userId, 6, '0', STR_PAD_LEFT) . '_' . time();
                    }
                    
                    // 创建业务员记录
                    $salesman = Salesman::create([
                        'user_id' => $userId,
                        'employee_id' => $employeeId,
                        'title' => $defaultTitle,
                        'department' => $defaultDepartment,
                        'region' => $defaultRegion,
                        'status' => 'active',
                        'extra_info' => json_encode([
                            'sync_date' => now()->toDateTimeString(),
                            'sync_source' => 'admin_batch_sync'
                        ])
                    ]);
                    
                    // 更新用户的业务员标记
                    $user->is_salesman = 1;
                    $user->save();
                    
                    $successCount++;
                    $syncedUsers[] = [
                        'user_id' => $userId,
                        'salesman_id' => $salesman->id,
                        'employee_id' => $employeeId,
                        'name' => $user->name,
                        'phone' => $user->phone,
                        'action' => 'created'
                    ];
                    
                } catch (\Exception $e) {
                    $failedCount++;
                    $errors[] = "用户ID {$userId} 同步失败：{$e->getMessage()}";
                }
            }
            
            DB::commit();
            
            return $this->success([
                'success_count' => $successCount,
                'failed_count' => $failedCount,
                'synced_users' => $syncedUsers,
                'errors' => $errors
            ], "批量同步完成，成功 {$successCount} 个，失败 {$failedCount} 个");
                
        } catch (\Exception $e) {
            DB::rollBack();
            
            return $this->error('批量同步失败：'.$e->getMessage(), 500);
        }
    }

    /**
     * 获取可同步的APP用户列表
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getAvailableUsers(Request $request)
    {
        try {
            $query = AppUser::where('is_salesman', 0)
                ->whereNotExists(function($query) {
                    $query->select(DB::raw(1))
                          ->from('salesmen')
                          ->whereRaw('salesmen.user_id = app_users.id');
                });
            
            // 搜索条件
            if ($request->filled('search')) {
                $search = $request->input('search');
                $query->where(function($q) use ($search) {
                    $q->where('name', 'like', "%{$search}%")
                      ->orWhere('phone', 'like', "%{$search}%")
                      ->orWhere('wechat_nickname', 'like', "%{$search}%");
                });
            }
            
            // 注册时间筛选
            if ($request->filled('start_date')) {
                $query->where('created_at', '>=', $request->input('start_date'));
            }
            if ($request->filled('end_date')) {
                $query->where('created_at', '<=', $request->input('end_date') . ' 23:59:59');
            }
            
            // 排序
            $orderBy = $request->input('order_by', 'created_at');
            $orderDir = $request->input('order_dir', 'desc');
            $query->orderBy($orderBy, $orderDir);
            
            // 分页
            $perPage = $request->input('per_page', 20);
            $page = $request->input('page', 1);
            
            $users = $query->paginate($perPage, ['*'], 'page', $page);
            
            return response()->json([
                'code' => 200,
                'message' => '获取成功',
                'data' => $users->items(),
                'total' => $users->total(),
                'current_page' => $users->currentPage(),
                'per_page' => $users->perPage(),
                'last_page' => $users->lastPage()
            ]);
            
        } catch (\Exception $e) {
            return $this->error('获取可同步用户失败：' . $e->getMessage(), 500);
        }
    }

    /**
     * 同步单个APP用户到业务员
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function syncSingleUser(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'user_id' => 'required|exists:app_users,id',
            'title' => 'required|string|max:50',
            'department' => 'nullable|string|max:100',
            'region' => 'nullable|string|max:100',
            'manager_id' => 'nullable|exists:salesmen,id',
            'employee_id' => 'nullable|string|max:50'
        ]);

        if ($validator->fails()) {
            return $this->error('验证失败', 422, $validator->errors());
        }

        DB::beginTransaction();
        
        try {
            $userId = $request->input('user_id');
            
            // 检查用户是否存在
            $user = AppUser::find($userId);
            if (!$user) {
                return $this->error('用户不存在', 404);
            }
            
            // 检查是否已经是业务员
            $existingSalesman = Salesman::where('user_id', $userId)->first();
            if ($existingSalesman) {
                return $this->error('该用户已经是业务员', 400);
            }
            
            // 生成或验证员工编号
            $employeeId = $request->input('employee_id');
            if (!$employeeId) {
                $employeeId = 'S' . str_pad($userId, 6, '0', STR_PAD_LEFT);
            }
            
            // 检查员工编号是否重复
            $existingEmployee = Salesman::where('employee_id', $employeeId)->first();
            if ($existingEmployee) {
                return $this->error('员工编号已存在', 400);
            }
            
            // 创建业务员记录
            $salesman = Salesman::create([
                'user_id' => $userId,
                'employee_id' => $employeeId,
                'title' => $request->input('title'),
                'department' => $request->input('department'),
                'region' => $request->input('region'),
                'manager_id' => $request->input('manager_id'),
                'status' => 'active',
                'extra_info' => json_encode([
                    'sync_date' => now()->toDateTimeString(),
                    'sync_source' => 'admin_single_sync'
                ])
            ]);
            
            // 更新用户的业务员标记
            $user->is_salesman = 1;
            $user->save();
            
            DB::commit();
            
            // 加载关联数据
            $salesman->load('user');
            
            return $this->success($salesman, '用户同步为业务员成功');
                
        } catch (\Exception $e) {
            DB::rollBack();
            
            return $this->error('用户同步失败：'.$e->getMessage(), 500);
        }
    }

    /**
     * 取消业务员身份，恢复为普通用户
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function unsyncSalesman(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'salesman_id' => 'required|exists:salesmen,id'
        ]);

        if ($validator->fails()) {
            return $this->error('验证失败', 422, $validator->errors());
        }

        DB::beginTransaction();
        
        try {
            $salesmanId = $request->input('salesman_id');
            
            $salesman = Salesman::find($salesmanId);
            if (!$salesman) {
                return $this->error('业务员不存在', 404);
            }
            
            // 检查是否有未完成的销售记录或提成
            $pendingSales = DB::table('salesman_sales')
                ->where('salesman_id', $salesmanId)
                ->where('status', 'pending')
                ->count();
                
            $pendingCommissions = DB::table('salesman_commissions')
                ->where('salesman_id', $salesmanId)
                ->where('status', 'pending')
                ->count();
                
            if ($pendingSales > 0 || $pendingCommissions > 0) {
                return $this->error('该业务员还有未完成的销售记录或待支付提成，无法取消业务员身份', 400);
            }
            
            // 更新用户标记
            if ($salesman->user_id) {
                $user = AppUser::find($salesman->user_id);
                if ($user) {
                    $user->is_salesman = 0;
                    $user->save();
                }
            }
            
            // 删除业务员记录
            $salesman->delete();
            
            DB::commit();
            
            return $this->success(null, '业务员身份取消成功，已恢复为普通用户');
                
        } catch (\Exception $e) {
            DB::rollBack();
            
            return $this->error('取消业务员身份失败：'.$e->getMessage(), 500);
        }
    }

    /**
     * 获取同步统计信息
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getSyncStats(Request $request)
    {
        try {
            // 总用户数
            $totalUsers = AppUser::count();
            
            // 业务员数量
            $totalSalesmen = Salesman::count();
            
            // 可同步用户数量
            $availableUsers = AppUser::where('is_salesman', 0)
                ->whereNotExists(function($query) {
                    $query->select(DB::raw(1))
                          ->from('salesmen')
                          ->whereRaw('salesmen.user_id = app_users.id');
                })
                ->count();
            
            // 今日新增业务员
            $todayNewSalesmen = Salesman::whereDate('created_at', Carbon::today())->count();
            
            // 本月新增业务员
            $monthNewSalesmen = Salesman::whereMonth('created_at', Carbon::now()->month)
                ->whereYear('created_at', Carbon::now()->year)
                ->count();
            
            // 活跃业务员数量
            $activeSalesmen = Salesman::where('status', 'active')->count();
            
            // 按部门统计
            $departmentStats = Salesman::select('department', DB::raw('count(*) as count'))
                ->groupBy('department')
                ->get()
                ->pluck('count', 'department')
                ->toArray();
            
            // 按地区统计
            $regionStats = Salesman::select('region', DB::raw('count(*) as count'))
                ->whereNotNull('region')
                ->where('region', '!=', '')
                ->groupBy('region')
                ->get()
                ->pluck('count', 'region')
                ->toArray();
            
            $stats = [
                'total_users' => $totalUsers,
                'total_salesmen' => $totalSalesmen,
                'available_users' => $availableUsers,
                'today_new_salesmen' => $todayNewSalesmen,
                'month_new_salesmen' => $monthNewSalesmen,
                'active_salesmen' => $activeSalesmen,
                'sync_rate' => $totalUsers > 0 ? round(($totalSalesmen / $totalUsers) * 100, 2) : 0,
                'department_distribution' => $departmentStats,
                'region_distribution' => $regionStats
            ];
            
            return $this->success($stats);
            
        } catch (\Exception $e) {
            return $this->error('获取同步统计失败：' . $e->getMessage(), 500);
        }
    }

    /**
     * 批量更新业务员信息
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function batchUpdate(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'salesman_ids' => 'required|array|min:1',
            'salesman_ids.*' => 'exists:salesmen,id',
            'update_data' => 'required|array',
            'update_data.department' => 'nullable|string|max:100',
            'update_data.region' => 'nullable|string|max:100',
            'update_data.manager_id' => 'nullable|exists:salesmen,id',
            'update_data.status' => 'nullable|in:active,leave,suspend'
        ]);

        if ($validator->fails()) {
            return $this->error('验证失败', 422, $validator->errors());
        }

        DB::beginTransaction();
        
        try {
            $salesmanIds = $request->input('salesman_ids');
            $updateData = $request->input('update_data');
            
            // 过滤空值
            $updateData = array_filter($updateData, function($value) {
                return $value !== null && $value !== '';
            });
            
            if (empty($updateData)) {
                return $this->error('没有有效的更新数据', 400);
            }
            
            // 批量更新
            $updatedCount = Salesman::whereIn('id', $salesmanIds)->update($updateData);
            
            DB::commit();
            
            return $this->success([
                'updated_count' => $updatedCount,
                'update_data' => $updateData
            ], "批量更新成功，共更新 {$updatedCount} 条记录");
                
        } catch (\Exception $e) {
            DB::rollBack();
            
            return $this->error('批量更新失败：'.$e->getMessage(), 500);
        }
    }
} 