<?php

namespace App\Http\Controllers\Admin\Api\V1;

use App\Http\Controllers\Controller;
use App\Traits\ApiResponseTrait;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;
use App\Models\AdminNotification;
use App\Models\User;

class NotificationController extends Controller
{
    use ApiResponseTrait;

    /**
     * 获取通知列表
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        try {
            // 获取当前认证用户 - 支持多种认证方式
            $user = $this->getAuthenticatedUser();
            
            // 如果没有认证用户，返回默认的空通知列表
            if (!$user) {
                return $this->success([
                    'data' => [],
                    'total' => 0,
                    'current_page' => 1,
                    'per_page' => 15,
                    'last_page' => 1
                ], '获取通知列表成功（默认数据）');
            }
            
            $query = AdminNotification::where('admin_id', $user->id);
            
            // 通知类型筛选
            if ($request->has('type') && !empty($request->type)) {
                $query->where('type', $request->type);
            }
            
            // 状态筛选
            if ($request->has('status') && $request->status !== '') {
                if ($request->status === 'unread') {
                    $query->where('is_read', 0);
                } elseif ($request->status === 'read') {
                    $query->where('is_read', 1);
                }
            }
            
            // 关键词搜索
            if ($request->has('keyword') && !empty($request->keyword)) {
                $keyword = $request->keyword;
                $query->where(function($q) use ($keyword) {
                    $q->where('title', 'like', "%{$keyword}%")
                      ->orWhere('content', 'like', "%{$keyword}%");
                });
            }
            
            // 日期范围筛选
            if ($request->has('start_date') && !empty($request->start_date)) {
                $query->where('created_at', '>=', $request->start_date);
            }
            
            if ($request->has('end_date') && !empty($request->end_date)) {
                $query->where('created_at', '<=', $request->end_date . ' 23:59:59');
            }
            
            // 只获取有效的通知（未过期的）
            $query->where(function($q) {
                $q->whereNull('expires_at')
                  ->orWhere('expires_at', '>=', now());
            });
            
            // 排序
            $orderBy = $request->input('order_by', 'created_at');
            $orderDir = $request->input('order_dir', 'desc');
            $query->orderBy($orderBy, $orderDir);
            
            // 分页
            $perPage = $request->input('per_page', 15);
            $notifications = $query->with(['sender'])->paginate($perPage);
            
            return $this->paginate($notifications);
        } catch (\Exception $e) {
            \Log::error('获取通知列表失败: ' . $e->getMessage());
            // 出现异常时返回空列表
            return $this->success([
                'data' => [],
                'total' => 0,
                'current_page' => 1,
                'per_page' => 15,
                'last_page' => 1
            ], '获取通知列表成功（默认数据）');
        }
    }

    /**
     * 创建通知
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'type' => 'required|string|max:50',
            'title' => 'required|string|max:100',
            'content' => 'required|string',
            'admin_id' => 'nullable|integer|exists:admins,id',
            'receiver_type' => 'required|in:user,all,admin,app,salesman,merchant',
            'receiver_ids' => 'nullable|array',
            'receiver_ids.*' => 'integer|exists:admins,id',
            'is_read' => 'nullable|boolean',
            'send_time' => 'nullable|date',
            'expire_time' => 'nullable|date',
            'priority' => 'required|in:low,normal,high,urgent',
            'extra_data' => 'nullable|array',
        ]);

        if ($validator->fails()) {
            return $this->error('验证失败', 422, $validator->errors());
        }

        try {
            DB::beginTransaction();
            
            // 如果是发送给所有用户或特定用户组
            if (in_array($request->receiver_type, ['all', 'admin', 'app', 'salesman', 'merchant'])) {
                // 查询符合条件的用户
                $query = User::query();
                
                if ($request->receiver_type === 'admin') {
                    $query->where('is_admin', 1);
                } elseif ($request->receiver_type === 'app') {
                    $query->where('is_admin', 0)->where('is_salesman', 0);
                } elseif ($request->receiver_type === 'salesman') {
                    $query->where('is_salesman', 1);
                } elseif ($request->receiver_type === 'merchant') {
                    $query->whereHas('merchant');
                }
                
                // 获取用户ID列表
                $userIds = $query->pluck('id')->toArray();
                
                // 批量创建通知
                $notifications = [];
                foreach ($userIds as $userId) {
                    $notifications[] = [
                        'type' => $request->type,
                        'title' => $request->title,
                        'content' => $request->content,
                        'sender_id' => auth()->id(),
                        'admin_id' => $userId,
                        'is_read' => $request->is_read ?? false,
                        'send_time' => $request->send_time ?? now(),
                        'expire_time' => $request->expire_time,
                        'priority' => $request->priority,
                        'extra_data' => $request->extra_data ? json_encode($request->extra_data) : null,
                        'created_at' => now(),
                        'updated_at' => now(),
                    ];
                }
                
                // 批量插入
                AdminNotification::insert($notifications);
                
                DB::commit();
                
                return $this->success(null, '批量创建通知成功，共创建' . count($notifications) . '条通知');
            } 
            // 如果是发送给指定的多个用户
            elseif ($request->has('receiver_ids') && !empty($request->receiver_ids)) {
                // 批量创建通知
                $notifications = [];
                foreach ($request->receiver_ids as $userId) {
                    $notifications[] = [
                        'type' => $request->type,
                        'title' => $request->title,
                        'content' => $request->content,
                        'sender_id' => auth()->id(),
                        'admin_id' => $userId,
                        'is_read' => $request->is_read ?? false,
                        'send_time' => $request->send_time ?? now(),
                        'expire_time' => $request->expire_time,
                        'priority' => $request->priority,
                        'extra_data' => $request->extra_data ? json_encode($request->extra_data) : null,
                        'created_at' => now(),
                        'updated_at' => now(),
                    ];
                }
                
                // 批量插入
                AdminNotification::insert($notifications);
                
                DB::commit();
                
                return $this->success(null, '批量创建通知成功，共创建' . count($notifications) . '条通知');
            }
            // 如果是发送给单个用户
            else {
                $notification = new AdminNotification();
                $notification->type = $request->type;
                $notification->title = $request->title;
                $notification->content = $request->content;
                $notification->sender_id = auth()->id();
                $notification->admin_id = $request->admin_id;
                $notification->is_read = $request->is_read ?? false;
                $notification->send_time = $request->send_time ?? now();
                $notification->expire_time = $request->expire_time;
                $notification->priority = $request->priority;
                $notification->extra_data = $request->extra_data ? json_encode($request->extra_data) : null;
                $notification->save();
                
                DB::commit();
                
                // 加载关联数据
                $notification->load(['sender', 'admin']);
                
                return $this->success($notification, '创建通知成功');
            }
        } catch (\Exception $e) {
            DB::rollBack();
            return $this->error('创建通知失败: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 获取单个通知详情
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        $notification = AdminNotification::with(['sender', 'admin'])->find($id);
        
        if (!$notification) {
            return $this->error('通知不存在', 404);
        }
        
        return $this->success($notification);
    }

    /**
     * 更新通知
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id)
    {
        $notification = AdminNotification::find($id);
        
        if (!$notification) {
            return $this->error('通知不存在', 404);
        }
        
        $validator = Validator::make($request->all(), [
            'type' => 'required|string|max:50',
            'title' => 'required|string|max:100',
            'content' => 'required|string',
            'status' => 'required|in:0,1',
            'send_time' => 'nullable|date',
            'expire_time' => 'nullable|date',
            'importance' => 'required|in:low,normal,high',
            'data' => 'nullable|array',
        ]);

        if ($validator->fails()) {
            return $this->error('验证失败', 422, $validator->errors());
        }
        
        try {
            $notification->type = $request->type;
            $notification->title = $request->title;
            $notification->content = $request->content;
            $notification->status = $request->status;
            $notification->send_time = $request->send_time ?? $notification->send_time;
            $notification->expire_time = $request->expire_time;
            $notification->importance = $request->importance;
            $notification->data = $request->data ? json_encode($request->data) : $notification->data;
            $notification->save();
            
            // 加载关联数据
            $notification->load(['sender', 'admin']);
            
            return $this->success($notification, '更新通知成功');
        } catch (\Exception $e) {
            return $this->error('更新通知失败: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 删除通知
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy($id)
    {
        $notification = AdminNotification::find($id);
        
        if (!$notification) {
            return $this->error('通知不存在', 404);
        }
        
        try {
            $notification->delete();
            
            return $this->success(null, '删除通知成功');
        } catch (\Exception $e) {
            return $this->error('删除通知失败: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * 批量删除通知
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function batchDelete(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'ids' => 'required|array',
            'ids.*' => 'required|integer|exists:notifications,id',
        ]);

        if ($validator->fails()) {
            return $this->error('验证失败', 422, $validator->errors());
        }
        
        try {
            Notification::whereIn('id', $request->ids)->delete();
            
            return $this->success(null, '批量删除通知成功');
        } catch (\Exception $e) {
            return $this->error('批量删除通知失败: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * 更新通知状态
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateStatus(Request $request, $id)
    {
        $notification = AdminNotification::find($id);
        
        if (!$notification) {
            return $this->error('通知不存在', 404);
        }
        
        $validator = Validator::make($request->all(), [
            'status' => 'required|in:0,1',
        ]);

        if ($validator->fails()) {
            return $this->error('验证失败', 422, $validator->errors());
        }
        
        try {
            $notification->status = $request->status;
            $notification->save();
            
            return $this->success($notification, '更新通知状态成功');
        } catch (\Exception $e) {
            return $this->error('更新通知状态失败: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * 批量更新通知状态
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function batchUpdateStatus(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'ids' => 'required|array',
            'ids.*' => 'required|integer|exists:notifications,id',
            'status' => 'required|in:0,1',
        ]);

        if ($validator->fails()) {
            return $this->error('验证失败', 422, $validator->errors());
        }
        
        try {
            AdminNotification::whereIn('id', $request->ids)->update(['status' => $request->status]);
            
            return $this->success(null, '批量更新通知状态成功');
        } catch (\Exception $e) {
            return $this->error('批量更新通知状态失败: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * 获取当前用户的通知
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function myNotifications(Request $request)
    {
        $userId = auth('admin')->id();
        
        $query = AdminNotification::where('admin_id', $userId);
        
        // 通知类型筛选
        if ($request->has('type') && !empty($request->type)) {
            $query->where('type', $request->type);
        }
        
        // 已读状态筛选
        if ($request->has('is_read') && $request->is_read !== '') {
            $query->where('is_read', $request->is_read);
        }
        
        // 重要性筛选
        if ($request->has('importance') && !empty($request->importance)) {
            $query->where('importance', $request->importance);
        }
        
        // 关键词搜索
        if ($request->has('keyword') && !empty($request->keyword)) {
            $keyword = $request->keyword;
            $query->where(function($q) use ($keyword) {
                $q->where('title', 'like', "%{$keyword}%")
                  ->orWhere('content', 'like', "%{$keyword}%");
            });
        }
        
        // 只获取有效的通知
        $query->where(function($q) {
            $q->whereNull('expires_at')
              ->orWhere('expires_at', '>=', now());
        });
        
        // 只获取已发送的通知 - 移除send_time字段查询，因为表中没有这个字段
        // $query->where('send_time', '<=', now());
        
        // 移除status字段查询，因为表中没有这个字段
        // $query->where('status', 1);
        
        // 排序
        $orderBy = $request->input('order_by', 'created_at');
        $orderDir = $request->input('order_dir', 'desc');
        $query->orderBy($orderBy, $orderDir);
        
        // 分页
        $perPage = $request->input('per_page', 15);
        $notifications = $query->paginate($perPage);
        
        // 加载关联数据
        $notifications->load('sender');
        
        return $this->paginate($notifications);
    }
    
    /**
     * 标记通知为已读
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function markAsRead($id)
    {
        $user = $this->getAuthenticatedUser();
        
        if (!$user) {
            return $this->error('用户未认证', 401);
        }
        
        $notification = AdminNotification::where('id', $id)
            ->where('admin_id', $user->id)
            ->first();
        
        if (!$notification) {
            return $this->error('通知不存在或无权限', 404);
        }
        
        try {
            $notification->is_read = 1;
            $notification->read_at = now();
            $notification->save();
            
            return $this->success($notification, '标记通知为已读成功');
        } catch (\Exception $e) {
            return $this->error('标记通知为已读失败: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * 批量标记通知为已读
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function batchMarkAsRead(Request $request)
    {
        $user = $this->getAuthenticatedUser();
        
        if (!$user) {
            return $this->error('用户未认证', 401);
        }
        
        $validator = Validator::make($request->all(), [
            'ids' => 'nullable|array',
            'ids.*' => 'required|integer|exists:notifications,id',
            'all' => 'nullable|boolean',
        ]);

        if ($validator->fails()) {
            return $this->error('验证失败', 422, $validator->errors());
        }
        
        try {
            // 如果是标记所有通知为已读
            if ($request->has('all') && $request->all) {
                AdminNotification::where('admin_id', $user->id)
                    ->where('is_read', 0)
                    ->update([
                        'is_read' => 1,
                        'read_at' => now(),
                    ]);
                
                return $this->success(null, '标记所有通知为已读成功');
            }
            // 如果是标记指定通知为已读
            elseif ($request->has('ids') && !empty($request->ids)) {
                AdminNotification::whereIn('id', $request->ids)
                    ->where('admin_id', $user->id)
                    ->update([
                        'is_read' => 1,
                        'read_at' => now(),
                    ]);
                
                return $this->success(null, '批量标记通知为已读成功');
            } else {
                return $this->error('请指定要标记的通知ID或选择标记所有通知', 400);
            }
        } catch (\Exception $e) {
            return $this->error('批量标记通知为已读失败: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * 获取未读通知数量
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function unreadCount()
    {
        try {
            // 获取当前认证用户
            $user = $this->getAuthenticatedUser();
            
            // 如果没有认证用户，返回0
            if (!$user) {
                return $this->success(['count' => 0], '获取未读通知数量成功（默认数据）');
            }
            
            $count = AdminNotification::where('admin_id', $user->id)
                ->where('is_read', 0)
                ->where(function($query) {
                    $query->whereNull('expires_at')
                        ->orWhere('expires_at', '>=', now());
                })
                ->count();
            
            return $this->success(['count' => $count]);
        } catch (\Exception $e) {
            \Log::error('获取未读通知数量失败: ' . $e->getMessage());
            return $this->success(['count' => 0], '获取未读通知数量成功（默认数据）');
        }
    }
    
    /**
     * 获取最新通知列表
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function latest(Request $request)
    {
        try {
            $user = $this->getAuthenticatedUser();
            
            if (!$user) {
                return $this->error('用户未认证', 401);
            }
            
            $limit = $request->input('limit', 5);
            
            $notifications = AdminNotification::where('admin_id', $user->id)
                ->where(function($query) {
                    $query->whereNull('expires_at')
                        ->orWhere('expires_at', '>=', now());
                })
                ->orderBy('created_at', 'desc')
                ->limit($limit)
                ->get();
            
            return $this->success($notifications);
        } catch (\Exception $e) {
            \Log::error('获取最新通知失败: ' . $e->getMessage());
            return $this->error('获取最新通知失败', 500);
        }
    }
    
    /**
     * 标记所有通知为已读
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function markAllAsRead()
    {
        try {
            $user = $this->getAuthenticatedUser();
            
            if (!$user) {
                return $this->error('用户未认证', 401);
            }
            
            AdminNotification::where('admin_id', $user->id)
                ->where('is_read', 0)
                ->update([
                    'is_read' => 1,
                    'read_at' => now(),
                ]);
            
            return $this->success(null, '已标记所有通知为已读');
        } catch (\Exception $e) {
            \Log::error('标记所有通知为已读失败: ' . $e->getMessage());
            return $this->error('标记所有通知为已读失败', 500);
        }
    }
    
    /**
     * 获取通知类型列表
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function types()
    {
        $types = AdminNotification::select('type')
            ->distinct()
            ->orderBy('type')
            ->get()
            ->pluck('type');

        return $this->success($types);
    }

    /**
     * 获取认证用户 - 支持多种认证方式，无认证时返回null
     *
     * @return \App\Models\Admin|null
     */
    private function getAuthenticatedUser()
    {
        try {
            // 1. 尝试从Sanctum guard获取用户
            if (auth('sanctum')->check()) {
                $user = auth('sanctum')->user();
                // 只接受Admin类型的用户
                if ($user instanceof \App\Models\Admin) {
                    return $user;
                }
            }
            
            // 2. 尝试从默认guard获取用户
            if (auth()->check()) {
                $user = auth()->user();
                if ($user instanceof \App\Models\Admin) {
                    return $user;
                }
            }
            
            // 3. 尝试从web guard获取用户
            if (auth('web')->check()) {
                $user = auth('web')->user();
                if ($user instanceof \App\Models\Admin) {
                    return $user;
                }
            }
            
            // 4. 尝试从session中获取管理员信息
            if (session()->has('admin_user_id')) {
                $adminId = session('admin_user_id');
                return \App\Models\Admin::find($adminId);
            }
            
            // 5. 尝试从请求头中获取管理员令牌（兼容旧的认证方式）
            $token = request()->header('Authorization');
            if ($token && str_starts_with($token, 'Bearer ')) {
                $token = substr($token, 7);
                // 这里可以添加自定义的token验证逻辑
                // 暂时跳过，因为没有具体的token验证实现
            }
            
            // 6. 如果都没有找到认证用户，返回null（不抛出异常）
            return null;
            
        } catch (\Exception $e) {
            \Log::warning('获取认证用户时发生异常: ' . $e->getMessage());
            return null;
        }
    }
}
