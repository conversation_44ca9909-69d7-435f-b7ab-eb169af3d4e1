<?php

namespace App\Http\Controllers\Admin\Api\V1;

use App\Http\Controllers\Controller;
use App\Traits\ApiResponseTrait;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;
use App\Models\Salesman;
use App\Models\SalesmanCustomer;
use Carbon\Carbon;

class SalesmanCustomerController extends Controller
{
    use ApiResponseTrait;

    /**
     * 获取业务员客户列表
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        try {
            $query = SalesmanCustomer::with(['salesman.user']);
            
            // 业务员筛选
            if ($request->filled('salesman_id')) {
                $query->where('salesman_id', $request->input('salesman_id'));
            }
            
            // 搜索条件
            if ($request->filled('search')) {
                $search = $request->input('search');
                $query->where(function($q) use ($search) {
                    $q->where('customer_name', 'like', "%{$search}%")
                      ->orWhere('customer_phone', 'like', "%{$search}%")
                      ->orWhere('customer_address', 'like', "%{$search}%");
                });
            }
            
            // 状态筛选
            if ($request->filled('status') && $request->input('status') !== '') {
                $query->where('status', $request->input('status'));
            }
            
            // 来源筛选
            if ($request->filled('source') && $request->input('source') !== '') {
                $query->where('source', $request->input('source'));
            }
            
            // 成交次数筛选
            if ($request->filled('min_deals')) {
                $query->where('deal_count', '>=', $request->input('min_deals'));
            }
            
            // 排序
            $orderBy = $request->input('order_by', 'created_at');
            $orderDir = $request->input('order_dir', 'desc');
            $query->orderBy($orderBy, $orderDir);
            
            // 分页
            $perPage = $request->input('per_page', 15);
            $page = $request->input('page', 1);
            
            $customers = $query->paginate($perPage, ['*'], 'page', $page);
            
            return response()->json([
                'code' => 200,
                'message' => '获取成功',
                'data' => $customers->items(),
                'total' => $customers->total(),
                'current_page' => $customers->currentPage(),
                'per_page' => $customers->perPage(),
                'last_page' => $customers->lastPage()
            ]);
            
        } catch (\Exception $e) {
            \Log::error('获取客户列表失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return $this->error('获取客户列表失败：' . $e->getMessage(), 500);
        }
    }

    /**
     * 创建客户记录
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'salesman_id' => 'required|exists:salesmen,id',
            'customer_name' => 'required|string|max:100',
            'customer_phone' => 'required|string|max:20',
            'customer_address' => 'nullable|string|max:255',
            'source' => 'required|in:referral,advertising,cold_call,online,manual',
            'status' => 'required|in:potential,active,inactive,lost',
            'remarks' => 'nullable|string|max:500'
        ]);

        if ($validator->fails()) {
            return $this->error('验证失败', 422, $validator->errors());
        }

        // 检查客户是否已存在
        $existingCustomer = SalesmanCustomer::where('salesman_id', $request->input('salesman_id'))
            ->where('customer_phone', $request->input('customer_phone'))
            ->first();
            
        if ($existingCustomer) {
            return $this->error('该客户已存在', 400);
        }
        
        try {
            // 创建客户记录
            $customer = SalesmanCustomer::create([
                'salesman_id' => $request->input('salesman_id'),
                'customer_name' => $request->input('customer_name'),
                'customer_phone' => $request->input('customer_phone'),
                'customer_address' => $request->input('customer_address'),
                'source' => $request->input('source'),
                'status' => $request->input('status'),
                'deal_count' => 0,
                'total_amount' => 0,
                'remarks' => $request->input('remarks')
            ]);
            
            // 加载关联数据
            $customer->load('salesman.user');
            
            return $this->success($customer, '客户记录创建成功');
                
        } catch (\Exception $e) {
            return $this->error('客户记录创建失败：'.$e->getMessage(), 500);
        }
    }

    /**
     * 获取客户详情
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        $customer = SalesmanCustomer::with(['salesman.user'])->find($id);
        
        if (!$customer) {
            return $this->error('客户不存在', 404);
        }
        
        // 获取客户的销售记录
        $salesRecords = DB::table('salesman_sales')
            ->where('salesman_id', $customer->salesman_id)
            ->where('customer_phone', $customer->customer_phone)
            ->orderBy('sale_date', 'desc')
            ->get();
            
        $customer->sales_records = $salesRecords;
        
        return $this->success($customer);
    }

    /**
     * 更新客户记录
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id)
    {
        $customer = SalesmanCustomer::find($id);
        
        if (!$customer) {
            return $this->error('客户不存在', 404);
        }
        
        $validator = Validator::make($request->all(), [
            'customer_name' => 'required|string|max:100',
            'customer_phone' => 'required|string|max:20',
            'customer_address' => 'nullable|string|max:255',
            'source' => 'required|in:referral,advertising,cold_call,online,manual',
            'status' => 'required|in:potential,active,inactive,lost',
            'remarks' => 'nullable|string|max:500'
        ]);

        if ($validator->fails()) {
            return $this->error('验证失败', 422, $validator->errors());
        }
        
        // 检查手机号是否与其他客户冲突
        $existingCustomer = SalesmanCustomer::where('salesman_id', $customer->salesman_id)
            ->where('customer_phone', $request->input('customer_phone'))
            ->where('id', '!=', $id)
            ->first();
            
        if ($existingCustomer) {
            return $this->error('该手机号已被其他客户使用', 400);
        }
        
        try {
            // 更新客户记录
            $customer->update([
                'customer_name' => $request->input('customer_name'),
                'customer_phone' => $request->input('customer_phone'),
                'customer_address' => $request->input('customer_address'),
                'source' => $request->input('source'),
                'status' => $request->input('status'),
                'remarks' => $request->input('remarks')
            ]);
            
            // 加载关联数据
            $customer->load('salesman.user');
            
            return $this->success($customer, '客户记录更新成功');
                
        } catch (\Exception $e) {
            return $this->error('客户记录更新失败：'.$e->getMessage(), 500);
        }
    }

    /**
     * 删除客户记录
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy($id)
    {
        $customer = SalesmanCustomer::find($id);
        
        if (!$customer) {
            return $this->error('客户不存在', 404);
        }
        
        try {
            $customer->delete();
            
            return $this->success(null, '客户记录删除成功');
                
        } catch (\Exception $e) {
            return $this->error('客户记录删除失败：'.$e->getMessage(), 500);
        }
    }

    /**
     * 获取客户统计数据
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function stats(Request $request)
    {
        try {
            $salesmanId = $request->input('salesman_id');
            $timeRange = $request->input('time_range', 'year');
            $startDate = $request->input('start_date');
            $endDate = $request->input('end_date');
            $type = $request->input('type', 'overview');
            $limit = $request->input('limit', 10);
            
            // 根据时间范围设置默认日期
            if (!$startDate || !$endDate) {
                switch ($timeRange) {
                    case 'today':
                        $startDate = Carbon::now()->startOfDay()->toDateString();
                        $endDate = Carbon::now()->endOfDay()->toDateString();
                        break;
                    case 'yesterday':
                        $startDate = Carbon::yesterday()->startOfDay()->toDateString();
                        $endDate = Carbon::yesterday()->endOfDay()->toDateString();
                        break;
                    case 'week':
                        $startDate = Carbon::now()->startOfWeek()->toDateString();
                        $endDate = Carbon::now()->endOfWeek()->toDateString();
                        break;
                    case 'month':
                        $startDate = Carbon::now()->startOfMonth()->toDateString();
                        $endDate = Carbon::now()->endOfMonth()->toDateString();
                        break;
                    case 'lastMonth':
                        $startDate = Carbon::now()->subMonth()->startOfMonth()->toDateString();
                        $endDate = Carbon::now()->subMonth()->endOfMonth()->toDateString();
                        break;
                    case 'quarter':
                        $startDate = Carbon::now()->startOfQuarter()->toDateString();
                        $endDate = Carbon::now()->endOfQuarter()->toDateString();
                        break;
                    case 'year':
                        $startDate = Carbon::now()->startOfYear()->toDateString();
                        $endDate = Carbon::now()->endOfYear()->toDateString();
                        break;
                    default:
                        $startDate = $startDate ?: Carbon::now()->startOfYear()->toDateString();
                        $endDate = $endDate ?: Carbon::now()->toDateString();
                }
            }
            
            $query = SalesmanCustomer::query();
            
            if ($salesmanId) {
                $query->where('salesman_id', $salesmanId);
            }
            
            // 添加时间范围过滤
            $query->whereBetween('created_at', [$startDate . ' 00:00:00', $endDate . ' 23:59:59']);
            
            switch ($type) {
                case 'overview':
                    $stats = [
                        'total_customers' => $query->count(),
                        'active_customers' => $query->where('status', 'active')->count(),
                        'potential_customers' => $query->where('status', 'potential')->count(),
                        'inactive_customers' => $query->where('status', 'inactive')->count(),
                        'lost_customers' => $query->where('status', 'lost')->count(),
                        'customers_with_deals' => $query->where('deal_count', '>', 0)->count(),
                        'avg_deal_count' => $query->avg('deal_count'),
                        'total_customer_value' => $query->sum('total_amount'),
                        'avg_customer_value' => $query->avg('total_amount'),
                        'customers_change' => 0, // 可以后续计算同比变化
                    ];
                    
                    // 按来源统计
                    $sourceStats = $query->select('source', DB::raw('count(*) as count'))
                        ->groupBy('source')
                        ->get()
                        ->pluck('count', 'source')
                        ->toArray();
                        
                    $stats['source_distribution'] = $sourceStats;
                    break;
                    
                case 'customer_ranking':
                    $stats = DB::table('salesman_customers')
                        ->join('salesmen', 'salesman_customers.salesman_id', '=', 'salesmen.id')
                        ->leftJoin('app_users', 'salesmen.user_id', '=', 'app_users.id')
                        ->select(
                            'salesmen.id',
                            DB::raw('COALESCE(app_users.name, "未知") as name'),
                            DB::raw('COUNT(salesman_customers.id) as customers'),
                            DB::raw('SUM(salesman_customers.total_amount) as total_value')
                        )
                        ->whereBetween('salesman_customers.created_at', [$startDate . ' 00:00:00', $endDate . ' 23:59:59'])
                        ->groupBy('salesmen.id', 'app_users.name')
                        ->orderBy('customers', 'desc')
                        ->limit($limit)
                        ->get()
                        ->map(function ($item, $index) {
                            $item->rank = $index + 1;
                            return $item;
                        });
                    break;
                    
                default:
                    $stats = [
                        'total_customers' => $query->count(),
                        'active_customers' => $query->where('status', 'active')->count(),
                        'potential_customers' => $query->where('status', 'potential')->count(),
                        'inactive_customers' => $query->where('status', 'inactive')->count(),
                        'lost_customers' => $query->where('status', 'lost')->count(),
                        'customers_with_deals' => $query->where('deal_count', '>', 0)->count(),
                        'avg_deal_count' => $query->avg('deal_count'),
                        'total_customer_value' => $query->sum('total_amount'),
                        'avg_customer_value' => $query->avg('total_amount')
                    ];
                    
                    // 按来源统计
                    $sourceStats = $query->select('source', DB::raw('count(*) as count'))
                        ->groupBy('source')
                        ->get()
                        ->pluck('count', 'source')
                        ->toArray();
                        
                    $stats['source_distribution'] = $sourceStats;
            }
            
            return $this->success($stats);
            
        } catch (\Exception $e) {
            \Log::error('获取客户统计数据失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return $this->error('获取统计数据失败：' . $e->getMessage(), 500);
        }
    }

    /**
     * 批量导入客户
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function batchImport(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'salesman_id' => 'required|exists:salesmen,id',
            'customers' => 'required|array|min:1',
            'customers.*.customer_name' => 'required|string|max:100',
            'customers.*.customer_phone' => 'required|string|max:20',
            'customers.*.customer_address' => 'nullable|string|max:255',
            'customers.*.source' => 'required|in:referral,advertising,cold_call,online,manual',
            'customers.*.remarks' => 'nullable|string|max:500'
        ]);

        if ($validator->fails()) {
            return $this->error('验证失败', 422, $validator->errors());
        }

        DB::beginTransaction();
        
        try {
            $salesmanId = $request->input('salesman_id');
            $customers = $request->input('customers');
            $successCount = 0;
            $failedCount = 0;
            $errors = [];
            
            foreach ($customers as $index => $customerData) {
                try {
                    // 检查客户是否已存在
                    $existingCustomer = SalesmanCustomer::where('salesman_id', $salesmanId)
                        ->where('customer_phone', $customerData['customer_phone'])
                        ->first();
                        
                    if ($existingCustomer) {
                        $failedCount++;
                        $errors[] = "第" . ($index + 1) . "行：客户 {$customerData['customer_name']} ({$customerData['customer_phone']}) 已存在";
                        continue;
                    }
                    
                    // 创建客户记录
                    SalesmanCustomer::create([
                        'salesman_id' => $salesmanId,
                        'customer_name' => $customerData['customer_name'],
                        'customer_phone' => $customerData['customer_phone'],
                        'customer_address' => $customerData['customer_address'] ?? '',
                        'source' => $customerData['source'],
                        'status' => 'potential',
                        'deal_count' => 0,
                        'total_amount' => 0,
                        'remarks' => $customerData['remarks'] ?? ''
                    ]);
                    
                    $successCount++;
                    
                } catch (\Exception $e) {
                    $failedCount++;
                    $errors[] = "第" . ($index + 1) . "行：{$e->getMessage()}";
                }
            }
            
            DB::commit();
            
            return $this->success([
                'success_count' => $successCount,
                'failed_count' => $failedCount,
                'errors' => $errors
            ], "批量导入完成，成功 {$successCount} 条，失败 {$failedCount} 条");
                
        } catch (\Exception $e) {
            DB::rollBack();
            
            return $this->error('批量导入失败：'.$e->getMessage(), 500);
        }
    }
} 