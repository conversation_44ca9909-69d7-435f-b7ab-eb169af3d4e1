<?php

namespace App\Http\Controllers\Admin\Api;

use App\Http\Controllers\Controller;
use App\Models\TappDevice;
use App\Models\AppUser;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\DB;

class TappDeviceApiController extends Controller
{
    /**
     * 获取点点够设备列表
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        try {
            $params = $request->all();
            
            // 记录请求参数
            Log::info('Tapp设备列表查询参数', $params);

            $query = TappDevice::query();

            // 关键词搜索（设备编号、IMEI、用户名）
            if (!empty($params['keyword'])) {
                $keyword = $params['keyword'];
                $query->where(function ($q) use ($keyword) {
                    $q->where('device_number', 'like', "%{$keyword}%")
                      ->orWhere('imei', 'like', "%{$keyword}%")
                      ->orWhere('app_user_name', 'like', "%{$keyword}%");
                });
            }

            // 设备状态筛选
            if (isset($params['status']) && $params['status'] !== '') {
                $query->where('status', $params['status']);
            }

            // 网络状态筛选
            if (isset($params['network_status']) && $params['network_status'] !== '') {
                $query->where('network_status', $params['network_status']);
            }

            // 设备类型筛选
            if (isset($params['device_type']) && $params['device_type'] !== '') {
                $query->where('device_type', $params['device_type']);
            }

            // 计费模式筛选
            if (isset($params['billing_mode']) && $params['billing_mode'] !== '') {
                $query->where('billing_mode', $params['billing_mode']);
            }

            // 自用状态筛选
            if (isset($params['is_self_use']) && $params['is_self_use'] !== '') {
                $query->where('is_self_use', $params['is_self_use']);
            }

            // 日期范围筛选
            if (!empty($params['start_date'])) {
                $query->whereDate('activate_date', '>=', $params['start_date']);
            }
            if (!empty($params['end_date'])) {
                $query->whereDate('activate_date', '<=', $params['end_date']);
            }

            // 水量范围筛选
            if (isset($params['water_min']) && is_numeric($params['water_min'])) {
                $query->where('cumulative_filtration_flow', '>=', $params['water_min']);
            }
            if (isset($params['water_max']) && is_numeric($params['water_max'])) {
                $query->where('cumulative_filtration_flow', '<=', $params['water_max']);
            }

            // 按激活时间倒序排序
            $items = $query->orderBy('activate_date', 'desc')
                ->paginate($request->input('limit', 10));

            $list = [];
            foreach ($items as $item) {
                // 获取最近的充值信息
                $lastRechargeInfo = $item->getLastRechargeInfo();
                
                $deviceData = [
                    'id' => $item->id,
                    'device_number' => $item->device_number,
                    'device_name' => $item->device_number,
                    'imei' => $item->imei,
                    'device_type' => $item->device_type,
                    'dealer_name' => $item->dealer_name,
                    'client_name' => $item->client_name,
                    'app_user_id' => $item->app_user_id,
                    'app_user_name' => $item->app_user_name,
                    'status' => $item->status,
                    'status_text' => $item->status_text,
                    'network_status' => $item->network_status,
                    'network_status_text' => $item->network_status == '1' ? '在线' : '离线',
                    'billing_mode' => $item->billing_mode,
                    'surplus_flow' => $item->surplus_flow,
                    'remaining_days' => $item->remaining_days,
                    'cumulative_filtration_flow' => $item->cumulative_filtration_flow,
                    'activate_date' => $item->activate_date,
                    'last_online_time' => $item->last_online_time,
                    'last_sync_time' => $item->last_sync_time,
                    'is_self_use' => $item->is_self_use,
                    'is_water_point' => $item->is_water_point,
                    'f1_life_percent' => $item->f1_life_percent,
                    'f2_life_percent' => $item->f2_life_percent,
                    'f3_life_percent' => $item->f3_life_percent,
                    'f1_flux' => $item->f1_flux,
                    'f1_flux_max' => $item->f1_flux_max,
                    'f2_flux' => $item->f2_flux,
                    'f2_flux_max' => $item->f2_flux_max,
                    'f3_flux' => $item->f3_flux,
                    'f3_flux_max' => $item->f3_flux_max,
                    'service_end_time' => $item->service_end_time,
                ];
                
                // 如果有充值信息，添加充值相关字段
                if ($lastRechargeInfo) {
                    $deviceData['recharge_info'] = [
                        'order_id' => $lastRechargeInfo->id,
                        'order_number' => $lastRechargeInfo->order_number,
                        'billing_mode' => $lastRechargeInfo->billing_mode,
                        'billing_mode_text' => $lastRechargeInfo->billing_mode == '1' ? '流量计费' : '包年计费',
                        'money' => $lastRechargeInfo->money,
                        'create_date' => $lastRechargeInfo->create_date,
                        'surrogate_type' => $lastRechargeInfo->surrogate_type,
                        'surrogate_type_text' => $lastRechargeInfo->surrogate_type == '1' ? '代充' : '自充',
                        'commission_amount' => $item->calculateCommissionAmount()
                    ];
                }
                
                $list[] = $deviceData;
            }

            return response()->json([
                'code' => 0,
                'message' => '获取成功',
                'data' => [
                    'total' => $items->total(),
                    'list' => $list
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('获取点点够设备列表失败', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'code' => 500,
                'message' => '获取设备列表失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 获取设备详情API
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        try {
            $device = TappDevice::findOrFail($id);
            
            // 获取设备的最近充值订单信息
            $lastRechargeInfo = $device->getLastRechargeInfo();
            
            $data = [
                'id' => $device->id,
                'device_number' => $device->device_number,
                'device_name' => $device->device_number,
                'imei' => $device->imei,
                'device_type' => $device->device_type,
                'dealer_name' => $device->dealer_name,
                'client_name' => $device->client_name,
                'app_user_id' => $device->app_user_id,
                'app_user_name' => $device->app_user_name,
                'status' => $device->status,
                'status_text' => $device->status_text,
                'network_status' => $device->network_status,
                'network_status_text' => $device->network_status == '1' ? '在线' : '离线',
                'billing_mode' => $device->billing_mode,
                'surplus_flow' => $device->surplus_flow,
                'remaining_days' => $device->remaining_days,
                'cumulative_filtration_flow' => $device->cumulative_filtration_flow,
                'activate_date' => $device->activate_date,
                'last_online_time' => $device->last_online_time,
                'last_sync_time' => $device->last_sync_time,
                'address' => $device->address,
                'remark' => $device->remark,
                'create_date' => $device->create_date,
                'update_date' => $device->update_date,
                'is_self_use' => $device->is_self_use,
                'is_water_point' => $device->is_water_point,
                'f1_life_percent' => $device->f1_life_percent,
                'f2_life_percent' => $device->f2_life_percent,
                'f3_life_percent' => $device->f3_life_percent,
                'f1_flux' => $device->f1_flux,
                'f1_flux_max' => $device->f1_flux_max,
                'f2_flux' => $device->f2_flux,
                'f2_flux_max' => $device->f2_flux_max,
                'f3_flux' => $device->f3_flux,
                'f3_flux_max' => $device->f3_flux_max,
                'service_end_time' => $device->service_end_time,
                // 充值订单相关信息
                'recharge_info' => $lastRechargeInfo ? [
                    'order_id' => $lastRechargeInfo->id,
                    'order_number' => $lastRechargeInfo->order_number,
                    'billing_mode' => $lastRechargeInfo->billing_mode,
                    'billing_mode_text' => $lastRechargeInfo->billing_mode == '1' ? '流量计费' : '包年计费',
                    'money' => $lastRechargeInfo->money,
                    'create_date' => $lastRechargeInfo->create_date,
                    'surrogate_type' => $lastRechargeInfo->surrogate_type,
                    'surrogate_type_text' => $lastRechargeInfo->surrogate_type == '1' ? '代充' : '自充',
                    'commission_amount' => $device->calculateCommissionAmount()
                ] : null,
                // 获取所有充值订单
                'recharge_orders' => $device->getRechargeOrders()
            ];

            return response()->json([
                'code' => 0,
                'message' => '获取成功',
                'data' => $data
            ]);
        } catch (\Exception $e) {
            Log::error('获取点点够设备详情失败', [
                'id' => $id,
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'code' => 500,
                'message' => '获取设备详情失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 更新设备信息API
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id)
    {
        try {
            $device = TappDevice::findOrFail($id);
            
            $validated = $request->validate([
                'app_user_id' => 'nullable|integer',
                'is_self_use' => 'nullable|boolean',
                'is_water_point' => 'nullable|boolean',
                'app_user_name' => 'nullable|string|max:50',
                'remark' => 'nullable|string|max:500',
            ]);
            
            DB::beginTransaction();
            
            // 判断是否只更新自用状态
            if (array_key_exists('is_self_use', $validated) && count($validated) === 1) {
                $oldValue = $device->is_self_use;
                $newValue = (int)$validated['is_self_use'];
                
                if ($oldValue !== $newValue) {
                    // 记录状态变更
                    $statusText = $newValue ? '自用' : '销售';
                    $oldStatusText = $oldValue ? '自用' : '销售';
                    
                    \Log::info("设备状态变更", [
                        'device_id' => $device->id,
                        'device_number' => $device->device_number,
                        'old_status' => $oldStatusText,
                        'new_status' => $statusText,
                        'user_id' => auth()->id() ?? null,
                        'user_name' => auth()->user()->name ?? 'unknown',
                        'timestamp' => now()->toDateTimeString(),
                    ]);
                    
                    // 更新设备状态
                    $device->is_self_use = $newValue;
                    $device->save();
                    
                    DB::commit();
                    return response()->json([
                        'code' => 0,
                        'message' => "设备 {$device->device_number} 已成功标记为{$statusText}状态",
                        'data' => $device
                    ]);
                }
            }
            
            // 判断是否只更新取水点状态
            if (array_key_exists('is_water_point', $validated) && count($validated) === 1) {
                $oldValue = $device->is_water_point;
                $newValue = (int)$validated['is_water_point'];
                
                if ($oldValue !== $newValue) {
                    // 记录状态变更
                    $statusText = $newValue ? '取水点' : '普通设备';
                    $oldStatusText = $oldValue ? '取水点' : '普通设备';
                    
                    \Log::info("设备取水点状态变更", [
                        'device_id' => $device->id,
                        'device_number' => $device->device_number,
                        'old_status' => $oldStatusText,
                        'new_status' => $statusText,
                        'user_id' => auth()->id() ?? null,
                        'user_name' => auth()->user()->name ?? 'unknown',
                        'timestamp' => now()->toDateTimeString(),
                    ]);
                    
                    // 更新设备状态
                    $device->is_water_point = $newValue;
                    $device->save();
                    
                    DB::commit();
                    return response()->json([
                         'code' => 0,
                         'message' => "设备 {$device->device_number} 已成功标记为{$statusText}",
                         'data' => $device
                     ]);
                 }
                 
                 DB::commit();
                 return response()->json([
                     'code' => 0,
                     'message' => "设备取水点状态未发生变化",
                     'data' => $device
                 ]);
             }
            
            // 记录设备信息更新
            Log::info('更新设备信息', [
                'device_id' => $id,
                'device_number' => $device->device_number,
                'old_values' => $device->only([
                    'device_type',
                    'remark',
                    'billing_mode',
                    'surplus_flow',
                    'remaining_days',
                    'status',
                    'app_user_id',
                    'app_user_name',
                    'is_self_use'
                ]),
                'new_values' => $validated,
                'user_id' => $request->user() ? $request->user()->id : null
            ]);
            
            $device->update($validated);

            DB::commit();
            return response()->json([
                'code' => 0,
                'message' => '更新成功',
                'data' => $device
            ]);
        } catch (\Exception $e) {
            Log::error('更新点点够设备信息失败', [
                'id' => $id,
                'data' => $request->all(),
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            DB::rollBack();
            return response()->json([
                'code' => 500,
                'message' => '更新设备信息失败: ' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

    /**
     * 设置设备状态API
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateStatus(Request $request, $id)
    {
        try {
            $device = TappDevice::findOrFail($id);
            $status = $request->input('status');
            
            if (!in_array($status, ['E', 'D', 'maintenance'])) {
                return response()->json([
                    'code' => 400,
                    'message' => '无效的状态值'
                ]);
            }
            
            $device->status = $status;
            $device->save();

            return response()->json([
                'code' => 0,
                'message' => '状态已更新',
                'data' => null
            ]);
        } catch (\Exception $e) {
            Log::error('设置点点够设备状态失败', [
                'id' => $id,
                'status' => $request->input('status'),
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'code' => 500,
                'message' => '设置设备状态失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 同步点点够设备数据
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function syncData(Request $request)
    {
        try {
            // 记录同步请求
            Log::info('开始同步点点够设备数据', [
                'user_id' => $request->user() ? $request->user()->id : 'guest',
                'force' => $request->input('force', false)
            ]);
            
            $force = $request->input('force', false);
            
            // 调用同步命令
            $exitCode = Artisan::call('tapp:sync-devices', [
                '--force' => $force
            ]);
            
            $output = Artisan::output();
            Log::info('同步设备命令输出', [
                'exit_code' => $exitCode,
                'output' => $output
            ]);
            
            if ($exitCode === 0) {
                return response()->json([
                    'code' => 0,
                    'message' => '设备数据同步成功',
                    'data' => [
                        'details' => nl2br($output)
                    ]
                ]);
            } else {
                Log::error('同步点点够设备数据命令执行失败', [
                    'exit_code' => $exitCode,
                    'output' => $output
                ]);
                
                return response()->json([
                    'code' => 500,
                    'message' => '设备数据同步失败: ' . (strpos($output, 'error') !== false ? $output : '执行过程中出现错误'),
                    'data' => [
                        'details' => nl2br($output)
                    ]
                ]);
            }
        } catch (\Exception $e) {
            Log::error('同步点点够设备数据失败', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'code' => 500,
                'message' => '同步设备数据失败: ' . $e->getMessage(),
                'data' => [
                    'details' => $e->getMessage() . "\n" . $e->getTraceAsString()
                ]
            ]);
        }
    }

    /**
     * 获取App用户（VIP用户）列表用于设备关联
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getAppUsers(Request $request)
    {
        try {
            $keyword = $request->input('keyword', '');
            $query = \App\Models\AppUser::query()
                ->select(['id', 'name', 'phone', 'avatar', 'wechat_avatar']);
            
            if (!empty($keyword)) {
                $query->where(function($q) use ($keyword) {
                    $q->where('name', 'like', "%{$keyword}%")
                      ->orWhere('phone', 'like', "%{$keyword}%")
                      ->orWhere('id', 'like', "%{$keyword}%");
                });
            }
            
            // 优先显示VIP用户
            $query->orderBy('is_vip', 'desc')
                  ->orderBy('id', 'desc')
                  ->limit(30);
            
            $users = $query->get();
            
            $list = [];
            foreach ($users as $user) {
                $vipLabel = $user->is_vip ? '[VIP]' : '';
                $list[] = [
                    'id' => $user->id,
                    'name' => $user->name . $vipLabel,
                    'phone' => $user->phone,
                    'avatar' => $user->avatar,
                    'wechat_avatar' => $user->wechat_avatar,
                    'label' => "{$user->name} ({$user->phone})"
                ];
            }
            
            return response()->json([
                'code' => 0,
                'message' => '获取成功',
                'data' => $list
            ]);
        } catch (\Exception $e) {
            Log::error('获取App用户列表失败', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'code' => 500,
                'message' => '获取用户列表失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 删除点点够设备
     *
     * @param int $id 设备ID
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy($id)
    {
        try {
            $device = TappDevice::findOrFail($id);
            
            DB::beginTransaction();
            try {
                // 删除设备
                $device->delete();
                
                DB::commit();
                
                return response()->json([
                    'code' => 0,
                    'message' => '设备删除成功',
                    'data' => null
                ]);
            } catch (\Exception $e) {
                DB::rollBack();
                throw $e;
            }
        } catch (\Exception $e) {
            Log::error('删除点点够设备失败', [
                'id' => $id,
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'code' => 500,
                'message' => '删除设备失败: ' . $e->getMessage(),
                'data' => null
            ]);
        }
    }
}