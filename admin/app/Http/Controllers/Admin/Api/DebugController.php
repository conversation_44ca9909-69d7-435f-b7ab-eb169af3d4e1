<?php

namespace App\Http\Controllers\Admin\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class DebugController extends Controller
{
    /**
     * 执行数据库查询（仅用于调试）
     */
    public function queryDb(Request $request)
    {
        $query = $request->input('query');
        
        if (!$query) {
            return response()->json([
                'code' => 1,
                'message' => '参数错误',
                'data' => null
            ], 400);
        }
        
        try {
            $result = [];
            
            // 根据查询类型执行不同的SQL
            switch ($query) {
                case 'salesmen_stats':
                    // 获取业务员统计
                    $salesmenCount = DB::table('salesmen')->count();
                    $appUsersSalesmanCount = DB::table('app_users')->where('is_salesman', 1)->count();
                    
                    $result = [
                        'salesmen' => $salesmenCount,
                        'appUsersSalesman' => $appUsersSalesmanCount
                    ];
                    break;
                    
                case 'salesmen_data':
                    // 获取业务员数据
                    $salesmen = DB::table('salesmen')
                        ->select('id', 'user_id', 'employee_id', 'title', 'status', 'created_at')
                        ->limit(10)
                        ->get();
                    
                    $result = [
                        'data' => $salesmen
                    ];
                    break;
                    
                default:
                    return response()->json([
                        'code' => 1,
                        'message' => '未知的查询类型',
                        'data' => null
                    ], 400);
            }
            
            return response()->json([
                'code' => 0,
                'message' => '查询成功',
                'data' => $result
            ]);
            
        } catch (\Exception $e) {
            Log::error('调试查询失败', [
                'query' => $query,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'code' => 1,
                'message' => '查询失败: ' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }
} 