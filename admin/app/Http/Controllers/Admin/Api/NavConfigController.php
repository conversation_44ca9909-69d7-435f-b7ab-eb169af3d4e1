<?php

namespace App\Http\Controllers\Admin\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class NavConfigController extends Controller
{
    /**
     * 获取导航配置列表
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        try {
            // 获取查询参数
            $type = $request->input('type');
            $platform = $request->input('platform');
            $status = $request->input('status');

            // 构建查询
            $query = DB::table('nav_config');
            
            // 应用过滤条件
            if ($type) {
                $query->where('type', $type);
            }
            
            if ($platform) {
                $query->where('platform', $platform);
            }
            
            if ($status !== null && $status !== '') {
                $query->where('status', $status);
            }
            
            // 获取数据
            $navItems = $query->orderBy('sort_order', 'asc')->get();
            
            // 返回数据
            return response()->json([
                'code' => 0,
                'message' => '获取导航配置列表成功',
                'data' => $navItems
            ]);
        } catch (\Exception $e) {
            Log::error('获取导航配置列表失败: ' . $e->getMessage());
            return response()->json([
                'code' => 1,
                'message' => '获取导航配置列表失败: ' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    /**
     * 获取单个导航配置详情
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show(Request $request, $id)
    {
        try {
            // 查询导航配置
            $navItem = DB::table('nav_config')->where('id', $id)->first();
            
            if (!$navItem) {
                return response()->json([
                    'code' => 1,
                    'message' => '导航配置不存在',
                    'data' => null
                ], 404);
            }
            
            // 返回数据
            return response()->json([
                'code' => 0,
                'message' => '获取导航配置详情成功',
                'data' => $navItem
            ]);
        } catch (\Exception $e) {
            Log::error('获取导航配置详情失败: ' . $e->getMessage());
            return response()->json([
                'code' => 1,
                'message' => '获取导航配置详情失败: ' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    /**
     * 创建导航配置
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        try {
            // 验证请求数据
            $validator = Validator::make($request->all(), [
                'title' => 'required|string|max:50',
                'icon' => 'required|string',
                'url' => 'required|string',
                'type' => 'required|string|in:main,sub',
                'platform' => 'required|string|in:app,admin,all',
                'parent_id' => 'nullable|integer',
                'sort_order' => 'nullable|integer',
                'status' => 'required|integer|in:0,1',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'code' => 1,
                    'message' => $validator->errors()->first(),
                    'data' => null
                ], 422);
            }

            // 获取最大排序值
            $maxSortOrder = DB::table('nav_config')->max('sort_order');
            $sortOrder = $request->input('sort_order', $maxSortOrder + 10);

            // 创建导航配置
            $id = DB::table('nav_config')->insertGetId([
                'title' => $request->input('title'),
                'icon' => $request->input('icon'),
                'url' => $request->input('url'),
                'type' => $request->input('type'),
                'platform' => $request->input('platform'),
                'parent_id' => $request->input('parent_id', 0),
                'sort_order' => $sortOrder,
                'status' => $request->input('status'),
                'created_at' => now(),
                'updated_at' => now(),
            ]);

            return response()->json([
                'code' => 0,
                'message' => '创建导航配置成功',
                'data' => [
                    'id' => $id
                ]
            ], 201);
        } catch (\Exception $e) {
            Log::error('创建导航配置失败: ' . $e->getMessage());
            return response()->json([
                'code' => 1,
                'message' => '创建导航配置失败: ' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    /**
     * 更新导航配置
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id)
    {
        try {
            // 查询导航配置
            $navItem = DB::table('nav_config')->where('id', $id)->first();
            
            if (!$navItem) {
                return response()->json([
                    'code' => 1,
                    'message' => '导航配置不存在',
                    'data' => null
                ], 404);
            }

            // 验证请求数据
            $validator = Validator::make($request->all(), [
                'title' => 'sometimes|required|string|max:50',
                'icon' => 'sometimes|required|string',
                'url' => 'sometimes|required|string',
                'type' => 'sometimes|required|string|in:main,sub',
                'platform' => 'sometimes|required|string|in:app,admin,all',
                'parent_id' => 'nullable|integer',
                'sort_order' => 'nullable|integer',
                'status' => 'sometimes|required|integer|in:0,1',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'code' => 1,
                    'message' => $validator->errors()->first(),
                    'data' => null
                ], 422);
            }

            // 更新数据
            $updateData = [];
            
            if ($request->has('title')) {
                $updateData['title'] = $request->input('title');
            }
            
            if ($request->has('icon')) {
                $updateData['icon'] = $request->input('icon');
            }
            
            if ($request->has('url')) {
                $updateData['url'] = $request->input('url');
            }
            
            if ($request->has('type')) {
                $updateData['type'] = $request->input('type');
            }
            
            if ($request->has('platform')) {
                $updateData['platform'] = $request->input('platform');
            }
            
            if ($request->has('parent_id')) {
                $updateData['parent_id'] = $request->input('parent_id');
            }
            
            if ($request->has('sort_order')) {
                $updateData['sort_order'] = $request->input('sort_order');
            }
            
            if ($request->has('status')) {
                $updateData['status'] = $request->input('status');
            }
            
            $updateData['updated_at'] = now();
            
            // 执行更新
            DB::table('nav_config')->where('id', $id)->update($updateData);

            return response()->json([
                'code' => 0,
                'message' => '更新导航配置成功',
                'data' => [
                    'id' => $id
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('更新导航配置失败: ' . $e->getMessage());
            return response()->json([
                'code' => 1,
                'message' => '更新导航配置失败: ' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    /**
     * 删除导航配置
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy(Request $request, $id)
    {
        try {
            // 查询导航配置
            $navItem = DB::table('nav_config')->where('id', $id)->first();
            
            if (!$navItem) {
                return response()->json([
                    'code' => 1,
                    'message' => '导航配置不存在',
                    'data' => null
                ], 404);
            }

            // 执行删除
            DB::table('nav_config')->where('id', $id)->delete();

            return response()->json([
                'code' => 0,
                'message' => '删除导航配置成功',
                'data' => null
            ]);
        } catch (\Exception $e) {
            Log::error('删除导航配置失败: ' . $e->getMessage());
            return response()->json([
                'code' => 1,
                'message' => '删除导航配置失败: ' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }
}
