<?php

namespace App\Http\Controllers\Admin\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\AppUser;
use Illuminate\Support\Facades\Log;

class TappDeviceController extends Controller
{
    /**
     * 获取App用户（VIP用户）列表用于设备关联
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getAppUsers(Request $request)
    {
        try {
            $keyword = $request->input('keyword', '');
            $query = \App\Models\AppUser::query()
                ->select(['id', 'name', 'phone', 'avatar', 'wechat_avatar']);
            
            if (!empty($keyword)) {
                $query->where(function($q) use ($keyword) {
                    $q->where('name', 'like', "%{$keyword}%")
                      ->orWhere('phone', 'like', "%{$keyword}%")
                      ->orWhere('id', 'like', "%{$keyword}%");
                });
            }
            
            // 优先显示VIP用户
            $query->orderBy('is_vip', 'desc')
                  ->orderBy('id', 'desc')
                  ->limit(30);
            
            $users = $query->get();
            
            $list = [];
            foreach ($users as $user) {
                $vipLabel = $user->is_vip ? '[VIP]' : '';
                $list[] = [
                    'id' => $user->id,
                    'name' => $user->name . $vipLabel,
                    'phone' => $user->phone,
                    'avatar' => $user->avatar,
                    'wechat_avatar' => $user->wechat_avatar,
                    'label' => "{$user->name} ({$user->phone})"
                ];
            }
            
            return response()->json([
                'code' => 0,
                'message' => '获取成功',
                'data' => $list
            ]);
        } catch (\Exception $e) {
            Log::error('获取App用户列表失败', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'code' => 500,
                'message' => '获取用户列表失败: ' . $e->getMessage()
            ]);
        }
    }
}
