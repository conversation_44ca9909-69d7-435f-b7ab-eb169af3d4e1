<?php

namespace App\Http\Controllers\Admin\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class InstallationBookingController extends Controller
{
    /**
     * 获取净水器安装预约列表
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        try {
            // 获取查询参数
            $page = $request->input('page', 1);
            $limit = $request->input('limit', 15);
            $status = $request->input('status');
            $startDate = $request->input('start_date');
            $endDate = $request->input('end_date');
            $search = $request->input('search');

            // 构建查询
            $query = DB::table('install_bookings');
            
            // 应用过滤条件
            if ($status) {
                $query->where('status', $status);
            }
            
            if ($startDate) {
                $query->whereDate('booking_date', '>=', $startDate);
            }
            
            if ($endDate) {
                $query->whereDate('booking_date', '<=', $endDate);
            }
            
            if ($search) {
                $query->where(function($q) use ($search) {
                    $q->where('booking_no', 'like', "%{$search}%")
                      ->orWhere('contact_name', 'like', "%{$search}%")
                      ->orWhere('contact_phone', 'like', "%{$search}%")
                      ->orWhere('address', 'like', "%{$search}%");
                });
            }
            
            // 获取总记录数
            $total = $query->count();
            
            // 应用排序和分页
            $bookings = $query->orderBy('created_at', 'desc')
                            ->skip(($page - 1) * $limit)
                            ->take($limit)
                            ->get();
            
            // 格式化数据
            $items = [];
            foreach ($bookings as $booking) {
                // 获取关联的用户信息
                $user = null;
                if ($booking->app_user_id) {
                    $user = DB::table('app_users')->where('id', $booking->app_user_id)->first();
                }
                
                // 获取关联的工程师信息
                $engineer = null;
                if ($booking->engineer_id) {
                    $engineer = DB::table('installation_engineers')->where('id', $booking->engineer_id)->first();
                }
                
                // 获取关联的设备信息
                $device = null;
                if ($booking->device_id) {
                    $device = DB::table('tapp_devices')->where('id', $booking->device_id)->first();
                }
                
                $items[] = [
                    'id' => $booking->id,
                    'booking_no' => $booking->booking_no,
                    'contact_name' => $booking->contact_name,
                    'contact_phone' => $booking->contact_phone,
                    'address' => $booking->address,
                    'booking_date' => $booking->booking_date,
                    'booking_time' => $booking->booking_time,
                    'status' => $booking->status,
                    'status_text' => $this->getStatusText($booking->status),
                    'remarks' => $booking->remarks,
                    'cancel_reason' => $booking->cancel_reason,
                    'user' => $user ? [
                        'id' => $user->id,
                        'name' => $user->name,
                        'mobile' => $user->mobile,
                        'avatar' => $user->avatar,
                    ] : null,
                    'engineer' => $engineer ? [
                        'id' => $engineer->id,
                        'name' => $engineer->name,
                        'mobile' => $engineer->mobile,
                    ] : null,
                    'device' => $device ? [
                        'id' => $device->id,
                        'model' => $device->model,
                        'sn' => $device->sn,
                    ] : null,
                    'created_at' => $booking->created_at,
                    'updated_at' => $booking->updated_at,
                ];
            }
            
            // 返回数据
            return response()->json([
                'code' => 0,
                'message' => '获取安装预约列表成功',
                'data' => [
                    'total' => $total,
                    'page' => (int)$page,
                    'limit' => (int)$limit,
                    'items' => $items
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('获取安装预约列表失败: ' . $e->getMessage());
            return response()->json([
                'code' => 1,
                'message' => '获取安装预约列表失败: ' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    /**
     * 获取净水器安装预约详情
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show(Request $request, $id)
    {
        try {
            // 查询预约详情
            $booking = DB::table('install_bookings')->where('id', $id)->first();
            
            if (!$booking) {
                return response()->json([
                    'code' => 1,
                    'message' => '安装预约不存在',
                    'data' => null
                ], 404);
            }
            
            // 获取关联的用户信息
            $user = null;
            if ($booking->app_user_id) {
                $user = DB::table('app_users')->where('id', $booking->app_user_id)->first();
            }
            
            // 获取关联的工程师信息
            $engineer = null;
            if ($booking->engineer_id) {
                $engineer = DB::table('installation_engineers')->where('id', $booking->engineer_id)->first();
            }
            
            // 获取关联的设备信息
            $device = null;
            if ($booking->device_id) {
                $device = DB::table('tapp_devices')->where('id', $booking->device_id)->first();
            }
            
            // 获取预约日志
            $logs = DB::table('install_booking_logs')
                    ->where('booking_id', $id)
                    ->orderBy('created_at', 'desc')
                    ->get();
            
            // 格式化数据
            $data = [
                'id' => $booking->id,
                'booking_no' => $booking->booking_no,
                'contact_name' => $booking->contact_name,
                'contact_phone' => $booking->contact_phone,
                'address' => $booking->address,
                'booking_date' => $booking->booking_date,
                'booking_time' => $booking->booking_time,
                'status' => $booking->status,
                'status_text' => $this->getStatusText($booking->status),
                'remarks' => $booking->remarks,
                'cancel_reason' => $booking->cancel_reason,
                'user' => $user ? [
                    'id' => $user->id,
                    'name' => $user->name,
                    'mobile' => $user->mobile,
                    'avatar' => $user->avatar,
                ] : null,
                'engineer' => $engineer ? [
                    'id' => $engineer->id,
                    'name' => $engineer->name,
                    'mobile' => $engineer->mobile,
                ] : null,
                'device' => $device ? [
                    'id' => $device->id,
                    'model' => $device->model,
                    'sn' => $device->sn,
                ] : null,
                'logs' => $logs,
                'created_at' => $booking->created_at,
                'updated_at' => $booking->updated_at,
            ];
            
            // 返回数据
            return response()->json([
                'code' => 0,
                'message' => '获取安装预约详情成功',
                'data' => $data
            ]);
        } catch (\Exception $e) {
            Log::error('获取安装预约详情失败: ' . $e->getMessage());
            return response()->json([
                'code' => 1,
                'message' => '获取安装预约详情失败: ' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    /**
     * 更新净水器安装预约状态
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateStatus(Request $request)
    {
        try {
            // 验证请求数据
            $validator = Validator::make($request->all(), [
                'id' => 'required|integer|exists:install_bookings,id',
                'status' => 'required|string|in:pending,confirmed,assigned,completed,cancelled',
                'engineer_id' => 'required_if:status,assigned|nullable|integer|exists:installation_engineers,id',
                'cancel_reason' => 'required_if:status,cancelled|nullable|string|max:255',
                'device_id' => 'nullable|integer|exists:tapp_devices,id',
                'device_model' => 'nullable|string|max:50',
                'device_sn' => 'nullable|string|max:50',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'code' => 1,
                    'message' => $validator->errors()->first(),
                    'data' => null
                ], 422);
            }

            // 获取预约ID和新状态
            $id = $request->input('id');
            $status = $request->input('status');
            
            // 查询预约详情
            $booking = DB::table('install_bookings')->where('id', $id)->first();
            
            if (!$booking) {
                return response()->json([
                    'code' => 1,
                    'message' => '安装预约不存在',
                    'data' => null
                ], 404);
            }
            
            // 记录原始状态，用于日志
            $originalStatus = $booking->status;
            
            // 准备更新数据
            $updateData = [
                'status' => $status,
                'updated_at' => now()
            ];
            
            // 根据状态设置额外数据
            if ($status === 'assigned' && $request->has('engineer_id')) {
                $updateData['engineer_id'] = $request->input('engineer_id');
            }
            
            if ($status === 'cancelled' && $request->has('cancel_reason')) {
                $updateData['cancel_reason'] = $request->input('cancel_reason');
            }
            
            if ($status === 'completed') {
                if ($request->has('device_id')) {
                    $updateData['device_id'] = $request->input('device_id');
                }
                
                if ($request->has('device_model')) {
                    $updateData['device_model'] = $request->input('device_model');
                }
                
                if ($request->has('device_sn')) {
                    $updateData['device_sn'] = $request->input('device_sn');
                }
                
                $updateData['completed_at'] = now();
            }
            
            // 执行更新
            DB::table('install_bookings')->where('id', $id)->update($updateData);
            
            // 记录操作日志
            $logDescription = "状态从 {$this->getStatusText($originalStatus)} 变更为 {$this->getStatusText($status)}";
            
            if ($status === 'assigned' && $request->has('engineer_id')) {
                $engineer = DB::table('installation_engineers')->where('id', $request->input('engineer_id'))->first();
                if ($engineer) {
                    $logDescription .= "，指派工程师：{$engineer->name}";
                }
            }
            
            if ($status === 'cancelled' && $request->has('cancel_reason')) {
                $logDescription .= "，取消原因：{$request->input('cancel_reason')}";
            }
            
            if ($status === 'completed') {
                if ($request->has('device_model') || $request->has('device_sn')) {
                    $logDescription .= "，设备信息：";
                    if ($request->has('device_model')) {
                        $logDescription .= "型号 {$request->input('device_model')} ";
                    }
                    if ($request->has('device_sn')) {
                        $logDescription .= "序列号 {$request->input('device_sn')}";
                    }
                }
            }
            
            DB::table('install_booking_logs')->insert([
                'booking_id' => $id,
                'admin_id' => auth()->id(),
                'action' => 'update_status',
                'description' => $logDescription,
                'created_at' => now(),
                'updated_at' => now(),
            ]);
            
            return response()->json([
                'code' => 0,
                'message' => '更新安装预约状态成功',
                'data' => [
                    'id' => $id,
                    'status' => $status,
                    'status_text' => $this->getStatusText($status)
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('更新安装预约状态失败: ' . $e->getMessage());
            return response()->json([
                'code' => 1,
                'message' => '更新安装预约状态失败: ' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    /**
     * 获取状态文本
     *
     * @param string $status
     * @return string
     */
    private function getStatusText($status)
    {
        $statusMap = [
            'pending' => '待确认',
            'confirmed' => '已确认',
            'assigned' => '已指派',
            'completed' => '已完成',
            'cancelled' => '已取消'
        ];
        
        return $statusMap[$status] ?? $status;
    }
}
