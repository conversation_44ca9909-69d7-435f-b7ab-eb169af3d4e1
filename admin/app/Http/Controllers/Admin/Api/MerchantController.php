<?php

namespace App\Http\Controllers\Admin\Api;

use App\Http\Controllers\Controller;
use App\Models\Merchant;
use App\Models\AppUser;
use App\Models\MerchantLog;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class MerchantController extends Controller
{
    /**
     * 获取商户列表
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        try {
            // 获取查询参数
            $page = $request->input('page', 1);
            $limit = $request->input('limit', 10);
            $status = $request->input('status');
            $keyword = $request->input('keyword');
            $sortField = $request->input('sort_field', 'created_at');
            $sortOrder = $request->input('sort_order', 'desc');

            // 验证参数
            if ($page < 1) $page = 1;
            if ($limit < 1 || $limit > 100) $limit = 10;

            // 允许的排序字段
            $allowedSortFields = ['id', 'name', 'status', 'balance', 'created_at', 'updated_at'];
            if (!in_array($sortField, $allowedSortFields)) {
                $sortField = 'created_at';
            }

            // 允许的排序方向
            $allowedSortOrders = ['asc', 'desc'];
            if (!in_array($sortOrder, $allowedSortOrders)) {
                $sortOrder = 'desc';
            }

            // 构建查询
            $query = Merchant::query();

            // 应用过滤条件
            if ($status) {
                $query->where('status', $status);
            }

            if ($keyword) {
                $query->where(function($q) use ($keyword) {
                    $q->where('name', 'like', "%{$keyword}%")
                      ->orWhere('merchant_id', 'like', "%{$keyword}%")
                      ->orWhere('principal_name', 'like', "%{$keyword}%")
                      ->orWhere('principal_mobile', 'like', "%{$keyword}%");
                });
            }

            // 获取总记录数
            $total = $query->count();

            // 应用排序和分页
            $merchants = $query->orderBy($sortField, $sortOrder)
                              ->skip(($page - 1) * $limit)
                              ->take($limit)
                              ->get();

            // 格式化数据
            $items = [];
            foreach ($merchants as $merchant) {
                // 获取交易统计
                $tradeStats = [
                    'total_count' => $merchant->trades()->where('status', 'success')->count(),
                    'total_amount' => floatval($merchant->trades()->where('status', 'success')->sum('amount')),
                    'today_amount' => floatval($merchant->trades()->where('status', 'success')->whereDate('pay_time', date('Y-m-d'))->sum('amount')),
                    'month_amount' => floatval($merchant->trades()->where('status', 'success')->whereMonth('pay_time', date('m'))->whereYear('pay_time', date('Y'))->sum('amount')),
                ];

                $items[] = [
                    'id' => $merchant->id,
                    'merchant_id' => $merchant->merchant_id,
                    'name' => $merchant->name,
                    'principal_name' => $merchant->principal_name,
                    'principal_mobile' => $merchant->principal_mobile,
                    'logo' => $merchant->logo,
                    'status' => $merchant->status,
                    'status_text' => $merchant->status_text,
                    'fee_rate' => floatval($merchant->fee_rate),
                    'trade_stats' => $tradeStats,
                    'has_app_user' => !empty($merchant->app_user_id),
                    'created_at' => $merchant->created_at ? $merchant->created_at->format('Y-m-d H:i:s') : null,
                    'updated_at' => $merchant->updated_at ? $merchant->updated_at->format('Y-m-d H:i:s') : null,
                ];
            }

            // 返回数据
            return response()->json([
                'code' => 0,
                'message' => '获取商户列表成功',
                'data' => [
                    'total' => $total,
                    'page' => (int)$page,
                    'limit' => (int)$limit,
                    'items' => $items
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('获取商户列表失败: ' . $e->getMessage());
            return response()->json([
                'code' => 1,
                'message' => '获取商户列表失败: ' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    /**
     * 获取商户详情
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show(Request $request, $id)
    {
        try {
            // 根据ID查询商户
            $merchant = Merchant::find($id);

            if (!$merchant) {
                return response()->json([
                    'code' => 1,
                    'message' => '商户不存在',
                    'data' => null
                ], 404);
            }

            // 获取关联的APP用户信息
            $appUser = null;
            if ($merchant->app_user_id) {
                $appUser = AppUser::find($merchant->app_user_id);
            }

            // 获取月交易统计
            $currentMonth = date('Y-m-01');
            $monthlyStats = [
                'trade_count' => $merchant->trades()->where('status', 'success')->where('pay_time', '>=', $currentMonth)->count(),
                'trade_amount' => floatval($merchant->trades()->where('status', 'success')->where('pay_time', '>=', $currentMonth)->sum('amount')),
                'income' => floatval($merchant->trades()->where('status', 'success')->where('pay_time', '>=', $currentMonth)->sum('actual_amount')),
            ];

            // 获取日交易统计 - 最近7天
            $dailyStats = [];
            for ($i = 6; $i >= 0; $i--) {
                $date = date('Y-m-d', strtotime("-{$i} days"));
                $dailyStats[] = [
                    'date' => $date,
                    'amount' => floatval($merchant->trades()->where('status', 'success')->whereDate('pay_time', $date)->sum('amount')),
                    'count' => $merchant->trades()->where('status', 'success')->whereDate('pay_time', $date)->count(),
                ];
            }

            // 格式化数据
            $data = [
                'id' => $merchant->id,
                'merchant_id' => $merchant->merchant_id,
                'name' => $merchant->name,
                'principal_name' => $merchant->principal_name,
                'principal_mobile' => $merchant->principal_mobile,
                'business_license' => $merchant->business_license,
                'id_card_number' => $merchant->id_card_number,
                'contact_address' => $merchant->contact_address,
                'business_category' => $merchant->business_category,
                'logo' => $merchant->logo,
                'qrcode' => $merchant->qrcode,
                'status' => $merchant->status,
                'status_text' => $merchant->status_text,
                'reject_reason' => $merchant->reject_reason,
                'fee_rate' => floatval($merchant->fee_rate),
                'bank_info' => $merchant->bank_info,
                'qualification_info' => $merchant->qualification_info,
                'additional_info' => $merchant->additional_info,
                'app_user' => $appUser ? [
                    'id' => $appUser->id,
                    'name' => $appUser->name,
                    'mobile' => $appUser->mobile,
                    'avatar' => $appUser->avatar,
                    'status' => $appUser->status,
                ] : null,
                'monthly_stats' => $monthlyStats,
                'daily_stats' => $dailyStats,
                'yearly_stats' => [
                    'trade_count' => $merchant->trades()->where('status', 'success')->whereYear('pay_time', date('Y'))->count(),
                    'trade_amount' => floatval($merchant->trades()->where('status', 'success')->whereYear('pay_time', date('Y'))->sum('amount')),
                    'income' => floatval($merchant->trades()->where('status', 'success')->whereYear('pay_time', date('Y'))->sum('actual_amount')),
                ],
                'total_stats' => [
                    'trade_count' => $merchant->trades()->where('status', 'success')->count(),
                    'trade_amount' => floatval($merchant->trades()->where('status', 'success')->sum('amount')),
                    'income' => floatval($merchant->trades()->where('status', 'success')->sum('actual_amount')),
                ],
                'payment_methods' => [
                    'alipay' => floatval($merchant->trades()->where('status', 'success')->where('payment_method', 'alipay')->sum('amount')),
                    'wechat' => floatval($merchant->trades()->where('status', 'success')->where('payment_method', 'wechat')->sum('amount')),
                    'other' => floatval($merchant->trades()->where('status', 'success')->whereNotIn('payment_method', ['alipay', 'wechat'])->sum('amount')),
                ],
                'created_at' => $merchant->created_at ? $merchant->created_at->format('Y-m-d H:i:s') : null,
                'updated_at' => $merchant->updated_at ? $merchant->updated_at->format('Y-m-d H:i:s') : null,
            ];

            return response()->json([
                'code' => 0,
                'message' => '获取商户详情成功',
                'data' => $data
            ]);
        } catch (\Exception $e) {
            Log::error('获取商户详情失败: ' . $e->getMessage());
            return response()->json([
                'code' => 1,
                'message' => '获取商户详情失败: ' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }
    /**
     * 创建商户
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        try {
            // 验证请求数据
            $validator = Validator::make($request->all(), [
                'name' => 'required|string|max:100|unique:merchants,name',
                'principal_name' => 'required|string|max:50',
                'principal_mobile' => 'required|string|regex:/^1[3-9]\d{9}$/',
                'fee_rate' => 'required|numeric|min:0|max:100',
                'logo' => 'nullable|string',
                'business_license' => 'nullable|string',
                'id_card_number' => 'nullable|string|max:18',
                'contact_address' => 'nullable|string|max:255',
                'business_category' => 'nullable|string|max:100',
                'bank_info' => 'nullable|array',
                'qualification_info' => 'nullable|array',
                'additional_info' => 'nullable|array',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'code' => 1,
                    'message' => $validator->errors()->first(),
                    'data' => null
                ], 422);
            }

            // 生成商户ID
            $merchantId = 'M' . date('YmdHis') . rand(1000, 9999);

            // 创建商户记录
            $merchant = new Merchant();
            $merchant->merchant_id = $merchantId;
            $merchant->name = $request->input('name');
            $merchant->logo = $request->input('logo', '');
            $merchant->principal_name = $request->input('principal_name');
            $merchant->principal_mobile = $request->input('principal_mobile');
            $merchant->business_license = $request->input('business_license', '');
            $merchant->id_card_number = $request->input('id_card_number', '');
            $merchant->contact_address = $request->input('contact_address', '');
            $merchant->business_category = $request->input('business_category', '');
            $merchant->fee_rate = $request->input('fee_rate');
            $merchant->status = 'pending';
            $merchant->bank_info = $request->input('bank_info', []);
            $merchant->qualification_info = $request->input('qualification_info', []);
            $merchant->additional_info = $request->input('additional_info', []);
            $merchant->save();

            // 记录操作日志
            MerchantLog::create([
                'merchant_id' => $merchant->id,
                'user_id' => auth()->id(),
                'user_type' => 'admin',
                'action' => 'create',
                'description' => "创建商户：{$merchant->name}",
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent()
            ]);

            return response()->json([
                'code' => 0,
                'message' => '创建商户成功',
                'data' => [
                    'id' => $merchant->id,
                    'merchant_id' => $merchant->merchant_id
                ]
            ], 201);
        } catch (\Exception $e) {
            Log::error('创建商户失败: ' . $e->getMessage());
            return response()->json([
                'code' => 1,
                'message' => '创建商户失败: ' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    /**
     * 更新商户信息
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id)
    {
        try {
            // 查找商户
            $merchant = Merchant::find($id);

            if (!$merchant) {
                return response()->json([
                    'code' => 1,
                    'message' => '商户不存在',
                    'data' => null
                ], 404);
            }

            // 验证请求数据
            $validator = Validator::make($request->all(), [
                'name' => 'sometimes|required|string|max:100|unique:merchants,name,' . $id,
                'principal_name' => 'sometimes|required|string|max:50',
                'principal_mobile' => 'sometimes|required|string|regex:/^1[3-9]\d{9}$/',
                'fee_rate' => 'sometimes|required|numeric|min:0|max:100',
                'logo' => 'nullable|string',
                'business_license' => 'nullable|string',
                'id_card_number' => 'nullable|string|max:18',
                'contact_address' => 'nullable|string|max:255',
                'business_category' => 'nullable|string|max:100',
                'status' => 'sometimes|required|in:pending,active,suspended,rejected',
                'reject_reason' => 'nullable|string|max:255',
                'bank_info' => 'nullable|array',
                'qualification_info' => 'nullable|array',
                'additional_info' => 'nullable|array',
                'app_user_id' => 'nullable|integer|exists:app_users,id',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'code' => 1,
                    'message' => $validator->errors()->first(),
                    'data' => null
                ], 422);
            }

            // 记录原始状态，用于判断状态是否变更
            $originalStatus = $merchant->status;

            // 更新商户信息
            $merchant->fill($request->only([
                'name', 'principal_name', 'principal_mobile', 'fee_rate', 'logo',
                'business_license', 'id_card_number', 'contact_address', 'business_category',
                'status', 'reject_reason', 'bank_info', 'qualification_info', 'additional_info',
                'app_user_id'
            ]));

            $merchant->save();

            // 记录操作日志
            $description = "更新商户信息：{$merchant->name}";

            // 如果状态发生变化，特别记录
            if ($originalStatus !== $merchant->status) {
                $description .= "，状态从 {$originalStatus} 变更为 {$merchant->status}";
            }

            MerchantLog::create([
                'merchant_id' => $merchant->id,
                'user_id' => auth()->id(),
                'user_type' => 'admin',
                'action' => 'update',
                'description' => $description,
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent()
            ]);

            return response()->json([
                'code' => 0,
                'message' => '更新商户信息成功',
                'data' => [
                    'id' => $merchant->id,
                    'merchant_id' => $merchant->merchant_id
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('更新商户信息失败: ' . $e->getMessage());
            return response()->json([
                'code' => 1,
                'message' => '更新商户信息失败: ' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    /**
     * 删除商户
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy(Request $request, $id)
    {
        try {
            // 查找商户
            $merchant = Merchant::find($id);

            if (!$merchant) {
                return response()->json([
                    'code' => 1,
                    'message' => '商户不存在',
                    'data' => null
                ], 404);
            }

            // 记录商户名称，用于日志
            $merchantName = $merchant->name;

            // 软删除商户
            $merchant->delete();

            // 记录操作日志
            MerchantLog::create([
                'merchant_id' => $id,
                'user_id' => auth()->id(),
                'user_type' => 'admin',
                'action' => 'delete',
                'description' => "删除商户：{$merchantName}",
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent()
            ]);

            return response()->json([
                'code' => 0,
                'message' => '删除商户成功',
                'data' => null
            ]);
        } catch (\Exception $e) {
            Log::error('删除商户失败: ' . $e->getMessage());
            return response()->json([
                'code' => 1,
                'message' => '删除商户失败: ' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    /**
     * 获取商户交易记录
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function trades(Request $request)
    {
        try {
            // 获取查询参数
            $merchantId = $request->input('merchant_id');
            $page = $request->input('page', 1);
            $limit = $request->input('limit', 10);
            $status = $request->input('status');
            $tradeType = $request->input('trade_type');
            $startDate = $request->input('start_date');
            $endDate = $request->input('end_date');
            $keyword = $request->input('keyword');

            // 验证商户ID
            if (!$merchantId) {
                return response()->json([
                    'code' => 1,
                    'message' => '缺少商户ID参数',
                    'data' => null
                ], 400);
            }

            // 查找商户
            $merchant = Merchant::find($merchantId);
            if (!$merchant) {
                return response()->json([
                    'code' => 1,
                    'message' => '商户不存在',
                    'data' => null
                ], 404);
            }

            // 构建查询
            $query = $merchant->trades();

            // 应用过滤条件
            if ($status) {
                $query->where('status', $status);
            }

            if ($tradeType) {
                $query->where('trade_type', $tradeType);
            }

            if ($startDate) {
                $query->whereDate('created_at', '>=', $startDate);
            }

            if ($endDate) {
                $query->whereDate('created_at', '<=', $endDate);
            }

            if ($keyword) {
                $query->where(function($q) use ($keyword) {
                    $q->where('trade_no', 'like', "%{$keyword}%")
                      ->orWhere('customer_name', 'like', "%{$keyword}%")
                      ->orWhere('customer_phone', 'like', "%{$keyword}%");
                });
            }

            // 获取总记录数
            $total = $query->count();

            // 应用排序和分页
            $trades = $query->orderBy('created_at', 'desc')
                          ->skip(($page - 1) * $limit)
                          ->take($limit)
                          ->get();

            // 返回数据
            return response()->json([
                'code' => 0,
                'message' => '获取商户交易记录成功',
                'data' => [
                    'total' => $total,
                    'page' => (int)$page,
                    'limit' => (int)$limit,
                    'items' => $trades
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('获取商户交易记录失败: ' . $e->getMessage());
            return response()->json([
                'code' => 1,
                'message' => '获取商户交易记录失败: ' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    /**
     * 获取商户结算记录
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function settlements(Request $request)
    {
        try {
            // 获取查询参数
            $merchantId = $request->input('merchant_id');
            $page = $request->input('page', 1);
            $limit = $request->input('limit', 10);
            $status = $request->input('status');
            $startDate = $request->input('start_date');
            $endDate = $request->input('end_date');

            // 验证商户ID
            if (!$merchantId) {
                return response()->json([
                    'code' => 1,
                    'message' => '缺少商户ID参数',
                    'data' => null
                ], 400);
            }

            // 查找商户
            $merchant = Merchant::find($merchantId);
            if (!$merchant) {
                return response()->json([
                    'code' => 1,
                    'message' => '商户不存在',
                    'data' => null
                ], 404);
            }

            // 构建查询
            $query = $merchant->settlements();

            // 应用过滤条件
            if ($status) {
                $query->where('status', $status);
            }

            if ($startDate) {
                $query->whereDate('created_at', '>=', $startDate);
            }

            if ($endDate) {
                $query->whereDate('created_at', '<=', $endDate);
            }

            // 获取总记录数
            $total = $query->count();

            // 应用排序和分页
            $settlements = $query->orderBy('created_at', 'desc')
                               ->skip(($page - 1) * $limit)
                               ->take($limit)
                               ->get();

            // 返回数据
            return response()->json([
                'code' => 0,
                'message' => '获取商户结算记录成功',
                'data' => [
                    'total' => $total,
                    'page' => (int)$page,
                    'limit' => (int)$limit,
                    'items' => $settlements
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('获取商户结算记录失败: ' . $e->getMessage());
            return response()->json([
                'code' => 1,
                'message' => '获取商户结算记录失败: ' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    /**
     * 创建商户结算记录
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function createSettlement(Request $request)
    {
        try {
            // 验证请求数据
            $validator = Validator::make($request->all(), [
                'merchant_id' => 'required|exists:merchants,id',
                'amount' => 'required|numeric|min:0.01',
                'remark' => 'nullable|string|max:255',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'code' => 1,
                    'message' => $validator->errors()->first(),
                    'data' => null
                ], 422);
            }

            // 查找商户
            $merchant = Merchant::find($request->input('merchant_id'));
            if (!$merchant) {
                return response()->json([
                    'code' => 1,
                    'message' => '商户不存在',
                    'data' => null
                ], 404);
            }

            // 创建结算记录
            $settlement = $merchant->settlements()->create([
                'amount' => $request->input('amount'),
                'status' => 'pending',
                'remark' => $request->input('remark', ''),
                'admin_id' => auth()->id(),
                'settlement_no' => 'S' . date('YmdHis') . rand(1000, 9999),
            ]);

            // 记录操作日志
            MerchantLog::create([
                'merchant_id' => $merchant->id,
                'user_id' => auth()->id(),
                'user_type' => 'admin',
                'action' => 'create_settlement',
                'description' => "创建商户结算记录：{$merchant->name}，金额：{$request->input('amount')}",
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent()
            ]);

            return response()->json([
                'code' => 0,
                'message' => '创建商户结算记录成功',
                'data' => [
                    'id' => $settlement->id,
                    'settlement_no' => $settlement->settlement_no
                ]
            ], 201);
        } catch (\Exception $e) {
            Log::error('创建商户结算记录失败: ' . $e->getMessage());
            return response()->json([
                'code' => 1,
                'message' => '创建商户结算记录失败: ' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    /**
     * 获取商户统计数据
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function stats(Request $request)
    {
        try {
            // 获取查询参数
            $merchantId = $request->input('merchant_id');
            $startDate = $request->input('start_date');
            $endDate = $request->input('end_date');

            // 验证商户ID
            if (!$merchantId) {
                return response()->json([
                    'code' => 1,
                    'message' => '缺少商户ID参数',
                    'data' => null
                ], 400);
            }

            // 查找商户
            $merchant = Merchant::find($merchantId);
            if (!$merchant) {
                return response()->json([
                    'code' => 1,
                    'message' => '商户不存在',
                    'data' => null
                ], 404);
            }

            // 构建查询
            $query = $merchant->trades()->where('status', 'success');

            // 应用日期过滤
            if ($startDate) {
                $query->whereDate('created_at', '>=', $startDate);
            }

            if ($endDate) {
                $query->whereDate('created_at', '<=', $endDate);
            }

            // 获取统计数据
            $totalAmount = $query->sum('amount');
            $totalCount = $query->count();
            $totalFee = $query->sum('fee');
            $totalActualAmount = $query->sum('actual_amount');

            // 获取支付方式统计
            $paymentMethodStats = $query->select('payment_method', DB::raw('count(*) as count'), DB::raw('sum(amount) as amount'))
                                      ->groupBy('payment_method')
                                      ->get();

            // 获取每日交易统计 - 最近30天
            $dailyStats = [];
            for ($i = 29; $i >= 0; $i--) {
                $date = date('Y-m-d', strtotime("-{$i} days"));
                $dailyStats[] = [
                    'date' => $date,
                    'amount' => floatval($merchant->trades()->where('status', 'success')->whereDate('created_at', $date)->sum('amount')),
                    'count' => $merchant->trades()->where('status', 'success')->whereDate('created_at', $date)->count(),
                ];
            }

            // 返回数据
            return response()->json([
                'code' => 0,
                'message' => '获取商户统计数据成功',
                'data' => [
                    'total_amount' => floatval($totalAmount),
                    'total_count' => $totalCount,
                    'total_fee' => floatval($totalFee),
                    'total_actual_amount' => floatval($totalActualAmount),
                    'payment_method_stats' => $paymentMethodStats,
                    'daily_stats' => $dailyStats
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('获取商户统计数据失败: ' . $e->getMessage());
            return response()->json([
                'code' => 1,
                'message' => '获取商户统计数据失败: ' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    /**
     * 导出商户数据
     *
     * @param Request $request
     * @param string $type 导出类型：trades, settlements
     * @return \Illuminate\Http\Response
     */
    public function export(Request $request, $type)
    {
        try {
            // 验证导出类型
            if (!in_array($type, ['trades', 'settlements'])) {
                return response()->json([
                    'code' => 1,
                    'message' => '不支持的导出类型',
                    'data' => null
                ], 400);
            }

            // 获取查询参数
            $merchantId = $request->input('merchant_id');
            $startDate = $request->input('start_date');
            $endDate = $request->input('end_date');
            $status = $request->input('status');

            // 验证商户ID
            if (!$merchantId) {
                return response()->json([
                    'code' => 1,
                    'message' => '缺少商户ID参数',
                    'data' => null
                ], 400);
            }

            // 查找商户
            $merchant = Merchant::find($merchantId);
            if (!$merchant) {
                return response()->json([
                    'code' => 1,
                    'message' => '商户不存在',
                    'data' => null
                ], 404);
            }

            // 根据类型导出不同的数据
            if ($type === 'trades') {
                // 导出交易记录
                return $this->exportTrades($merchant, $startDate, $endDate, $status);
            } else {
                // 导出结算记录
                return $this->exportSettlements($merchant, $startDate, $endDate, $status);
            }
        } catch (\Exception $e) {
            Log::error('导出商户数据失败: ' . $e->getMessage());
            return response()->json([
                'code' => 1,
                'message' => '导出商户数据失败: ' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    /**
     * 导出交易记录
     *
     * @param Merchant $merchant
     * @param string|null $startDate
     * @param string|null $endDate
     * @param string|null $status
     * @return \Illuminate\Http\Response
     */
    private function exportTrades($merchant, $startDate, $endDate, $status)
    {
        // 构建查询
        $query = $merchant->trades();

        // 应用过滤条件
        if ($status) {
            $query->where('status', $status);
        }

        if ($startDate) {
            $query->whereDate('created_at', '>=', $startDate);
        }

        if ($endDate) {
            $query->whereDate('created_at', '<=', $endDate);
        }

        // 获取数据
        $trades = $query->orderBy('created_at', 'desc')->get();

        // 导出CSV
        $filename = "merchant_{$merchant->id}_trades_" . date('YmdHis') . ".csv";
        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => "attachment; filename=\"{$filename}\"",
        ];

        $callback = function() use ($trades) {
            $file = fopen('php://output', 'w');

            // 添加CSV头
            fputcsv($file, [
                '交易号', '交易类型', '支付方式', '金额', '手续费', '实际金额', '状态', '创建时间', '支付时间'
            ]);

            // 添加数据行
            foreach ($trades as $trade) {
                fputcsv($file, [
                    $trade->trade_no,
                    $trade->trade_type,
                    $trade->payment_method,
                    $trade->amount,
                    $trade->fee,
                    $trade->actual_amount,
                    $trade->status,
                    $trade->created_at,
                    $trade->pay_time,
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    /**
     * 导出结算记录
     *
     * @param Merchant $merchant
     * @param string|null $startDate
     * @param string|null $endDate
     * @param string|null $status
     * @return \Illuminate\Http\Response
     */
    private function exportSettlements($merchant, $startDate, $endDate, $status)
    {
        // 构建查询
        $query = $merchant->settlements();

        // 应用过滤条件
        if ($status) {
            $query->where('status', $status);
        }

        if ($startDate) {
            $query->whereDate('created_at', '>=', $startDate);
        }

        if ($endDate) {
            $query->whereDate('created_at', '<=', $endDate);
        }

        // 获取数据
        $settlements = $query->orderBy('created_at', 'desc')->get();

        // 导出CSV
        $filename = "merchant_{$merchant->id}_settlements_" . date('YmdHis') . ".csv";
        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => "attachment; filename=\"{$filename}\"",
        ];

        $callback = function() use ($settlements) {
            $file = fopen('php://output', 'w');

            // 添加CSV头
            fputcsv($file, [
                '结算号', '金额', '状态', '备注', '创建时间', '完成时间'
            ]);

            // 添加数据行
            foreach ($settlements as $settlement) {
                fputcsv($file, [
                    $settlement->settlement_no,
                    $settlement->amount,
                    $settlement->status,
                    $settlement->remark,
                    $settlement->created_at,
                    $settlement->completed_at,
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }
}