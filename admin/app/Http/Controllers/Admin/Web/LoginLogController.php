<?php

namespace App\Http\Controllers\Admin\Web;

use App\Http\Controllers\Controller;
use App\Models\LoginLog;
use App\Models\AppUser;
use App\Models\Admin;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;

class LoginLogController extends Controller
{
    /**
     * 获取登录日志列表
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $page = $request->input('page', 1);
            $limit = $request->input('limit', 20);
            $keyword = $request->input('keyword', '');
            $loginMethod = $request->input('loginMethod', '');
            $status = $request->input('status', '');
            $startDate = $request->input('startDate', '');
            $endDate = $request->input('endDate', '');
            $userType = $request->input('userType', ''); // 新增用户类型筛选

            // 构建查询
            $query = LoginLog::with(['appUser', 'adminUser'])
                ->orderBy('created_at', 'desc');

            // 关键词搜索
            if (!empty($keyword)) {
                $query->search($keyword);
            }

            // 登录方式筛选
            if (!empty($loginMethod)) {
                $query->byLoginMethod($loginMethod);
            }

            // 状态筛选
            if (!empty($status)) {
                $query->byStatus($status);
            }

            // 用户类型筛选
            if (!empty($userType)) {
                $query->byUserType($userType);
            }

            // 日期范围筛选
            if (!empty($startDate) && !empty($endDate)) {
                $query->byDateRange($startDate, $endDate);
            }

            // 分页查询
            $total = $query->count();
            $logs = $query->offset(($page - 1) * $limit)
                         ->limit($limit)
                         ->get();

            // 格式化数据
            $formattedLogs = $logs->map(function ($log) {
                $userInfo = null;
                
                if ($log->user_type === 'app_user' && $log->appUser) {
                    $userInfo = [
                        'id' => $log->appUser->id,
                        'name' => $log->appUser->name,
                        'phone' => $log->appUser->phone,
                        'wechat_nickname' => $log->appUser->wechat_nickname,
                        'created_at' => $log->appUser->created_at,
                    ];
                } elseif ($log->user_type === 'admin_user' && $log->adminUser) {
                    $userInfo = [
                        'id' => $log->adminUser->id,
                        'name' => $log->adminUser->name,
                        'username' => $log->adminUser->username,
                        'created_at' => $log->adminUser->created_at,
                    ];
                }

                return [
                    'id' => $log->id,
                    'user_id' => $log->user_id,
                    'user_type' => $log->user_type,
                    'user_info' => $userInfo,
                    'login_method' => $log->login_method,
                    'ip_address' => $log->ip_address,
                    'user_agent' => $log->user_agent,
                    'status' => $log->status,
                    'message' => $log->message,
                    'created_at' => $log->created_at->format('Y-m-d H:i:s'),
                    'updated_at' => $log->updated_at->format('Y-m-d H:i:s'),
                ];
            });

            return response()->json([
                'code' => 0,
                'message' => '获取登录日志成功',
                'data' => [
                    'list' => $formattedLogs,
                    'total' => $total,
                    'page' => $page,
                    'limit' => $limit,
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'code' => 1,
                'message' => '获取登录日志失败：' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    /**
     * 获取登录日志详情
     *
     * @param int $id
     * @return JsonResponse
     */
    public function show(int $id): JsonResponse
    {
        try {
            $log = LoginLog::with(['appUser', 'adminUser'])->find($id);

            if (!$log) {
                return response()->json([
                    'code' => 1,
                    'message' => '登录日志不存在',
                    'data' => null
                ], 404);
            }

            $userInfo = null;
            if ($log->user_type === 'app_user' && $log->appUser) {
                $userInfo = $log->appUser;
            } elseif ($log->user_type === 'admin_user' && $log->adminUser) {
                $userInfo = $log->adminUser;
            }

            return response()->json([
                'code' => 0,
                'message' => '获取登录日志详情成功',
                'data' => [
                    'id' => $log->id,
                    'user_id' => $log->user_id,
                    'user_type' => $log->user_type,
                    'user_info' => $userInfo,
                    'login_method' => $log->login_method,
                    'ip_address' => $log->ip_address,
                    'user_agent' => $log->user_agent,
                    'status' => $log->status,
                    'message' => $log->message,
                    'created_at' => $log->created_at->format('Y-m-d H:i:s'),
                    'updated_at' => $log->updated_at->format('Y-m-d H:i:s'),
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'code' => 1,
                'message' => '获取登录日志详情失败：' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    /**
     * 获取登录统计信息
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function statistics(Request $request): JsonResponse
    {
        try {
            $startDate = $request->input('startDate', now()->subDays(7)->format('Y-m-d'));
            $endDate = $request->input('endDate', now()->format('Y-m-d'));

            // 总登录次数
            $totalLogins = LoginLog::byDateRange($startDate, $endDate)->count();

            // 成功登录次数
            $successLogins = LoginLog::byDateRange($startDate, $endDate)
                ->byStatus('success')
                ->count();

            // 失败登录次数
            $failedLogins = LoginLog::byDateRange($startDate, $endDate)
                ->byStatus('failed')
                ->count();

            // 按登录方式统计
            $loginMethodStats = LoginLog::byDateRange($startDate, $endDate)
                ->select('login_method', DB::raw('count(*) as count'))
                ->groupBy('login_method')
                ->get();

            // 按用户类型统计
            $userTypeStats = LoginLog::byDateRange($startDate, $endDate)
                ->select('user_type', DB::raw('count(*) as count'))
                ->groupBy('user_type')
                ->get();

            // 按日期统计（最近7天）
            $dailyStats = LoginLog::byDateRange($startDate, $endDate)
                ->select(
                    DB::raw('DATE(created_at) as date'),
                    DB::raw('count(*) as total'),
                    DB::raw('sum(case when status = "success" then 1 else 0 end) as success'),
                    DB::raw('sum(case when status = "failed" then 1 else 0 end) as failed')
                )
                ->groupBy(DB::raw('DATE(created_at)'))
                ->orderBy('date', 'asc')
                ->get();

            return response()->json([
                'code' => 0,
                'message' => '获取登录统计成功',
                'data' => [
                    'summary' => [
                        'total_logins' => $totalLogins,
                        'success_logins' => $successLogins,
                        'failed_logins' => $failedLogins,
                        'success_rate' => $totalLogins > 0 ? round(($successLogins / $totalLogins) * 100, 2) : 0,
                    ],
                    'login_method_stats' => $loginMethodStats,
                    'user_type_stats' => $userTypeStats,
                    'daily_stats' => $dailyStats,
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'code' => 1,
                'message' => '获取登录统计失败：' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    /**
     * 删除登录日志（批量删除）
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function destroy(Request $request): JsonResponse
    {
        try {
            $ids = $request->input('ids', []);
            
            if (empty($ids)) {
                return response()->json([
                    'code' => 1,
                    'message' => '请选择要删除的日志',
                    'data' => null
                ], 400);
            }

            $deletedCount = LoginLog::whereIn('id', $ids)->delete();

            return response()->json([
                'code' => 0,
                'message' => "成功删除 {$deletedCount} 条登录日志",
                'data' => [
                    'deleted_count' => $deletedCount
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'code' => 1,
                'message' => '删除登录日志失败：' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    /**
     * 清理过期日志
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function cleanup(Request $request): JsonResponse
    {
        try {
            $days = $request->input('days', 90); // 默认清理90天前的日志
            
            $cutoffDate = now()->subDays($days);
            $deletedCount = LoginLog::where('created_at', '<', $cutoffDate)->delete();

            return response()->json([
                'code' => 0,
                'message' => "成功清理 {$deletedCount} 条过期登录日志",
                'data' => [
                    'deleted_count' => $deletedCount,
                    'cutoff_date' => $cutoffDate->format('Y-m-d H:i:s')
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'code' => 1,
                'message' => '清理过期日志失败：' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }
} 