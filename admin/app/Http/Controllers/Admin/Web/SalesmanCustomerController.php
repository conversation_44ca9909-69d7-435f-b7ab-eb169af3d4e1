<?php

namespace App\Http\Controllers\Admin\Web;

use App\Http\Controllers\Controller;
use App\Models\Salesman;
use App\Models\SalesmanCustomer;
use Illuminate\Http\Request;

class SalesmanCustomerController extends Controller
{
    /**
     * 显示业务员客户列表
     */
    public function index(Request $request, Salesman $salesman)
    {
        $query = $salesman->customers();
        
        // 搜索条件
        if ($request->has('search')) {
            $search = $request->input('search');
            $query->where(function ($q) use ($search) {
                $q->where('customer_name', 'like', "%{$search}%")
                  ->orWhere('customer_phone', 'like', "%{$search}%");
            });
        }
        
        // 状态筛选
        if ($request->has('status') && $request->input('status') !== 'all') {
            $query->where('status', $request->input('status'));
        }
        
        // 排序
        $sortField = $request->input('sort', 'created_at');
        $sortDirection = $request->input('direction', 'desc');
        $query->orderBy($sortField, $sortDirection);
        
        $customers = $query->paginate(15);
        
        return view('admin.salesmen.customers.index', compact('salesman', 'customers'));
    }
    
    /**
     * 显示创建客户表单
     */
    public function create(Salesman $salesman)
    {
        return view('admin.salesmen.customers.create', compact('salesman'));
    }
    
    /**
     * 存储新客户
     */
    public function store(Request $request, Salesman $salesman)
    {
        $this->validate($request, [
            'customer_name' => 'required|string|max:100',
            'customer_phone' => 'nullable|string|max:20',
            'customer_address' => 'nullable|string|max:255',
            'source' => 'nullable|string|max:100',
            'status' => 'required|in:potential,active,inactive',
            'deal_count' => 'nullable|integer|min:0',
            'total_amount' => 'nullable|numeric|min:0',
            'last_purchase_date' => 'nullable|date',
            'remarks' => 'nullable|string|max:1000',
        ]);
        
        try {
            // 创建客户记录
            $customer = new SalesmanCustomer();
            $customer->salesman_id = $salesman->id;
            $customer->customer_name = $request->input('customer_name');
            $customer->customer_phone = $request->input('customer_phone');
            $customer->customer_address = $request->input('customer_address');
            $customer->source = $request->input('source');
            $customer->status = $request->input('status');
            $customer->deal_count = $request->input('deal_count', 0);
            $customer->total_amount = $request->input('total_amount', 0);
            $customer->last_purchase_date = $request->input('last_purchase_date');
            $customer->remarks = $request->input('remarks');
            $customer->save();
            
            return redirect()->route('admin.salesmen.customers.index', $salesman)
                ->with('success', '客户创建成功');
                
        } catch (\Exception $e) {
            return back()->withErrors(['error' => '客户创建失败：'.$e->getMessage()])
                ->withInput();
        }
    }
}
