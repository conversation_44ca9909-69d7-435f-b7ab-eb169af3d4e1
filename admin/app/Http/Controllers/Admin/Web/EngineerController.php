<?php

namespace App\Http\Controllers\Admin\Web;

use App\Http\Controllers\Controller;
use App\Models\WaterMaster;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class EngineerController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        try {
            $query = DB::table('installation_engineers');

            // 关键词搜索
            if ($request->has('keyword') && $request->keyword) {
                $keyword = $request->keyword;
                $query->where(function ($q) use ($keyword) {
                    $q->where('name', 'like', "%{$keyword}%")
                      ->orWhere('phone', 'like', "%{$keyword}%")
                      ->orWhere('area', 'like', "%{$keyword}%");
                });
            }

            // 状态筛选
            if ($request->has('status') && $request->status) {
                $query->where('status', $request->status);
            }

            // 级别筛选
            if ($request->has('level') && $request->level) {
                $query->where('level', $request->level);
            }

            // 获取分页数据
            $page = $request->input('page', 1);
            $limit = $request->input('limit', 10);

            // 计算总数
            $total = $query->count();

            // 获取分页数据
            $engineers = $query->orderBy('created_at', 'desc')
                ->offset(($page - 1) * $limit)
                ->limit($limit)
                ->get();

            // 更新工程师的安装数量
            foreach ($engineers as $engineer) {
                try {
                    // 获取工程师完成的安装数量
                    $completedInstallations = DB::table('install_bookings')
                        ->where('engineer_id', $engineer->id)
                        ->where('status', 'completed')
                        ->count();

                    // 记录计算结果
                    Log::info("工程师ID {$engineer->id} 的完成安装数量: {$completedInstallations}");

                    // 更新工程师安装数量
                    $engineer->completed_installations = $completedInstallations;

                    // 同时更新数据库中的安装数量
                    DB::table('installation_engineers')
                        ->where('id', $engineer->id)
                        ->update(['completed_installations' => $completedInstallations]);
                } catch (\Exception $e) {
                    Log::error("更新工程师ID {$engineer->id} 的安装数量失败: " . $e->getMessage());
                }
            }

            return response()->json([
                'code' => 0,
                'message' => '获取工程师列表成功',
                'data' => [
                    'list' => $engineers,
                    'pagination' => [
                        'total' => $total,
                        'page' => (int)$page,
                        'limit' => (int)$limit
                    ]
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('获取工程师列表失败: ' . $e->getMessage());
            return response()->json([
                'code' => 1,
                'message' => '获取工程师列表失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        try {
            // 验证请求数据
            $validator = Validator::make($request->all(), [
                'name' => 'required|string|max:50',
                'phone' => 'required|string|max:20',
                'area' => 'nullable|string|max:100',
                'level' => 'nullable|string|in:junior,normal,senior',
                'skills' => 'nullable|string',
                'status' => 'nullable|string|in:active,busy,leave,disabled',
                'remark' => 'nullable|string'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'code' => 1,
                    'message' => '数据验证失败',
                    'errors' => $validator->errors()
                ]);
            }

            // 准备数据
            $data = [
                'name' => $request->name,
                'phone' => $request->phone,
                'area' => $request->area,
                'level' => $request->level ?? 'normal',
                'skills' => $request->skills,
                'status' => $request->status ?? 'active',
                'remark' => $request->remark,
                'created_at' => now(),
                'updated_at' => now()
            ];

            // 插入数据
            $id = DB::table('installation_engineers')->insertGetId($data);

            return response()->json([
                'code' => 0,
                'message' => '添加工程师成功',
                'data' => [
                    'id' => $id
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('添加工程师失败: ' . $e->getMessage());
            return response()->json([
                'code' => 1,
                'message' => '添加工程师失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show($id)
    {
        try {
            $engineer = DB::table('installation_engineers')->where('id', $id)->first();

            if (!$engineer) {
                return response()->json([
                    'code' => 1,
                    'message' => '工程师不存在'
                ]);
            }

            // 获取工程师完成的安装数量
            $completedInstallations = DB::table('install_bookings')
                ->where('engineer_id', $id)
                ->where('status', 'completed')
                ->count();

            // 获取工程师当前进行中的安装数量
            $inProgressInstallations = DB::table('install_bookings')
                ->where('engineer_id', $id)
                ->whereIn('status', ['assigned', 'in_progress'])
                ->count();

            $engineer->completed_installations = $completedInstallations;
            $engineer->in_progress_installations = $inProgressInstallations;

            return response()->json([
                'code' => 0,
                'message' => '获取工程师详情成功',
                'data' => $engineer
            ]);
        } catch (\Exception $e) {
            Log::error('获取工程师详情失败: ' . $e->getMessage());
            return response()->json([
                'code' => 1,
                'message' => '获取工程师详情失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        try {
            // 检查工程师是否存在
            $engineer = DB::table('installation_engineers')->where('id', $id)->first();
            if (!$engineer) {
                return response()->json([
                    'code' => 1,
                    'message' => '工程师不存在'
                ]);
            }

            // 验证请求数据
            $validator = Validator::make($request->all(), [
                'name' => 'nullable|string|max:50',
                'phone' => 'nullable|string|max:20',
                'area' => 'nullable|string|max:100',
                'level' => 'nullable|string|in:junior,normal,senior',
                'skills' => 'nullable|string',
                'status' => 'nullable|string|in:active,busy,leave,disabled',
                'remark' => 'nullable|string'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'code' => 1,
                    'message' => '数据验证失败',
                    'errors' => $validator->errors()
                ]);
            }

            // 准备更新数据
            $data = [];
            if ($request->has('name')) $data['name'] = $request->name;
            if ($request->has('phone')) $data['phone'] = $request->phone;
            if ($request->has('area')) $data['area'] = $request->area;
            if ($request->has('level')) $data['level'] = $request->level;
            if ($request->has('skills')) $data['skills'] = $request->skills;
            if ($request->has('status')) $data['status'] = $request->status;
            if ($request->has('remark')) $data['remark'] = $request->remark;
            $data['updated_at'] = now();

            // 更新数据
            DB::table('installation_engineers')->where('id', $id)->update($data);

            return response()->json([
                'code' => 0,
                'message' => '更新工程师成功'
            ]);
        } catch (\Exception $e) {
            Log::error('更新工程师失败: ' . $e->getMessage());
            return response()->json([
                'code' => 1,
                'message' => '更新工程师失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        try {
            // 检查工程师是否存在
            $engineer = DB::table('installation_engineers')->where('id', $id)->first();
            if (!$engineer) {
                return response()->json([
                    'code' => 1,
                    'message' => '工程师不存在'
                ]);
            }

            // 检查是否有关联的安装预约
            $hasBookings = DB::table('install_bookings')
                ->where('engineer_id', $id)
                ->whereIn('status', ['pending', 'confirmed', 'assigned', 'in_progress'])
                ->exists();

            if ($hasBookings) {
                return response()->json([
                    'code' => 1,
                    'message' => '该工程师有进行中的安装预约，无法删除'
                ]);
            }

            // 删除工程师
            DB::table('installation_engineers')->where('id', $id)->delete();

            return response()->json([
                'code' => 0,
                'message' => '删除工程师成功'
            ]);
        } catch (\Exception $e) {
            Log::error('删除工程师失败: ' . $e->getMessage());
            return response()->json([
                'code' => 1,
                'message' => '删除工程师失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 获取可用的工程师列表（用于派工）
     */
    public function getAvailableEngineers()
    {
        try {
            $engineers = DB::table('installation_engineers')
                ->where('status', 'active')
                ->orWhere('status', 'busy')
                ->select('id', 'name', 'phone', 'area', 'level', 'status', 'completed_installations')
                ->orderBy('level', 'desc')
                ->orderBy('completed_installations', 'desc')
                ->get();

            // 更新工程师的安装数量
            foreach ($engineers as $engineer) {
                try {
                    // 获取工程师完成的安装数量
                    $completedInstallations = DB::table('install_bookings')
                        ->where('engineer_id', $engineer->id)
                        ->where('status', 'completed')
                        ->count();

                    // 记录计算结果
                    Log::info("可用工程师ID {$engineer->id} 的完成安装数量: {$completedInstallations}");

                    // 更新工程师安装数量
                    $engineer->completed_installations = $completedInstallations;

                    // 同时更新数据库中的安装数量
                    DB::table('installation_engineers')
                        ->where('id', $engineer->id)
                        ->update(['completed_installations' => $completedInstallations]);
                } catch (\Exception $e) {
                    Log::error("更新可用工程师ID {$engineer->id} 的安装数量失败: " . $e->getMessage());
                }
            }

            return response()->json([
                'code' => 0,
                'message' => '获取可用工程师列表成功',
                'data' => $engineers
            ]);
        } catch (\Exception $e) {
            Log::error('获取可用工程师列表失败: ' . $e->getMessage());
            return response()->json([
                'code' => 1,
                'message' => '获取可用工程师列表失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 从净水器数据库同步工程师数据
     */
    public function syncFromWaterDb()
    {
        try {
            // 检查数据库连接
            try {
                Log::info('开始查询净水器数据库中的工程师数据');

                // 先查询 dealer_number 为 "00000019" 的渠道商 ID
                $dealer = \DB::connection('water_db')
                    ->table('wb_dealer')
                    ->where('dealer_number', '00000019')
                    ->first();

                if (!$dealer) {
                    Log::error('未找到 dealer_number 为 00000019 的渠道商');
                    return response()->json([
                        'code' => 1,
                        'message' => '未找到指定的渠道商，无法同步工程师数据'
                    ]);
                }

                Log::info('找到渠道商: ' . $dealer->dealer_name . ', ID: ' . $dealer->id);

                // 只同步指定渠道商的工程师
                $waterMasters = WaterMaster::where('status', 'E')
                    ->where('dealer_id', $dealer->id)
                    ->get();

                Log::info('查询到的工程师数据数量: ' . $waterMasters->count());

                // 输出前三条数据作为样本
                if ($waterMasters->count() > 0) {
                    $sampleData = $waterMasters->take(3)->toArray();
                    Log::info('样本数据: ' . json_encode($sampleData, JSON_UNESCAPED_UNICODE));
                }
            } catch (\Exception $e) {
                Log::error('连接净水器数据库失败: ' . $e->getMessage());
                return response()->json([
                    'code' => 1,
                    'message' => '连接净水器数据库失败，请检查数据库配置'
                ]);
            }

            if ($waterMasters->isEmpty()) {
                return response()->json([
                    'code' => 1,
                    'message' => '净水器数据库中没有找到工程师数据'
                ]);
            }

            $syncCount = 0;
            $skipCount = 0;
            $errorCount = 0;
            $errors = [];

            foreach ($waterMasters as $master) {
                // 检查必要字段
                if (empty($master->master_name) || empty($master->phone)) {
                    $skipCount++;
                    continue;
                }

                // 检查是否已存在相同手机号的工程师
                $existingEngineer = DB::table('installation_engineers')
                    ->where('phone', $master->phone)
                    ->first();

                try {
                    // 从净水器数据库获取安装数量
                    $completedInstallations = 0;
                    try {
                        // 尝试从净水器数据库中获取安装数量
                        // 这里需要根据实际的表结构进行查询
                        // 例如，如果有安装记录表，可以计算安装数量
                        // 这里使用了一个示例查询，需要根据实际情况调整
                        $completedInstallations = \DB::connection('water_db')
                            ->table('wb_install_record') // 假设安装记录表名为 wb_install_record
                            ->where('master_id', $master->id)
                            ->where('status', 'completed') // 假设完成状态字段为 status
                            ->count();

                        Log::info("从净水器数据库获取工程师 {$master->master_name} 的安装数量: {$completedInstallations}");
                    } catch (\Exception $e) {
                        // 如果查询失败，记录错误并继续
                        Log::error("从净水器数据库获取工程师安装数量失败: " . $e->getMessage());
                        // 如果已存在工程师，保留原有安装数量
                        if ($existingEngineer) {
                            $completedInstallations = $existingEngineer->completed_installations;
                        }
                    }

                    if ($existingEngineer) {
                        // 更新已存在的工程师
                        DB::table('installation_engineers')
                            ->where('id', $existingEngineer->id)
                            ->update([
                                'name' => $master->master_name,
                                'area' => $this->formatArea($master->province, $master->city, $master->area),
                                'remark' => $master->remark,
                                'completed_installations' => $completedInstallations,
                                'updated_at' => now()
                            ]);
                    } else {
                        // 新增工程师
                        DB::table('installation_engineers')->insert([
                            'name' => $master->master_name,
                            'phone' => $master->phone,
                            'area' => $this->formatArea($master->province, $master->city, $master->area),
                            'level' => 'normal',
                            'status' => 'active',
                            'remark' => $master->remark,
                            'completed_installations' => $completedInstallations,
                            'created_at' => now(),
                            'updated_at' => now()
                        ]);
                    }

                    $syncCount++;
                } catch (\Exception $e) {
                    $errorCount++;
                    $errors[] = [
                        'name' => $master->master_name,
                        'phone' => $master->phone,
                        'error' => $e->getMessage()
                    ];
                    Log::error('同步工程师数据失败', [
                        'master_id' => $master->id,
                        'name' => $master->master_name,
                        'phone' => $master->phone,
                        'error' => $e->getMessage()
                    ]);
                }
            }

            return response()->json([
                'code' => 0,
                'message' => '同步完成',
                'data' => [
                    'total' => $waterMasters->count(),
                    'sync_count' => $syncCount,
                    'skip_count' => $skipCount,
                    'error_count' => $errorCount,
                    'errors' => $errors
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('同步工程师数据失败: ' . $e->getMessage());
            return response()->json([
                'code' => 1,
                'message' => '同步工程师数据失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 格式化区域信息
     */
    private function formatArea($province, $city, $area)
    {
        $result = [];

        if (!empty($province) && !is_numeric($province)) {
            $result[] = $province;
        }

        if (!empty($city) && !is_numeric($city)) {
            $result[] = $city;
        }

        if (!empty($area) && !is_numeric($area)) {
            $result[] = $area;
        }

        return implode(' ', $result);
    }

    /**
     * 获取净水器数据库中的安装记录表结构
     */
    public function getWaterDbTables()
    {
        try {
            // 获取所有表
            $tables = \DB::connection('water_db')->select('SHOW TABLES');

            $tableList = [];
            $tableStructures = [];
            foreach ($tables as $table) {
                $tableName = reset($table);
                $tableList[] = $tableName;

                // 获取表结构
                $columns = \DB::connection('water_db')->select("SHOW COLUMNS FROM {$tableName}");
                $columnList = [];
                foreach ($columns as $column) {
                    $columnList[] = [
                        'name' => $column->Field,
                        'type' => $column->Type,
                        'null' => $column->Null,
                        'key' => $column->Key,
                        'default' => $column->Default,
                        'extra' => $column->Extra
                    ];
                }

                $tableStructures[$tableName] = $columnList;
            }

            // 查询渠道商信息
            $dealerInfo = null;
            try {
                $dealer = \DB::connection('water_db')
                    ->table('wb_dealer')
                    ->where('dealer_number', '00000019')
                    ->first();

                if ($dealer) {
                    $dealerInfo = [
                        'id' => $dealer->id,
                        'dealer_number' => $dealer->dealer_number,
                        'dealer_name' => $dealer->dealer_name
                    ];

                    // 查询该渠道商下的工程师数量
                    $engineerCount = \DB::connection('water_db')
                        ->table('wb_master')
                        ->where('dealer_id', $dealer->id)
                        ->where('status', 'E')
                        ->count();

                    $dealerInfo['engineer_count'] = $engineerCount;
                }
            } catch (\Exception $e) {
                Log::error('查询渠道商信息失败: ' . $e->getMessage());
            }

            return response()->json([
                'code' => 0,
                'message' => '获取净水器数据库表结构成功',
                'data' => [
                    'tables' => $tableList,
                    'structures' => $tableStructures,
                    'dealer_info' => $dealerInfo
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('获取净水器数据库表结构失败: ' . $e->getMessage());
            return response()->json([
                'code' => 1,
                'message' => '获取净水器数据库表结构失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 更新所有工程师的安装数量
     */
    public function updateAllEngineersInstallationCount()
    {
        try {
            // 获取所有工程师
            $engineers = DB::table('installation_engineers')->get();

            $updateCount = 0;

            foreach ($engineers as $engineer) {
                try {
                    // 获取工程师完成的安装数量
                    $completedInstallations = DB::table('install_bookings')
                        ->where('engineer_id', $engineer->id)
                        ->where('status', 'completed')
                        ->count();

                    // 记录计算结果
                    Log::info("全量更新: 工程师ID {$engineer->id} 的完成安装数量: {$completedInstallations}");

                    // 更新工程师安装数量
                    DB::table('installation_engineers')
                        ->where('id', $engineer->id)
                        ->update(['completed_installations' => $completedInstallations]);

                    $updateCount++;
                } catch (\Exception $e) {
                    Log::error("全量更新: 更新工程师ID {$engineer->id} 的安装数量失败: " . $e->getMessage());
                }
            }

            return response()->json([
                'code' => 0,
                'message' => "成功更新 {$updateCount} 个工程师的安装数量",
                'data' => [
                    'update_count' => $updateCount
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('更新工程师安装数量失败: ' . $e->getMessage());
            return response()->json([
                'code' => 1,
                'message' => '更新工程师安装数量失败: ' . $e->getMessage()
            ]);
        }
    }
}
