<?php

namespace App\Http\Controllers\Admin\Web;

use App\Http\Controllers\Controller;
use App\Models\Installation\InstallBooking;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use App\Http\Resources\InstallBookingResource; // 可以考虑使用
use Illuminate\Support\Str; // 用于生成 booking_no

class InstallBookingController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = InstallBooking::query()
                    ->with(['user:id,name,nickname,avatar', 'referrer:id,name,nickname,avatar']); // 预加载用户和推荐人信息，只选择需要的字段

        // 搜索: 订单号, 联系人, 联系电话
        if ($search = $request->input('search')) {
            $query->where(function($q) use ($search) {
                $q->where('booking_no', 'like', "%{$search}%")
                  ->orWhere('contact_name', 'like', "%{$search}%")
                  ->orWhere('contact_phone', 'like', "%{$search}%");
            });
        }

        // 按状态筛选
        if ($status = $request->input('status')) {
            $query->where('status', $status);
        }

        // 按日期范围筛选 (创建时间)
        if ($startDate = $request->input('start_date')) {
            $query->whereDate('created_at', '>=', $startDate);
        }
        if ($endDate = $request->input('end_date')) {
            $query->whereDate('created_at', '<=', $endDate);
        }

        // 排序 (默认按创建时间降序)
        $sortBy = $request->input('sort_by', 'created_at');
        $sortOrder = $request->input('sort_order', 'desc');
        if (in_array($sortBy, ['created_at', 'install_time', 'id'])) { // 添加允许排序的字段
             $query->orderBy($sortBy, $sortOrder);
        }


        // 分页
        $limit = $request->input('limit', 15);
        $bookings = $query->paginate($limit);

        // 可以使用 API Resource 来格式化输出，但暂时直接返回
        return response()->json($bookings);
        // 如果使用 Resource: return InstallBookingResource::collection($bookings);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'user_id' => 'nullable|exists:users,id', // 用户ID可选，但如果提供必须存在
            'referrer_id' => 'nullable|exists:users,id', // 推荐人ID可选
            'package_type' => 'required|string|max:255', // 套餐类型必填
            'package_price' => 'required|numeric|min:0', // 套餐价格必填
            'installation_fee' => 'nullable|numeric|min:0', // 安装费可选
            'total_amount' => 'required|numeric|min:0', // 总金额必填
            'contact_name' => 'required|string|max:255', // 联系人必填
            'contact_phone' => 'required|string|max:255', // 联系电话必填
            'install_address' => 'required|string', // 安装地址必填
            'install_time' => 'required|date_format:Y-m-d H:i:s', // 安装时间必填，格式 YYYY-MM-DD HH:MM:SS
            'remarks' => 'nullable|string',
            'payment_status' => 'nullable|in:unpaid,paid,failed,refunded', // 允许的支付状态
            'payment_method' => 'nullable|string|max:255',
            'transaction_id' => 'nullable|string|max:255',
            'status' => 'nullable|in:pending,confirmed,assigned,completed,cancelled', // 初始状态可选，默认为 pending
            'engineer_id' => 'nullable|exists:users,id', // 工程师ID可选
            // 其他字段根据需要添加验证
        ]);

        if ($validator->fails()) {
            return response()->json(['message' => '验证失败', 'errors' => $validator->errors()], 422);
        }

        $data = $validator->validated();

        // 生成唯一的 booking_no
        $data['booking_no'] = 'BK' . date('YmdHis') . Str::random(6);
        // 设置默认状态
        $data['status'] = $data['status'] ?? 'pending';
        $data['payment_status'] = $data['payment_status'] ?? 'unpaid';

        $installBooking = InstallBooking::create($data);

        // 创建成功后加载关联数据返回
        $installBooking->load(['user:id,name,nickname,avatar', 'referrer:id,name,nickname,avatar', 'engineer:id,name,nickname,avatar,phone']);

        return response()->json(['message' => '创建成功', 'data' => $installBooking], 201);
    }

    /**
     * Display the specified resource.
     */
    public function show($id) // 直接接收 ID
    {
        $installBooking = InstallBooking::with([
            'user:id,name,nickname,avatar,phone',
            'referrer:id,name,nickname,avatar',
            'engineer:id,name,nickname,avatar,phone' // 预加载工程师信息
            ])->find($id);

        if (!$installBooking) {
            return response()->json(['message' => '预约记录未找到'], 404);
        }

        // 可以使用 API Resource
        return response()->json(['data' => $installBooking]);
        // return new InstallBookingResource($installBooking);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(InstallBooking $installBooking)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, $id)
    {
        $installBooking = InstallBooking::find($id);
        if (!$installBooking) {
            return response()->json(['message' => '预约记录未找到'], 404);
        }

        // 区分是更新状态/分配工程师，还是完整的编辑
        // 如果请求中只包含少量特定字段（如 status, engineer_id），则按之前的逻辑处理
        // 如果包含更多字段，则认为是完整编辑，需要更全面的验证规则

        $isStatusUpdate = $request->has('status') && count($request->except(['_method'])) <= 5; // 简单判断，可调整

        if ($isStatusUpdate) {
             $rules = [
                'status' => 'sometimes|in:pending,confirmed,assigned,completed,cancelled',
                'engineer_id' => 'nullable|required_if:status,assigned|exists:users,id',
                'device_id' => 'nullable|string|max:255',
                'device_model' => 'nullable|string|max:255',
                'device_sn' => 'nullable|string|max:255',
                'cancellation_reason' => 'nullable|required_if:status,cancelled|string',
            ];
        } else {
             // 完整编辑的验证规则 (允许修改更多字段)
             $rules = [
                'user_id' => 'sometimes|nullable|exists:users,id',
                'referrer_id' => 'sometimes|nullable|exists:users,id',
                'package_type' => 'sometimes|required|string|max:255',
                'package_price' => 'sometimes|required|numeric|min:0',
                'installation_fee' => 'sometimes|nullable|numeric|min:0',
                'total_amount' => 'sometimes|required|numeric|min:0',
                'contact_name' => 'sometimes|required|string|max:255',
                'contact_phone' => 'sometimes|required|string|max:255',
                'install_address' => 'sometimes|required|string',
                'install_time' => 'sometimes|required|date_format:Y-m-d H:i:s',
                'remarks' => 'sometimes|nullable|string',
                'payment_status' => 'sometimes|nullable|in:unpaid,paid,failed,refunded',
                'payment_method' => 'sometimes|nullable|string|max:255',
                'transaction_id' => 'sometimes|nullable|string|max:255',
                'status' => 'sometimes|required|in:pending,confirmed,assigned,completed,cancelled',
                'engineer_id' => 'sometimes|nullable|exists:users,id',
                'device_id' => 'sometimes|nullable|string|max:255',
                'device_model' => 'sometimes|nullable|string|max:255',
                'device_sn' => 'sometimes|nullable|string|max:255',
                'cancellation_reason' => 'sometimes|nullable|string',
                // 其他允许编辑的字段
            ];
        }

        $dataToValidate = $request->only(array_keys($rules));
        $validator = Validator::make($dataToValidate, $rules);

        if ($validator->fails()) {
            return response()->json(['message' => '验证失败', 'errors' => $validator->errors()], 422);
        }

        $validatedData = $validator->validated();

        // 如果是状态更新，且状态为 'completed'，自动填充完成时间
        if (isset($validatedData['status']) && $validatedData['status'] === 'completed' && !$installBooking->completion_time) {
             $validatedData['completion_time'] = now();
        }
        // 如果是状态更新，且状态为 'paid'，自动填充支付时间
        if (isset($validatedData['payment_status']) && $validatedData['payment_status'] === 'paid' && !$installBooking->payment_time) {
             $validatedData['payment_time'] = now();
        }

        $installBooking->update($validatedData);

        $installBooking->load(['user:id,name,nickname,avatar,phone', 'referrer:id,name,nickname,avatar', 'engineer:id,name,nickname,avatar,phone']);

        $message = $isStatusUpdate ? '状态更新成功' : '预约信息更新成功';
        return response()->json(['message' => $message, 'data' => $installBooking]);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($id)
    {
        $installBooking = InstallBooking::find($id);
        if (!$installBooking) {
            return response()->json(['message' => '预约记录未找到'], 404);
        }

        // 可以在这里添加权限检查，例如只有特定状态才能删除
        // if ($installBooking->status !== 'pending' && $installBooking->status !== 'cancelled') {
        //     return response()->json(['message' => '只有待处理或已取消的预约才能删除'], 403);
        // }

        $installBooking->delete(); // 软删除

        return response()->json(['message' => '删除成功'], 200);
    }

    /**
     * 处理微信地址选择
     */
    public function processWechatAddress(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'address_data' => 'required|array',
            'address_data.userName' => 'required|string',
            'address_data.telNumber' => 'required|string',
            'address_data.provinceName' => 'required|string',
            'address_data.cityName' => 'required|string',
            'address_data.countryName' => 'required|string',
            'address_data.detailInfo' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json(['message' => '验证失败', 'errors' => $validator->errors()], 422);
        }

        $addressData = $request->address_data;

        // 记录日志
        Log::channel('wechat')->info('微信地址选择', ['address_data' => $addressData]);

        // 构建完整地址
        $fullAddress = $addressData['provinceName'] . $addressData['cityName'] . $addressData['countryName'] . $addressData['detailInfo'];

        // 返回处理后的数据
        return response()->json([
            'message' => '地址处理成功',
            'data' => [
                'contact_name' => $addressData['userName'],
                'contact_phone' => $addressData['telNumber'],
                'install_address' => $fullAddress,
                'province' => $addressData['provinceName'],
                'city' => $addressData['cityName'],
                'district' => $addressData['countryName'],
                'address_detail' => $addressData['detailInfo'],
                'postal_code' => $addressData['postalCode'] ?? '',
                'original_data' => $addressData
            ]
        ]);
    }

    /**
     * 创建微信支付订单
     */
    public function createPayment(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'booking_id' => 'required|exists:install_bookings,id',
            'openid' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json(['message' => '验证失败', 'errors' => $validator->errors()], 422);
        }

        $bookingId = $request->booking_id;
        $openid = $request->openid;

        // 查询预约信息
        $booking = InstallBooking::findOrFail($bookingId);

        // 检查支付状态
        if ($booking->payment_status === 'paid') {
            return response()->json(['message' => '该预约已支付，无需重复支付'], 400);
        }

        // 生成订单号
        $orderNo = 'INST' . date('YmdHis') . Str::random(6);

        // 更新预约订单号
        $booking->booking_no = $orderNo;
        $booking->save();

        try {
            // 调用微信支付API
            $response = Http::post(url('/api/admin/installation/create_payment.php'), [
                'booking_id' => $bookingId,
                'openid' => $openid,
            ]);

            if ($response->successful() && $response->json('code') === 0) {
                return response()->json([
                    'message' => '创建支付订单成功',
                    'data' => $response->json('data')
                ]);
            } else {
                Log::channel('wechat')->error('创建微信支付订单失败', [
                    'response' => $response->json(),
                    'booking_id' => $bookingId
                ]);
                return response()->json(['message' => $response->json('message') ?? '创建支付订单失败'], 500);
            }
        } catch (\Exception $e) {
            Log::channel('wechat')->error('创建微信支付订单异常', [
                'error' => $e->getMessage(),
                'booking_id' => $bookingId
            ]);
            return response()->json(['message' => '创建支付订单异常: ' . $e->getMessage()], 500);
        }
    }

    /**
     * 检查支付状态
     */
    public function checkPayment(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'booking_id' => 'required|exists:install_bookings,id',
        ]);

        if ($validator->fails()) {
            return response()->json(['message' => '验证失败', 'errors' => $validator->errors()], 422);
        }

        $bookingId = $request->booking_id;

        // 查询预约信息
        $booking = InstallBooking::select(
            'id', 'booking_no', 'payment_status', 'payment_time',
            'payment_method', 'transaction_id', 'status', 'total_amount'
        )->findOrFail($bookingId);

        // 构建响应数据
        $responseData = [
            'booking_id' => $booking->id,
            'order_no' => $booking->booking_no,
            'payment_status' => $booking->payment_status,
            'payment_time' => $booking->payment_time,
            'payment_method' => $booking->payment_method,
            'transaction_id' => $booking->transaction_id,
            'status' => $booking->status,
            'total_amount' => $booking->total_amount,
            'is_paid' => $booking->payment_status === 'paid'
        ];

        return response()->json(['message' => '获取支付状态成功', 'data' => $responseData]);
    }
}
