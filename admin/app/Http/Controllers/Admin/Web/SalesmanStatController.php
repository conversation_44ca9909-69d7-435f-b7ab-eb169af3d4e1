<?php

namespace App\Http\Controllers\Admin\Web;

use App\Http\Controllers\Controller;
use App\Models\Salesman;
use App\Models\SalesmanSale;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class SalesmanStatController extends Controller
{
    /**
     * 显示业务员数据统计
     */
    public function index(Request $request)
    {
        // 获取活跃的业务员
        $salesmen = Salesman::with('user')
            ->where('status', 'active')
            ->get();
            
        // 获取总销售额
        $totalSales = SalesmanSale::where('status', 'completed')
            ->sum('amount');
            
        // 获取总销售量
        $totalQuantity = SalesmanSale::where('status', 'completed')
            ->sum('quantity');
            
        // 获取总提成金额
        $totalCommission = SalesmanSale::where('status', 'completed')
            ->sum('commission_amount');
            
        // 获取月度销售数据 - 最近6个月
        $monthlySales = SalesmanSale::where('status', 'completed')
            ->select(
                DB::raw('DATE_FORMAT(sale_date, "%Y-%m") as month'),
                DB::raw('SUM(amount) as total_amount'),
                DB::raw('SUM(quantity) as total_quantity')
            )
            ->groupBy('month')
            ->orderBy('month', 'desc')
            ->limit(6)
            ->get();
            
        // 获取业务员排行榜（按销售量）
        $topSalesmenByQuantity = Salesman::with('user')
            ->join('salesman_sales', 'salesmen.id', '=', 'salesman_sales.salesman_id')
            ->select(
                'salesmen.id',
                DB::raw('SUM(salesman_sales.quantity) as total_quantity')
            )
            ->where('salesman_sales.status', 'completed')
            ->groupBy('salesmen.id')
            ->orderBy('total_quantity', 'desc')
            ->limit(10)
            ->get()
            ->map(function ($item) {
                $salesman = Salesman::with('user')->find($item->id);
                return [
                    'id' => $salesman->id,
                    'name' => $salesman->user->name ?? '未知',
                    'total_quantity' => $item->total_quantity,
                ];
            });
            
        // 获取业务员排行榜（按销售额）
        $topSalesmenByAmount = Salesman::with('user')
            ->join('salesman_sales', 'salesmen.id', '=', 'salesman_sales.salesman_id')
            ->select(
                'salesmen.id',
                DB::raw('SUM(salesman_sales.amount) as total_amount')
            )
            ->where('salesman_sales.status', 'completed')
            ->groupBy('salesmen.id')
            ->orderBy('total_amount', 'desc')
            ->limit(10)
            ->get()
            ->map(function ($item) {
                $salesman = Salesman::with('user')->find($item->id);
                return [
                    'id' => $salesman->id,
                    'name' => $salesman->user->name ?? '未知',
                    'total_amount' => $item->total_amount,
                ];
            });
            
        return view('admin.salesmen.stats.index', compact(
            'salesmen',
            'totalSales',
            'totalQuantity',
            'totalCommission',
            'monthlySales',
            'topSalesmenByQuantity',
            'topSalesmenByAmount'
        ));
    }
    
    /**
     * 显示特定业务员的详细统计数据
     */
    public function show(Request $request, Salesman $salesman)
    {
        $salesman->load('user');
        
        // 获取时间范围
        $startDate = $request->input('start_date', date('Y-m-01'));
        $endDate = $request->input('end_date', date('Y-m-t'));
        
        // 获取期间的销售统计
        $salesStats = SalesmanSale::getSalesStatsByDateRange(
            $salesman->id,
            $startDate,
            $endDate
        );
        
        // 获取销售趋势数据 - 按日
        $dailySalesTrend = SalesmanSale::where('salesman_id', $salesman->id)
            ->where('status', 'completed')
            ->whereBetween('sale_date', [$startDate, $endDate])
            ->select(
                DB::raw('DATE(sale_date) as date'),
                DB::raw('SUM(amount) as total_amount'),
                DB::raw('SUM(quantity) as total_quantity')
            )
            ->groupBy('date')
            ->orderBy('date')
            ->get();
            
        // 获取产品销售分布
        $productDistribution = SalesmanSale::where('salesman_id', $salesman->id)
            ->where('status', 'completed')
            ->whereBetween('sale_date', [$startDate, $endDate])
            ->select(
                'product_name',
                DB::raw('SUM(quantity) as total_quantity'),
                DB::raw('SUM(amount) as total_amount')
            )
            ->groupBy('product_name')
            ->orderBy('total_quantity', 'desc')
            ->get();
            
        return view('admin.salesmen.stats.show', compact(
            'salesman',
            'startDate',
            'endDate',
            'salesStats',
            'dailySalesTrend',
            'productDistribution'
        ));
    }
}
