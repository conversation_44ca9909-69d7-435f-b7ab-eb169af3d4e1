<?php

namespace App\Http\Controllers\Admin\Web;

use App\Http\Controllers\Controller;
use App\Models\AdminNotification;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class NotificationController extends Controller
{
    /**
     * 获取通知列表
     */
    public function index(Request $request)
    {
        try {
            $admin = Auth::user();
            $page = $request->get('page', 1);
            $limit = $request->get('limit', 10);
            $onlyUnread = $request->get('only_unread', false);
            
            $query = AdminNotification::where(function ($q) use ($admin) {
                $q->where('admin_id', $admin->id)
                  ->orWhereNull('admin_id'); // 全体通知
            });

            if ($onlyUnread) {
                $query->where('is_read', false);
            }

            // 排除已过期的通知
            $query->where(function ($q) {
                $q->whereNull('expires_at')
                  ->orWhere('expires_at', '>', now());
            });

            $total = $query->count();
            $notifications = $query->with(['sender'])
                                  ->orderBy('priority', 'desc')
                                  ->orderBy('created_at', 'desc')
                                  ->offset(($page - 1) * $limit)
                                  ->limit($limit)
                                  ->get();

            return response()->json([
                'code' => 0,
                'message' => '获取成功',
                'data' => [
                    'list' => $notifications,
                    'total' => $total,
                    'page' => $page,
                    'limit' => $limit
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('获取通知列表失败', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'code' => 500,
                'message' => '获取通知列表失败'
            ], 500);
        }
    }

    /**
     * 获取未读通知数量
     */
    public function unreadCount()
    {
        try {
            $admin = Auth::user();
            $count = AdminNotification::getUnreadCount($admin->id);

            return response()->json([
                'code' => 0,
                'message' => '获取成功',
                'data' => [
                    'count' => $count
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('获取未读通知数量失败', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'code' => 500,
                'message' => '获取未读通知数量失败'
            ], 500);
        }
    }

    /**
     * 获取最新通知（用于下拉显示）
     */
    public function latest(Request $request)
    {
        try {
            $admin = Auth::user();
            $limit = $request->get('limit', 5);
            
            $notifications = AdminNotification::getAdminNotifications($admin->id, $limit);

            return response()->json([
                'code' => 0,
                'message' => '获取成功',
                'data' => $notifications
            ]);
        } catch (\Exception $e) {
            Log::error('获取最新通知失败', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'code' => 500,
                'message' => '获取最新通知失败'
            ], 500);
        }
    }

    /**
     * 标记通知为已读
     */
    public function markAsRead(Request $request, $id)
    {
        try {
            $admin = Auth::user();
            
            $notification = AdminNotification::where(function ($q) use ($admin) {
                $q->where('admin_id', $admin->id)
                  ->orWhereNull('admin_id');
            })->findOrFail($id);

            $notification->markAsRead();

            return response()->json([
                'code' => 0,
                'message' => '标记成功'
            ]);
        } catch (\Exception $e) {
            Log::error('标记通知已读失败', [
                'id' => $id,
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'code' => 500,
                'message' => '标记失败'
            ], 500);
        }
    }

    /**
     * 批量标记为已读
     */
    public function markAllAsRead()
    {
        try {
            $admin = Auth::user();
            AdminNotification::markAllAsRead($admin->id);

            return response()->json([
                'code' => 0,
                'message' => '全部标记成功'
            ]);
        } catch (\Exception $e) {
            Log::error('批量标记通知已读失败', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'code' => 500,
                'message' => '批量标记失败'
            ], 500);
        }
    }

    /**
     * 删除通知
     */
    public function destroy($id)
    {
        try {
            $admin = Auth::user();
            
            $notification = AdminNotification::where(function ($q) use ($admin) {
                $q->where('admin_id', $admin->id)
                  ->orWhereNull('admin_id');
            })->findOrFail($id);

            $notification->delete();

            return response()->json([
                'code' => 0,
                'message' => '删除成功'
            ]);
        } catch (\Exception $e) {
            Log::error('删除通知失败', [
                'id' => $id,
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'code' => 500,
                'message' => '删除失败'
            ], 500);
        }
    }

    /**
     * 创建通知（管理员发送）
     */
    public function store(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'title' => 'required|string|max:255',
                'content' => 'required|string',
                'type' => 'in:info,success,warning,error',
                'priority' => 'in:low,normal,high,urgent',
                'admin_id' => 'nullable|exists:admins,id',
                'action_url' => 'nullable|string',
                'action_text' => 'nullable|string',
                'expires_at' => 'nullable|date|after:now'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'code' => 1,
                    'message' => '参数验证失败',
                    'errors' => $validator->errors()
                ], 422);
            }

            $admin = Auth::user();
            
            $notification = AdminNotification::create([
                'title' => $request->title,
                'content' => $request->content,
                'type' => $request->get('type', 'info'),
                'priority' => $request->get('priority', 'normal'),
                'admin_id' => $request->admin_id,
                'sender_id' => $admin->id,
                'action_url' => $request->action_url,
                'action_text' => $request->action_text,
                'expires_at' => $request->expires_at,
                'is_system' => false
            ]);

            return response()->json([
                'code' => 0,
                'message' => '通知发送成功',
                'data' => $notification
            ]);
        } catch (\Exception $e) {
            Log::error('创建通知失败', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'code' => 500,
                'message' => '创建通知失败'
            ], 500);
        }
    }

    /**
     * 获取通知详情
     */
    public function show($id)
    {
        try {
            $admin = Auth::user();
            
            $notification = AdminNotification::where(function ($q) use ($admin) {
                $q->where('admin_id', $admin->id)
                  ->orWhereNull('admin_id');
            })->with(['sender'])->findOrFail($id);

            // 自动标记为已读
            if (!$notification->is_read) {
                $notification->markAsRead();
            }

            return response()->json([
                'code' => 0,
                'message' => '获取成功',
                'data' => $notification
            ]);
        } catch (\Exception $e) {
            Log::error('获取通知详情失败', [
                'id' => $id,
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'code' => 500,
                'message' => '获取通知详情失败'
            ], 500);
        }
    }
}
