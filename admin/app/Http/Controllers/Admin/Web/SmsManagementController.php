<?php

namespace App\Http\Controllers\Admin\Web;

use App\Http\Controllers\Controller;
use App\Models\SmsCode;
use App\Models\SmsLog;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Carbon;

class SmsManagementController extends Controller
{
    /**
     * 显示短信日志列表
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function logs(Request $request)
    {
        // 查询构建
        $query = SmsLog::query();
        
        // 搜索条件
        if ($request->filled('phone')) {
            $query->where('phone', 'like', '%' . $request->input('phone') . '%');
        }
        
        if ($request->filled('status') && $request->input('status') !== 'all') {
            $query->where('status', $request->input('status'));
        }
        
        if ($request->filled('type') && $request->input('type') !== 'all') {
            $query->where('type', $request->input('type'));
        }
        
        if ($request->filled('date_range')) {
            $dates = explode(' - ', $request->input('date_range'));
            if (count($dates) == 2) {
                $start_date = Carbon::parse($dates[0])->startOfDay();
                $end_date = Carbon::parse($dates[1])->endOfDay();
                $query->whereBetween('created_at', [$start_date, $end_date]);
            }
        }
        
        // 排序
        $query->orderBy('id', 'desc');
        
        // 分页
        $logs = $query->paginate(20);
        
        // 统计信息
        $total = SmsLog::count();
        $success = SmsLog::where('status', 1)->count();
        $failed = SmsLog::where('status', 0)->count();
        
        // 获取所有验证码类型
        $types = SmsLog::select('type')->distinct()->pluck('type')->toArray();
        
        return view('admin.sms.logs', compact('logs', 'total', 'success', 'failed', 'types'));
    }
    
    /**
     * 显示短信日志详情
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function logDetail($id)
    {
        $log = SmsLog::findOrFail($id);
        return view('admin.sms.log_detail', compact('log'));
    }
    
    /**
     * 显示短信验证码列表
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function codes(Request $request)
    {
        // 查询构建
        $query = SmsCode::query();
        
        // 搜索条件
        if ($request->filled('phone')) {
            $query->where('phone', 'like', '%' . $request->input('phone') . '%');
        }
        
        if ($request->filled('status') && $request->input('status') !== 'all') {
            $query->where('is_used', $request->input('status') === 'used' ? 1 : 0);
        }
        
        if ($request->filled('type') && $request->input('type') !== 'all') {
            $query->where('type', $request->input('type'));
        }
        
        if ($request->filled('date_range')) {
            $dates = explode(' - ', $request->input('date_range'));
            if (count($dates) == 2) {
                $start_date = Carbon::parse($dates[0])->startOfDay();
                $end_date = Carbon::parse($dates[1])->endOfDay();
                $query->whereBetween('created_at', [$start_date, $end_date]);
            }
        }
        
        // 排序
        $query->orderBy('id', 'desc');
        
        // 分页
        $codes = $query->paginate(20);
        
        // 统计信息
        $total = SmsCode::count();
        $used = SmsCode::where('is_used', 1)->count();
        $unused = SmsCode::where('is_used', 0)->count();
        
        // 获取所有验证码类型
        $types = SmsCode::select('type')->distinct()->pluck('type')->toArray();
        
        return view('admin.sms.codes', compact('codes', 'total', 'used', 'unused', 'types'));
    }
    
    /**
     * 显示短信统计信息
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function statistics(Request $request)
    {
        // 获取时间范围参数
        $range = $request->input('range', 'week');
        $customStartDate = $request->input('start_date');
        $customEndDate = $request->input('end_date');
        
        // 根据时间范围确定起止时间
        $startDate = now();
        $endDate = now();
        
        switch ($range) {
            case 'today':
                $startDate = now()->startOfDay();
                break;
            case 'yesterday':
                $startDate = now()->subDay()->startOfDay();
                $endDate = now()->subDay()->endOfDay();
                break;
            case 'week':
                $startDate = now()->startOfWeek();
                break;
            case 'month':
                $startDate = now()->startOfMonth();
                break;
            case 'year':
                $startDate = now()->startOfYear();
                break;
            case 'custom':
                if ($customStartDate && $customEndDate) {
                    $startDate = Carbon::parse($customStartDate)->startOfDay();
                    $endDate = Carbon::parse($customEndDate)->endOfDay();
                }
                break;
        }
        
        // 统计每日短信发送量
        $dailySendingData = $this->getDailySending($startDate, $endDate);
        
        // 统计短信类型分布
        $typeDistribution = $this->getTypeDistribution($startDate, $endDate);
        
        // 统计成功率
        $successRateData = $this->getSuccessRate($startDate, $endDate);
        
        // 统计高频手机号
        $frequentPhones = $this->getFrequentPhones($startDate, $endDate);
        
        // 统计整体数据
        $totalStats = $this->getTotalStats($startDate, $endDate);
        
        return view('admin.sms.statistics', compact(
            'dailySendingData',
            'typeDistribution',
            'successRateData',
            'frequentPhones',
            'totalStats',
            'range',
            'startDate',
            'endDate'
        ));
    }
    
    /**
     * 获取每日短信发送量
     *
     * @param  Carbon  $startDate
     * @param  Carbon  $endDate
     * @return array
     */
    private function getDailySending($startDate, $endDate)
    {
        $data = SmsLog::selectRaw('DATE(created_at) as date, COUNT(*) as count')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->groupBy('date')
            ->orderBy('date')
            ->get();
            
        // 补充没有数据的日期
        $result = [];
        $current = clone $startDate;
        
        while ($current <= $endDate) {
            $dateString = $current->format('Y-m-d');
            $count = 0;
            
            foreach ($data as $item) {
                if ($item->date == $dateString) {
                    $count = $item->count;
                    break;
                }
            }
            
            $result[] = [
                'date' => $dateString,
                'count' => $count
            ];
            
            $current->addDay();
        }
        
        return $result;
    }
    
    /**
     * 获取短信类型分布
     *
     * @param  Carbon  $startDate
     * @param  Carbon  $endDate
     * @return array
     */
    private function getTypeDistribution($startDate, $endDate)
    {
        return SmsLog::selectRaw('type, COUNT(*) as count')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->groupBy('type')
            ->orderBy('count', 'desc')
            ->get()
            ->map(function ($item) {
                $types = [
                    'login' => '登录验证',
                    'register' => '注册验证',
                    'reset' => '重置密码',
                    'bind' => '绑定手机',
                    'verify' => '身份验证',
                ];
                
                return [
                    'type' => $types[$item->type] ?? $item->type,
                    'count' => $item->count
                ];
            })
            ->toArray();
    }
    
    /**
     * 获取成功率数据
     *
     * @param  Carbon  $startDate
     * @param  Carbon  $endDate
     * @return array
     */
    private function getSuccessRate($startDate, $endDate)
    {
        $data = SmsLog::selectRaw('DATE(created_at) as date, status, COUNT(*) as count')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->groupBy('date', 'status')
            ->orderBy('date')
            ->get();
            
        // 按日期整理数据
        $result = [];
        $dateData = [];
        
        foreach ($data as $item) {
            if (!isset($dateData[$item->date])) {
                $dateData[$item->date] = [
                    'total' => 0,
                    'success' => 0,
                    'failed' => 0
                ];
            }
            
            $dateData[$item->date]['total'] += $item->count;
            
            if ($item->status == 1) {
                $dateData[$item->date]['success'] += $item->count;
            } else {
                $dateData[$item->date]['failed'] += $item->count;
            }
        }
        
        // 计算成功率
        foreach ($dateData as $date => $stats) {
            $successRate = $stats['total'] > 0 ? round(($stats['success'] / $stats['total']) * 100, 2) : 0;
            
            $result[] = [
                'date' => $date,
                'successRate' => $successRate,
                'total' => $stats['total'],
                'success' => $stats['success'],
                'failed' => $stats['failed']
            ];
        }
        
        return $result;
    }
    
    /**
     * 获取高频使用手机号
     *
     * @param  Carbon  $startDate
     * @param  Carbon  $endDate
     * @return array
     */
    private function getFrequentPhones($startDate, $endDate)
    {
        return SmsLog::selectRaw('phone, COUNT(*) as count')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->groupBy('phone')
            ->orderBy('count', 'desc')
            ->limit(10)
            ->get()
            ->map(function ($item) {
                return [
                    'phone' => substr_replace($item->phone, '****', 3, 4),
                    'count' => $item->count
                ];
            })
            ->toArray();
    }
    
    /**
     * 获取总体统计数据
     *
     * @param  Carbon  $startDate
     * @param  Carbon  $endDate
     * @return array
     */
    private function getTotalStats($startDate, $endDate)
    {
        $smsLogsTotal = SmsLog::whereBetween('created_at', [$startDate, $endDate])->count();
        $smsLogsSuccess = SmsLog::whereBetween('created_at', [$startDate, $endDate])->where('status', 1)->count();
        $smsLogsFailed = SmsLog::whereBetween('created_at', [$startDate, $endDate])->where('status', 0)->count();
        $smsCodesTotal = SmsCode::whereBetween('created_at', [$startDate, $endDate])->count();
        $smsCodesUsed = SmsCode::whereBetween('created_at', [$startDate, $endDate])->where('is_used', 1)->count();
        
        // 计算平均每日发送量
        $daysDiff = max(1, $startDate->diffInDays($endDate) + 1);
        $dailyAverage = round($smsLogsTotal / $daysDiff, 2);
        
        // 计算验证码使用率
        $usageRate = $smsCodesTotal > 0 ? round(($smsCodesUsed / $smsCodesTotal) * 100, 2) : 0;
        
        // 计算发送成功率
        $successRate = $smsLogsTotal > 0 ? round(($smsLogsSuccess / $smsLogsTotal) * 100, 2) : 0;
        
        return [
            'total' => $smsLogsTotal,
            'success' => $smsLogsSuccess,
            'failed' => $smsLogsFailed,
            'dailyAverage' => $dailyAverage,
            'successRate' => $successRate,
            'codesTotal' => $smsCodesTotal,
            'codesUsed' => $smsCodesUsed,
            'usageRate' => $usageRate
        ];
    }
    
    /**
     * 返回API格式的短信统计数据
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function apiStatistics(Request $request)
    {
        // 获取时间范围参数
        $range = $request->input('range', 'week');
        $customStartDate = $request->input('start_date');
        $customEndDate = $request->input('end_date');
        
        // 根据时间范围确定起止时间
        $startDate = now();
        $endDate = now();
        
        switch ($range) {
            case 'today':
                $startDate = now()->startOfDay();
                break;
            case 'yesterday':
                $startDate = now()->subDay()->startOfDay();
                $endDate = now()->subDay()->endOfDay();
                break;
            case 'week':
                $startDate = now()->startOfWeek();
                break;
            case 'month':
                $startDate = now()->startOfMonth();
                break;
            case 'year':
                $startDate = now()->startOfYear();
                break;
            case 'custom':
                if ($customStartDate && $customEndDate) {
                    $startDate = Carbon::parse($customStartDate)->startOfDay();
                    $endDate = Carbon::parse($customEndDate)->endOfDay();
                }
                break;
            case 'all':
                // 获取最早一条记录的时间
                $firstRecord = SmsLog::orderBy('created_at', 'asc')->first();
                if ($firstRecord) {
                    $startDate = Carbon::parse($firstRecord->created_at)->startOfDay();
                } else {
                    $startDate = now()->subYear()->startOfDay(); // 如果没有记录，默认显示过去一年
                }
                break;
        }
        
        // 强制获取所有记录 - 调试用
        $allTotal = SmsLog::count();
        $allSuccess = SmsLog::where('status', 1)->count();
        $allFailed = SmsLog::where('status', 0)->count();
        
        // 记录调试日志
        \Log::info('SMS统计数据 - API:', [
            'range' => $range,
            'startDate' => $startDate->format('Y-m-d'),
            'endDate' => $endDate->format('Y-m-d'),
            'allTotal' => $allTotal,
            'allSuccess' => $allSuccess,
            'allFailed' => $allFailed
        ]);
        
        return response()->json([
            'dailySending' => $this->getDailySending($startDate, $endDate),
            'typeDistribution' => $this->getTypeDistribution($startDate, $endDate),
            'successRate' => $this->getSuccessRate($startDate, $endDate),
            'frequentPhones' => $this->getFrequentPhones($startDate, $endDate),
            'totalStats' => $this->getTotalStats($startDate, $endDate),
            'allStats' => [  // 添加所有数据的统计
                'total' => $allTotal,
                'success' => $allSuccess,
                'failed' => $allFailed
            ],
            'timeRange' => [
                'start' => $startDate->format('Y-m-d'),
                'end' => $endDate->format('Y-m-d'),
            ]
        ]);
    }
    
    /**
     * 获取短信日志统计数据（API接口）
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function logsStats()
    {
        // 获取实际数据库中记录数
        $total = SmsLog::count();
        $success = SmsLog::where('status', 1)->count();
        $failed = SmsLog::where('status', 0)->count();
        
        // 写入日志便于调试
        \Log::info('SMS统计数据:', [
            'total' => $total,
            'success' => $success,
            'failed' => $failed
        ]);
        
        return response()->json([
            'data' => [
                'total' => $total,
                'success' => $success,
                'failed' => $failed
            ]
        ]);
    }
    
    /**
     * 获取验证码列表（API接口）
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function codesList(Request $request)
    {
        // 查询构建
        $query = SmsCode::query();
        
        // 搜索条件
        if ($request->filled('phone')) {
            $query->where('phone', 'like', '%' . $request->input('phone') . '%');
        }
        
        if ($request->filled('status')) {
            $isUsed = $request->input('status') === 'used';
            $query->where('is_used', $isUsed);
        }
        
        if ($request->filled('type') && $request->input('type') !== '') {
            $query->where('type', $request->input('type'));
        }
        
        if ($request->filled('date_start') && $request->filled('date_end')) {
            $start_date = Carbon::parse($request->input('date_start'))->startOfDay();
            $end_date = Carbon::parse($request->input('date_end'))->endOfDay();
            $query->whereBetween('created_at', [$start_date, $end_date]);
        }
        
        // 排序
        $query->orderBy('id', 'desc');
        
        // 分页
        $page = $request->input('page', 1);
        $perPage = $request->input('per_page', 10);
        $codes = $query->paginate($perPage, ['*'], 'page', $page);
        
        return response()->json([
            'data' => $codes->items(),
            'total' => $codes->total(),
            'per_page' => $codes->perPage(),
            'current_page' => $codes->currentPage(),
            'last_page' => $codes->lastPage()
        ]);
    }
    
    /**
     * 获取验证码统计数据（API接口）
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function codesStats()
    {
        $total = SmsCode::count();
        $used = SmsCode::where('is_used', 1)->count();
        $unused = SmsCode::where('is_used', 0)->count();
        
        return response()->json([
            'data' => [
                'total' => $total,
                'used' => $used,
                'unused' => $unused
            ]
        ]);
    }
    
    /**
     * 获取短信日志列表 (API)
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function apiLogs(Request $request)
    {
        // 查询构建
        $query = SmsLog::query();
        
        // 搜索条件
        if ($request->filled('phone')) {
            $query->where('phone', 'like', '%' . $request->input('phone') . '%');
        }
        
        if ($request->filled('status') && $request->input('status') !== 'all') {
            $query->where('status', $request->input('status'));
        }
        
        if ($request->filled('type') && $request->input('type') !== 'all') {
            $query->where('type', $request->input('type'));
        }
        
        if ($request->filled('date_range')) {
            $dates = $request->input('date_range');
            if (is_array($dates) && count($dates) == 2) {
                $start_date = Carbon::parse($dates[0])->startOfDay();
                $end_date = Carbon::parse($dates[1])->endOfDay();
                $query->whereBetween('created_at', [$start_date, $end_date]);
            }
        }
        
        // 排序
        $sortBy = $request->input('sortBy', 'id');
        $sortDesc = $request->boolean('sortDesc', true);
        $query->orderBy($sortBy, $sortDesc ? 'desc' : 'asc');
        
        // 分页
        $perPage = $request->input('perPage', 20);
        $logs = $query->paginate($perPage);
        
        // 统计信息 (可选，如果前端需要)
        $total = SmsLog::count();
        $success = SmsLog::where('status', 1)->count();
        $failed = SmsLog::where('status', 0)->count();
        
        return response()->json([
            'logs' => $logs,
            'total' => $total,
            'success' => $success,
            'failed' => $failed,
        ]);
    }
} 