<?php

namespace App\Http\Controllers\Admin\Web;

use App\Http\Controllers\Controller;
use App\Models\Product;
use App\Models\ProductCategory;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class ProductController extends Controller
{
    /**
     * 获取商品列表
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        try {
            $query = Product::query();
            
            // 关键词搜索
            if ($request->has('keyword') && !empty($request->keyword)) {
                $query->where(function ($q) use ($request) {
                    $q->where('name', 'like', '%' . $request->keyword . '%')
                      ->orWhere('sku', 'like', '%' . $request->keyword . '%');
                });
            }
            
            // 分类搜索
            if ($request->has('category_id') && !empty($request->category_id)) {
                $query->where('category_id', $request->category_id);
            }
            
            // 状态搜索
            if ($request->has('status') && $request->status !== '' && $request->status !== null) {
                $query->where('status', $request->status);
            }
            
            // 排序
            $sortBy = $request->input('sort_by', 'created_at');
            $sortOrder = $request->input('sort_order', 'desc');
            $query->orderBy($sortBy, $sortOrder);
            
            // 分页
            $limit = $request->input('limit', 10);
            $products = $query->paginate($limit);
            
            // 处理商品数据，添加分类名称
            $data = $products->getCollection()->map(function ($product) {
                $product->category_name = $product->category ? $product->category->name : '未分类';
                return $product;
            });
            
            // 替换分页集合中的数据
            $products->setCollection($data);
            
            return response()->json([
                'code' => 0,
                'message' => '获取商品列表成功',
                'data' => $products->items(),
                'total' => $products->total(),
                'per_page' => $products->perPage(),
                'current_page' => $products->currentPage()
            ]);
        } catch (\Exception $e) {
            \Log::error('获取商品列表失败：' . $e->getMessage());
            return response()->json([
                'code' => 1,
                'message' => '获取商品列表失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取商品分类
     *
     * @return \Illuminate\Http\Response
     */
    public function getCategories()
    {
        $categories = ProductCategory::where('parent_id', 0)->get();
        $result = [];
        
        foreach ($categories as $category) {
            $item = [
                'id' => $category->id,
                'name' => $category->name,
                'children' => []
            ];
            
            $children = ProductCategory::where('parent_id', $category->id)->get();
            foreach ($children as $child) {
                $item['children'][] = [
                    'id' => $child->id,
                    'name' => $child->name
                ];
            }
            
            $result[] = $item;
        }
        
        return response()->json([
            'code' => 0,
            'message' => '获取商品分类成功',
            'data' => $result
        ]);
    }

    /**
     * 存储新商品
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        // 验证输入
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'sku' => 'required|string|max:100|unique:products,sku',
            'category_id' => 'required|exists:product_categories,id',
            'price' => 'required|numeric|min:0',
            'stock' => 'required|integer|min:0',
            'status' => 'required|in:0,1',
        ]);
        
        // 创建商品
        $product = new Product();
        $product->fill($request->all());
        
        // 处理商品缩略图
        if ($request->has('thumbnail') && !empty($request->thumbnail)) {
            // 如果是base64图片数据，将其保存为文件
            if (Str::startsWith($request->thumbnail, 'data:image')) {
                $image = $request->thumbnail;
                $image = str_replace('data:image/png;base64,', '', $image);
                $image = str_replace('data:image/jpeg;base64,', '', $image);
                $image = str_replace(' ', '+', $image);
                $imageName = 'products/' . Str::random(32) . '.png';
                Storage::disk('public')->put($imageName, base64_decode($image));
                $appUrl = config('app.url');
                $product->thumbnail = $appUrl . '/storage/' . $imageName;
            } else {
                $product->thumbnail = $request->thumbnail;
            }
        }
        
        $product->save();
        
        // 如果商品属于某个分类，更新该分类的商品数量
        if ($product->category) {
            $product->category->updateProductCount();
        }
        
        return response()->json([
            'code' => 0,
            'message' => '商品添加成功',
            'data' => $product
        ]);
    }

    /**
     * 获取单个商品
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $product = Product::findOrFail($id);
        
        return response()->json([
            'code' => 0,
            'message' => '获取商品详情成功',
            'data' => $product
        ]);
    }

    /**
     * 更新商品
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        try {
            // 验证输入
            $validated = $request->validate([
                'name' => 'required|string|max:255',
                'sku' => 'required|string|max:100|unique:products,sku,' . $id,
                'category_id' => 'required|exists:product_categories,id',
                'price' => 'required|numeric|min:0',
                'stock' => 'required|integer|min:0',
                'status' => 'required|in:0,1',
            ]);
            
            // 获取商品
            $product = Product::findOrFail($id);
            $oldCategoryId = $product->category_id;
            
            // 记录缩略图处理情况
            \Log::info('商品更新 - 图片处理开始, 商品ID: ' . $id);
            \Log::info('原始缩略图: ' . ($product->thumbnail ?? 'null'));
            \Log::info('请求中的缩略图: ' . ($request->thumbnail ?? 'null'));
            
            // 更新商品信息
            $product->fill($request->except('thumbnail'));
            
            // 处理商品缩略图
            if ($request->has('thumbnail')) {
                if (!empty($request->thumbnail)) {
                    // 如果与原缩略图不同，且是base64图片，保存新图片
                    if ($request->thumbnail !== $product->thumbnail && Str::startsWith($request->thumbnail, 'data:image')) {
                        \Log::info('处理Base64图片');
                        // 删除旧图片
                        if ($product->thumbnail && Storage::disk('public')->exists(str_replace('/storage/', '', $product->thumbnail))) {
                            Storage::disk('public')->delete(str_replace('/storage/', '', $product->thumbnail));
                        }
                        
                        // 保存新图片
                        $image = $request->thumbnail;
                        $image = str_replace('data:image/png;base64,', '', $image);
                        $image = str_replace('data:image/jpeg;base64,', '', $image);
                        $image = str_replace(' ', '+', $image);
                        $imageName = 'products/' . Str::random(32) . '.png';
                        Storage::disk('public')->put($imageName, base64_decode($image));
                        $appUrl = config('app.url');
                        $product->thumbnail = $appUrl . '/storage/' . $imageName;
                        \Log::info('新图片路径: ' . $product->thumbnail);
                    } else if ($request->thumbnail !== $product->thumbnail) {
                        \Log::info('使用非Base64图片');
                        // 非base64图片，直接更新
                        $product->thumbnail = $request->thumbnail;
                        \Log::info('更新后的图片路径: ' . $product->thumbnail);
                    } else {
                        \Log::info('图片路径未变更，保持原值');
                    }
                } else {
                    \Log::info('请求要求清除图片');
                    // 删除商品缩略图
                    if ($product->thumbnail && Storage::disk('public')->exists(str_replace('/storage/', '', $product->thumbnail))) {
                        Storage::disk('public')->delete(str_replace('/storage/', '', $product->thumbnail));
                    }
                    $product->thumbnail = null;
                }
            }
            
            $product->save();
            \Log::info('商品保存完成，最终缩略图路径: ' . ($product->thumbnail ?? 'null'));
            
            // 如果分类发生变化，更新相关分类的商品数量
            if ($oldCategoryId != $product->category_id) {
                if ($oldCategoryId) {
                    $oldCategory = ProductCategory::find($oldCategoryId);
                    if ($oldCategory) {
                        $oldCategory->updateProductCount();
                    }
                }
                
                if ($product->category) {
                    $product->category->updateProductCount();
                }
            }
            
            return response()->json([
                'code' => 0,
                'message' => '商品更新成功',
                'data' => $product
            ]);
        } catch (\Exception $e) {
            \Log::error('商品更新失败：' . $e->getMessage());
            \Log::error($e->getTraceAsString());
            return response()->json([
                'code' => 1,
                'message' => '商品更新失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 删除商品
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        try {
            $product = Product::findOrFail($id);
            
            // 删除商品缩略图
            if ($product->thumbnail && Storage::disk('public')->exists(str_replace('/storage/', '', $product->thumbnail))) {
                Storage::disk('public')->delete(str_replace('/storage/', '', $product->thumbnail));
            }
            
            // 记录分类ID
            $categoryId = $product->category_id;
            
            // 删除商品
            $product->delete();
            
            // 更新分类的商品数量
            if ($categoryId) {
                $category = ProductCategory::find($categoryId);
                if ($category) {
                    $category->updateProductCount();
                }
            }
            
            return response()->json([
                'code' => 0,
                'message' => '商品删除成功'
            ]);
        } catch (\Exception $e) {
            \Log::error('商品删除失败：' . $e->getMessage());
            return response()->json([
                'code' => 1,
                'message' => '商品删除失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 更新商品状态
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function updateStatus(Request $request, $id)
    {
        $request->validate([
            'status' => 'required|in:0,1',
        ]);
        
        $product = Product::findOrFail($id);
        $product->status = $request->status;
        $product->save();
        
        return response()->json([
            'code' => 0,
            'message' => '商品状态更新成功',
            'data' => $product
        ]);
    }
    
    /**
     * 批量操作商品
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function batchOperation(Request $request)
    {
        $request->validate([
            'ids' => 'required|array',
            'ids.*' => 'required|integer|exists:products,id',
            'operation' => 'required|string|in:delete,on_sale,off_sale,recommend,unrecommend,hot,unhot,new,unnew',
        ]);
        
        $products = Product::whereIn('id', $request->ids)->get();
        
        if ($products->isEmpty()) {
            return response()->json([
                'code' => 1,
                'message' => '未找到相关商品'
            ]);
        }
        
        switch ($request->operation) {
            case 'delete':
                foreach ($products as $product) {
                    // 删除商品缩略图
                    if ($product->thumbnail && Storage::disk('public')->exists(str_replace('/storage/', '', $product->thumbnail))) {
                        Storage::disk('public')->delete(str_replace('/storage/', '', $product->thumbnail));
                    }
                    
                    // 记录分类ID
                    $categoryId = $product->category_id;
                    
                    // 删除商品
                    $product->delete();
                    
                    // 更新分类的商品数量
                    if ($categoryId) {
                        $category = ProductCategory::find($categoryId);
                        if ($category) {
                            $category->updateProductCount();
                        }
                    }
                }
                $message = '批量删除成功';
                break;
            case 'on_sale':
                foreach ($products as $product) {
                    $product->status = 1;
                    $product->save();
                }
                $message = '批量上架成功';
                break;
            case 'off_sale':
                foreach ($products as $product) {
                    $product->status = 0;
                    $product->save();
                }
                $message = '批量下架成功';
                break;
            case 'recommend':
                foreach ($products as $product) {
                    $product->is_recommend = true;
                    $product->save();
                }
                $message = '批量设为推荐成功';
                break;
            case 'unrecommend':
                foreach ($products as $product) {
                    $product->is_recommend = false;
                    $product->save();
                }
                $message = '批量取消推荐成功';
                break;
            case 'hot':
                foreach ($products as $product) {
                    $product->is_hot = true;
                    $product->save();
                }
                $message = '批量设为热门成功';
                break;
            case 'unhot':
                foreach ($products as $product) {
                    $product->is_hot = false;
                    $product->save();
                }
                $message = '批量取消热门成功';
                break;
            case 'new':
                foreach ($products as $product) {
                    $product->is_new = true;
                    $product->save();
                }
                $message = '批量设为新品成功';
                break;
            case 'unnew':
                foreach ($products as $product) {
                    $product->is_new = false;
                    $product->save();
                }
                $message = '批量取消新品成功';
                break;
            default:
                $message = '操作不支持';
                break;
        }
        
        return response()->json([
            'code' => 0,
            'message' => $message
        ]);
    }
}
