<?php

namespace App\Http\Controllers\Admin\Web;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use App\Models\Admin;

class UserProfileController extends Controller
{
    /**
     * 上传头像
     */
    public function uploadAvatar(Request $request)
    {
        // 验证上传的文件
        $validator = Validator::make($request->all(), [
            'file' => 'required|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'code' => 1,
                'message' => '验证失败',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $file = $request->file('file');
            $user = auth()->user();
            
            // 生成唯一的文件名
            $fileName = 'avatar_' . $user->id . '_' . time() . '.' . $file->getClientOriginalExtension();
            
            // 存储头像
            $path = $file->storeAs('avatars', $fileName, 'public');
            $url = Storage::url($path);
            
            // 更新用户头像
            $user->avatar = $url;
            $user->save();
            
            return response()->json([
                'code' => 0,
                'message' => '头像上传成功',
                'data' => [
                    'url' => $url
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'code' => 1,
                'message' => '头像上传失败: ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * 更新用户个人资料
     */
    public function updateProfile(Request $request)
    {
        // 验证请求数据
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255',
            'phone' => 'nullable|string|max:20',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'code' => 1,
                'message' => '验证失败',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $user = auth()->user();
            
            // 更新用户资料
            $user->name = $request->name;
            $user->email = $request->email;
            $user->phone = $request->phone;
            $user->save();
            
            return response()->json([
                'code' => 0,
                'message' => '个人资料更新成功',
                'data' => $user
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'code' => 1,
                'message' => '个人资料更新失败: ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * 修改密码
     */
    public function changePassword(Request $request)
    {
        // 验证请求数据
        $validator = Validator::make($request->all(), [
            'current_password' => 'required|string',
            'new_password' => 'required|string|min:6',
            'confirm_password' => 'required|string|same:new_password',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'code' => 1,
                'message' => '验证失败',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $user = auth()->user();
            
            // 验证当前密码
            if (!Hash::check($request->current_password, $user->password)) {
                return response()->json([
                    'code' => 1,
                    'message' => '当前密码不正确'
                ], 422);
            }
            
            // 更新密码
            $user->password = Hash::make($request->new_password);
            $user->save();
            
            return response()->json([
                'code' => 0,
                'message' => '密码修改成功'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'code' => 1,
                'message' => '密码修改失败: ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * 更新微信绑定信息
     */
    public function updateWechatBinding(Request $request)
    {
        // 验证请求数据
        $validator = Validator::make($request->all(), [
            'bind_action' => 'required|string|in:bind,unbind',
            'open_id' => 'nullable|string|required_if:bind_action,bind',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'code' => 1,
                'message' => '验证失败',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $user = auth()->user();
            $bindAction = $request->bind_action;
            
            if ($bindAction === 'bind') {
                // 检查OpenID是否已被其他用户绑定
                $existingUser = Admin::where('open_id', $request->open_id)
                    ->where('id', '!=', $user->id)
                    ->first();
                    
                if ($existingUser) {
                    return response()->json([
                        'code' => 1,
                        'message' => '该微信账号已被其他用户绑定',
                    ], 400);
                }
                
                // 更新用户的OpenID
                $user->open_id = $request->open_id;
                $user->wechat_bind_time = now();
                $user->save();
                
                return response()->json([
                    'code' => 0,
                    'message' => '微信账号绑定成功',
                    'data' => [
                        'open_id' => $user->open_id,
                        'bind_time' => $user->wechat_bind_time
                    ]
                ]);
            } else {
                // 解绑微信账号
                $user->open_id = null;
                $user->wechat_bind_time = null;
                $user->save();
                
                return response()->json([
                    'code' => 0,
                    'message' => '微信账号解绑成功'
                ]);
            }
        } catch (\Exception $e) {
            return response()->json([
                'code' => 1,
                'message' => '操作失败: ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * 获取微信绑定二维码
     */
    public function getWechatQrCode(Request $request)
    {
        try {
            $user = auth()->user();
            $timestamp = time();
            $randomStr = \Str::random(8);
            $bindToken = hash('sha256', $user->id . $timestamp . $randomStr);
            
            // 保存绑定token到数据库或缓存
            \Cache::put('wechat_bind_token:'.$bindToken, $user->id, now()->addMinutes(10));
            
            // 生成绑定URL (实际项目中需要替换为真实的微信二维码生成逻辑)
            $bindUrl = url('/Tapp/app/pages/auth/wechat-binding.html?token=' . $bindToken);
            
            return response()->json([
                'code' => 0,
                'message' => '获取微信绑定二维码成功',
                'data' => [
                    'bind_token' => $bindToken,
                    'qr_code_url' => $bindUrl,
                    'expire_time' => now()->addMinutes(10)->timestamp
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'code' => 1,
                'message' => '获取微信绑定二维码失败: ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * 检查微信绑定状态
     */
    public function checkWechatBindStatus(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'bind_token' => 'required|string'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'code' => 1,
                'message' => '验证失败',
                'errors' => $validator->errors()
            ], 422);
        }
        
        try {
            $bindToken = $request->bind_token;
            $user = auth()->user();
            
            // 检查用户是否已绑定微信
            if ($user->open_id) {
                return response()->json([
                    'code' => 0,
                    'message' => '该账号已绑定微信',
                    'data' => [
                        'binding_status' => 'bound',
                        'open_id' => $user->open_id,
                        'bind_time' => $user->wechat_bind_time
                    ]
                ]);
            }
            
            // 检查绑定状态
            $cacheKey = 'wechat_bind_complete:'.$bindToken;
            $bindComplete = \Cache::get($cacheKey, false);
            
            if ($bindComplete) {
                // 绑定已完成，获取绑定的openID
                $openId = \Cache::get('wechat_bind_openid:'.$bindToken);
                
                // 更新用户信息
                $user->open_id = $openId;
                $user->wechat_bind_time = now();
                $user->save();
                
                // 清除缓存
                \Cache::forget($cacheKey);
                \Cache::forget('wechat_bind_openid:'.$bindToken);
                
                return response()->json([
                    'code' => 0,
                    'message' => '微信绑定成功',
                    'data' => [
                        'binding_status' => 'success',
                        'open_id' => $openId
                    ]
                ]);
            }
            
            return response()->json([
                'code' => 0,
                'message' => '等待微信扫码绑定',
                'data' => [
                    'binding_status' => 'pending'
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'code' => 1,
                'message' => '检查微信绑定状态失败: ' . $e->getMessage()
            ], 500);
        }
    }
}
