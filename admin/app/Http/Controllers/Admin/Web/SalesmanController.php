<?php

namespace App\Http\Controllers\Admin\Web;

use App\Http\Controllers\Controller;
use App\Models\AppUser;
use App\Models\Salesman;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rule;

class SalesmanController extends Controller
{
    /**
     * 显示业务员列表
     */
    public function index(Request $request)
    {
        $query = Salesman::with('user')->orderBy('created_at', 'desc');
        
        // 搜索条件
        if ($request->has('search')) {
            $search = $request->input('search');
            $query->whereHas('user', function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('phone', 'like', "%{$search}%");
            })->orWhere('employee_id', 'like', "%{$search}%");
        }
        
        // 状态筛选
        if ($request->has('status') && $request->input('status') !== 'all') {
            $query->where('status', $request->input('status'));
        }
        
        $salesmen = $query->paginate(15);
        
        return view('admin.salesmen.index', compact('salesmen'));
    }
    
    /**
     * 显示创建业务员表单
     */
    public function create()
    {
        // 获取未分配为业务员的用户
        $availableUsers = AppUser::where('is_salesman', 0)->get();
        
        // 获取可选的上级经理（当前所有业务员）
        $managers = Salesman::with('user')->get();
        
        return view('admin.salesmen.create', compact('availableUsers', 'managers'));
    }
    
    /**
     * 存储新创建的业务员
     */
    public function store(Request $request)
    {
        $this->validate($request, [
            'user_id' => 'required|exists:app_users,id|unique:salesmen,user_id',
            'employee_id' => 'nullable|string|max:50|unique:salesmen,employee_id',
            'title' => 'required|string|max:50',
            'department' => 'nullable|string|max:100',
            'region' => 'nullable|string|max:100',
            'manager_id' => 'nullable|exists:salesmen,id',
            'status' => 'required|in:active,leave,suspend',
        ]);
        
        DB::beginTransaction();
        
        try {
            // 创建业务员记录
            $salesman = new Salesman();
            $salesman->user_id = $request->input('user_id');
            $salesman->employee_id = $request->input('employee_id') ?: 'S'.str_pad($request->input('user_id'), 6, '0', STR_PAD_LEFT);
            $salesman->title = $request->input('title');
            $salesman->department = $request->input('department');
            $salesman->region = $request->input('region');
            $salesman->manager_id = $request->input('manager_id');
            $salesman->status = $request->input('status');
            $salesman->save();
            
            // 更新用户标记为业务员
            $user = AppUser::find($request->input('user_id'));
            $user->is_salesman = 1;
            $user->save();
            
            // 如果选择了上级经理，更新manager字段
            if ($request->filled('manager_id')) {
                $manager = Salesman::with('user')->find($request->input('manager_id'));
                if ($manager && $manager->user) {
                    $salesman->manager = $manager->user->name;
                    $salesman->save();
                }
            }
            
            DB::commit();
            
            return redirect()->route('admin.salesmen.index')
                ->with('success', '业务员创建成功');
                
        } catch (\Exception $e) {
            DB::rollBack();
            
            return back()->withErrors(['error' => '业务员创建失败：'.$e->getMessage()])
                ->withInput();
        }
    }
    
    /**
     * 显示指定业务员详情
     */
    public function show(Salesman $salesman)
    {
        $salesman->load(['user', 'managerUser.user']);
        
        // 获取销售统计数据
        $todaySales = \App\Models\SalesmanSale::getTodaySales($salesman->id);
        $monthSales = \App\Models\SalesmanSale::getMonthSales($salesman->id);
        $yearSales = \App\Models\SalesmanSale::getYearSales($salesman->id);
        $totalSales = $salesman->getTotalSalesAttribute();
        
        // 获取提成统计数据
        $pendingCommission = \App\Models\SalesmanCommission::getPendingCommissionAmount($salesman->id);
        $monthCommission = \App\Models\SalesmanCommission::getMonthCommissionAmount($salesman->id);
        $totalCommission = \App\Models\SalesmanCommission::getTotalCommissionAmount($salesman->id);
        
        // 获取最近的销售记录
        $recentSales = $salesman->sales()->orderBy('created_at', 'desc')->limit(5)->get();
        
        // 获取下属业务员
        $subordinates = $salesman->subordinates()->with('user')->get();
        
        // 获取客户数量
        $customerCount = \App\Models\SalesmanCustomer::getCustomerCount($salesman->id);
        
        return view('admin.salesmen.show', compact(
            'salesman', 
            'todaySales', 
            'monthSales', 
            'yearSales', 
            'totalSales', 
            'pendingCommission',
            'monthCommission',
            'totalCommission',
            'recentSales',
            'subordinates',
            'customerCount'
        ));
    }
    
    /**
     * 显示编辑业务员表单
     */
    public function edit(Salesman $salesman)
    {
        $salesman->load('user');
        
        // 获取可选的上级经理（除自己外的所有业务员）
        $managers = Salesman::with('user')
            ->where('id', '!=', $salesman->id)
            ->get();
        
        return view('admin.salesmen.edit', compact('salesman', 'managers'));
    }
    
    /**
     * 更新业务员信息
     */
    public function update(Request $request, Salesman $salesman)
    {
        $this->validate($request, [
            'employee_id' => [
                'nullable',
                'string',
                'max:50',
                Rule::unique('salesmen')->ignore($salesman->id),
            ],
            'title' => 'required|string|max:50',
            'department' => 'nullable|string|max:100',
            'region' => 'nullable|string|max:100',
            'manager_id' => [
                'nullable',
                'exists:salesmen,id',
                function ($attribute, $value, $fail) use ($salesman) {
                    if ($value == $salesman->id) {
                        $fail('上级经理不能选择自己');
                    }
                },
            ],
            'status' => 'required|in:active,leave,suspend',
        ]);
        
        try {
            $salesman->employee_id = $request->input('employee_id');
            $salesman->title = $request->input('title');
            $salesman->department = $request->input('department');
            $salesman->region = $request->input('region');
            $salesman->manager_id = $request->input('manager_id');
            $salesman->status = $request->input('status');
            
            // 如果选择了上级经理，更新manager字段
            if ($request->filled('manager_id')) {
                $manager = Salesman::with('user')->find($request->input('manager_id'));
                if ($manager && $manager->user) {
                    $salesman->manager = $manager->user->name;
                }
            } else {
                $salesman->manager = null;
            }
            
            $salesman->save();
            
            return redirect()->route('admin.salesmen.show', $salesman)
                ->with('success', '业务员信息更新成功');
                
        } catch (\Exception $e) {
            return back()->withErrors(['error' => '业务员更新失败：'.$e->getMessage()])
                ->withInput();
        }
    }
    
    /**
     * 删除业务员
     */
    public function destroy(Salesman $salesman)
    {
        try {
            DB::beginTransaction();
            
            // 获取关联的用户
            $user = $salesman->user;
            
            // 删除业务员记录
            $salesman->delete();
            
            // 更新用户不再是业务员
            if ($user) {
                $user->is_salesman = 0;
                $user->save();
            }
            
            DB::commit();
            
            return redirect()->route('admin.salesmen.index')
                ->with('success', '业务员删除成功');
                
        } catch (\Exception $e) {
            DB::rollBack();
            
            return back()->withErrors(['error' => '业务员删除失败：'.$e->getMessage()]);
        }
    }
}
