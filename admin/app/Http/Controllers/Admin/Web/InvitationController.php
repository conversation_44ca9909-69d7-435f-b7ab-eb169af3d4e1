<?php

namespace App\Http\Controllers\Admin\Web;

use App\Http\Controllers\Controller;
use App\Models\InvitationTemplate;
use App\Models\InvitationGuest;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Storage;

class InvitationController extends Controller
{
    /**
     * 获取邀请函模板列表
     */
    public function index(Request $request)
    {
        $query = InvitationTemplate::query();
        
        // 搜索条件
        if ($request->has('title')) {
            $query->where('title', 'like', '%' . $request->input('title') . '%');
        }
        if ($request->has('is_active')) {
            $query->where('is_active', $request->input('is_active'));
        }
        
        // 排序
        $sortField = $request->input('sort_field', 'created_at');
        $sortOrder = $request->input('sort_order', 'desc');
        $query->orderBy($sortField, $sortOrder);
        
        // 分页
        $perPage = $request->input('per_page', 15);
        $templates = $query->paginate($perPage);
        
        return response()->json([
            'code' => 0,
            'message' => '获取邀请函模板列表成功',
            'data' => $templates,
        ]);
    }
    
    /**
     * 创建新邀请函模板
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'content' => 'nullable|string',
            'background_image' => 'nullable|string',
            'background_music' => 'nullable|string',
            'map_location' => 'nullable|string',
            'map_address' => 'nullable|string',
            'map_longitude' => 'nullable|string',
            'map_latitude' => 'nullable|string',
            'event_time' => 'nullable|date',
            'extra_info' => 'nullable|array',
            'is_active' => 'boolean',
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                'code' => 1,
                'message' => '验证失败',
                'errors' => $validator->errors(),
            ], 422);
        }
        
        $template = InvitationTemplate::create($request->all());
        
        return response()->json([
            'code' => 0,
            'message' => '创建邀请函模板成功',
            'data' => $template,
        ]);
    }
    
    /**
     * 获取单个邀请函模板详情
     */
    public function show($id)
    {
        $template = InvitationTemplate::findOrFail($id);
        
        return response()->json([
            'code' => 0,
            'message' => '获取邀请函模板详情成功',
            'data' => $template,
        ]);
    }
    
    /**
     * 更新邀请函模板
     */
    public function update(Request $request, $id)
    {
        $template = InvitationTemplate::findOrFail($id);
        
        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'content' => 'nullable|string',
            'background_image' => 'nullable|string',
            'background_music' => 'nullable|string',
            'map_location' => 'nullable|string',
            'map_address' => 'nullable|string',
            'map_longitude' => 'nullable|string',
            'map_latitude' => 'nullable|string',
            'event_time' => 'nullable|date',
            'extra_info' => 'nullable|array',
            'is_active' => 'boolean',
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                'code' => 1,
                'message' => '验证失败',
                'errors' => $validator->errors(),
            ], 422);
        }
        
        $template->update($request->all());
        
        return response()->json([
            'code' => 0,
            'message' => '更新邀请函模板成功',
            'data' => $template,
        ]);
    }
    
    /**
     * 删除邀请函模板
     */
    public function destroy($id)
    {
        $template = InvitationTemplate::findOrFail($id);
        $template->delete();
        
        return response()->json([
            'code' => 0,
            'message' => '删除邀请函模板成功',
        ]);
    }
    
    /**
     * 修改邀请函模板状态
     */
    public function updateStatus(Request $request, $id)
    {
        $template = InvitationTemplate::findOrFail($id);
        
        $validator = Validator::make($request->all(), [
            'is_active' => 'required|boolean',
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                'code' => 1,
                'message' => '验证失败',
                'errors' => $validator->errors(),
            ], 422);
        }
        
        $template->update([
            'is_active' => $request->input('is_active'),
        ]);
        
        return response()->json([
            'code' => 0,
            'message' => '更新邀请函模板状态成功',
            'data' => $template,
        ]);
    }
    
    /**
     * 获取邀请函访客列表
     */
    public function guests(Request $request, $templateId)
    {
        $template = InvitationTemplate::findOrFail($templateId);
        
        $query = $template->guests();
        
        // 搜索条件
        if ($request->has('name')) {
            $query->where('name', 'like', '%' . $request->input('name') . '%');
        }
        if ($request->has('phone')) {
            $query->where('phone', 'like', '%' . $request->input('phone') . '%');
        }
        if ($request->has('status')) {
            $query->where('status', $request->input('status'));
        }
        
        // 排序
        $sortField = $request->input('sort_field', 'created_at');
        $sortOrder = $request->input('sort_order', 'desc');
        $query->orderBy($sortField, $sortOrder);
        
        // 分页
        $perPage = $request->input('per_page', 15);
        $guests = $query->with(['appUser', 'referrer'])->paginate($perPage);
        
        return response()->json([
            'code' => 0,
            'message' => '获取邀请函访客列表成功',
            'data' => $guests,
        ]);
    }
    
    /**
     * 上传背景图片
     */
    public function uploadBackgroundImage(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'image' => 'required|image|max:10240', // 最大10MB
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                'code' => 1,
                'message' => '验证失败',
                'errors' => $validator->errors(),
            ], 422);
        }
        
        $file = $request->file('image');
        $path = $file->store('invitation/background', 'public');
        
        return response()->json([
            'code' => 0,
            'message' => '上传背景图片成功',
            'data' => [
                'url' => Storage::url($path),
                'path' => $path,
            ],
        ]);
    }
    
    /**
     * 上传背景音乐
     */
    public function uploadBackgroundMusic(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'music' => 'required|file|mimes:mp3,wav,ogg|max:20480', // 最大20MB
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                'code' => 1,
                'message' => '验证失败',
                'errors' => $validator->errors(),
            ], 422);
        }
        
        $file = $request->file('music');
        $path = $file->store('invitation/music', 'public');
        
        return response()->json([
            'code' => 0,
            'message' => '上传背景音乐成功',
            'data' => [
                'url' => Storage::url($path),
                'path' => $path,
            ],
        ]);
    }
    
    /**
     * 获取统计数据
     */
    public function statistics($templateId)
    {
        $template = InvitationTemplate::findOrFail($templateId);
        
        $totalGuests = $template->guests()->count();
        $joinedGuests = $template->joinedGuests()->count();
        $registeredGuests = $template->registeredGuests()->count();
        
        // 按日期统计来访人数
        $dailyStats = $template->guests()
            ->selectRaw('DATE(created_at) as date, COUNT(*) as count')
            ->groupBy('date')
            ->orderBy('date')
            ->get();
        
        return response()->json([
            'code' => 0,
            'message' => '获取统计数据成功',
            'data' => [
                'total_guests' => $totalGuests,
                'joined_guests' => $joinedGuests,
                'registered_guests' => $registeredGuests,
                'daily_stats' => $dailyStats,
            ],
        ]);
    }
} 