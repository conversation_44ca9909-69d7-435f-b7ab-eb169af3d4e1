<?php

namespace App\Http\Controllers\Admin\Web;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\ProductCategory;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class ProductCategoryController extends Controller
{
    /**
     * 显示分类列表
     */
    public function index(Request $request)
    {
        try {
            $query = ProductCategory::query();
            
            // 搜索条件
            if ($request->has('name') && $request->name) {
                $query->where('name', 'like', '%' . $request->name . '%');
            }
            
            if ($request->has('status') && $request->status !== null && $request->status !== '') {
                $query->where('status', $request->status);
            }
            
            // 不获取子分类，通过前端处理树形结构
            $query->orderBy('sort', 'asc');
            
            $categories = $query->get();
            
            // 构建树形结构
            $tree = $this->buildCategoryTree($categories);
            
            return response()->json([
                'code' => 0,
                'message' => 'success',
                'data' => $tree
            ]);
        } catch (\Exception $e) {
            \Log::error('获取分类列表失败：' . $e->getMessage());
            return response()->json([
                'code' => 1,
                'message' => '获取分类列表失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 显示创建分类表单
     */
    public function create()
    {
        // 获取所有分类作为父级选择
        $categories = ProductCategory::where('status', 1)
            ->orderBy('sort', 'asc')
            ->get();
            
        return response()->json([
            'code' => 0,
            'message' => 'success',
            'data' => [
                'categories' => $categories
            ]
        ]);
    }

    /**
     * 存储新创建的分类
     */
    public function store(Request $request)
    {
        // 验证请求数据
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:20',
            'parent_id' => 'required|integer',
            'sort' => 'required|integer|min:0',
            'status' => 'required|in:1,2',
            'icon' => 'nullable|string'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'code' => 1,
                'message' => $validator->errors()->first()
            ], 422);
        }

        try {
            // 开启事务
            DB::beginTransaction();
            
            // 创建分类
            $category = new ProductCategory();
            $category->name = $request->name;
            $category->parent_id = $request->parent_id;
            $category->sort = $request->sort;
            $category->status = $request->status;
            $category->icon = $request->icon ?? '';
            
            $category->save();
            
            // 提交事务
            DB::commit();
            
            return response()->json([
                'code' => 0,
                'message' => '分类创建成功',
                'data' => $category
            ]);
        } catch (\Exception $e) {
            // 回滚事务
            DB::rollBack();
            Log::error('分类创建失败: ' . $e->getMessage());
            
            return response()->json([
                'code' => 1,
                'message' => '分类创建失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 显示指定分类
     */
    public function show($id)
    {
        $category = ProductCategory::find($id);
        
        if (!$category) {
            return response()->json([
                'code' => 1,
                'message' => '分类不存在'
            ], 404);
        }
        
        // 获取父级分类信息
        if ($category->parent_id > 0) {
            $category->load('parent');
        }
        
        return response()->json([
            'code' => 0,
            'message' => 'success',
            'data' => $category
        ]);
    }

    /**
     * 显示编辑分类表单
     */
    public function edit($id)
    {
        $category = ProductCategory::find($id);
        
        if (!$category) {
            return response()->json([
                'code' => 1,
                'message' => '分类不存在'
            ], 404);
        }
        
        // 获取所有可用作父级的分类
        $categories = ProductCategory::where('status', 1)
            ->where('id', '!=', $id) // 排除当前分类
            ->orderBy('sort', 'asc')
            ->get();
            
        return response()->json([
            'code' => 0,
            'message' => 'success',
            'data' => [
                'category' => $category,
                'categories' => $categories
            ]
        ]);
    }

    /**
     * 更新指定分类
     */
    public function update(Request $request, $id)
    {
        $category = ProductCategory::find($id);
        
        if (!$category) {
            return response()->json([
                'code' => 1,
                'message' => '分类不存在'
            ], 404);
        }

        // 验证请求数据
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:20',
            'parent_id' => 'required|integer',
            'sort' => 'required|integer|min:0',
            'status' => 'required|in:1,2',
            'icon' => 'nullable|string'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'code' => 1,
                'message' => $validator->errors()->first()
            ], 422);
        }
        
        // 检查父级分类是否为自己或自己的子分类
        if ($request->parent_id > 0 && $request->parent_id == $id) {
            return response()->json([
                'code' => 1,
                'message' => '父级分类不能是自己'
            ], 422);
        }
        
        // 检查是否会形成循环依赖
        if ($request->parent_id > 0 && $this->isDescendant($id, $request->parent_id)) {
            return response()->json([
                'code' => 1,
                'message' => '父级分类不能是自己的子分类'
            ], 422);
        }

        try {
            // 开启事务
            DB::beginTransaction();
            
            // 更新分类
            $category->name = $request->name;
            $category->parent_id = $request->parent_id;
            $category->sort = $request->sort;
            $category->status = $request->status;
            
            // 处理icon字段
            if ($request->has('icon')) {
                // 如果有提供icon字段，则更新
                $category->icon = $request->icon;
            }
            
            $category->save();
            
            // 提交事务
            DB::commit();
            
            return response()->json([
                'code' => 0,
                'message' => '分类更新成功',
                'data' => $category
            ]);
        } catch (\Exception $e) {
            // 回滚事务
            DB::rollBack();
            Log::error('分类更新失败: ' . $e->getMessage());
            
            return response()->json([
                'code' => 1,
                'message' => '分类更新失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 删除指定分类
     */
    public function destroy($id)
    {
        $category = ProductCategory::find($id);
        
        if (!$category) {
            return response()->json([
                'code' => 1,
                'message' => '分类不存在'
            ], 404);
        }
        
        // 检查是否有子分类
        $childCount = ProductCategory::where('parent_id', $id)->count();
        if ($childCount > 0) {
            return response()->json([
                'code' => 1,
                'message' => '该分类下有子分类，不能删除'
            ], 422);
        }
        
        // 检查分类下是否有商品
        $productCount = $category->products()->count();
        if ($productCount > 0) {
            return response()->json([
                'code' => 1,
                'message' => '该分类下有商品，不能删除'
            ], 422);
        }

        try {
            $category->delete();
            
            return response()->json([
                'code' => 0,
                'message' => '分类删除成功'
            ]);
        } catch (\Exception $e) {
            Log::error('分类删除失败: ' . $e->getMessage());
            
            return response()->json([
                'code' => 1,
                'message' => '分类删除失败: ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * 获取所有分类（不分页）
     */
    public function all()
    {
        $categories = ProductCategory::where('status', 1)
            ->orderBy('sort', 'asc')
            ->get();
            
        // 构建树形结构
        $tree = $this->buildCategoryTree($categories);
        
        return response()->json([
            'code' => 0,
            'message' => 'success',
            'data' => $tree
        ]);
    }
    
    /**
     * 构建分类树
     *
     * @param \Illuminate\Database\Eloquent\Collection $categories
     * @param int $parentId
     * @return array
     */
    protected function buildCategoryTree($categories, $parentId = 0)
    {
        $tree = [];
        
        foreach ($categories as $category) {
            if ($category->parent_id == $parentId) {
                $children = $this->buildCategoryTree($categories, $category->id);
                
                if (!empty($children)) {
                    $category->children = $children;
                }
                
                $tree[] = $category;
            }
        }
        
        return $tree;
    }
    
    /**
     * 检查是否是子分类
     *
     * @param int $parentId
     * @param int $childId
     * @return bool
     */
    protected function isDescendant($parentId, $childId)
    {
        $category = ProductCategory::find($childId);
        
        if (!$category) {
            return false;
        }
        
        if ($category->parent_id == $parentId) {
            return true;
        }
        
        if ($category->parent_id > 0) {
            return $this->isDescendant($parentId, $category->parent_id);
        }
        
        return false;
    }
}
