<?php

namespace App\Http\Controllers\Admin\Web;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Product;
use App\Models\ProductCategory;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class MallController extends Controller
{
    /**
     * 显示商品列表
     */
    public function index(Request $request)
    {
        $query = Product::query();
        
        // 搜索条件
        if ($request->has('keyword') && $request->keyword) {
            $query->where(function($q) use ($request) {
                $q->where('name', 'like', '%' . $request->keyword . '%')
                  ->orWhere('title', 'like', '%' . $request->keyword . '%');
            });
        }
        
        if ($request->has('status') && $request->status !== null) {
            $query->where('status', $request->status);
        }
        
        if ($request->has('category_id') && $request->category_id) {
            $query->where('cate_id', $request->category_id);
        }
        
        // 排序
        $query->orderBy($request->input('sort', 'id'), $request->input('order', 'desc'));
        
        // 分页
        $perPage = $request->input('limit', 15);
        $products = $query->paginate($perPage);
        
        // 加载分类信息
        $products->load('category');
        
        // 处理商品数据，添加分类名称等
        $data = $products->map(function ($product) {
            $product->category_name = $product->category ? $product->category->name : '';
            return $product;
        });
        
        return response()->json([
            'code' => 0,
            'message' => 'success',
            'data' => $data,
            'total' => $products->total()
        ]);
    }

    /**
     * 显示创建商品表单
     */
    public function create()
    {
        // 获取所有分类
        $categories = ProductCategory::where('status', 1)
            ->orderBy('sort', 'asc')
            ->get();
            
        return response()->json([
            'code' => 0,
            'message' => 'success',
            'data' => [
                'categories' => $categories
            ]
        ]);
    }

    /**
     * 存储新创建的商品
     */
    public function store(Request $request)
    {
        // 验证请求数据
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:100',
            'title' => 'required|string|max:100',
            'price' => 'required|numeric|min:0',
            'market_price' => 'required|numeric|min:0',
            'stock' => 'required|integer|min:0',
            'cate_id' => 'required',
            'status' => 'required|in:0,1',
            'img' => 'nullable|string'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'code' => 1,
                'message' => $validator->errors()->first()
            ], 422);
        }

        try {
            // 开启事务
            DB::beginTransaction();
            
            // 创建商品
            $product = new Product();
            $product->name = $request->name;
            $product->title = $request->title;
            $product->price = $request->price;
            $product->market_price = $request->market_price;
            $product->discount_price = $request->discount_price ?? 0;
            $product->stock = $request->stock;
            $product->cate_id = $request->cate_id;
            $product->status = $request->status;
            $product->img = $request->img;
            $product->images = $request->images ?? '';
            $product->describe = $request->describe ?? '';
            $product->intro = $request->intro ?? '';
            $product->is_sku = $request->is_sku ?? 0;
            $product->unit = $request->unit ?? '';
            $product->integral = $request->integral ?? 0;
            $product->superior_type = $request->superior_type ?? 2; // 默认关闭分销
            $product->postage = $request->postage ?? 0;
            $product->template_id = $request->template_id ?? null;
            $product->template_type = $request->template_type ?? 1; // 默认包邮
            $product->deliveryDay = $request->deliveryDay ?? 3;
            $product->create_time = now();
            $product->update_time = now();
            
            $product->save();
            
            // 提交事务
            DB::commit();
            
            return response()->json([
                'code' => 0,
                'message' => '商品创建成功',
                'data' => $product
            ]);
        } catch (\Exception $e) {
            // 回滚事务
            DB::rollBack();
            Log::error('商品创建失败: ' . $e->getMessage());
            
            return response()->json([
                'code' => 1,
                'message' => '商品创建失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 显示指定商品
     */
    public function show($id)
    {
        $product = Product::with('category')->find($id);
        
        if (!$product) {
            return response()->json([
                'code' => 1,
                'message' => '商品不存在'
            ], 404);
        }
        
        return response()->json([
            'code' => 0,
            'message' => 'success',
            'data' => $product
        ]);
    }

    /**
     * 显示编辑商品表单
     */
    public function edit($id)
    {
        $product = Product::find($id);
        
        if (!$product) {
            return response()->json([
                'code' => 1,
                'message' => '商品不存在'
            ], 404);
        }
        
        // 获取所有分类
        $categories = ProductCategory::where('status', 1)
            ->orderBy('sort', 'asc')
            ->get();
            
        return response()->json([
            'code' => 0,
            'message' => 'success',
            'data' => [
                'product' => $product,
                'categories' => $categories
            ]
        ]);
    }

    /**
     * 更新指定商品
     */
    public function update(Request $request, $id)
    {
        $product = Product::find($id);
        
        if (!$product) {
            return response()->json([
                'code' => 1,
                'message' => '商品不存在'
            ], 404);
        }
        
        // 验证请求数据
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:100',
            'title' => 'required|string|max:100',
            'price' => 'required|numeric|min:0',
            'market_price' => 'required|numeric|min:0',
            'stock' => 'required|integer|min:0',
            'cate_id' => 'required',
            'status' => 'required|in:0,1',
            'img' => 'nullable|string'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'code' => 1,
                'message' => $validator->errors()->first()
            ], 422);
        }

        try {
            // 开启事务
            DB::beginTransaction();
            
            // 更新商品
            $product->name = $request->name;
            $product->title = $request->title;
            $product->price = $request->price;
            $product->market_price = $request->market_price;
            $product->discount_price = $request->discount_price ?? $product->discount_price;
            $product->stock = $request->stock;
            $product->cate_id = $request->cate_id;
            $product->status = $request->status;
            $product->img = $request->img;
            $product->images = $request->images ?? $product->images;
            $product->describe = $request->describe ?? $product->describe;
            $product->intro = $request->intro ?? $product->intro;
            $product->is_sku = $request->is_sku ?? $product->is_sku;
            $product->unit = $request->unit ?? $product->unit;
            $product->integral = $request->integral ?? $product->integral;
            $product->superior_type = $request->superior_type ?? $product->superior_type;
            $product->postage = $request->postage ?? $product->postage;
            $product->template_id = $request->template_id ?? $product->template_id;
            $product->template_type = $request->template_type ?? $product->template_type;
            $product->deliveryDay = $request->deliveryDay ?? $product->deliveryDay;
            $product->update_time = now();
            
            $product->save();
            
            // 提交事务
            DB::commit();
            
            return response()->json([
                'code' => 0,
                'message' => '商品更新成功',
                'data' => $product
            ]);
        } catch (\Exception $e) {
            // 回滚事务
            DB::rollBack();
            Log::error('商品更新失败: ' . $e->getMessage());
            
            return response()->json([
                'code' => 1,
                'message' => '商品更新失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 删除指定商品
     */
    public function destroy($id)
    {
        $product = Product::find($id);
        
        if (!$product) {
            return response()->json([
                'code' => 1,
                'message' => '商品不存在'
            ], 404);
        }

        try {
            // 更新商品状态为删除
            $product->status = 2; // 2表示删除
            $product->update_time = now();
            $product->save();
            
            return response()->json([
                'code' => 0,
                'message' => '商品删除成功'
            ]);
        } catch (\Exception $e) {
            Log::error('商品删除失败: ' . $e->getMessage());
            
            return response()->json([
                'code' => 1,
                'message' => '商品删除失败: ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * 批量设置商品状态
     */
    public function setStatus(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'ids' => 'required|array',
            'ids.*' => 'required|integer',
            'status' => 'required|in:0,1'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'code' => 1,
                'message' => $validator->errors()->first()
            ], 422);
        }

        try {
            Product::whereIn('id', $request->ids)
                ->where('status', '!=', 2) // 不处理已删除的商品
                ->update([
                    'status' => $request->status,
                    'update_time' => now()
                ]);
            
            return response()->json([
                'code' => 0,
                'message' => '批量设置商品状态成功'
            ]);
        } catch (\Exception $e) {
            Log::error('批量设置商品状态失败: ' . $e->getMessage());
            
            return response()->json([
                'code' => 1,
                'message' => '批量设置商品状态失败: ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * 获取商品分类列表
     */
    public function getCategories()
    {
        $categories = ProductCategory::where('status', 1)
            ->orderBy('sort', 'asc')
            ->get();
            
        return response()->json([
            'code' => 0,
            'message' => 'success',
            'data' => $categories
        ]);
    }
}
