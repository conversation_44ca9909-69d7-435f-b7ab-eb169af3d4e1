<?php

namespace App\Http\Controllers\Admin\Web;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\Log;

class AdminMenuController extends Controller
{
    /**
     * 获取管理员菜单 - RESTful API风格
     *
     * 首先尝试从数据库获取菜单数据，如果失败则使用默认菜单
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getMenus(Request $request)
    {
        try {
            Log::info('开始获取菜单数据 - RESTful API');

            // 检查admin_menu表是否存在
            if (!Schema::hasTable('admin_menu')) {
                Log::warning('admin_menu表不存在，返回默认菜单');
                return $this->getDefaultMenus();
            }

            // 从数据库获取菜单数据
            $menus = DB::table('admin_menu')
                ->where('is_enabled', 1)
                ->orderBy('sort_order', 'asc')
                ->get(['id', 'parent_id', 'title', 'icon', 'path', 'sort_order', 'is_enabled']);

            // 如果菜单表为空，返回默认菜单结构
            if ($menus->isEmpty()) {
                Log::warning('admin_menu表为空，返回默认菜单');
                return $this->getDefaultMenus();
            }

            // 构建菜单树
            $menuTree = $this->buildMenuTree($menus);

            return response()->json([
                'code' => 0,
                'message' => '成功获取菜单',
                'data' => $menuTree
            ]);
        } catch (\Exception $e) {
            Log::error('获取菜单数据失败: ' . $e->getMessage());
            return $this->getDefaultMenus();
        }
    }

    /**
     * 构建菜单树
     *
     * @param \Illuminate\Support\Collection $menus
     * @return array
     */
    private function buildMenuTree($menus)
    {
        // 将菜单项按照parent_id分组
        $menuMap = [];
        foreach ($menus as $menu) {
            $menuMap[$menu->parent_id][] = $menu;
        }

        // 递归构建菜单树
        return $this->buildMenuTreeRecursive($menuMap, 0);
    }

    /**
     * 递归构建菜单树
     *
     * @param array $menuMap
     * @param int $parentId
     * @return array
     */
    private function buildMenuTreeRecursive($menuMap, $parentId)
    {
        $result = [];

        if (!isset($menuMap[$parentId])) {
            return $result;
        }

        foreach ($menuMap[$parentId] as $menu) {
            $menuItem = [
                'id' => (string)$menu->id,
                'path' => $menu->path,
                'meta' => [
                    'title' => $menu->title,
                    'icon' => $menu->icon
                ]
            ];

            // 如果有子菜单，递归构建子菜单
            if (isset($menuMap[$menu->id])) {
                $menuItem['children'] = $this->buildMenuTreeRecursive($menuMap, $menu->id);
            } else {
                $menuItem['children'] = [];
            }

            $result[] = $menuItem;
        }

        return $result;
    }

    /**
     * 获取默认菜单
     *
     * @return \Illuminate\Http\JsonResponse
     */
    private function getDefaultMenus()
    {
        return response()->json([
            'code' => 0,
            'message' => '成功获取菜单（默认）',
            'data' => [
                [
                    'id' => '1',
                    'path' => 'dashboard',
                    'meta' => ['title' => '控制面板', 'icon' => 'Monitor'],
                    'children' => []
                ],
                [
                    'id' => '2',
                    'path' => 'users',
                    'meta' => ['title' => '用户管理', 'icon' => 'User'],
                    'children' => [
                        ['id' => '2-1', 'path' => 'users/app-users', 'meta' => ['title' => 'APP用户', 'icon' => 'Avatar']],
                        ['id' => '2-2', 'path' => 'users/admins', 'meta' => ['title' => '后台管理员', 'icon' => 'UserFilled']],
                        ['id' => '2-3', 'path' => 'users/salesmen', 'meta' => ['title' => '业务员管理', 'icon' => 'User']],
                        ['id' => '2-4', 'path' => 'users/salesman-stats', 'meta' => ['title' => '业务员统计', 'icon' => 'DataAnalysis']]
                    ]
                ],
                [
                    'id' => '3',
                    'path' => 'mall',
                    'meta' => ['title' => '商城管理', 'icon' => 'ShoppingCart'],
                    'children' => [
                        ['id' => '3-1', 'path' => 'mall/categories', 'meta' => ['title' => '商品分类', 'icon' => 'List']],
                        ['id' => '3-2', 'path' => 'mall/products', 'meta' => ['title' => '商品管理', 'icon' => 'Goods']],
                        ['id' => '3-3', 'path' => 'mall/orders', 'meta' => ['title' => '订单管理', 'icon' => 'Document']]
                    ]
                ],
                [
                    'id' => '4',
                    'path' => 'devices',
                    'meta' => ['title' => '设备管理', 'icon' => 'Cpu'],
                    'children' => [
                        ['id' => '4-1', 'path' => 'devices/list', 'meta' => ['title' => '设备列表', 'icon' => 'Grid']],
                        ['id' => '4-2', 'path' => 'devices/stats', 'meta' => ['title' => '设备统计', 'icon' => 'DataAnalysis']],
                        ['id' => '4-3', 'path' => 'tapp-devices', 'meta' => ['title' => '点点够设备', 'icon' => 'Grid']]
                    ]
                ],
                [
                    'id' => '5',
                    'path' => 'vip',
                    'meta' => ['title' => 'VIP管理', 'icon' => 'Star'],
                    'children' => [
                        ['id' => '5-1', 'path' => 'vip-dividends', 'meta' => ['title' => 'VIP分红', 'icon' => 'Money']],
                        ['id' => '5-2', 'path' => 'users/vip-dividends', 'meta' => ['title' => 'VIP用户', 'icon' => 'User']]
                    ]
                ],
                [
                    'id' => '6',
                    'path' => 'installation',
                    'meta' => ['title' => '安装管理', 'icon' => 'Tools'],
                    'children' => [
                        ['id' => '6-1', 'path' => 'installation/booking', 'meta' => ['title' => '安装预约', 'icon' => 'Calendar']],
                        ['id' => '6-2', 'path' => 'installation/statistics', 'meta' => ['title' => '安装统计', 'icon' => 'DataAnalysis']]
                    ]
                ],
                [
                    'id' => '7',
                    'path' => 'system',
                    'meta' => ['title' => '系统管理', 'icon' => 'Setting'],
                    'children' => [
                        ['id' => '7-1', 'path' => 'system/menu', 'meta' => ['title' => '菜单管理', 'icon' => 'Menu']],
                        ['id' => '7-2', 'path' => 'system/nav', 'meta' => ['title' => '导航管理', 'icon' => 'Operation']],
                        ['id' => '7-3', 'path' => 'system/sms/logs', 'meta' => ['title' => '短信日志', 'icon' => 'ChatDotRound']],
                        ['id' => '7-4', 'path' => 'system/sms/stats', 'meta' => ['title' => '短信统计', 'icon' => 'DataLine']]
                    ]
                ]
            ]
        ]);
    }
}
