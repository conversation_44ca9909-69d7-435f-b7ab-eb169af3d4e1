<?php

namespace App\Http\Controllers\Admin\Web;

use App\Http\Controllers\Controller;
use App\Models\Salesman;
use App\Models\SalesmanTarget;
use Carbon\Carbon;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class SalesmanTargetController extends Controller
{
    /**
     * 显示业务员目标列表
     *
     * @param Salesman $salesman
     * @return \Illuminate\View\View
     */
    public function index(Request $request, Salesman $salesman)
    {
        // 获取过滤条件
        $periodType = $request->input('period_type');
        $status = $request->input('status');

        // 查询目标
        $targets = SalesmanTarget::where('salesman_id', $salesman->id);

        // 应用过滤
        if ($periodType) {
            $targets->where('period_type', $periodType);
        }
        
        if ($status) {
            $targets->where('status', $status);
        }
        
        // 获取结果，按开始日期倒序排列
        $targets = $targets->orderBy('start_date', 'desc')->paginate(15);
        
        return view('admin.salesmen.targets.index', [
            'salesman' => $salesman,
            'targets' => $targets,
            'periodType' => $periodType,
            'status' => $status
        ]);
    }
    
    /**
     * 显示创建业务员目标表单
     *
     * @param Salesman $salesman
     * @return \Illuminate\View\View
     */
    public function create(Salesman $salesman)
    {
        return view('admin.salesmen.targets.create', [
            'salesman' => $salesman
        ]);
    }
    
    /**
     * 存储新创建的业务员目标
     *
     * @param \Illuminate\Http\Request $request
     * @param Salesman $salesman
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(Request $request, Salesman $salesman)
    {
        // 验证输入
        $validatedData = $request->validate([
            'period_type' => 'required|in:month,year,quarter',
            'period' => 'required|string',
            'target_quantity' => 'required|numeric|min:0',
            'target_amount' => 'required|numeric|min:0',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after_or_equal:start_date',
            'achievement' => 'nullable|numeric|min:0',
            'status' => 'required|in:in_progress,completed,failed',
            'remarks' => 'nullable|string',
        ]);

        // 验证周期格式
        $this->validatePeriodFormat($validatedData['period_type'], $validatedData['period']);

        // 检查是否已存在相同周期的目标
        $existingTarget = SalesmanTarget::where('salesman_id', $salesman->id)
            ->where('period_type', $validatedData['period_type'])
            ->where('period', $validatedData['period'])
            ->first();

        if ($existingTarget) {
            return redirect()->back()
                ->withInput()
                ->withErrors(['period' => '该业务员在相同周期内已存在销售目标']);
        }
        
        try {
            // 创建目标
            $target = new SalesmanTarget();
            $target->salesman_id = $salesman->id;
            $target->period_type = $validatedData['period_type'];
            $target->period = $validatedData['period'];
            $target->target_quantity = $validatedData['target_quantity'];
            $target->target_amount = $validatedData['target_amount'];
            $target->start_date = $validatedData['start_date'];
            $target->end_date = $validatedData['end_date'];
            $target->achievement = $validatedData['achievement'] ?? 0;
            $target->status = $validatedData['status'];
            $target->remarks = $validatedData['remarks'] ?? null;
            $target->save();

            return redirect()->route('admin.salesmen.targets.index', $salesman)
                ->with('success', '销售目标创建成功！');
        } catch (Exception $e) {
            Log::error('创建销售目标失败: ' . $e->getMessage());
            return redirect()->back()
                ->withInput()
                ->withErrors(['error' => '创建销售目标失败，请稍后重试']);
        }
    }

    /**
     * 显示编辑业务员目标表单
     *
     * @param Salesman $salesman
     * @param SalesmanTarget $target
     * @return \Illuminate\View\View
     */
    public function edit(Salesman $salesman, SalesmanTarget $target)
    {
        // 确保目标属于业务员
        if ($target->salesman_id !== $salesman->id) {
            abort(404);
        }

        return view('admin.salesmen.targets.edit', [
            'salesman' => $salesman,
            'target' => $target
        ]);
    }

    /**
     * 更新业务员目标
     *
     * @param \Illuminate\Http\Request $request
     * @param Salesman $salesman
     * @param SalesmanTarget $target
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(Request $request, Salesman $salesman, SalesmanTarget $target)
    {
        // 确保目标属于业务员
        if ($target->salesman_id !== $salesman->id) {
            abort(404);
        }

        // 验证输入
        $validatedData = $request->validate([
            'period_type' => 'required|in:month,year,quarter',
            'period' => 'required|string',
            'target_quantity' => 'required|numeric|min:0',
            'target_amount' => 'required|numeric|min:0',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after_or_equal:start_date',
            'achievement' => 'nullable|numeric|min:0',
            'status' => 'required|in:in_progress,completed,failed',
            'remarks' => 'nullable|string',
        ]);

        // 验证周期格式
        $this->validatePeriodFormat($validatedData['period_type'], $validatedData['period']);

        // 检查是否已存在相同周期的目标（排除当前目标）
        $existingTarget = SalesmanTarget::where('salesman_id', $salesman->id)
            ->where('period_type', $validatedData['period_type'])
            ->where('period', $validatedData['period'])
            ->where('id', '!=', $target->id)
            ->first();

        if ($existingTarget) {
            return redirect()->back()
                ->withInput()
                ->withErrors(['period' => '该业务员在相同周期内已存在销售目标']);
        }

        try {
            // 更新目标
            $target->period_type = $validatedData['period_type'];
            $target->period = $validatedData['period'];
            $target->target_quantity = $validatedData['target_quantity'];
            $target->target_amount = $validatedData['target_amount'];
            $target->start_date = $validatedData['start_date'];
            $target->end_date = $validatedData['end_date'];
            $target->achievement = $validatedData['achievement'] ?? $target->achievement;
            $target->status = $validatedData['status'];
            $target->remarks = $validatedData['remarks'] ?? null;
            $target->save();
            
            return redirect()->route('admin.salesmen.targets.index', $salesman)
                ->with('success', '销售目标更新成功！');
        } catch (Exception $e) {
            Log::error('更新销售目标失败: ' . $e->getMessage());
            return redirect()->back()
                ->withInput()
                ->withErrors(['error' => '更新销售目标失败，请稍后重试']);
        }
    }

    /**
     * 删除业务员目标
     *
     * @param Salesman $salesman
     * @param SalesmanTarget $target
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroy(Salesman $salesman, SalesmanTarget $target)
    {
        // 确保目标属于业务员
        if ($target->salesman_id !== $salesman->id) {
            abort(404);
        }

        try {
            $target->delete();
            return redirect()->route('admin.salesmen.targets.index', $salesman)
                ->with('success', '销售目标已成功删除！');
        } catch (Exception $e) {
            Log::error('删除销售目标失败: ' . $e->getMessage());
            return redirect()->back()
                ->withErrors(['error' => '删除销售目标失败，请稍后重试']);
        }
    }

    /**
     * 更新所有业务员的目标完成情况
     *
     * @return \Illuminate\Http\RedirectResponse
     */
    public function updateAchievements()
    {
        try {
            // 获取所有有活跃目标的业务员
            $salesmenWithTargets = SalesmanTarget::select('salesman_id')
                ->where('status', 'in_progress')
                ->distinct()
                ->get()
                ->pluck('salesman_id');

            $updatedCount = 0;

            // 处理每个业务员的目标
            foreach ($salesmenWithTargets as $salesmanId) {
                $salesman = Salesman::find($salesmanId);
                if (!$salesman) continue;

                // 获取业务员的所有进行中的目标
                $activeTargets = SalesmanTarget::where('salesman_id', $salesmanId)
                    ->where('status', 'in_progress')
                    ->get();

                foreach ($activeTargets as $target) {
                    // 更新目标完成情况
                    $achievement = $this->calculateAchievement($salesman, $target);
                    $target->achievement = $achievement;

                    // 更新目标状态
                    if ($target->achievement >= $target->target_quantity) {
                        $target->status = 'completed';
                    }

                    // 如果目标已过期且未完成，标记为失败
                    if (Carbon::parse($target->end_date)->isPast() && $target->achievement < $target->target_quantity) {
                        $target->status = 'failed';
                    }

                    $target->save();
                    $updatedCount++;
                }
            }

            return redirect()->back()
                ->with('success', "已成功更新 {$updatedCount} 个销售目标的完成情况！");
        } catch (Exception $e) {
            Log::error('更新目标完成情况失败: ' . $e->getMessage());
            return redirect()->back()
                ->withErrors(['error' => '更新目标完成情况失败，请稍后重试']);
        }
    }

    /**
     * 验证周期格式
     *
     * @param string $periodType
     * @param string $period
     * @throws \Illuminate\Validation\ValidationException
     */
    private function validatePeriodFormat($periodType, $period)
    {
        $valid = false;
        switch ($periodType) {
            case 'month':
                // 验证月度格式 YYYY-MM
                $valid = preg_match('/^\d{4}-\d{2}$/', $period);
                break;
            case 'year':
                // 验证年度格式 YYYY
                $valid = preg_match('/^\d{4}$/', $period);
                break;
            case 'quarter':
                // 验证季度格式 YYYY-QN
                $valid = preg_match('/^\d{4}-Q[1-4]$/', $period);
                break;
        }

        if (!$valid) {
            throw \Illuminate\Validation\ValidationException::withMessages([
                'period' => ['周期格式不正确，请使用正确的格式']
            ]);
        }
    }

    /**
     * 计算目标完成情况
     *
     * @param Salesman $salesman
     * @param SalesmanTarget $target
     * @return int
     */
    private function calculateAchievement(Salesman $salesman, SalesmanTarget $target)
    {
        try {
            // 构建查询条件
            $query = DB::table('tapp_devices')
                ->where('salesman_id', $salesman->id)
                ->whereNotNull('activated_at');

            // 根据目标周期类型设置时间范围
            if ($target->start_date && $target->end_date) {
                $query->whereBetween('activated_at', [
                    Carbon::parse($target->start_date)->startOfDay(),
                    Carbon::parse($target->end_date)->endOfDay()
                ]);
            }

            // 统计激活设备数量
            return $query->count();
        } catch (Exception $e) {
            Log::error('计算目标完成情况失败: ' . $e->getMessage());
            return 0;
        }
    }
}
