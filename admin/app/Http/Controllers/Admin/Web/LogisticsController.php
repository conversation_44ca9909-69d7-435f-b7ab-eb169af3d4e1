<?php

namespace App\Http\Controllers\Admin\Web;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Logistics;
use App\Models\Order;
use App\Models\OrderItem;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class LogisticsController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Logistics::query();
        
        // 搜索条件
        if ($request->has('keyword') && $request->keyword) {
            $query->where(function($q) use ($request) {
                $q->where('number', 'like', '%' . $request->keyword . '%')
                  ->orWhere('expName', 'like', '%' . $request->keyword . '%');
            });
        }
        
        if ($request->has('deliverystatus') && $request->deliverystatus !== null) {
            $query->where('deliverystatus', $request->deliverystatus);
        }
        
        // 默认按照ID倒序排序
        $query->orderBy($request->input('sort', 'id'), $request->input('order', 'desc'));
        
        // 分页
        $perPage = $request->input('limit', 15);
        $logistics = $query->paginate($perPage);
        
        // 处理物流数据，添加状态文本等
        $data = $logistics->map(function ($item) {
            $item->status_text = $item->getDeliveryStatusTextAttribute();
            return $item;
        });
        
        return response()->json([
            'code' => 0,
            'message' => 'success',
            'data' => $data,
            'total' => $logistics->total()
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        // 验证请求数据
        $validator = Validator::make($request->all(), [
            'number' => 'required|string|max:60|unique:ddg_order_delivery,number',
            'expName' => 'required|string|max:50',
            'deliverystatus' => 'required|integer|min:0|max:6'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'code' => 1,
                'message' => $validator->errors()->first()
            ], 422);
        }

        try {
            // 创建物流信息
            $logistics = new Logistics();
            $logistics->number = $request->number;
            $logistics->expName = $request->expName;
            $logistics->deliverystatus = $request->deliverystatus;
            $logistics->list = $request->list ?? '';
            $logistics->takeTime = $request->takeTime ?? '';
            $logistics->save();
            
            return response()->json([
                'code' => 0,
                'message' => '物流信息添加成功',
                'data' => $logistics
            ]);
        } catch (\Exception $e) {
            Log::error('物流信息添加失败: ' . $e->getMessage());
            
            return response()->json([
                'code' => 1,
                'message' => '物流信息添加失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show($id)
    {
        $logistics = Logistics::find($id);
        
        if (!$logistics) {
            return response()->json([
                'code' => 1,
                'message' => '物流信息不存在'
            ], 404);
        }
        
        // 添加状态文本
        $logistics->status_text = $logistics->getDeliveryStatusTextAttribute();
        
        // 查找关联的订单项
        $orderItems = OrderItem::where('ship_area_id', $logistics->number)
            ->with('order')
            ->get();
        
        return response()->json([
            'code' => 0,
            'message' => 'success',
            'data' => [
                'logistics' => $logistics,
                'order_items' => $orderItems
            ]
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, $id)
    {
        $logistics = Logistics::find($id);
        
        if (!$logistics) {
            return response()->json([
                'code' => 1,
                'message' => '物流信息不存在'
            ], 404);
        }
        
        // 验证请求数据
        $validator = Validator::make($request->all(), [
            'number' => 'required|string|max:60|unique:ddg_order_delivery,number,' . $id,
            'expName' => 'required|string|max:50',
            'deliverystatus' => 'required|integer|min:0|max:6'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'code' => 1,
                'message' => $validator->errors()->first()
            ], 422);
        }

        try {
            // 更新物流信息
            $logistics->number = $request->number;
            $logistics->expName = $request->expName;
            $logistics->deliverystatus = $request->deliverystatus;
            $logistics->list = $request->list ?? $logistics->list;
            $logistics->takeTime = $request->takeTime ?? $logistics->takeTime;
            $logistics->save();
            
            return response()->json([
                'code' => 0,
                'message' => '物流信息更新成功',
                'data' => $logistics
            ]);
        } catch (\Exception $e) {
            Log::error('物流信息更新失败: ' . $e->getMessage());
            
            return response()->json([
                'code' => 1,
                'message' => '物流信息更新失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($id)
    {
        $logistics = Logistics::find($id);
        
        if (!$logistics) {
            return response()->json([
                'code' => 1,
                'message' => '物流信息不存在'
            ], 404);
        }
        
        // 检查是否有关联的订单
        $orderItemsCount = OrderItem::where('ship_area_id', $logistics->number)->count();
        if ($orderItemsCount > 0) {
            return response()->json([
                'code' => 1,
                'message' => '该物流信息已关联订单，不能删除'
            ], 422);
        }

        try {
            $logistics->delete();
            
            return response()->json([
                'code' => 0,
                'message' => '物流信息删除成功'
            ]);
        } catch (\Exception $e) {
            Log::error('物流信息删除失败: ' . $e->getMessage());
            
            return response()->json([
                'code' => 1,
                'message' => '物流信息删除失败: ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * 查询物流轨迹
     */
    public function track($number)
    {
        $logistics = Logistics::where('number', $number)->first();
        
        if (!$logistics) {
            return response()->json([
                'code' => 1,
                'message' => '物流信息不存在'
            ], 404);
        }
        
        // 这里可以调用第三方物流查询API获取物流轨迹
        // 由于实际查询需要接入第三方API，这里返回模拟数据
        $trackInfo = [
            'number' => $logistics->number,
            'expName' => $logistics->expName,
            'status' => $logistics->deliverystatus,
            'status_text' => $logistics->getDeliveryStatusTextAttribute(),
            'tracks' => []
        ];
        
        // 生成模拟的物流轨迹数据
        if ($logistics->list) {
            // 如果有物流轨迹数据，直接使用
            $trackInfo['tracks'] = json_decode($logistics->list, true) ?? [];
        } else {
            // 否则生成模拟数据
            $now = now();
            
            switch ($logistics->deliverystatus) {
                case 0: // 快递收件(揽件)
                    $trackInfo['tracks'][] = [
                        'time' => $now->toDateTimeString(),
                        'context' => '【' . $logistics->expName . '】已揽收'
                    ];
                    break;
                    
                case 1: // 在途中
                    $trackInfo['tracks'][] = [
                        'time' => $now->subHours(12)->toDateTimeString(),
                        'context' => '【' . $logistics->expName . '】已揽收'
                    ];
                    $trackInfo['tracks'][] = [
                        'time' => $now->addHours(6)->toDateTimeString(),
                        'context' => '运输中，请耐心等待'
                    ];
                    break;
                    
                case 2: // 正在派件
                    $trackInfo['tracks'][] = [
                        'time' => $now->subDays(2)->toDateTimeString(),
                        'context' => '【' . $logistics->expName . '】已揽收'
                    ];
                    $trackInfo['tracks'][] = [
                        'time' => $now->subDays(1)->toDateTimeString(),
                        'context' => '运输中，请耐心等待'
                    ];
                    $trackInfo['tracks'][] = [
                        'time' => $now->toDateTimeString(),
                        'context' => '快递已到达，正在派送中'
                    ];
                    break;
                    
                case 3: // 已签收
                    $trackInfo['tracks'][] = [
                        'time' => $now->subDays(3)->toDateTimeString(),
                        'context' => '【' . $logistics->expName . '】已揽收'
                    ];
                    $trackInfo['tracks'][] = [
                        'time' => $now->subDays(2)->toDateTimeString(),
                        'context' => '运输中，请耐心等待'
                    ];
                    $trackInfo['tracks'][] = [
                        'time' => $now->subDays(1)->toDateTimeString(),
                        'context' => '快递已到达，正在派送中'
                    ];
                    $trackInfo['tracks'][] = [
                        'time' => $now->toDateTimeString(),
                        'context' => '已签收，感谢使用' . $logistics->expName
                    ];
                    break;
                    
                default:
                    break;
            }
        }
        
        return response()->json([
            'code' => 0,
            'message' => 'success',
            'data' => $trackInfo
        ]);
    }
    
    /**
     * 手动更新物流状态
     */
    public function updateStatus(Request $request, $id)
    {
        $logistics = Logistics::find($id);
        
        if (!$logistics) {
            return response()->json([
                'code' => 1,
                'message' => '物流信息不存在'
            ], 404);
        }
        
        // 验证请求数据
        $validator = Validator::make($request->all(), [
            'deliverystatus' => 'required|integer|min:0|max:6'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'code' => 1,
                'message' => $validator->errors()->first()
            ], 422);
        }

        try {
            // 更新状态
            $logistics->deliverystatus = $request->deliverystatus;
            $logistics->save();
            
            // 如果物流状态是已签收，自动更新相关订单状态为已完成
            if ($request->deliverystatus == 3) {
                // 获取关联的订单项
                $orderItems = OrderItem::where('ship_area_id', $logistics->number)
                    ->where('status', 3) // 已发货状态
                    ->get();
                
                foreach ($orderItems as $item) {
                    DB::beginTransaction();
                    
                    try {
                        // 更新订单项状态为已完成
                        $item->status = 4; // 已完成
                        $item->update_time = now();
                        $item->save();
                        
                        // 检查订单中是否所有订单项都已完成
                        $order = Order::where('order_id', $item->order_id)->first();
                        if ($order) {
                            $unfinishedCount = OrderItem::where('order_id', $order->order_id)
                                ->where('status', '<>', 4)
                                ->where('status', '<>', 6) // 排除退款完成的订单项
                                ->where('status', '<>', 7) // 排除已取消的订单项
                                ->count();
                                
                            if ($unfinishedCount == 0) {
                                // 更新订单状态为已完成
                                $order->status = 4; // 已完成
                                $order->confirm = 2; // 已确认收货
                                $order->confirm_time = now();
                                $order->update_time = now();
                                $order->save();
                            }
                        }
                        
                        DB::commit();
                    } catch (\Exception $e) {
                        DB::rollBack();
                        Log::error('更新订单状态失败: ' . $e->getMessage());
                    }
                }
            }
            
            return response()->json([
                'code' => 0,
                'message' => '物流状态更新成功',
                'data' => $logistics
            ]);
        } catch (\Exception $e) {
            Log::error('物流状态更新失败: ' . $e->getMessage());
            
            return response()->json([
                'code' => 1,
                'message' => '物流状态更新失败: ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * 获取物流公司列表
     */
    public function companies()
    {
        // 这里可以返回常用的物流公司列表
        $companies = [
            ['code' => 'SF', 'name' => '顺丰速运'],
            ['code' => 'ZTO', 'name' => '中通快递'],
            ['code' => 'YTO', 'name' => '圆通速递'],
            ['code' => 'YD', 'name' => '韵达速递'],
            ['code' => 'STO', 'name' => '申通快递'],
            ['code' => 'YZPY', 'name' => '邮政快递包裹'],
            ['code' => 'EMS', 'name' => 'EMS'],
            ['code' => 'HTKY', 'name' => '百世快递'],
            ['code' => 'JD', 'name' => '京东物流'],
            ['code' => 'QFKD', 'name' => '全峰快递']
        ];
        
        return response()->json([
            'code' => 0,
            'message' => 'success',
            'data' => $companies
        ]);
    }
}
