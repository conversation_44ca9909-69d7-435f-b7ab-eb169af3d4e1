<?php

namespace App\Http\Controllers\Admin\Web;

use App\Http\Controllers\Controller;
use App\Models\WaterOrder;
use App\Models\WaterDevice;
use App\Models\TappDevice;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class MallOrderController extends Controller
{
    /**
     * 显示充值订单列表页面
     */
    public function index()
    {
        return view('layouts.app');
    }
    
    /**
     * 充值订单详情页
     */
    public function show($id)
    {
        return view('layouts.app');
    }
    
    /**
     * 获取充值订单列表API
     */
    public function apiIndex(Request $request)
    {
        try {
            $query = WaterOrder::query()
                ->rechargeOrders(); // 只查询充值订单
                
            // 搜索条件处理
            if ($request->has('keyword') && !empty($request->keyword)) {
                $keyword = $request->keyword;
                $query->where(function($q) use ($keyword) {
                    $q->where('order_number', 'like', "%{$keyword}%")
                      ->orWhere('device_number', 'like', "%{$keyword}%")
                      ->orWhere('client_name', 'like', "%{$keyword}%")
                      ->orWhere('client_phone', 'like', "%{$keyword}%");
                });
            }
            
            // 订单状态筛选
            if ($request->has('order_status') && $request->order_status !== '') {
                $query->where('order_status', $request->order_status);
            }
            
            // 计费模式筛选
            if ($request->has('billing_mode') && $request->billing_mode !== '') {
                $query->where('billing_mode', $request->billing_mode);
            }
            
            // 代充筛选
            if ($request->has('surrogate_type') && $request->surrogate_type !== '') {
                $query->where('surrogate_type', $request->surrogate_type);
            }
            
            // 时间范围筛选
            if ($request->has('start_date') && !empty($request->start_date)) {
                $query->whereDate('create_date', '>=', $request->start_date);
            }
            
            if ($request->has('end_date') && !empty($request->end_date)) {
                $query->whereDate('create_date', '<=', $request->end_date);
            }
            
            // 金额范围筛选
            if ($request->has('min_amount') && is_numeric($request->min_amount)) {
                $query->where('money', '>=', $request->min_amount);
            }
            
            if ($request->has('max_amount') && is_numeric($request->max_amount)) {
                $query->where('money', '<=', $request->max_amount);
            }
            
            // 默认按创建时间倒序排序
            $query->orderBy('create_date', 'desc');
            
            // 分页
            $limit = $request->input('limit', 10);
            $orders = $query->paginate($limit);
            
            // 处理数据
            $list = [];
            foreach ($orders as $order) {
                // 查询关联的本地设备
                $tappDevice = TappDevice::where('device_number', $order->device_number)->first();
                
                // 准备返回数据
                $orderData = [
                    'id' => $order->id,
                    'order_number' => $order->order_number,
                    'device_number' => $order->device_number,
                    'client_name' => $order->client_name,
                    'client_phone' => $order->client_phone,
                    'order_status' => $order->order_status,
                    'order_status_text' => $order->order_status_text,
                    'billing_mode' => $order->billing_mode,
                    'billing_mode_text' => $order->billing_mode_text,
                    'money' => $order->money,
                    'time_gross' => $order->time_gross,
                    'flow' => $order->flow,
                    'surrogate_type' => $order->surrogate_type,
                    'surrogate_type_text' => $order->surrogate_type_text,
                    'create_date' => $order->create_date,
                    'update_date' => $order->update_date,
                    'dealer_id' => $order->dealer_id,
                    'dealer_name' => null,
                    'app_user_id' => $tappDevice ? $tappDevice->app_user_id : null,
                    'app_user_name' => $tappDevice ? $tappDevice->app_user_name : null,
                    'commission_amount' => $order->calculateCommission()
                ];
                
                // 如果有经销商信息，获取经销商名称
                if ($order->dealer && $order->dealer->dealer_name) {
                    $orderData['dealer_name'] = $order->dealer->dealer_name;
                }
                
                $list[] = $orderData;
            }
            
            return response()->json([
                'code' => 0,
                'message' => '获取成功',
                'data' => [
                    'total' => $orders->total(),
                    'list' => $list
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('获取充值订单列表失败', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'code' => 500,
                'message' => '获取订单列表失败: ' . $e->getMessage()
            ]);
        }
    }
    
    /**
     * 获取充值订单详情API
     */
    public function apiShow($id)
    {
        try {
            $order = WaterOrder::find($id);
            
            if (!$order) {
                return response()->json([
                    'code' => 404,
                    'message' => '订单不存在'
                ]);
            }
            
            // 查询关联的本地设备
            $tappDevice = TappDevice::where('device_number', $order->device_number)->first();
            
            // 准备返回数据
            $data = [
                'id' => $order->id,
                'order_number' => $order->order_number,
                'order_type' => $order->order_type,
                'order_type_text' => $order->order_type_text,
                'device_id' => $order->device_id,
                'device_number' => $order->device_number,
                'client_id' => $order->client_id,
                'client_name' => $order->client_name,
                'client_phone' => $order->client_phone,
                'order_status' => $order->order_status,
                'order_status_text' => $order->order_status_text,
                'billing_mode' => $order->billing_mode,
                'billing_mode_text' => $order->billing_mode_text,
                'money' => $order->money,
                'time_gross' => $order->time_gross,
                'flow' => $order->flow,
                'surrogate_type' => $order->surrogate_type,
                'surrogate_type_text' => $order->surrogate_type_text,
                'create_date' => $order->create_date,
                'update_date' => $order->update_date,
                'dealer_id' => $order->dealer_id,
                'dealer_name' => $order->dealer ? $order->dealer->dealer_name : null,
                'transaction_id' => $order->transaction_id,
                'prepay_id' => $order->prepay_id,
                'out_trade_no' => $order->out_trade_no,
                'app_user_id' => $tappDevice ? $tappDevice->app_user_id : null,
                'app_user_name' => $tappDevice ? $tappDevice->app_user_name : null,
                'commission_amount' => $order->calculateCommission(),
                'address' => $order->address,
                'province' => $order->province,
                'city' => $order->city,
                'area' => $order->area,
                'remark' => $order->remark,
                'is_water_recharge' => true // 标记为净水器充值订单
            ];
            
            // 如果有设备信息，添加设备详情
            if ($tappDevice) {
                $data['device_info'] = [
                    'id' => $tappDevice->id,
                    'device_number' => $tappDevice->device_number,
                    'device_type' => $tappDevice->device_type,
                    'status' => $tappDevice->status,
                    'status_text' => $tappDevice->status_text,
                    'network_status' => $tappDevice->network_status,
                    'network_status_text' => $tappDevice->network_status == '1' ? '在线' : '离线',
                    'activate_date' => $tappDevice->activate_date,
                    'is_self_use' => $tappDevice->is_self_use,
                    'surplus_flow' => $tappDevice->surplus_flow, // 剩余流量
                    'remaining_days' => $tappDevice->remaining_days, // 剩余天数
                    'cumulative_filtration_flow' => $tappDevice->cumulative_filtration_flow, // 累计滤水量
                    'last_online_time' => $tappDevice->last_online_time, // 最后上线时间
                    'f1_life_percent' => $tappDevice->f1_life_percent, // PP棉滤芯寿命百分比
                    'f2_life_percent' => $tappDevice->f2_life_percent, // 活性炭滤芯寿命百分比
                    'f3_life_percent' => $tappDevice->f3_life_percent  // RO反渗透滤芯寿命百分比
                ];
            }
            
            return response()->json([
                'code' => 0,
                'message' => '获取成功',
                'data' => $data
            ]);
        } catch (\Exception $e) {
            Log::error('获取充值订单详情失败', [
                'id' => $id,
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'code' => 500,
                'message' => '获取订单详情失败: ' . $e->getMessage()
            ]);
        }
    }
} 