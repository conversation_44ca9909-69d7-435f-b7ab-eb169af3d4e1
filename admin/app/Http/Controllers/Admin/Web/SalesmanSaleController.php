<?php

namespace App\Http\Controllers\Admin\Web;

use App\Http\Controllers\Controller;
use App\Models\Salesman;
use App\Models\SalesmanSale;
use Illuminate\Http\Request;

class SalesmanSaleController extends Controller
{
    /**
     * 显示业务员销售记录列表
     */
    public function index(Request $request, Salesman $salesman)
    {
        $query = $salesman->sales()->orderBy('sale_date', 'desc');
        
        // 日期范围筛选
        if ($request->has('start_date') && $request->has('end_date')) {
            $query->whereBetween('sale_date', [
                $request->input('start_date'),
                $request->input('end_date')
            ]);
        }
        
        // 状态筛选
        if ($request->has('status') && $request->input('status') !== 'all') {
            $query->where('status', $request->input('status'));
        }
        
        $sales = $query->paginate(15);
        
        return view('admin.salesmen.sales.index', compact('salesman', 'sales'));
    }
    
    /**
     * 显示创建销售记录表单
     */
    public function create(Salesman $salesman)
    {
        return view('admin.salesmen.sales.create', compact('salesman'));
    }
    
    /**
     * 存储新销售记录
     */
    public function store(Request $request, Salesman $salesman)
    {
        $this->validate($request, [
            'order_id' => 'nullable|string|max:100',
            'amount' => 'required|numeric|min:0',
            'commission_rate' => 'required|numeric|min:0|max:100',
            'product_name' => 'required|string|max:255',
            'product_id' => 'nullable|string|max:100',
            'quantity' => 'required|integer|min:1',
            'customer_name' => 'required|string|max:100',
            'customer_phone' => 'nullable|string|max:20',
            'status' => 'required|in:pending,completed,cancelled',
            'sale_date' => 'required|date',
            'remarks' => 'nullable|string|max:1000',
        ]);
        
        try {
            // 计算提成金额
            $commissionAmount = $request->input('amount') * ($request->input('commission_rate') / 100);
            
            // 创建销售记录
            $sale = new SalesmanSale();
            $sale->salesman_id = $salesman->id;
            $sale->order_id = $request->input('order_id');
            $sale->amount = $request->input('amount');
            $sale->commission_rate = $request->input('commission_rate');
            $sale->commission_amount = $commissionAmount;
            $sale->product_name = $request->input('product_name');
            $sale->product_id = $request->input('product_id');
            $sale->quantity = $request->input('quantity');
            $sale->customer_name = $request->input('customer_name');
            $sale->customer_phone = $request->input('customer_phone');
            $sale->status = $request->input('status');
            $sale->sale_date = $request->input('sale_date');
            $sale->remarks = $request->input('remarks');
            $sale->save();
            
            return redirect()->route('admin.salesmen.sales.index', $salesman)
                ->with('success', '销售记录创建成功');
                
        } catch (\Exception $e) {
            return back()->withErrors(['error' => '销售记录创建失败：'.$e->getMessage()])
                ->withInput();
        }
    }
}
