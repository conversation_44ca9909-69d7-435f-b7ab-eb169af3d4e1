<?php

namespace App\Http\Controllers\Admin\Web;

use App\Http\Controllers\Controller;
use App\Models\SystemSetting;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Str;

class SettingController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }

    /**
     * 获取指定分组的设置
     */
    public function getSettings(Request $request)
    {
        $group = $request->input('group', 'basic');

        $settings = SystemSetting::where('group', $group)
            ->orderBy('sort')
            ->get();

        return response()->json([
            'code' => 0,
            'message' => '获取成功',
            'data' => $settings
        ]);
    }

    /**
     * 获取设置值（前端使用）
     */
    public function getSiteSettings()
    {
        try {
            \Log::info('获取站点基本设置');
            // 优先从SystemConfig模型获取基本设置
            $configs = \App\Models\SystemConfig::where('module', 'basic')->get();

            if ($configs->isEmpty()) {
                \Log::warning('未找到站点基本设置，尝试从SystemSetting获取');
                $settings = SystemSetting::getSettingsByGroup('basic');
            } else {
                \Log::info('从SystemConfig获取到 ' . $configs->count() . ' 条基本设置');
                $settings = $configs->pluck('value', 'key')->toArray();
            }

            // 确保返回的是对象，即使没有设置
            if (empty($settings)) {
                \Log::warning('未找到任何基本设置，使用默认值');
                $settings = [
                    'site_name' => '点点够管理系统',
                    'site_logo' => '/images/logo.png',
                    'site_icp' => '粤ICP备12345678号',
                    'contact_phone' => '************',
                    'contact_email' => '<EMAIL>',
                    'copyright' => '©2023 点点够科技有限公司 版权所有'
                ];
            }

            \Log::info('返回站点基本设置', ['data' => $settings]);
            return response()->json([
                'code' => 0,
                'message' => '获取成功',
                'data' => $settings
            ]);
        } catch (\Exception $e) {
            \Log::error('获取站点基本设置失败: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString()
            ]);

            // 发生错误时也返回默认设置
            $defaultSettings = [
                'site_name' => '点点够管理系统',
                'site_logo' => '/images/logo.png',
                'site_icp' => '粤ICP备12345678号',
                'contact_phone' => '************',
                'contact_email' => '<EMAIL>',
                'copyright' => '©2023 点点够科技有限公司 版权所有'
            ];

            return response()->json([
                'code' => 0,
                'message' => '获取成功 (使用默认设置)',
                'data' => $defaultSettings
            ]);
        }
    }

    /**
     * 更新设置
     */
    public function updateSettings(Request $request)
    {
        $settings = $request->input('settings');

        if (!is_array($settings)) {
            return response()->json([
                'code' => 1,
                'message' => '参数错误'
            ]);
        }

        // 处理可能的文件上传
        $files = $request->allFiles();
        foreach ($files as $key => $file) {
            if ($file && $file->isValid()) {
                $setting = SystemSetting::where('key', $key)->first();

                if ($setting && $setting->type === 'image') {
                    // 如果是图片类型，保存图片
                    $ext = $file->getClientOriginalExtension();
                    $filename = Str::random(20) . '.' . $ext;
                    $path = $file->storeAs('public/settings', $filename);
                    $url = Storage::url($path);

                    // 更新设置值为图片URL
                    $settings[$key] = $url;
                }
            }
        }

        SystemSetting::updateSettings($settings);

        // 清除所有设置缓存
        Cache::flush();

        return response()->json([
            'code' => 0,
            'message' => '设置已更新'
        ]);
    }

    /**
     * 上传Logo图片
     */
    public function uploadLogo(Request $request)
    {
        try {
            $request->validate([
                'logo' => 'required|image|mimes:jpeg,png,jpg,gif|max:2048',
            ]);

            if ($request->hasFile('logo') && $request->file('logo')->isValid()) {
                $file = $request->file('logo');
                $ext = $file->getClientOriginalExtension();
                $timestamp = time();
                $filename = 'logo_' . $timestamp . '.' . $ext;

                // 保存到网站根目录images目录下
                $uploadPath = base_path('../../images');
                // 确保目录存在
                if (!file_exists($uploadPath)) {
                    mkdir($uploadPath, 0755, true);
                }

                $file->move($uploadPath, $filename);

                // 更新设置，使用网站根目录路径
                $logoPath = '/images/' . $filename;

                // 记录详细日志
                \Log::info('Logo上传成功', [
                    'filename' => $filename,
                    'path' => $logoPath,
                    'full_path' => $uploadPath . '/' . $filename
                ]);

                // 更新SystemSetting
                $settingUpdated = SystemSetting::updateSetting('site_logo', $logoPath);
                \Log::info('SystemSetting更新结果', ['success' => $settingUpdated]);

                // 更新SystemConfig
                $configUpdated = \App\Models\SystemConfig::setConfigValue('basic', 'site_logo', $logoPath);
                \Log::info('SystemConfig更新结果', ['success' => $configUpdated]);

                // 清除缓存
                \Cache::forget('system_settings:basic');
                \Cache::forget('system_setting:site_logo');
                \Cache::flush();

                return response()->json([
                    'code' => 0,
                    'message' => 'Logo上传成功',
                    'data' => [
                        'url' => $logoPath . '?v=' . $timestamp
                    ]
                ]);
            }

            return response()->json([
                'code' => 1,
                'message' => 'Logo上传失败：未找到有效的图片文件'
            ]);
        } catch (\Exception $e) {
            \Log::error('Logo上传失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'code' => 1,
                'message' => 'Logo上传失败: ' . $e->getMessage()
            ]);
        }
    }
}
