<?php

namespace App\Http\Controllers\Admin\Web;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\OrderRefund;
use App\Models\Order;
use App\Models\OrderItem;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class OrderRefundController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = OrderRefund::query();
        
        // 搜索条件
        if ($request->has('keyword') && $request->keyword) {
            $query->where(function($q) use ($request) {
                $q->where('aftermarket_trade_no', 'like', '%' . $request->keyword . '%')
                  ->orWhere('aftermarket_phone', 'like', '%' . $request->keyword . '%');
            });
        }
        
        if ($request->has('verify_status') && $request->verify_status !== null) {
            $query->where('verify_status', $request->verify_status);
        }
        
        if ($request->has('aftermarket_type') && $request->aftermarket_type !== null) {
            $query->where('aftermarket_type', $request->aftermarket_type);
        }
        
        if ($request->has('start_date') && $request->start_date) {
            $query->where('created_at', '>=', $request->start_date . ' 00:00:00');
        }
        
        if ($request->has('end_date') && $request->end_date) {
            $query->where('created_at', '<=', $request->end_date . ' 23:59:59');
        }
        
        // 默认按照创建时间倒序排序
        $query->orderBy($request->input('sort', 'created_at'), $request->input('order', 'desc'));
        
        // 分页
        $perPage = $request->input('limit', 15);
        $refunds = $query->paginate($perPage);
        
        // 加载订单和订单项信息
        $refunds->load(['order', 'orderItem', 'user']);
        
        // 处理维权订单数据，添加状态文本等
        $data = $refunds->map(function ($refund) {
            $refund->verify_status_text = $refund->getVerifyStatusTextAttribute();
            $refund->apply_from_text = $refund->getApplyFromTextAttribute();
            $refund->aftermarket_type_text = $refund->getAftermarketTypeTextAttribute();
            
            // 关联订单商品名称
            if ($refund->orderItem) {
                $refund->goods_name = $refund->orderItem->name;
                $refund->goods_image = $refund->orderItem->image_url;
            } else {
                $refund->goods_name = '';
                $refund->goods_image = '';
            }
            
            return $refund;
        });
        
        return response()->json([
            'code' => 0,
            'message' => 'success',
            'data' => $data,
            'total' => $refunds->total()
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show($id)
    {
        $refund = OrderRefund::with(['order', 'orderItem', 'user'])->find($id);
        
        if (!$refund) {
            return response()->json([
                'code' => 1,
                'message' => '维权订单不存在'
            ], 404);
        }
        
        // 添加状态文本
        $refund->verify_status_text = $refund->getVerifyStatusTextAttribute();
        $refund->apply_from_text = $refund->getApplyFromTextAttribute();
        $refund->aftermarket_type_text = $refund->getAftermarketTypeTextAttribute();
        
        return response()->json([
            'code' => 0,
            'message' => 'success',
            'data' => $refund
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }

    /**
     * 审核维权订单
     */
    public function audit(Request $request, $id)
    {
        $refund = OrderRefund::find($id);
        
        if (!$refund) {
            return response()->json([
                'code' => 1,
                'message' => '维权订单不存在'
            ], 404);
        }
        
        if ($refund->verify_status != 0) {
            return response()->json([
                'code' => 1,
                'message' => '维权订单已审核，不能重复审核'
            ], 422);
        }
        
        // 验证请求数据
        $validator = Validator::make($request->all(), [
            'verify_status' => 'required|in:1,2',
            'aftermarket_description' => 'nullable|string|max:500'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'code' => 1,
                'message' => $validator->errors()->first()
            ], 422);
        }

        try {
            // 开启事务
            DB::beginTransaction();
            
            // 更新维权订单
            $refund->verify_status = $request->verify_status;
            $refund->aftermarket_description = $request->aftermarket_description ?? $refund->aftermarket_description;
            $refund->updated_at = now();
            $refund->save();
            
            // 如果是审核通过，并且是仅退款，直接进入退款状态
            if ($request->verify_status == 1 && $refund->aftermarket_type == 0) {
                $refund->verify_status = 3; // 退款中
                $refund->save();
                
                // 更新订单状态
                $order = Order::where('id', $refund->order_id)->first();
                if ($order) {
                    $order->status = 5; // 申请退款
                    $order->update_time = now();
                    $order->save();
                }
                
                // 更新订单项状态
                if ($refund->order_item_id) {
                    OrderItem::where('id', $refund->order_item_id)
                        ->update([
                            'status' => 5,
                            'update_time' => now()
                        ]);
                }
            }
            
            // 如果是审核不通过
            if ($request->verify_status == 2) {
                // 什么都不做，只更新状态
            }
            
            // 提交事务
            DB::commit();
            
            return response()->json([
                'code' => 0,
                'message' => $request->verify_status == 1 ? '审核通过成功' : '审核拒绝成功',
                'data' => $refund
            ]);
        } catch (\Exception $e) {
            // 回滚事务
            DB::rollBack();
            Log::error('维权订单审核失败: ' . $e->getMessage());
            
            return response()->json([
                'code' => 1,
                'message' => '维权订单审核失败: ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * 处理退款
     */
    public function refund(Request $request, $id)
    {
        $refund = OrderRefund::find($id);
        
        if (!$refund) {
            return response()->json([
                'code' => 1,
                'message' => '维权订单不存在'
            ], 404);
        }
        
        if ($refund->verify_status != 3) {
            return response()->json([
                'code' => 1,
                'message' => '维权订单状态不是退款中，无法处理退款'
            ], 422);
        }
        
        // 验证请求数据
        $validator = Validator::make($request->all(), [
            'real_refund_money' => 'required|numeric|min:0'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'code' => 1,
                'message' => $validator->errors()->first()
            ], 422);
        }

        try {
            // 开启事务
            DB::beginTransaction();
            
            // 更新维权订单
            $refund->real_refund_money = $request->real_refund_money;
            $refund->verify_status = 4; // 退款完成
            $refund->updated_at = now();
            $refund->save();
            
            // 更新订单状态
            $order = Order::where('id', $refund->order_id)->first();
            if ($order) {
                $order->status = 6; // 退款完成
                $order->pay_status = 3; // 已退款
                $order->update_time = now();
                $order->save();
            }
            
            // 更新订单项状态
            if ($refund->order_item_id) {
                OrderItem::where('id', $refund->order_item_id)
                    ->update([
                        'status' => 6,
                        'update_time' => now()
                    ]);
            }
            
            // 这里需要调用退款API，具体实现根据支付方式确定
            // ...
            
            // 提交事务
            DB::commit();
            
            return response()->json([
                'code' => 0,
                'message' => '退款处理成功',
                'data' => $refund
            ]);
        } catch (\Exception $e) {
            // 回滚事务
            DB::rollBack();
            Log::error('退款处理失败: ' . $e->getMessage());
            
            return response()->json([
                'code' => 1,
                'message' => '退款处理失败: ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * 获取维权订单统计数据
     */
    public function statistics()
    {
        // 获取维权订单各状态数量
        $statistics = [
            'wait_audit' => OrderRefund::where('verify_status', 0)->count(), // 待审核
            'audit_pass' => OrderRefund::where('verify_status', 1)->count(), // 审核通过
            'audit_reject' => OrderRefund::where('verify_status', 2)->count(), // 审核拒绝
            'refunding' => OrderRefund::where('verify_status', 3)->count(), // 退款中
            'refunded' => OrderRefund::where('verify_status', 4)->count(), // 退款完成
            
            // 今日维权订单数
            'today_refunds' => OrderRefund::where('created_at', '>=', date('Y-m-d') . ' 00:00:00')
                ->where('created_at', '<=', date('Y-m-d') . ' 23:59:59')
                ->count(),
                
            // 今日退款金额
            'today_refund_amount' => OrderRefund::where('updated_at', '>=', date('Y-m-d') . ' 00:00:00')
                ->where('updated_at', '<=', date('Y-m-d') . ' 23:59:59')
                ->where('verify_status', 4)
                ->sum('real_refund_money'),
                
            // 本月维权订单数
            'month_refunds' => OrderRefund::where('created_at', '>=', date('Y-m-01') . ' 00:00:00')
                ->where('created_at', '<=', date('Y-m-d') . ' 23:59:59')
                ->count(),
                
            // 本月退款金额
            'month_refund_amount' => OrderRefund::where('updated_at', '>=', date('Y-m-01') . ' 00:00:00')
                ->where('updated_at', '<=', date('Y-m-d') . ' 23:59:59')
                ->where('verify_status', 4)
                ->sum('real_refund_money')
        ];
        
        return response()->json([
            'code' => 0,
            'message' => 'success',
            'data' => $statistics
        ]);
    }
}
