<?php

namespace App\Http\Controllers\Admin\Web;

use App\Http\Controllers\Controller;
use App\Models\Salesman;
use App\Models\SalesmanCommission;
use Illuminate\Http\Request;

class SalesmanCommissionController extends Controller
{
    /**
     * 显示业务员提成记录列表
     */
    public function index(Request $request, Salesman $salesman)
    {
        $query = $salesman->commissions()->orderBy('period', 'desc');
        
        // 状态筛选
        if ($request->has('status') && $request->input('status') !== 'all') {
            $query->where('status', $request->input('status'));
        }
        
        $commissions = $query->paginate(15);
        
        return view('admin.salesmen.commissions.index', compact('salesman', 'commissions'));
    }
    
    /**
     * 显示创建提成记录表单
     */
    public function create(Salesman $salesman)
    {
        return view('admin.salesmen.commissions.create', compact('salesman'));
    }
    
    /**
     * 存储新提成记录
     */
    public function store(Request $request, Salesman $salesman)
    {
        $this->validate($request, [
            'amount' => 'required|numeric|min:0',
            'period' => 'required|string|max:7',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after_or_equal:start_date',
            'status' => 'required|in:pending,paid',
            'payment_date' => 'nullable|required_if:status,paid|date',
            'remarks' => 'nullable|string|max:1000',
        ]);
        
        try {
            // 创建提成记录
            $commission = new SalesmanCommission();
            $commission->salesman_id = $salesman->id;
            $commission->amount = $request->input('amount');
            $commission->period = $request->input('period');
            $commission->start_date = $request->input('start_date');
            $commission->end_date = $request->input('end_date');
            $commission->status = $request->input('status');
            $commission->payment_date = $request->input('payment_date');
            $commission->remarks = $request->input('remarks');
            $commission->save();
            
            return redirect()->route('admin.salesmen.commissions.index', $salesman)
                ->with('success', '提成记录创建成功');
                
        } catch (\Exception $e) {
            return back()->withErrors(['error' => '提成记录创建失败：'.$e->getMessage()])
                ->withInput();
        }
    }
}
