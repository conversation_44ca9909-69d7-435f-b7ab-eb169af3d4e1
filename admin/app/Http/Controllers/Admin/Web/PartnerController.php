<?php

namespace App\Http\Controllers\Admin\Web;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class PartnerController extends Controller
{
    /**
     * 获取合作伙伴列表
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        $query = User::query()
            ->where(function($q) {
                $q->where('is_pay_institution', 1)
                  ->orWhere('is_water_purifier_agent', 1);
            });
            
        // 搜索条件
        if ($request->has('search')) {
            $search = $request->input('search');
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('mobile', 'like', "%{$search}%");
            });
        }
        
        // 排序
        $sortField = $request->input('sort_field', 'created_at');
        $sortOrder = $request->input('sort_order', 'desc');
        $query->orderBy($sortField, $sortOrder);
        
        $partners = $query->select([
                'id', 
                'name', 
                'mobile', 
                'is_pay_institution', 
                'is_water_purifier_agent',
                'balance',
                'points',
                'status',
                'created_at'
            ])
            ->paginate($request->input('per_page', 15));
            
        return response()->json([
            'code' => 0,
            'message' => '获取成功',
            'data' => $partners
        ]);
    }
    
    /**
     * 获取单个合作伙伴详情
     * 
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        $partner = User::where('id', $id)
            ->where(function($q) {
                $q->where('is_pay_institution', 1)
                  ->orWhere('is_water_purifier_agent', 1);
            })
            ->first();
            
        if (!$partner) {
            return response()->json([
                'code' => 404,
                'message' => '合作伙伴不存在'
            ], 404);
        }
        
        return response()->json([
            'code' => 0,
            'message' => '获取成功',
            'data' => $partner
        ]);
    }
    
    /**
     * 更新合作伙伴信息
     * 
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id)
    {
        $partner = User::where('id', $id)
            ->where(function($q) {
                $q->where('is_pay_institution', 1)
                  ->orWhere('is_water_purifier_agent', 1);
            })
            ->first();
            
        if (!$partner) {
            return response()->json([
                'code' => 404,
                'message' => '合作伙伴不存在'
            ], 404);
        }
        
        $data = $request->validate([
            'name' => 'sometimes|string|max:50',
            'mobile' => 'sometimes|string|max:20',
            'is_pay_institution' => 'sometimes|boolean',
            'is_water_purifier_agent' => 'sometimes|boolean',
        ]);
        
        $partner->update($data);
        
        return response()->json([
            'code' => 0,
            'message' => '更新成功',
            'data' => $partner
        ]);
    }
    
    /**
     * 修改合作伙伴状态
     * 
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function changeStatus(Request $request, $id)
    {
        $partner = User::where('id', $id)
            ->where(function($q) {
                $q->where('is_pay_institution', 1)
                  ->orWhere('is_water_purifier_agent', 1);
            })
            ->first();
            
        if (!$partner) {
            return response()->json([
                'code' => 404,
                'message' => '合作伙伴不存在'
            ], 404);
        }
        
        $request->validate([
            'status' => 'required|in:0,1',
        ]);
        
        $partner->status = $request->input('status');
        $partner->save();
        
        return response()->json([
            'code' => 0,
            'message' => '状态修改成功',
            'data' => $partner
        ]);
    }
    
    /**
     * 调整合作伙伴余额
     * 
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function adjustBalance(Request $request, $id)
    {
        $partner = User::where('id', $id)
            ->where(function($q) {
                $q->where('is_pay_institution', 1)
                  ->orWhere('is_water_purifier_agent', 1);
            })
            ->first();
            
        if (!$partner) {
            return response()->json([
                'code' => 404,
                'message' => '合作伙伴不存在'
            ], 404);
        }
        
        $request->validate([
            'amount' => 'required|numeric',
            'type' => 'required|in:add,subtract',
            'reason' => 'required|string|max:255',
        ]);
        
        try {
            DB::beginTransaction();
            
            $amount = abs($request->input('amount'));
            $type = $request->input('type');
            $reason = $request->input('reason');
            
            if ($type == 'add') {
                $partner->balance += $amount;
            } else {
                if ($partner->balance < $amount) {
                    return response()->json([
                        'code' => 400,
                        'message' => '余额不足'
                    ], 400);
                }
                $partner->balance -= $amount;
            }
            
            $partner->save();
            
            // 记录余额变动日志
            // 这里可以添加记录余额变动的代码
            
            DB::commit();
            
            return response()->json([
                'code' => 0,
                'message' => '余额调整成功',
                'data' => [
                    'balance' => $partner->balance
                ]
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('调整合作伙伴余额失败: ' . $e->getMessage());
            
            return response()->json([
                'code' => 500,
                'message' => '操作失败: ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * 调整合作伙伴积分
     * 
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function adjustPoints(Request $request, $id)
    {
        $partner = User::where('id', $id)
            ->where(function($q) {
                $q->where('is_pay_institution', 1)
                  ->orWhere('is_water_purifier_agent', 1);
            })
            ->first();
            
        if (!$partner) {
            return response()->json([
                'code' => 404,
                'message' => '合作伙伴不存在'
            ], 404);
        }
        
        $request->validate([
            'points' => 'required|integer',
            'type' => 'required|in:add,subtract',
            'reason' => 'required|string|max:255',
        ]);
        
        try {
            DB::beginTransaction();
            
            $points = abs($request->input('points'));
            $type = $request->input('type');
            $reason = $request->input('reason');
            
            if ($type == 'add') {
                $partner->points += $points;
            } else {
                if ($partner->points < $points) {
                    return response()->json([
                        'code' => 400,
                        'message' => '积分不足'
                    ], 400);
                }
                $partner->points -= $points;
            }
            
            $partner->save();
            
            // 记录积分变动日志
            // 这里可以添加记录积分变动的代码
            
            DB::commit();
            
            return response()->json([
                'code' => 0,
                'message' => '积分调整成功',
                'data' => [
                    'points' => $partner->points
                ]
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('调整合作伙伴积分失败: ' . $e->getMessage());
            
            return response()->json([
                'code' => 500,
                'message' => '操作失败: ' . $e->getMessage()
            ], 500);
        }
    }
} 