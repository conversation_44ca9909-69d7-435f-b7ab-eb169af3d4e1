<?php

namespace App\Http\Controllers\Admin\Web;

use App\Http\Controllers\Controller;
use App\Models\WaterDevice;
use App\Models\WaterClient;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Schema;
use Exception;

class DeviceController extends Controller
{
    /**
     * 设备列表
     */
    public function index(Request $request)
    {
        try {
            $query = WaterDevice::with('client');
    
            // 搜索条件
            if ($request->filled('device_number')) {
                $query->where('device_number', 'like', '%'.$request->input('device_number').'%');
            }
            if ($request->filled('client_name')) {
                $query->whereHas('client', function($q) use ($request) {
                    $q->where('name', 'like', '%'.$request->input('client_name').'%');
                });
            }
    
            $devices = $query->paginate(15);
    
            return view('admin.devices.index', compact('devices'));
        } catch (\Exception $e) {
            Log::error('获取设备列表失败: ' . $e->getMessage());
            return view('admin.devices.index', ['devices' => collect(), 'error' => $e->getMessage()]);
        }
    }

    /**
     * 设备详情
     */
    public function show($id)
    {
        try {
            $device = WaterDevice::with(['client' => function($query) {
                $query->select([
                    'id',
                    'name as client_name',
                    'phone as client_phone',
                    'province',
                    'city',
                    'area',
                    'address',
                    'wx_nickname',
                    'wx_head_img'
                ]);
            }])->findOrFail($id);
    
            return view('admin.devices.show', compact('device'));
        } catch (\Exception $e) {
            Log::error('获取设备详情失败: ' . $e->getMessage());
            return redirect()->back()->with('error', '获取设备详情失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取设备列表(API)
     */
    public function apiIndex(Request $request)
    {
        try {
            // 验证数据库连接
            try {
                DB::connection('water_db')->getPdo();
                Log::info('成功连接到水系统数据库');
            } catch (Exception $e) {
                Log::error('无法连接到水系统数据库: ' . $e->getMessage());
                throw new Exception('无法连接到水系统数据库，请联系管理员');
            }

            // 确保使用正确的数据库连接
            Log::info('使用数据库连接: ' . config('database.connections.water_db.database'));
            
            // 构建查询
            $query = WaterDevice::with(['client', 'saleDealer'])
                ->whereNotNull('client_id'); // 只查询已关联客户的设备

            // 添加调试日志
            Log::info('设备查询参数：' . json_encode($request->all()));

            // 查询条件
            if ($request->filled('keyword')) {
                $keyword = $request->input('keyword');
                $query->where(function($q) use ($keyword) {
                    $q->where('device_number', 'like', "%{$keyword}%")
                      ->orWhere('imei', 'like', "%{$keyword}%")
                      ->orWhereHas('client', function($subQuery) use ($keyword) {
                          $subQuery->where('name', 'like', "%{$keyword}%")
                                 ->orWhere('phone', 'like', "%{$keyword}%");
                      });
                });
            }

            // 按设备状态筛选
            if ($request->filled('status')) {
                $query->where('status', $request->input('status'));
            }

            // 按网络状态筛选
            if ($request->filled('network_status')) {
                $query->where('network_status', $request->input('network_status'));
            }

            // 按客户ID筛选
            if ($request->filled('client_id')) {
                $query->where('client_id', $request->input('client_id'));
            }

            // 分页参数
            $page = $request->input('page', 1);
            $limit = $request->input('limit', 10);

            // 总记录数
            $total = $query->count();

            // 获取分页数据
            $items = $query->orderBy('activate_date', 'desc')
                ->offset(($page - 1) * $limit)
                ->limit($limit)
                ->get();

            // 添加结果调试日志
            Log::info('设备查询结果数量：' . count($items));
            if (count($items) > 0) {
                Log::info('第一条设备数据：' . json_encode($items[0]));
                if ($items[0]->saleDealer) {
                    Log::info('第一条设备销售渠道商：' . json_encode($items[0]->saleDealer->toArray()));
                } else {
                    Log::warning('第一条设备无销售渠道商！');
                }
            }

            // 处理结果
            $results = [];
            foreach ($items as $item) {
                // 确保设备编号正确展示（8800开头）
                $deviceNumber = $item->device_number;
                
                // 设备名称逻辑 - 如果client关联存在则使用client_product_name，否则使用device_number
                $deviceName = $item->client && !empty($item->client->client_product_name) 
                    ? $item->client->client_product_name 
                    : (!empty($deviceNumber) ? ('设备' . substr($deviceNumber, -4)) : '未命名设备');

                // 组装客户完整地址
                $address = $item->client ? $item->client->full_address : $item->address ?? '';

                // 获取渠道商信息，组合"8位数字渠道商编号+渠道商名"
                $dealerName = '未分配';
                if ($item->saleDealer) {
                    $dealerNumber = $item->saleDealer->dealer_number ?? '';
                    $dealerName = $dealerNumber ? $dealerNumber . ' ' . $item->saleDealer->dealer_name : $item->saleDealer->dealer_name;
                }

                $results[] = [
                    'id' => $item->id,
                    'device_number' => $deviceNumber,
                    'device_name' => $deviceName,
                    'device_type' => $item->device_type ?? '标准型',
                    'imei' => $item->imei,
                    'iccid' => $item->iccid,
                    'status' => $item->status,
                    'status_text' => $item->status_text,
                    'network_status' => $item->network_status,
                    'network_status_text' => $item->network_status_text,
                    'billing_mode' => $item->billing_mode,
                    'surplus_flow' => $item->surplus_flow,
                    'remaining_days' => $item->remaining_days,
                    'cumulative_filtration_flow' => $item->cumulative_filtration_flow,
                    'service_end_time' => $item->service_end_time,
                    'remark' => $item->remark,
                    'cod_after' => $item->cod_after,
                    'cod_before' => $item->cod_before,
                    'toc_after' => $item->toc_after,
                    'toc_before' => $item->toc_before,
                    'raw_water_value' => $item->raw_water_value,
                    'purification_water_value' => $item->purification_water_value,
                    'create_date' => $item->create_date,
                    'update_date' => $item->update_date,
                    'activate_date' => $item->activate_date,
                    'filter_date' => $item->filter_date,
                    'client_id' => $item->client ? $item->client->id : null,
                    'client_name' => $item->client ? $item->client->name : '未关联客户',
                    'client_phone' => $item->client ? $item->client->phone : '',
                    'client_address' => $address,
                    'parameters' => json_encode([
                        'cod_after' => $item->cod_after,
                        'cod_before' => $item->cod_before,
                        'toc_after' => $item->toc_after,
                        'toc_before' => $item->toc_before,
                        'raw_water_value' => $item->raw_water_value,
                        'purification_water_value' => $item->purification_water_value,
                        'imei' => $item->imei,
                        'iccid' => $item->iccid
                    ]),
                    'current_water_level' => $item->current_water_level,
                    'last_online_time' => $item->update_date,
                    'longitude' => $item->longitude,
                    'latitude' => $item->latitude,
                    'dealer_name' => $dealerName
                ];
            }

            return response()->json([
                'code' => 0,
                'message' => '获取成功',
                'data' => [
                    'list' => $results,
                    'total' => $total,
                    'page' => $page,
                    'limit' => $limit
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('获取设备列表失败：' . $e->getMessage(), [
                'trace' => $e->getTraceAsString(),
                'line' => $e->getLine(),
                'file' => $e->getFile()
            ]);
            return response()->json([
                'code' => 1,
                'message' => '获取设备列表失败：' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

    /**
     * 获取设备详情(API)
     */
    public function apiShow($id)
    {
        try {
            // 验证数据库连接
            try {
                DB::connection('water_db')->getPdo();
                Log::info('成功连接到水系统数据库');
            } catch (Exception $e) {
                Log::error('无法连接到水系统数据库: ' . $e->getMessage());
                throw new Exception('无法连接到水系统数据库，请联系管理员');
            }

            // 查询设备详情，并预加载关联的客户信息
            $device = WaterDevice::with(['client', 'saleDealer'])->find($id);
            
            if (!$device) {
                return response()->json([
                    'code' => 1,
                    'message' => '设备不存在',
                    'data' => null
                ]);
            }

            // 设备名称逻辑
            $deviceName = $device->client && !empty($device->client->client_product_name) 
                ? $device->client->client_product_name 
                : (!empty($device->device_number) ? ('设备' . substr($device->device_number, -4)) : '未命名设备');

            // 客户地址
            $address = $device->client ? $device->client->full_address : $device->address ?? '';

            // 获取渠道商信息，组合"8位数字渠道商编号+渠道商名"
            $dealerName = '未分配';
            if ($device->saleDealer) {
                $dealerNumber = $device->saleDealer->dealer_number ?? '';
                $dealerName = $dealerNumber ? $dealerNumber . ' ' . $device->saleDealer->dealer_name : $device->saleDealer->dealer_name;
            }

            $result = [
                'id' => $device->id,
                'device_number' => $device->device_number,
                'device_name' => $deviceName,
                'device_type' => $device->device_type ?? '标准型',
                'imei' => $device->imei,
                'iccid' => $device->iccid,
                'status' => $device->status,
                'status_text' => $device->status_text,
                'network_status' => $device->network_status,
                'network_status_text' => $device->network_status_text,
                'bind_status' => $device->bind_status,
                'bind_status_text' => $device->bind_status_text,
                'remark' => $device->remark,
                'cod_after' => $device->cod_after,
                'cod_before' => $device->cod_before,
                'toc_after' => $device->toc_after,
                'toc_before' => $device->toc_before,
                'raw_water_value' => $device->raw_water_value,
                'purification_water_value' => $device->purification_water_value,
                'create_date' => $device->create_date,
                'update_date' => $device->update_date,
                'activate_date' => $device->activate_date,
                'service_end_time' => $device->service_end_time,
                'filter_date' => $device->filter_date,
                'client_id' => $device->client ? $device->client->id : null,
                'client_name' => $device->client ? $device->client->name : '未关联客户',
                'client_phone' => $device->client ? $device->client->phone : '',
                'client_address' => $address,
                'wx_nickname' => $device->client ? $device->client->wx_nickname : '',
                'wx_head_img' => $device->client ? $device->client->wx_head_img : '',
                'remaining_days' => $device->remaining_days,
                'surplus_flow' => $device->surplus_flow,
                'parameters' => json_encode([
                    'cod_after' => $device->cod_after,
                    'cod_before' => $device->cod_before,
                    'toc_after' => $device->toc_after,
                    'toc_before' => $device->toc_before,
                    'raw_water_value' => $device->raw_water_value,
                    'purification_water_value' => $device->purification_water_value,
                    'imei' => $device->imei,
                    'iccid' => $device->iccid,
                    'surplus_flow' => $device->surplus_flow,
                    'remaining_days' => $device->remaining_days,
                    'f1_flux' => $device->f1_flux,
                    'f2_flux' => $device->f2_flux,
                    'f3_flux' => $device->f3_flux,
                    'f1_flux_max' => $device->f1_flux_max,
                    'f2_flux_max' => $device->f2_flux_max,
                    'f3_flux_max' => $device->f3_flux_max
                ]),
                'current_water_level' => $device->current_water_level,
                'last_online_time' => $device->update_date,
                'longitude' => $device->longitude,
                'latitude' => $device->latitude,
                'dealer_name' => $dealerName
            ];

            return response()->json([
                'code' => 0,
                'message' => '获取成功',
                'data' => $result
            ]);
        } catch (\Exception $e) {
            Log::error('获取设备详情失败：' . $e->getMessage());
            return response()->json([
                'code' => 1,
                'message' => '获取设备详情失败：' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

    /**
     * 获取客户列表(API)
     */
    public function apiClients(Request $request)
    {
        try {
            $query = WaterClient::query();

            // 搜索条件
            if ($request->filled('keyword')) {
                $keyword = $request->input('keyword');
                $query->where(function($q) use ($keyword) {
                    $q->where('name', 'like', "%{$keyword}%")
                      ->orWhere('phone', 'like', "%{$keyword}%")
                      ->orWhere('client_device_name', 'like', "%{$keyword}%");
                });
            }

            // 状态条件
            if ($request->filled('status')) {
                $query->where('status', $request->input('status'));
            }

            // 分页参数
            $page = $request->input('page', 1);
            $limit = $request->input('limit', 10);

            $clients = $query->paginate($limit, ['*'], 'page', $page);

            // 处理结果
            $results = [];
            foreach ($clients as $client) {
                $address = '';
                if (!empty($client->province)) $address .= $client->province;
                if (!empty($client->city)) $address .= $client->city;
                if (!empty($client->area)) $address .= $client->area;
                if (!empty($client->address)) $address .= $client->address;

                $results[] = [
                    'id' => $client->id,
                    'name' => $client->name,
                    'phone' => $client->phone,
                    'address' => $address,
                    'status' => $client->status,
                    'status_text' => $client->status == 'E' ? '启用' : '禁用',
                    'remark' => $client->remark,
                    'wx_nickname' => $client->wx_nickname,
                    'wx_head_img' => $client->wx_head_img,
                    'client_device_id' => $client->client_device_id,
                    'client_device_name' => $client->client_device_name,
                    'create_date' => $client->create_date,
                    'update_date' => $client->update_date
                ];
            }

            return response()->json([
                'code' => 0,
                'message' => '获取成功',
                'data' => [
                    'list' => $results,
                    'total' => $clients->total(),
                    'page' => $page,
                    'limit' => $limit
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('获取客户列表失败：' . $e->getMessage());
            return response()->json([
                'code' => 1,
                'message' => '获取客户列表失败：' . $e->getMessage(),
                'data' => null
            ]);
        }
    }
    
    /**
     * 计算水量百分比
     * 
     * @param object $device 设备数据
     * @return int 水量百分比(0-100)
     */
    private function calculateWaterLevel($device)
    {
        // 这里可以根据实际情况计算水量
        // 示例：根据原水和净水值的比例计算
        if (empty($device->raw_water_value) || empty($device->purification_water_value)) {
            return 100; // 默认满水
        }
        
        $rawWater = floatval($device->raw_water_value);
        $purifiedWater = floatval($device->purification_water_value);
        
        if ($rawWater <= 0) {
            return 0;
        }
        
        $level = ($purifiedWater / $rawWater) * 100;
        
        // 确保返回值在0-100范围内
        return max(0, min(100, intval($level)));
    }

    /**
     * 更新设备状态(API)
     */
    public function apiUpdateStatus(Request $request, $id)
    {
        try {
            $device = WaterDevice::findOrFail($id);
            
            $validator = \Illuminate\Support\Facades\Validator::make($request->all(), [
                'status' => 'required|string|in:online,offline,maintenance,E,D',
            ]);
            
            if ($validator->fails()) {
                return response()->json([
                    'code' => 1,
                    'message' => $validator->errors()->first(),
                    'data' => null
                ], 422);
            }
            
            $device->status = $request->input('status');
            $device->update_date = now();
            $device->save();
            
            return response()->json([
                'code' => 0,
                'message' => '设备状态更新成功',
                'data' => null
            ]);
        } catch (\Exception $e) {
            Log::error('更新设备状态失败：' . $e->getMessage());
            return response()->json([
                'code' => 1,
                'message' => '更新设备状态失败：' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

    /**
     * 创建设备(API)
     */
    public function apiStore(Request $request)
    {
        try {
            $validator = \Illuminate\Support\Facades\Validator::make($request->all(), [
                'device_name' => 'required|string|max:100',
                'device_number' => 'required|string|max:50',
                'water_point_id' => 'required|string',
                'status' => 'required|string|in:online,offline,maintenance,E,D',
                'billing_mode' => 'sometimes|string|in:0,1',
                'surplus_flow' => 'sometimes|numeric|min:0',
                'remaining_days' => 'sometimes|integer|min:0',
                'service_end_time' => 'sometimes|nullable|date',
                'address' => 'sometimes|nullable|string|max:255',
                'current_water_level' => 'nullable|numeric|min:0|max:100',
                'parameters' => 'nullable|string',
                'remark' => 'nullable|string',
            ]);
            
            if ($validator->fails()) {
                return response()->json([
                    'code' => 1,
                    'message' => $validator->errors()->first(),
                    'data' => null
                ], 422);
            }
            
            // 检查客户是否存在
            $client = WaterClient::find($request->input('water_point_id'));
            if (!$client) {
                return response()->json([
                    'code' => 1,
                    'message' => '指定的客户不存在',
                    'data' => null
                ], 422);
            }
            
            // 创建设备
            $device = new WaterDevice();
            $device->id = \Illuminate\Support\Str::uuid();
            $device->device_id = $client->client_device_id ?? $request->input('device_number');
            $device->device_number = $request->input('device_number');
            $device->status = $request->input('status');
            $device->remark = $request->input('remark');
            $device->billing_mode = $request->input('billing_mode');
            $device->surplus_flow = $request->input('surplus_flow');
            $device->remaining_days = $request->input('remaining_days');
            $device->service_end_time = $request->input('service_end_time');
            $device->address = $request->input('address');
            $device->create_date = now();
            $device->update_date = now();
            $device->save();
            
            return response()->json([
                'code' => 0,
                'message' => '设备创建成功',
                'data' => $device
            ]);
        } catch (\Exception $e) {
            Log::error('创建设备失败：' . $e->getMessage());
            return response()->json([
                'code' => 1,
                'message' => '创建设备失败：' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

    /**
     * 更新设备(API)
     */
    public function apiUpdate(Request $request, $id)
    {
        try {
            // 验证数据库连接
            try {
                DB::connection('water_db')->getPdo();
                Log::info('成功连接到水系统数据库');
            } catch (Exception $e) {
                Log::error('无法连接到水系统数据库: ' . $e->getMessage());
                throw new Exception('无法连接到水系统数据库，请联系管理员');
            }

            // 开始水系统数据库事务
            DB::connection('water_db')->beginTransaction();
            
            // 查询设备信息
            $device = WaterDevice::find($id);
            
            if (!$device) {
                return response()->json([
                    'code' => 1,
                    'message' => '设备不存在',
                    'data' => null
                ]);
            }
            
            // 更新设备基础信息
            $updated = false;
            
            // 逐项更新设备字段
            $fillable = [
                'device_type', 'raw_water_value', 'purification_water_value',
                'billing_mode', 'surplus_flow', 'remaining_days',
                'remark', 'status', 'cod_after', 'cod_before', 
                'toc_after', 'toc_before', 'longitude', 'latitude', 'address'
            ];
            
            foreach ($fillable as $field) {
                if ($request->has($field)) {
                    $device->$field = $request->input($field);
                    $updated = true;
                }
            }
            
            if ($updated) {
                // 保存设备信息
                $device->save();
                
                // 如果有设备备注更新，同时更新到客户记录中
                if ($request->has('remark') && $device->client) {
                    $device->client->remark = $request->input('remark');
                    $device->client->save();
                }
                
                // 如果需要更新客户信息
                if ($request->has('client') && is_array($request->input('client'))) {
                    $clientData = $request->input('client');
                    
                    if (!empty($device->client_id)) {
                        $client = WaterClient::find($device->client_id);
                        
                        if ($client) {
                            // 更新客户信息
                            if (isset($clientData['name'])) {
                                $client->name = $clientData['name'];
                            }
                            
                            if (isset($clientData['phone'])) {
                                $client->phone = $clientData['phone'];
                            }
                            
                            if (isset($clientData['province'])) {
                                $client->province = $clientData['province'];
                            }
                            
                            if (isset($clientData['city'])) {
                                $client->city = $clientData['city'];
                            }
                            
                            if (isset($clientData['area'])) {
                                $client->area = $clientData['area'];
                            }
                            
                            if (isset($clientData['address'])) {
                                $client->address = $clientData['address'];
                            }
                            
                            $client->save();
                        }
                    }
                }
            }
            
            // 提交事务
            DB::connection('water_db')->commit();
            
            return response()->json([
                'code' => 0,
                'message' => '设备信息更新成功',
                'data' => $device
            ]);
        } catch (\Exception $e) {
            // 回滚事务
            DB::connection('water_db')->rollBack();
            
            Log::error('更新设备信息失败：' . $e->getMessage(), [
                'trace' => $e->getTraceAsString(),
                'line' => $e->getLine(),
                'file' => $e->getFile()
            ]);
            
            return response()->json([
                'code' => 1,
                'message' => '更新设备信息失败：' . $e->getMessage(),
                'data' => null
            ]);
        }
    }
}
