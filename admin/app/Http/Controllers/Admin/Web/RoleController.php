<?php

namespace App\Http\Controllers\Admin\Web;

use App\Http\Controllers\Controller;
use App\Models\Role;
use App\Models\Permission;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;

class RoleController extends Controller
{
    /**
     * 获取角色列表
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        try {
            $query = Role::query();

            // 搜索条件
            if ($request->has('keyword') && $request->input('keyword') !== '') {
                $keyword = $request->input('keyword');
                $query->where(function($q) use ($keyword) {
                    $q->where('name', 'like', "%{$keyword}%")
                      ->orWhere('display_name', 'like', "%{$keyword}%")
                      ->orWhere('description', 'like', "%{$keyword}%");
                });
            }

            // 分页
            $perPage = $request->input('per_page', 15);
            $roles = $query->orderBy('id', 'desc')->paginate($perPage);

            // 加载权限关联
            $roles->load('permissions');

            return response()->json([
                'code' => 0,
                'message' => '获取成功',
                'data' => [
                    'data' => $roles->items(),
                    'total' => $roles->total(),
                    'per_page' => $roles->perPage(),
                    'current_page' => $roles->currentPage(),
                    'last_page' => $roles->lastPage()
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('获取角色列表失败: ' . $e->getMessage(), [
                'exception' => $e,
                'request' => $request->all()
            ]);

            return response()->json([
                'code' => 1,
                'message' => '获取角色列表失败: ' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    /**
     * 创建角色
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:50|unique:admin_roles,name',
            'display_name' => 'required|string|max:50',
            'description' => 'nullable|string|max:255',
            'permissions' => 'nullable|array',
            'permissions.*' => 'exists:admin_permissions,id'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'code' => 1,
                'message' => '验证失败',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $role = Role::create([
                'name' => $request->input('name'),
                'display_name' => $request->input('display_name'),
                'description' => $request->input('description'),
                'is_system' => false
            ]);

            // 同步权限
            if ($request->has('permissions')) {
                $role->permissions()->sync($request->input('permissions'));
            }

            return response()->json([
                'code' => 0,
                'message' => '角色创建成功',
                'data' => $role
            ]);
        } catch (\Exception $e) {
            Log::error('创建角色失败: ' . $e->getMessage(), [
                'exception' => $e,
                'request' => $request->all()
            ]);

            return response()->json([
                'code' => 1,
                'message' => '创建角色失败: ' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    /**
     * 获取单个角色信息
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        try {
            $role = Role::with('permissions')->findOrFail($id);

            return response()->json([
                'code' => 0,
                'message' => '获取成功',
                'data' => $role
            ]);
        } catch (\Exception $e) {
            Log::error('获取角色详情失败: ' . $e->getMessage(), [
                'exception' => $e,
                'id' => $id
            ]);

            return response()->json([
                'code' => 1,
                'message' => '获取角色详情失败: ' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    /**
     * 更新角色信息
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id)
    {
        $role = Role::findOrFail($id);

        // 系统角色不允许修改名称
        $nameRule = $role->is_system ? 'required|string|max:50' : 'required|string|max:50|unique:admin_roles,name,'.$id;

        $validator = Validator::make($request->all(), [
            'name' => $nameRule,
            'display_name' => 'required|string|max:50',
            'description' => 'nullable|string|max:255',
            'permissions' => 'nullable|array',
            'permissions.*' => 'exists:admin_permissions,id'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'code' => 1,
                'message' => '验证失败',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            // 系统角色不允许修改名称
            if (!$role->is_system) {
                $role->name = $request->input('name');
            }

            $role->display_name = $request->input('display_name');
            $role->description = $request->input('description');
            $role->save();

            // 同步权限
            if ($request->has('permissions')) {
                $role->permissions()->sync($request->input('permissions'));
            }

            return response()->json([
                'code' => 0,
                'message' => '角色更新成功',
                'data' => $role
            ]);
        } catch (\Exception $e) {
            Log::error('更新角色失败: ' . $e->getMessage(), [
                'exception' => $e,
                'id' => $id,
                'request' => $request->all()
            ]);

            return response()->json([
                'code' => 1,
                'message' => '更新角色失败: ' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    /**
     * 删除角色
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy($id)
    {
        try {
            $role = Role::findOrFail($id);

            // 系统角色不允许删除
            if ($role->is_system) {
                return response()->json([
                    'code' => 1,
                    'message' => '系统角色不允许删除',
                    'data' => null
                ], 403);
            }

            // 检查是否有用户使用此角色
            if ($role->admins()->count() > 0) {
                return response()->json([
                    'code' => 1,
                    'message' => '该角色下有用户，不允许删除',
                    'data' => null
                ], 403);
            }

            // 删除角色
            $role->permissions()->detach();
            $role->delete();

            return response()->json([
                'code' => 0,
                'message' => '角色删除成功',
                'data' => null
            ]);
        } catch (\Exception $e) {
            Log::error('删除角色失败: ' . $e->getMessage(), [
                'exception' => $e,
                'id' => $id
            ]);

            return response()->json([
                'code' => 1,
                'message' => '删除角色失败: ' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    /**
     * 获取所有权限列表（按模块分组）
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getAllPermissions()
    {
        try {
            $permissions = Permission::all()->groupBy('module');

            return response()->json([
                'code' => 0,
                'message' => '获取成功',
                'data' => $permissions
            ]);
        } catch (\Exception $e) {
            Log::error('获取权限列表失败: ' . $e->getMessage(), [
                'exception' => $e
            ]);

            return response()->json([
                'code' => 1,
                'message' => '获取权限列表失败: ' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    /**
     * 更新角色权限
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function updatePermissions(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'permissions' => 'required|array',
            'permissions.*' => 'exists:admin_permissions,id'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'code' => 1,
                'message' => '验证失败',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $role = Role::findOrFail($id);

            // 同步权限
            $role->permissions()->sync($request->input('permissions'));

            return response()->json([
                'code' => 0,
                'message' => '角色权限更新成功',
                'data' => $role->load('permissions')
            ]);
        } catch (\Exception $e) {
            Log::error('更新角色权限失败: ' . $e->getMessage(), [
                'exception' => $e,
                'id' => $id,
                'request' => $request->all()
            ]);

            return response()->json([
                'code' => 1,
                'message' => '更新角色权限失败: ' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }
}
