<?php

namespace App\Http\Controllers\Admin\Web;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Models\Admin;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Laravel\Sanctum\PersonalAccessToken;
use Carbon\Carbon;

class AdminAuthController extends Controller
{
    /**
     * 管理员API登录
     */
    public function login(Request $request)
    {
        // 记录请求数据
        Log::info('管理员登录请求', [
            'ip' => $request->ip(),
            'userAgent' => $request->userAgent(),
            'username' => $request->input('username')
        ]);

        try {
            $credentials = $request->validate([
                'username' => 'required|string',
                'password' => 'required|string',
            ]);

            // 查找管理员
            $admin = Admin::where('username', $credentials['username'])->first();

            // 记录查询结果
            Log::info('管理员查询结果', [
                'username' => $credentials['username'],
                'found' => $admin ? 'yes' : 'no'
            ]);

            if (!$admin || !Hash::check($credentials['password'], $admin->password)) {
                // 密码错误，记录日志
                Log::warning('管理员登录失败 - 用户名或密码错误', [
                    'username' => $credentials['username'],
                    'ip' => $request->ip()
                ]);

                return response()->json([
                    'code' => 1,
                    'message' => '用户名或密码错误'
                ], 401);
            }

            // 删除此用户之前的所有token
            $admin->tokens()->delete();

            // 创建新token，设置有效期为24小时
            $token = $admin->createToken('admin-token', ['*'], Carbon::now()->addDay())->plainTextToken;

            // 记录登录成功日志
            Log::info('管理员登录成功', [
                'username' => $admin->username,
                'id' => $admin->id,
                'token_length' => strlen($token)
            ]);

            // 返回登录成功的响应
            return response()->json([
                'code' => 0,
                'message' => '登录成功',
                'data' => [
                    'token' => $token,
                    'user' => [
                        'id' => $admin->id,
                        'username' => $admin->username,
                        'name' => $admin->name,
                        'avatar' => $admin->avatar
                    ]
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('管理员登录异常', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'code' => 500,
                'message' => '服务器错误: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取管理员信息
     */
    public function getInfo(Request $request)
    {
        try {
            $user = $request->user();

            if (!$user) {
                return response()->json([
                    'code' => 1,
                    'message' => '未找到用户信息'
                ], 401);
            }

            return response()->json([
                'code' => 0,
                'data' => [
                    'id' => $user->id,
                    'username' => $user->username,
                    'name' => $user->name,
                    'avatar' => $user->avatar
                ],
                'message' => '获取成功'
            ]);
        } catch (\Exception $e) {
            Log::error('获取管理员信息异常', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'code' => 500,
                'message' => '服务器错误'
            ], 500);
        }
    }

    /**
     * 获取当前管理员信息 (me方法)
     */
    public function me(Request $request)
    {
        try {
            $user = $request->user();

            if (!$user) {
                return response()->json([
                    'code' => 1,
                    'message' => '未找到用户信息'
                ], 401);
            }

            return response()->json([
                'code' => 0,
                'data' => [
                    'id' => $user->id,
                    'username' => $user->username,
                    'name' => $user->name,
                    'email' => $user->email,
                    'phone' => $user->phone,
                    'avatar' => $user->avatar
                ],
                'message' => '获取成功'
            ]);
        } catch (\Exception $e) {
            Log::error('获取管理员信息异常', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'code' => 500,
                'message' => '服务器错误'
            ], 500);
        }
    }

    /**
     * 管理员退出登录
     */
    public function logout(Request $request)
    {
        try {
            // 删除当前token
            $request->user()->currentAccessToken()->delete();

            return response()->json([
                'code' => 0,
                'message' => '已退出登录'
            ]);
        } catch (\Exception $e) {
            Log::error('管理员登出异常', [
                'message' => $e->getMessage()
            ]);

            return response()->json([
                'code' => 500,
                'message' => '退出登录失败'
            ], 500);
        }
    }

    /**
     * 验证令牌是否有效
     */
    public function verifyToken(Request $request)
    {
        try {
            $tokenString = $request->input('token');

            if (!$tokenString) {
                // 尝试从请求头中获取token
                $bearerToken = $request->bearerToken();
                if ($bearerToken) {
                    $tokenString = $bearerToken;
                } else {
                    // 尝试从Authorization头中获取token
                    $authHeader = $request->header('Authorization');
                    if ($authHeader && strpos($authHeader, 'Bearer ') === 0) {
                        $tokenString = substr($authHeader, 7);
                    }
                }
            }

            if (!$tokenString) {
                return response()->json([
                    'code' => 1,
                    'message' => '未提供令牌'
                ], 401);
            }

            // 解析token
            $tokenParts = explode('|', $tokenString);
            if (count($tokenParts) !== 2) {
                return response()->json([
                    'code' => 1,
                    'message' => '令牌格式无效'
                ], 401);
            }

            $tokenId = $tokenParts[0];

            // 查找token
            $token = PersonalAccessToken::find($tokenId);
            if (!$token) {
                return response()->json([
                    'code' => 1,
                    'message' => '令牌不存在'
                ], 401);
            }

            // 获取用户
            $user = $token->tokenable;
            if (!$user || get_class($user) !== Admin::class) {
                return response()->json([
                    'code' => 1,
                    'message' => '令牌关联的用户不是管理员'
                ], 401);
            }

            // 检查令牌过期时间
            $expiresAt = $token->expires_at;
            $now = Carbon::now();

            // 如果令牌已过期
            if ($expiresAt && $now->isAfter($expiresAt)) {
                return response()->json([
                    'code' => 1,
                    'message' => '令牌已过期'
                ], 401);
            }

            // 如果令牌快过期了（剩余时间小于1小时）
            $expiringSoon = false;
            if ($expiresAt) {
                $hoursDiff = $now->diffInHours($expiresAt);
                $expiringSoon = $hoursDiff < 1;
            }

            return response()->json([
                'code' => 0,
                'message' => '令牌有效',
                'data' => [
                    'user' => [
                        'id' => $user->id,
                        'username' => $user->username,
                        'name' => $user->name,
                        'avatar' => $user->avatar
                    ],
                    'expires_at' => $expiresAt ? $expiresAt->toDateTimeString() : null,
                    'expiring_soon' => $expiringSoon
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('验证令牌异常', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'code' => 500,
                'message' => '验证令牌失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 刷新令牌
     */
    public function refreshToken(Request $request)
    {
        try {
            $tokenString = $request->input('token');

            if (!$tokenString) {
                // 尝试从请求头中获取token
                $bearerToken = $request->bearerToken();
                if ($bearerToken) {
                    $tokenString = $bearerToken;
                } else {
                    // 尝试从Authorization头中获取token
                    $authHeader = $request->header('Authorization');
                    if ($authHeader && strpos($authHeader, 'Bearer ') === 0) {
                        $tokenString = substr($authHeader, 7);
                    }
                }
            }

            if (!$tokenString) {
                return response()->json([
                    'code' => 1,
                    'message' => '未提供令牌'
                ], 401);
            }

            // 解析token
            $tokenParts = explode('|', $tokenString);
            if (count($tokenParts) !== 2) {
                return response()->json([
                    'code' => 1,
                    'message' => '令牌格式无效'
                ], 401);
            }

            $tokenId = $tokenParts[0];

            // 查找token
            $token = PersonalAccessToken::find($tokenId);
            if (!$token) {
                return response()->json([
                    'code' => 1,
                    'message' => '令牌不存在'
                ], 401);
            }

            // 获取用户
            $user = $token->tokenable;
            if (!$user || get_class($user) !== Admin::class) {
                return response()->json([
                    'code' => 1,
                    'message' => '令牌关联的用户不是管理员'
                ], 401);
            }

            // 删除当前token
            $token->delete();

            // 创建新的token，有效期24小时
            $newToken = $user->createToken('admin-token', ['*'], Carbon::now()->addDay())->plainTextToken;

            Log::info('管理员令牌刷新成功', [
                'user_id' => $user->id,
                'username' => $user->username
            ]);

            return response()->json([
                'code' => 0,
                'message' => '令牌刷新成功',
                'data' => [
                    'token' => $newToken,
                    'user' => [
                        'id' => $user->id,
                        'username' => $user->username,
                        'name' => $user->name,
                        'avatar' => $user->avatar
                    ]
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('刷新令牌异常', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'code' => 500,
                'message' => '刷新令牌失败: ' . $e->getMessage()
            ], 500);
        }
    }
}