<?php

namespace App\Http\Controllers\Admin\Web;

use App\Http\Controllers\Controller;
use App\Models\TappDevice;
use App\Models\WaterDevice;
use App\Models\WaterClient;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Artisan;

class TappDeviceController extends Controller
{
    /**
     * 显示点点够设备列表页面
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        return view('layouts.app');
    }

    /**
     * 显示设备详情页面
     *
     * @param int $id
     * @return \Illuminate\View\View
     */
    public function show($id)
    {
        try {
            $device = TappDevice::findOrFail($id);

            // 获取设备的充值历史数据
            $waterHistory = [];
            
            // 尝试获取7天的制水趋势数据
            $waterTrend = [
                'dates' => [],
                'water_production' => [],
                'temperatures' => []
            ];
            
            // 获取设备的充值订单信息
            $deviceOrders = $this->getDeviceOrders($device->device_number);
            
            return view('admin.tapp_devices.show', [
                'device' => $device,
                'waterHistory' => $waterHistory,
                'waterTrend' => $waterTrend,
                'deviceOrders' => $deviceOrders
            ]);
        } catch (\Exception $e) {
            Log::error('显示点点够设备详情失败', [
                'id' => $id,
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return redirect()->route('admin.tapp_devices.index')
                ->with('error', '获取设备详情失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取设备的充值订单
     *
     * @param string $deviceNumber
     * @return array
     */
    private function getDeviceOrders($deviceNumber)
    {
        try {
            // 连接净水器数据库查询wb_order表
            $orders = DB::connection('water_db')
                ->table('wb_order')
                ->where('device_number', $deviceNumber)
                ->where('order_status', '103') // 只查询支付成功的订单
                ->select([
                    'id',
                    'order_number',
                    'billing_mode',
                    'money',
                    'order_status',
                    'create_date',
                    'surrogate_type'
                ])
                ->orderBy('create_date', 'desc')
                ->limit(10) // 最近10条订单
                ->get();
                
            return $orders;
        } catch (\Exception $e) {
            Log::error('获取设备充值订单失败', [
                'device_number' => $deviceNumber,
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return [];
        }
    }

    /**
     * 显示设备编辑页面
     *
     * @param int $id
     * @return \Illuminate\View\View
     */
    public function edit($id)
    {
        return view('layouts.app');
    }

    /**
     * 获取设备列表API (参照总部设备V1 API结构)
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function apiIndex(Request $request)
    {
        try {
            $query = TappDevice::query();

            // 🔥 重要：只显示已激活的设备
            // 激活条件：activate_date不为空且不为'0000-00-00 00:00:00'
            $query->whereNotNull('activate_date')
                  ->where('activate_date', '!=', '')
                  ->where('activate_date', '!=', '0000-00-00 00:00:00');

            // 关键词搜索（设备编号、IMEI、用户名、地址、客户名称、销售商名称）
            if ($request->filled('keyword')) {
                $keyword = $request->input('keyword');
                $query->where(function ($q) use ($keyword) {
                    $q->where('device_number', 'like', "%{$keyword}%")
                      ->orWhere('imei', 'like', "%{$keyword}%")
                      ->orWhere('app_user_name', 'like', "%{$keyword}%")
                      ->orWhere('address', 'like', "%{$keyword}%")
                      ->orWhere('client_name', 'like', "%{$keyword}%")
                      ->orWhere('dealer_name', 'like', "%{$keyword}%")
                      ->orWhere('iccid', 'like', "%{$keyword}%");
                });
            }

            // 设备状态筛选
            if ($request->filled('status')) {
                $query->where('status', $request->input('status'));
            }

            // 网络状态筛选
            if ($request->filled('network_status')) {
                $query->where('network_status', $request->input('network_status'));
            }

            // 设备类型筛选
            if ($request->filled('device_type')) {
                $query->where('device_type', $request->input('device_type'));
            }

            // 计费模式筛选
            if ($request->filled('billing_mode')) {
                $query->where('billing_mode', $request->input('billing_mode'));
            }

            // 自用状态筛选
            if ($request->filled('is_self_use')) {
                $query->where('is_self_use', $request->input('is_self_use'));
            }

            // 销售商筛选（根据dealer_name字段）
            if ($request->filled('dealer_name')) {
                $query->where('dealer_name', 'like', "%{$request->input('dealer_name')}%");
            }

            // 客户筛选
            if ($request->filled('client_name')) {
                $query->where('client_name', 'like', "%{$request->input('client_name')}%");
            }

            // VIP用户筛选
            if ($request->filled('app_user_id')) {
                $query->where('app_user_id', $request->input('app_user_id'));
            }

            // 日期范围筛选
            if ($request->filled('start_date')) {
                $query->whereDate('activate_date', '>=', $request->input('start_date'));
            }
            if ($request->filled('end_date')) {
                $query->whereDate('activate_date', '<=', $request->input('end_date'));
            }

            // 水量范围筛选
            if ($request->filled('water_min') && is_numeric($request->input('water_min'))) {
                $query->where('cumulative_filtration_flow', '>=', $request->input('water_min'));
            }
            if ($request->filled('water_max') && is_numeric($request->input('water_max'))) {
                $query->where('cumulative_filtration_flow', '<=', $request->input('water_max'));
            }

            // 剩余用量筛选（低水量预警）
            if ($request->filled('low_water_alert') && $request->input('low_water_alert') == '1') {
                $query->where(function($q) {
                    $q->where(function($subQ) {
                        // 流量计费模式：剩余流量少于50L
                        $subQ->where('billing_mode', '1')
                             ->where('surplus_flow', '<', 50);
                    })->orWhere(function($subQ) {
                        // 包年计费模式：剩余天数少于30天
                        $subQ->where('billing_mode', '0')
                             ->where('remaining_days', '<', 30);
                    });
                });
            }

            // 即将到期预警筛选
            if ($request->filled('expire_alert') && $request->input('expire_alert') == '1') {
                $query->where('remaining_days', '<', 30);
            }

            // 滤芯预警筛选（任一滤芯寿命低于20%）
            if ($request->filled('filter_alert') && $request->input('filter_alert') == '1') {
                $query->where(function($q) {
                    $q->where('f1_life_percent', '<', 20)
                      ->orWhere('f2_life_percent', '<', 20)
                      ->orWhere('f3_life_percent', '<', 20);
                });
            }

            // 地理位置筛选（按经纬度范围）
            if ($request->filled('longitude_min') && $request->filled('longitude_max')) {
                $query->whereBetween('longitude', [$request->input('longitude_min'), $request->input('longitude_max')]);
            }
            if ($request->filled('latitude_min') && $request->filled('latitude_max')) {
                $query->whereBetween('latitude', [$request->input('latitude_min'), $request->input('latitude_max')]);
            }

            // 分页参数处理 - 兼容per_page和limit两种参数名
            $perPage = $request->input('per_page', $request->input('limit', 10));
            $page = $request->input('page', 1);

            // 按激活时间倒序排序并分页
            $devices = $query->orderBy('activate_date', 'desc')
                           ->paginate($perPage, ['*'], 'page', $page);

            // 格式化设备数据
            $formattedDevices = collect($devices->items())->map(function ($device) {
                // 获取最近的充值信息
                $lastRechargeInfo = $device->getLastRechargeInfo();
                
                $deviceData = [
                    'id' => $device->id,
                    'device_number' => $device->device_number,
                    'device_name' => $device->device_number,
                    'imei' => $device->imei,
                    'iccid' => $device->iccid,
                    'device_type' => $device->device_type,
                    'dealer_name' => $device->dealer_name,
                    'client_name' => $device->client_name,
                    'app_user_id' => $device->app_user_id,
                    'app_user_name' => $device->app_user_name,
                    'status' => $device->status,
                    'status_text' => $device->status_text,
                    'network_status' => $device->network_status,
                    'network_status_text' => $device->network_status == '1' ? '在线' : '离线',
                    'is_online' => $device->network_status == '1',
                    'billing_mode' => $device->billing_mode,
                    'surplus_flow' => $device->surplus_flow,
                    'remaining_days' => $device->remaining_days,
                    'cumulative_filtration_flow' => $device->cumulative_filtration_flow,
                    'activate_date' => $device->activate_date,
                    'last_online_time' => $device->last_online_time,
                    'last_sync_time' => $device->last_sync_time,
                    'is_self_use' => $device->is_self_use,
                    'f1_life_percent' => $device->f1_life_percent,
                    'f2_life_percent' => $device->f2_life_percent,
                    'f3_life_percent' => $device->f3_life_percent,
                    'f1_flux' => $device->f1_flux,
                    'f1_flux_max' => $device->f1_flux_max,
                    'f2_flux' => $device->f2_flux,
                    'f2_flux_max' => $device->f2_flux_max,
                    'f3_flux' => $device->f3_flux,
                    'f3_flux_max' => $device->f3_flux_max,
                    'service_end_time' => $device->service_end_time,
                    'address' => $device->address,
                    'longitude' => $device->longitude,
                    'latitude' => $device->latitude,
                    'raw_water_value' => $device->raw_water_value,
                    'purification_water_value' => $device->purification_water_value,
                    
                    // 计算字段和预警状态
                    'water_level_percentage' => $device->surplus_flow > 0 ? 
                        min(100, ($device->surplus_flow / 1000) * 100) : 0,
                    'days_percentage' => $device->remaining_days > 0 ? 
                        min(100, ($device->remaining_days / 365) * 100) : 0,
                    'is_low_water' => ($device->billing_mode == '1' && $device->surplus_flow < 50) || 
                                     ($device->billing_mode == '0' && $device->remaining_days < 30),
                    'is_expire_soon' => $device->remaining_days < 30,
                    'has_filter_alert' => ($device->f1_life_percent < 20) || 
                                         ($device->f2_life_percent < 20) || 
                                         ($device->f3_life_percent < 20),
                ];
                
                // 如果有充值信息，添加充值相关字段
                if ($lastRechargeInfo) {
                    $deviceData['recharge_info'] = [
                        'order_id' => $lastRechargeInfo->id,
                        'order_number' => $lastRechargeInfo->order_number,
                        'billing_mode' => $lastRechargeInfo->billing_mode,
                        'billing_mode_text' => $lastRechargeInfo->billing_mode == '1' ? '流量计费' : '包年计费',
                        'money' => $lastRechargeInfo->money,
                        'create_date' => $lastRechargeInfo->create_date,
                        'surrogate_type' => $lastRechargeInfo->surrogate_type,
                        'surrogate_type_text' => $lastRechargeInfo->surrogate_type == '1' ? '代充' : '自充',
                        'commission_amount' => $device->calculateCommissionAmount()
                    ];
                }
                
                return $deviceData;
            });

            // 计算统计信息
            $statistics = [
                'total_devices' => $devices->total(),
                'online_devices' => $formattedDevices->where('is_online', true)->count(),
                'offline_devices' => $formattedDevices->where('is_online', false)->count(),
                'low_water_devices' => $formattedDevices->where('is_low_water', true)->count(),
                'expire_soon_devices' => $formattedDevices->where('is_expire_soon', true)->count(),
                'filter_alert_devices' => $formattedDevices->where('has_filter_alert', true)->count(),
                'flow_billing_devices' => $formattedDevices->where('billing_mode', '1')->count(),
                'annual_billing_devices' => $formattedDevices->where('billing_mode', '0')->count(),
                'self_use_devices' => $formattedDevices->where('is_self_use', 1)->count(),
                'for_sale_devices' => $formattedDevices->where('is_self_use', 0)->count(),
            ];

            // 使用标准化的响应格式（参照总部设备V1 API）
            return response()->json([
                'code' => 200,
                'message' => '获取成功',
                'data' => [
                    'data' => $formattedDevices->values(),
                    'total' => $devices->total(),
                    'current_page' => $devices->currentPage(),
                    'per_page' => $devices->perPage(),
                    'last_page' => $devices->lastPage(),
                    'statistics' => $statistics
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('获取点点够设备列表失败', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request_params' => $request->all()
            ]);

            return response()->json([
                'code' => 500,
                'message' => '获取设备列表失败: ' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

    /**
     * 获取设备详情API
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function apiShow($id)
    {
        try {
            $device = TappDevice::findOrFail($id);
            
            // 获取设备的最近充值订单信息
            $lastRechargeInfo = $device->getLastRechargeInfo();
            
            $data = [
                'id' => $device->id,
                'device_number' => $device->device_number,
                'device_name' => $device->device_number,
                'imei' => $device->imei,
                'device_type' => $device->device_type,
                'dealer_name' => $device->dealer_name,
                'client_name' => $device->client_name,
                'app_user_id' => $device->app_user_id,
                'app_user_name' => $device->app_user_name,
                'status' => $device->status,
                'status_text' => $device->status_text,
                'network_status' => $device->network_status,
                'network_status_text' => $device->network_status == '1' ? '在线' : '离线',
                'billing_mode' => $device->billing_mode,
                'surplus_flow' => $device->surplus_flow,
                'remaining_days' => $device->remaining_days,
                'cumulative_filtration_flow' => $device->cumulative_filtration_flow,
                'activate_date' => $device->activate_date,
                'last_online_time' => $device->last_online_time,
                'last_sync_time' => $device->last_sync_time,
                'address' => $device->address,
                'remark' => $device->remark,
                'create_date' => $device->create_date,
                'update_date' => $device->update_date,
                'is_self_use' => $device->is_self_use,
                'f1_life_percent' => $device->f1_life_percent,
                'f2_life_percent' => $device->f2_life_percent,
                'f3_life_percent' => $device->f3_life_percent,
                'f1_flux' => $device->f1_flux,
                'f1_flux_max' => $device->f1_flux_max,
                'f2_flux' => $device->f2_flux,
                'f2_flux_max' => $device->f2_flux_max,
                'f3_flux' => $device->f3_flux,
                'f3_flux_max' => $device->f3_flux_max,
                'service_end_time' => $device->service_end_time,
                // 充值订单相关信息
                'recharge_info' => $lastRechargeInfo ? [
                    'order_id' => $lastRechargeInfo->id,
                    'order_number' => $lastRechargeInfo->order_number,
                    'billing_mode' => $lastRechargeInfo->billing_mode,
                    'billing_mode_text' => $lastRechargeInfo->billing_mode == '1' ? '流量计费' : '包年计费',
                    'money' => $lastRechargeInfo->money,
                    'create_date' => $lastRechargeInfo->create_date,
                    'surrogate_type' => $lastRechargeInfo->surrogate_type,
                    'surrogate_type_text' => $lastRechargeInfo->surrogate_type == '1' ? '代充' : '自充',
                    'commission_amount' => $device->calculateCommissionAmount()
                ] : null,
                // 获取所有充值订单
                'recharge_orders' => $device->getRechargeOrders()
            ];

            return response()->json([
                'code' => 0,
                'message' => '获取成功',
                'data' => $data
            ]);
        } catch (\Exception $e) {
            Log::error('获取点点够设备详情失败', [
                'id' => $id,
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'code' => 500,
                'message' => '获取设备详情失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 更新设备信息API
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function apiUpdate(Request $request, $id)
    {
        try {
            $device = TappDevice::findOrFail($id);
            
            // 可更新的字段
            $data = $request->only([
                'device_type',
                'remark',
                'billing_mode',
                'surplus_flow',
                'remaining_days',
                'status',
                'app_user_id',
                'app_user_name',
                'is_self_use'
            ]);
            
            $device->update($data);

            return response()->json([
                'code' => 0,
                'message' => '更新成功',
                'data' => null
            ]);
        } catch (\Exception $e) {
            Log::error('更新点点够设备信息失败', [
                'id' => $id,
                'data' => $request->all(),
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'code' => 500,
                'message' => '更新设备信息失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 设置设备状态API
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function apiSetStatus(Request $request, $id)
    {
        try {
            $device = TappDevice::findOrFail($id);
            $status = $request->input('status');
            
            if (!in_array($status, ['E', 'D', 'maintenance'])) {
                return response()->json([
                    'code' => 400,
                    'message' => '无效的状态值'
                ]);
            }
            
            $device->status = $status;
            $device->save();

            return response()->json([
                'code' => 0,
                'message' => '状态已更新',
                'data' => null
            ]);
        } catch (\Exception $e) {
            Log::error('设置点点够设备状态失败', [
                'id' => $id,
                'status' => $request->input('status'),
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'code' => 500,
                'message' => '设置设备状态失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 同步点点够设备数据
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function syncData(Request $request)
    {
        try {
            $force = $request->input('force', false);
            
            // 调用同步命令
            $exitCode = Artisan::call('tapp:sync-devices', [
                '--force' => $force
            ]);
            
            $output = Artisan::output();
            
            if ($exitCode === 0) {
                return response()->json([
                    'code' => 0,
                    'message' => '设备数据同步成功',
                    'data' => [
                        'details' => nl2br($output)
                    ]
                ]);
            } else {
                return response()->json([
                    'code' => 500,
                    'message' => '设备数据同步失败',
                    'data' => [
                        'details' => nl2br($output)
                    ]
                ]);
            }
        } catch (\Exception $e) {
            Log::error('同步点点够设备数据失败', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'code' => 500,
                'message' => '同步设备数据失败: ' . $e->getMessage(),
                'data' => [
                    'details' => $e->getMessage() . "\n" . $e->getTraceAsString()
                ]
            ]);
        }
    }

    /**
     * 获取App用户（VIP用户）列表用于设备关联
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getAppUsers(Request $request)
    {
        try {
            $keyword = $request->input('keyword', '');
            $query = \App\Models\AppUser::query()
                ->select(['id', 'name', 'phone', 'avatar']);
            
            if (!empty($keyword)) {
                $query->where(function($q) use ($keyword) {
                    $q->where('name', 'like', "%{$keyword}%")
                      ->orWhere('phone', 'like', "%{$keyword}%")
                      ->orWhere('id', 'like', "%{$keyword}%");
                });
            }
            
            // 优先显示VIP用户
            $query->orderBy('is_vip', 'desc')
                  ->orderBy('id', 'desc')
                  ->limit(30);
            
            $users = $query->get();
            
            $list = [];
            foreach ($users as $user) {
                $vipLabel = $user->is_vip ? '[VIP]' : '';
                $list[] = [
                    'id' => $user->id,
                    'name' => $user->name . $vipLabel,
                    'phone' => $user->phone,
                    'avatar' => $user->avatar,
                    'label' => "{$user->name} ({$user->phone})"
                ];
            }
            
            return response()->json([
                'code' => 0,
                'message' => '获取成功',
                'data' => $list
            ]);
        } catch (\Exception $e) {
            Log::error('获取App用户列表失败', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'code' => 500,
                'message' => '获取用户列表失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 删除设备
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy($id)
    {
        try {
            $device = TappDevice::findOrFail($id);
            
            DB::beginTransaction();
            try {
                // 删除设备
                $device->delete();
                
                DB::commit();
                
                return response()->json([
                    'code' => 0,
                    'message' => '设备删除成功',
                    'data' => null
                ]);
            } catch (\Exception $e) {
                DB::rollBack();
                throw $e;
            }
        } catch (\Exception $e) {
            Log::error('删除点点够设备失败', [
                'id' => $id,
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'code' => 500,
                'message' => '删除设备失败: ' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

    /**
     * 获取设备统计数据
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getStats()
    {
        try {
            $stats = [
                'total' => TappDevice::count(),
                'online' => TappDevice::where('network_status', '1')->count(),
                'offline' => TappDevice::where('network_status', '0')->count(),
                'enabled' => TappDevice::where('status', 'E')->count(),
                'disabled' => TappDevice::where('status', 'D')->count(),
                'maintenance' => TappDevice::where('status', 'maintenance')->count(),
                'self_use' => TappDevice::where('is_self_use', 1)->count(),
                'for_sale' => TappDevice::where('is_self_use', 0)->count(),
            ];

            return response()->json([
                'code' => 0,
                'message' => '获取成功',
                'data' => $stats
            ]);
        } catch (\Exception $e) {
            Log::error('获取设备统计数据失败', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'code' => 500,
                'message' => '获取统计数据失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 批量操作设备
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function batchOperate(Request $request)
    {
        try {
            $ids = $request->input('ids', []);
            $action = $request->input('action');
            
            if (empty($ids) || !$action) {
                return response()->json([
                    'code' => 400,
                    'message' => '参数错误'
                ]);
            }

            $devices = TappDevice::whereIn('id', $ids)->get();
            
            if ($devices->isEmpty()) {
                return response()->json([
                    'code' => 404,
                    'message' => '未找到指定设备'
                ]);
            }

            DB::beginTransaction();
            try {
                switch ($action) {
                    case 'enable':
                        $devices->each(function ($device) {
                            $device->update(['status' => 'E']);
                        });
                        $message = '批量启用成功';
                        break;
                    case 'disable':
                        $devices->each(function ($device) {
                            $device->update(['status' => 'D']);
                        });
                        $message = '批量禁用成功';
                        break;
                    case 'delete':
                        $devices->each(function ($device) {
                            $device->delete();
                        });
                        $message = '批量删除成功';
                        break;
                    default:
                        throw new \Exception('不支持的操作类型');
                }
                
                DB::commit();
                
                return response()->json([
                    'code' => 0,
                    'message' => $message,
                    'data' => null
                ]);
            } catch (\Exception $e) {
                DB::rollBack();
                throw $e;
            }
        } catch (\Exception $e) {
            Log::error('批量操作设备失败', [
                'ids' => $request->input('ids'),
                'action' => $request->input('action'),
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'code' => 500,
                'message' => '批量操作失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 导出设备数据
     *
     * @param Request $request
     * @return \Illuminate\Http\Response
     */
    public function export(Request $request)
    {
        try {
            // 这里可以根据需要实现导出功能
            return response()->json([
                'code' => 501,
                'message' => '导出功能暂未实现'
            ]);
        } catch (\Exception $e) {
            Log::error('导出设备数据失败', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'code' => 500,
                'message' => '导出失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 获取设备制水记录
     *
     * @param int $id
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getWaterLogs($id, Request $request)
    {
        try {
            // 这里可以根据需要实现制水记录功能
            return response()->json([
                'code' => 501,
                'message' => '制水记录功能暂未实现'
            ]);
        } catch (\Exception $e) {
            Log::error('获取设备制水记录失败', [
                'id' => $id,
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'code' => 500,
                'message' => '获取制水记录失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 获取设备滤芯信息
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function getFilters($id)
    {
        try {
            $device = TappDevice::findOrFail($id);
            
            $filters = [
                'f1' => [
                    'name' => 'PP棉滤芯',
                    'flux' => $device->f1_flux,
                    'flux_max' => $device->f1_flux_max,
                    'life_percent' => $device->f1_life_percent,
                ],
                'f2' => [
                    'name' => '活性炭滤芯',
                    'flux' => $device->f2_flux,
                    'flux_max' => $device->f2_flux_max,
                    'life_percent' => $device->f2_life_percent,
                ],
                'f3' => [
                    'name' => 'RO反渗透滤芯',
                    'flux' => $device->f3_flux,
                    'flux_max' => $device->f3_flux_max,
                    'life_percent' => $device->f3_life_percent,
                ],
            ];

            return response()->json([
                'code' => 0,
                'message' => '获取成功',
                'data' => $filters
            ]);
        } catch (\Exception $e) {
            Log::error('获取设备滤芯信息失败', [
                'id' => $id,
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'code' => 500,
                'message' => '获取滤芯信息失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 更新设备滤芯信息
     *
     * @param int $id
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateFilters($id, Request $request)
    {
        try {
            // 这里可以根据需要实现滤芯更新功能
            return response()->json([
                'code' => 501,
                'message' => '滤芯更新功能暂未实现'
            ]);
        } catch (\Exception $e) {
            Log::error('更新设备滤芯信息失败', [
                'id' => $id,
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'code' => 500,
                'message' => '更新滤芯信息失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 重置设备滤芯
     *
     * @param int $id
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function resetFilter($id, Request $request)
    {
        try {
            // 这里可以根据需要实现滤芯重置功能
            return response()->json([
                'code' => 501,
                'message' => '滤芯重置功能暂未实现'
            ]);
        } catch (\Exception $e) {
            Log::error('重置设备滤芯失败', [
                'id' => $id,
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'code' => 500,
                'message' => '重置滤芯失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 获取设备在线状态
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function getOnlineStatus($id)
    {
        try {
            $device = TappDevice::findOrFail($id);
            
            return response()->json([
                'code' => 0,
                'message' => '获取成功',
                'data' => [
                    'network_status' => $device->network_status,
                    'network_status_text' => $device->network_status == '1' ? '在线' : '离线',
                    'last_online_time' => $device->last_online_time,
                    'signal_intensity' => $device->signal_intensity,
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('获取设备在线状态失败', [
                'id' => $id,
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'code' => 500,
                'message' => '获取在线状态失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 控制设备
     *
     * @param int $id
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function control($id, Request $request)
    {
        try {
            // 这里可以根据需要实现设备控制功能
            return response()->json([
                'code' => 501,
                'message' => '设备控制功能暂未实现'
            ]);
        } catch (\Exception $e) {
            Log::error('控制设备失败', [
                'id' => $id,
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'code' => 500,
                'message' => '控制设备失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 获取设备报警信息
     *
     * @param int $id
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getAlerts($id, Request $request)
    {
        try {
            // 这里可以根据需要实现报警信息功能
            return response()->json([
                'code' => 501,
                'message' => '报警信息功能暂未实现'
            ]);
        } catch (\Exception $e) {
            Log::error('获取设备报警信息失败', [
                'id' => $id,
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'code' => 500,
                'message' => '获取报警信息失败: ' . $e->getMessage()
            ]);
        }
    }
} 