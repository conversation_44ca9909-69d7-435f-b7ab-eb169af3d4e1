<?php

namespace App\Http\Controllers\Admin\Web;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Admin;
use App\Models\Role;
use App\Models\Permission;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;

class AdminUserController extends Controller
{
    /**
     * 获取管理员列表
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        try {
            $query = Admin::query();

            // 搜索条件
            if ($request->has('keyword') && $request->input('keyword') !== '') {
                $keyword = $request->input('keyword');
                $query->where(function($q) use ($keyword) {
                    $q->where('username', 'like', "%{$keyword}%")
                      ->orWhere('name', 'like', "%{$keyword}%")
                      ->orWhere('email', 'like', "%{$keyword}%")
                      ->orWhere('phone', 'like', "%{$keyword}%");
                });
            }

            if ($request->has('status') && $request->input('status') !== '') {
                $query->where('status', $request->input('status'));
            }

            if ($request->has('role') && $request->input('role') !== '') {
                $role = $request->input('role');

                // 兼容旧版：按role字段筛选
                $query->where('role', $role);

                // 新版：按角色关联筛选
                if ($request->has('use_roles_table') && $request->boolean('use_roles_table')) {
                    $query = $query->whereHas('roles', function($q) use ($role) {
                        $q->where('name', $role);
                    });
                }
            }

            // 按角色ID筛选
            if ($request->has('role_id') && $request->input('role_id') !== '') {
                $roleId = $request->input('role_id');
                $query->whereHas('roles', function($q) use ($roleId) {
                    $q->where('id', $roleId);
                });
            }

            // 分页
            $perPage = $request->input('per_page', 15);
            $adminUsers = $query->with('roles')->orderBy('id', 'desc')->paginate($perPage);

            // 简化返回结构
            return response()->json([
                'code' => 0,
                'message' => '获取成功',
                'data' => [
                    'data' => $adminUsers->items(),
                    'total' => $adminUsers->total(),
                    'per_page' => $adminUsers->perPage(),
                    'current_page' => $adminUsers->currentPage(),
                    'last_page' => $adminUsers->lastPage()
                ]
            ]);
        } catch (\Exception $e) {
            // 捕获并记录任何异常
            \Log::error('获取管理员列表失败: ' . $e->getMessage(), [
                'exception' => $e,
                'request' => $request->all()
            ]);

            return response()->json([
                'code' => 1,
                'message' => '获取管理员列表失败: ' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    /**
     * 创建管理员
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'username' => 'required|string|max:255|unique:admin_users',
            'password' => 'required|string|min:6',
            'name' => 'required|string|max:255',
            'email' => 'nullable|email|unique:admin_users',
            'phone' => 'nullable|string|max:20',
            'role' => 'required|string|in:super_admin,admin', // 兼容旧版
            'roles' => 'nullable|array',
            'roles.*' => 'exists:admin_roles,id',
            'status' => 'required|string|in:active,disabled',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'code' => 1,
                'message' => '验证失败',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            // 开始事务
            DB::beginTransaction();

            // 创建管理员
            $admin = Admin::create([
                'username' => $request->input('username'),
                'password' => Hash::make($request->input('password')),
                'name' => $request->input('name'),
                'email' => $request->input('email'),
                'phone' => $request->input('phone'),
                'role' => $request->input('role'), // 兼容旧版
                'status' => $request->input('status'),
            ]);

            // 分配角色
            if ($request->has('roles')) {
                $admin->roles()->sync($request->input('roles'));
            } else {
                // 如果没有指定角色，根据role字段分配默认角色
                $roleName = $request->input('role') === 'super_admin' ? 'super_admin' : 'admin';
                $role = Role::where('name', $roleName)->first();
                if ($role) {
                    $admin->roles()->sync([$role->id]);
                }
            }

            // 提交事务
            DB::commit();

            // 加载角色关联
            $admin->load('roles');

            return response()->json([
                'code' => 0,
                'message' => '管理员创建成功',
                'data' => $admin
            ]);
        } catch (\Exception $e) {
            // 回滚事务
            DB::rollBack();

            \Log::error('创建管理员失败: ' . $e->getMessage(), [
                'exception' => $e,
                'request' => $request->all()
            ]);

            return response()->json([
                'code' => 1,
                'message' => '创建管理员失败: ' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    /**
     * 获取单个管理员信息
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        try {
            $admin = Admin::with(['roles', 'roles.permissions'])->findOrFail($id);

            // 获取所有可用角色
            $allRoles = Role::all();

            return response()->json([
                'code' => 0,
                'message' => '获取成功',
                'data' => [
                    'admin' => $admin,
                    'all_roles' => $allRoles
                ]
            ]);
        } catch (\Exception $e) {
            \Log::error('获取管理员详情失败: ' . $e->getMessage(), [
                'exception' => $e,
                'id' => $id
            ]);

            return response()->json([
                'code' => 1,
                'message' => '获取管理员详情失败: ' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    /**
     * 更新管理员信息
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id)
    {
        try {
            $admin = Admin::findOrFail($id);

            $rules = [
                'name' => 'required|string|max:255',
                'email' => 'nullable|email|unique:admin_users,email,'.$id,
                'phone' => 'nullable|string|max:20',
                'role' => 'required|string|in:super_admin,admin', // 兼容旧版
                'roles' => 'nullable|array',
                'roles.*' => 'exists:admin_roles,id',
                'status' => 'required|string|in:active,disabled',
            ];

            if ($request->filled('username')) {
                $rules['username'] = 'required|string|max:255|unique:admin_users,username,'.$id;
            }

            if ($request->filled('password')) {
                $rules['password'] = 'required|string|min:6';
            }

            $validator = Validator::make($request->all(), $rules);

            if ($validator->fails()) {
                return response()->json([
                    'code' => 1,
                    'message' => '验证失败',
                    'errors' => $validator->errors()
                ], 422);
            }

            // 开始事务
            DB::beginTransaction();

            $updateData = [
                'name' => $request->input('name'),
                'email' => $request->input('email'),
                'phone' => $request->input('phone'),
                'role' => $request->input('role'), // 兼容旧版
                'status' => $request->input('status'),
            ];

            if ($request->filled('username')) {
                $updateData['username'] = $request->input('username');
            }

            if ($request->filled('password')) {
                $updateData['password'] = Hash::make($request->input('password'));
            }

            $admin->update($updateData);

            // 更新角色
            if ($request->has('roles')) {
                $admin->roles()->sync($request->input('roles'));
            } else {
                // 如果没有指定角色，根据role字段分配默认角色
                $roleName = $request->input('role') === 'super_admin' ? 'super_admin' : 'admin';
                $role = Role::where('name', $roleName)->first();
                if ($role) {
                    $admin->roles()->sync([$role->id]);
                }
            }

            // 提交事务
            DB::commit();

            // 加载角色关联
            $admin->load('roles');

            return response()->json([
                'code' => 0,
                'message' => '管理员信息更新成功',
                'data' => $admin
            ]);
        } catch (\Exception $e) {
            // 回滚事务
            DB::rollBack();

            \Log::error('更新管理员信息失败: ' . $e->getMessage(), [
                'exception' => $e,
                'id' => $id,
                'request' => $request->all()
            ]);

            return response()->json([
                'code' => 1,
                'message' => '更新管理员信息失败: ' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    /**
     * 删除管理员
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy($id)
    {
        try {
            $admin = Admin::findOrFail($id);

            // 防止删除自己
            if ($admin->id === auth()->user()->id) {
                return response()->json([
                    'code' => 1,
                    'message' => '不能删除当前登录的管理员'
                ], 400);
            }

            // 开始事务
            DB::beginTransaction();

            // 删除角色关联
            $admin->roles()->detach();

            // 删除管理员
            $admin->delete();

            // 提交事务
            DB::commit();

            return response()->json([
                'code' => 0,
                'message' => '管理员删除成功'
            ]);
        } catch (\Exception $e) {
            // 回滚事务
            DB::rollBack();

            \Log::error('删除管理员失败: ' . $e->getMessage(), [
                'exception' => $e,
                'id' => $id
            ]);

            return response()->json([
                'code' => 1,
                'message' => '删除管理员失败: ' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }
}
