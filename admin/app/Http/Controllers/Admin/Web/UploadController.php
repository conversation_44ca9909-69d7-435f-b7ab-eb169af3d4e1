<?php

namespace App\Http\Controllers\Admin\Web;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class UploadController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }

    /**
     * 上传图片文件
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function uploadImage(Request $request)
    {
        $request->validate([
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:5120', // 最大5MB
            'thumbnail_file' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:5120', // 最大5MB
        ]);

        try {
            // 处理商品图片上传
            if ($request->hasFile('thumbnail_file')) {
                $file = $request->file('thumbnail_file');
                $filename = 'products/' . time() . '_' . Str::random(10) . '.' . $file->getClientOriginalExtension();
                
                // 存储文件
                $path = $file->storeAs('public', $filename);
                
                // 构建URL - 确保URL格式一致性
                // 使用完整URL路径
                $appUrl = config('app.url');
                $url = $appUrl . '/storage/' . $filename;
                
                return response()->json([
                    'code' => 0,
                    'message' => '图片上传成功',
                    'data' => [
                        'url' => $url,
                        'path' => $filename,
                    ]
                ]);
            }

            // 处理普通图片上传
            if ($request->hasFile('image')) {
                $file = $request->file('image');
                $filename = 'products/' . time() . '_' . Str::random(10) . '.' . $file->getClientOriginalExtension();
                
                // 存储文件
                $path = $file->storeAs('public', $filename);
                
                // 构建URL - 确保URL格式一致性
                $appUrl = config('app.url');
                $url = $appUrl . '/storage/' . $filename;
                
                return response()->json([
                    'code' => 0,
                    'message' => '图片上传成功',
                    'data' => [
                        'url' => $url,
                        'path' => $filename,
                    ]
                ]);
            }
            
            // 处理base64图片
            if ($request->has('base64')) {
                $image = $request->input('base64');
                $image = str_replace('data:image/png;base64,', '', $image);
                $image = str_replace('data:image/jpeg;base64,', '', $image);
                $image = str_replace('data:image/jpg;base64,', '', $image);
                $image = str_replace(' ', '+', $image);
                
                $filename = 'products/' . time() . '_' . Str::random(10) . '.png';
                Storage::disk('public')->put($filename, base64_decode($image));
                
                // 构建URL - 确保URL格式一致性
                $appUrl = config('app.url');
                $url = $appUrl . '/storage/' . $filename;
                
                return response()->json([
                    'code' => 0,
                    'message' => '图片上传成功',
                    'data' => [
                        'url' => $url,
                        'path' => $filename,
                    ]
                ]);
            }
            
            return response()->json([
                'code' => 1,
                'message' => '没有找到有效的图片文件'
            ], 400);
        } catch (\Exception $e) {
            return response()->json([
                'code' => 1,
                'message' => '图片上传失败: ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * 上传普通文件
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function uploadFile(Request $request)
    {
        $request->validate([
            'file' => 'required|file|max:10240', // 最大10MB
        ]);

        try {
            if ($request->hasFile('file')) {
                $file = $request->file('file');
                $filename = 'files/' . time() . '_' . Str::random(10) . '.' . $file->getClientOriginalExtension();
                
                // 存储文件
                $path = $file->storeAs('public', $filename);
                $url = Storage::url($path);
                
                return response()->json([
                    'code' => 0,
                    'message' => '文件上传成功',
                    'data' => [
                        'url' => $url,
                        'path' => $filename,
                        'name' => $file->getClientOriginalName(),
                        'extension' => $file->getClientOriginalExtension(),
                        'size' => $file->getSize(),
                    ]
                ]);
            }
            
            return response()->json([
                'code' => 1,
                'message' => '没有找到有效的文件'
            ], 400);
        } catch (\Exception $e) {
            return response()->json([
                'code' => 1,
                'message' => '文件上传失败: ' . $e->getMessage()
            ], 500);
        }
    }
}
