<?php

namespace App\Http\Controllers\Admin\Web;

use App\Http\Controllers\Controller;
use App\Models\Banner;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class BannerController extends Controller
{
    /**
     * 获取轮播图列表
     */
    public function index(Request $request)
    {
        $query = Banner::query();
        
        // 筛选条件
        if ($request->has('title')) {
            $query->where('title', 'like', '%' . $request->title . '%');
        }
        
        if ($request->has('status') && $request->status !== '') {
            $query->where('status', $request->status);
        }
        
        if ($request->has('position') && $request->position !== '') {
            $query->where('position', $request->position);
        }
        
        // 排序
        $query->orderBy('sort', 'asc');
        
        // 分页
        $banners = $query->paginate($request->input('limit', 10));
        
        return response()->json([
            'code' => 0,
            'message' => 'success',
            'data' => $banners->items(),
            'total' => $banners->total()
        ]);
    }

    /**
     * 存储新轮播图
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'image_url' => 'required|string',
            'link_url' => 'nullable|string',
            'link_type' => 'nullable|string',
            'position' => 'nullable|string',
            'sort' => 'nullable|integer',
            'status' => 'required|string',
            'start_time' => 'nullable|date',
            'end_time' => 'nullable|date',
        ]);
        
        // 处理图片上传
        if ($request->hasFile('image')) {
            $file = $request->file('image');
            $path = $file->store('banners', 'public');
            $validated['image_url'] = '/storage/' . $path;
        }
        
        // 设置默认值
        if (!isset($validated['sort'])) {
            $validated['sort'] = 0;
        }
        
        // 创建轮播图
        $banner = Banner::create($validated);
        
        return response()->json([
            'code' => 0,
            'message' => '轮播图创建成功',
            'data' => $banner
        ]);
    }

    /**
     * 获取指定轮播图
     */
    public function show($id)
    {
        $banner = Banner::findOrFail($id);
        
        return response()->json([
            'code' => 0,
            'message' => 'success',
            'data' => $banner
        ]);
    }

    /**
     * 更新轮播图
     */
    public function update(Request $request, $id)
    {
        $banner = Banner::findOrFail($id);
        
        $rules = [
            'title' => 'required|string|max:255',
            'link_url' => 'nullable|string',
            'link_type' => 'nullable|string',
            'position' => 'nullable|string',
            'sort' => 'nullable|integer',
            'status' => 'required|string',
            'start_time' => 'nullable|date',
            'end_time' => 'nullable|date',
        ];
        
        // 只有当没有上传新图片且没有传入image_url时才需要验证image_url
        if (!$request->hasFile('image') && !$request->has('image_url')) {
            $rules['image_url'] = 'required|string';
        }
        
        $validated = $request->validate($rules);
        
        // 处理图片上传
        if ($request->hasFile('image')) {
            $file = $request->file('image');
            $path = $file->store('banners', 'public');
            $validated['image_url'] = '/storage/' . $path;
            
            // 删除旧图片
            if ($banner->image_url && !Str::contains($banner->image_url, 'default')) {
                $oldPath = str_replace('/storage/', '', $banner->image_url);
                Storage::disk('public')->delete($oldPath);
            }
        } else if (!isset($validated['image_url']) && $banner->image_url) {
            // 如果没有上传新图片且没有传入image_url，则保留原来的image_url
            $validated['image_url'] = $banner->image_url;
        }
        
        // 更新轮播图
        $banner->update($validated);
        
        return response()->json([
            'code' => 0,
            'message' => '轮播图更新成功',
            'data' => $banner
        ]);
    }

    /**
     * 删除轮播图
     */
    public function destroy($id)
    {
        $banner = Banner::findOrFail($id);
        
        // 删除图片
        if ($banner->image_url && !Str::contains($banner->image_url, 'default')) {
            $path = str_replace('/storage/', '', $banner->image_url);
            Storage::disk('public')->delete($path);
        }
        
        $banner->delete();
        
        return response()->json([
            'code' => 0,
            'message' => '轮播图删除成功'
        ]);
    }
    
    /**
     * 更新轮播图状态
     */
    public function updateStatus(Request $request, $id)
    {
        $banner = Banner::findOrFail($id);
        
        $validated = $request->validate([
            'status' => 'required|string',
        ]);
        
        $banner->update($validated);
        
        return response()->json([
            'code' => 0,
            'message' => '状态更新成功',
            'data' => $banner
        ]);
    }
}
