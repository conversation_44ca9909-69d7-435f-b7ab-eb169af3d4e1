<?php

namespace App\Http\Controllers\Admin\Web;

use App\Http\Controllers\Controller;
use App\Models\Permission;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;

class PermissionController extends Controller
{
    /**
     * 获取权限列表
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        try {
            $query = Permission::query();
            
            // 搜索条件
            if ($request->has('keyword') && $request->input('keyword') !== '') {
                $keyword = $request->input('keyword');
                $query->where(function($q) use ($keyword) {
                    $q->where('name', 'like', "%{$keyword}%")
                      ->orWhere('display_name', 'like', "%{$keyword}%")
                      ->orWhere('description', 'like', "%{$keyword}%")
                      ->orWhere('module', 'like', "%{$keyword}%");
                });
            }
            
            // 按模块筛选
            if ($request->has('module') && $request->input('module') !== '') {
                $query->where('module', $request->input('module'));
            }
            
            // 分页
            $perPage = $request->input('per_page', 15);
            $permissions = $query->orderBy('module', 'asc')->orderBy('id', 'asc')->paginate($perPage);
            
            return response()->json([
                'code' => 0,
                'message' => '获取成功',
                'data' => [
                    'data' => $permissions->items(),
                    'total' => $permissions->total(),
                    'per_page' => $permissions->perPage(),
                    'current_page' => $permissions->currentPage(),
                    'last_page' => $permissions->lastPage()
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('获取权限列表失败: ' . $e->getMessage(), [
                'exception' => $e,
                'request' => $request->all()
            ]);
            
            return response()->json([
                'code' => 1,
                'message' => '获取权限列表失败: ' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }
    
    /**
     * 创建权限
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:50|unique:admin_permissions,name',
            'display_name' => 'required|string|max:50',
            'description' => 'nullable|string|max:255',
            'module' => 'required|string|max:50'
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                'code' => 1,
                'message' => '验证失败',
                'errors' => $validator->errors()
            ], 422);
        }
        
        try {
            $permission = Permission::create([
                'name' => $request->input('name'),
                'display_name' => $request->input('display_name'),
                'description' => $request->input('description'),
                'module' => $request->input('module')
            ]);
            
            return response()->json([
                'code' => 0,
                'message' => '权限创建成功',
                'data' => $permission
            ]);
        } catch (\Exception $e) {
            Log::error('创建权限失败: ' . $e->getMessage(), [
                'exception' => $e,
                'request' => $request->all()
            ]);
            
            return response()->json([
                'code' => 1,
                'message' => '创建权限失败: ' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }
    
    /**
     * 获取单个权限信息
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        try {
            $permission = Permission::with('roles')->findOrFail($id);
            
            return response()->json([
                'code' => 0,
                'message' => '获取成功',
                'data' => $permission
            ]);
        } catch (\Exception $e) {
            Log::error('获取权限详情失败: ' . $e->getMessage(), [
                'exception' => $e,
                'id' => $id
            ]);
            
            return response()->json([
                'code' => 1,
                'message' => '获取权限详情失败: ' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }
    
    /**
     * 更新权限信息
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:50|unique:admin_permissions,name,'.$id,
            'display_name' => 'required|string|max:50',
            'description' => 'nullable|string|max:255',
            'module' => 'required|string|max:50'
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                'code' => 1,
                'message' => '验证失败',
                'errors' => $validator->errors()
            ], 422);
        }
        
        try {
            $permission = Permission::findOrFail($id);
            $permission->update([
                'name' => $request->input('name'),
                'display_name' => $request->input('display_name'),
                'description' => $request->input('description'),
                'module' => $request->input('module')
            ]);
            
            return response()->json([
                'code' => 0,
                'message' => '权限更新成功',
                'data' => $permission
            ]);
        } catch (\Exception $e) {
            Log::error('更新权限失败: ' . $e->getMessage(), [
                'exception' => $e,
                'id' => $id,
                'request' => $request->all()
            ]);
            
            return response()->json([
                'code' => 1,
                'message' => '更新权限失败: ' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }
    
    /**
     * 删除权限
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy($id)
    {
        try {
            $permission = Permission::findOrFail($id);
            
            // 检查是否有角色使用此权限
            if ($permission->roles()->count() > 0) {
                return response()->json([
                    'code' => 1,
                    'message' => '该权限已被角色使用，不允许删除',
                    'data' => null
                ], 403);
            }
            
            // 删除权限
            $permission->delete();
            
            return response()->json([
                'code' => 0,
                'message' => '权限删除成功',
                'data' => null
            ]);
        } catch (\Exception $e) {
            Log::error('删除权限失败: ' . $e->getMessage(), [
                'exception' => $e,
                'id' => $id
            ]);
            
            return response()->json([
                'code' => 1,
                'message' => '删除权限失败: ' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }
    
    /**
     * 获取所有模块列表
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getModules()
    {
        try {
            $modules = Permission::select('module')->distinct()->orderBy('module')->get()->pluck('module');
            
            return response()->json([
                'code' => 0,
                'message' => '获取成功',
                'data' => $modules
            ]);
        } catch (\Exception $e) {
            Log::error('获取模块列表失败: ' . $e->getMessage(), [
                'exception' => $e
            ]);
            
            return response()->json([
                'code' => 1,
                'message' => '获取模块列表失败: ' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }
}
