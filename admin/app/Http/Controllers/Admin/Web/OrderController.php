<?php

namespace App\Http\Controllers\Admin\Web;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Logistics;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class OrderController extends Controller
{
    /**
     * 显示订单列表
     */
    public function index(Request $request)
    {
        $query = Order::query();
        
        // 搜索条件
        if ($request->has('keyword') && $request->keyword) {
            $query->where(function($q) use ($request) {
                $q->where('order_no', 'like', '%' . $request->keyword . '%')
                  ->orWhere('receiver_name', 'like', '%' . $request->keyword . '%')
                  ->orWhere('receiver_phone', 'like', '%' . $request->keyword . '%');
            });
        }
        
        if ($request->has('status') && $request->status !== null) {
            $query->where('status', $request->status);
        }
        
        if ($request->has('pay_status') && $request->pay_status !== null) {
            $query->where('pay_status', $request->pay_status);
        }
        
        if ($request->has('ship_status') && $request->ship_status !== null) {
            $query->where('ship_status', $request->ship_status);
        }
        
        if ($request->has('start_date') && $request->start_date) {
            $query->where('created_at', '>=', $request->start_date . ' 00:00:00');
        }
        
        if ($request->has('end_date') && $request->end_date) {
            $query->where('created_at', '<=', $request->end_date . ' 23:59:59');
        }
        
        // 默认按照创建时间倒序排序
        $query->orderBy($request->input('sort', 'created_at'), $request->input('order', 'desc'));
        
        // 分页
        $perPage = $request->input('limit', 15);
        $orders = $query->paginate($perPage);
        
        // 处理订单数据，添加状态文本等
        $data = $orders->map(function ($order) {
            $order->status_text = $order->getStatusTextAttribute();
            $order->refund_status_text = $order->getRefundStatusTextAttribute();
            $order->payment_status_text = $order->getPaymentStatusTextAttribute();
            $order->shipping_status_text = $order->getShippingStatusTextAttribute();
            return $order;
        });
        
        return response()->json([
            'code' => 0,
            'message' => 'success',
            'data' => $data,
            'total' => $orders->total()
        ]);
    }

    /**
     * 显示订单详情
     */
    public function show($id)
    {
        $order = Order::with('items')->find($id);
        
        if (!$order) {
            return response()->json([
                'code' => 1,
                'message' => '订单不存在'
            ], 404);
        }
        
        // 加载用户信息
        $order->load('user');
        
        // 添加状态文本
        $order->status_text = $order->getStatusTextAttribute();
        $order->refund_status_text = $order->getRefundStatusTextAttribute();
        $order->payment_status_text = $order->getPaymentStatusTextAttribute();
        $order->shipping_status_text = $order->getShippingStatusTextAttribute();
        
        return response()->json([
            'code' => 0,
            'message' => 'success',
            'data' => $order
        ]);
    }

    /**
     * 发货操作
     */
    public function ship(Request $request, $id)
    {
        $order = Order::find($id);
        
        if (!$order) {
            return response()->json([
                'code' => 1,
                'message' => '订单不存在'
            ], 404);
        }
        
        if ($order->status != 2 || $order->pay_status != 2 || $order->ship_status != 1) {
            return response()->json([
                'code' => 1,
                'message' => '订单状态不正确，无法发货'
            ], 422);
        }
        
        // 验证请求数据
        $validator = Validator::make($request->all(), [
            'ship_area_name' => 'required|string|max:100',
            'ship_area_id' => 'required|string|max:50'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'code' => 1,
                'message' => $validator->errors()->first()
            ], 422);
        }

        try {
            // 开启事务
            DB::beginTransaction();
            
            // 更新订单为已发货状态
            $order->status = 3; // 已发货
            $order->ship_status = 2; // 已发货
            $order->ship_area_name = $request->ship_area_name;
            $order->ship_area_id = $request->ship_area_id;
            $order->update_time = now();
            $order->save();
            
            // 更新订单项状态
            OrderItem::where('order_id', $order->order_id)
                ->where('status', 2)
                ->update([
                    'status' => 3,
                    'ship_area_name' => $request->ship_area_name,
                    'ship_area_id' => $request->ship_area_id,
                    'update_time' => now()
                ]);
            
            // 记录物流信息
            $logistics = Logistics::where('number', $request->ship_area_id)->first();
            if (!$logistics) {
                $logistics = new Logistics();
                $logistics->number = $request->ship_area_id;
                $logistics->expName = $request->ship_area_name;
                $logistics->deliverystatus = 0; // 快递收件(揽件)
                $logistics->save();
            }
            
            // 提交事务
            DB::commit();
            
            return response()->json([
                'code' => 0,
                'message' => '订单发货成功',
                'data' => $order
            ]);
        } catch (\Exception $e) {
            // 回滚事务
            DB::rollBack();
            Log::error('订单发货失败: ' . $e->getMessage());
            
            return response()->json([
                'code' => 1,
                'message' => '订单发货失败: ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * 修改订单收货信息
     */
    public function updateAddress(Request $request, $id)
    {
        $order = Order::find($id);
        
        if (!$order) {
            return response()->json([
                'code' => 1,
                'message' => '订单不存在'
            ], 404);
        }
        
        // 已发货的订单不允许修改收货信息
        if ($order->ship_status != 1) {
            return response()->json([
                'code' => 1,
                'message' => '订单已发货，无法修改收货信息'
            ], 422);
    }

        // 验证请求数据
        $validator = Validator::make($request->all(), [
            'ship_name' => 'required|string|max:50',
            'ship_mobile' => 'required|string|max:15',
            'city' => 'required|string|max:100',
            'ship_address' => 'required|string|max:200'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'code' => 1,
                'message' => $validator->errors()->first()
            ], 422);
        }

        try {
            // 更新订单收货信息
            $order->ship_name = $request->ship_name;
            $order->ship_mobile = $request->ship_mobile;
            $order->city = $request->city;
            $order->ship_address = $request->ship_address;
            $order->update_time = now();
            $order->save();
            
            return response()->json([
                'code' => 0,
                'message' => '收货信息更新成功',
                'data' => $order
            ]);
        } catch (\Exception $e) {
            Log::error('收货信息更新失败: ' . $e->getMessage());
            
            return response()->json([
                'code' => 1,
                'message' => '收货信息更新失败: ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * 取消订单
     */
    public function cancel($id)
    {
        $order = Order::find($id);
        
        if (!$order) {
            return response()->json([
                'code' => 1,
                'message' => '订单不存在'
            ], 404);
        }
        
        // 只有未支付或待发货的订单允许取消
        if ($order->status > 2) {
            return response()->json([
                'code' => 1,
                'message' => '订单状态不允许取消'
            ], 422);
        }

        try {
            // 开启事务
            DB::beginTransaction();
            
            // 更新订单状态为已取消
            $order->status = 7; // 已取消
            $order->update_time = now();
            $order->save();
            
            // 更新订单项状态
            OrderItem::where('order_id', $order->order_id)->update([
                'status' => 7,
                'update_time' => now()
            ]);
            
            // 如果订单已支付，需要处理退款逻辑
            if ($order->pay_status == 2) {
                // 这里需要调用退款API，具体实现根据支付方式确定
                // ...
                
                // 更新支付状态为已退款
                $order->pay_status = 3; // 已退款
                $order->save();
            }
            
            // 恢复商品库存
            $orderItems = OrderItem::where('order_id', $order->order_id)->get();
            foreach ($orderItems as $item) {
                // 恢复商品库存
                if ($item->goods_id) {
                    DB::statement("UPDATE ddg_goods SET stock = stock + ?, freeze_stock = freeze_stock - ? WHERE id = ?", [
                        $item->nums,
                        $item->nums,
                        $item->goods_id
                    ]);
                }
            }
            
            // 提交事务
            DB::commit();
            
            return response()->json([
                'code' => 0,
                'message' => '订单取消成功',
                'data' => $order
            ]);
        } catch (\Exception $e) {
            // 回滚事务
            DB::rollBack();
            Log::error('订单取消失败: ' . $e->getMessage());
            
            return response()->json([
                'code' => 1,
                'message' => '订单取消失败: ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * 确认订单
     */
    public function confirm($id)
    {
        $order = Order::find($id);
        
        if (!$order) {
            return response()->json([
                'code' => 1,
                'message' => '订单不存在'
            ], 404);
        }
        
        // 只有已发货的订单允许确认收货
        if ($order->status != 3 || $order->ship_status != 2) {
            return response()->json([
                'code' => 1,
                'message' => '订单状态不允许确认收货'
            ], 422);
        }

        try {
            // 开启事务
            DB::beginTransaction();
            
            // 更新订单状态为已完成
            $order->status = 4; // 已完成
            $order->confirm = 2; // 已确认收货
            $order->confirm_time = now();
            $order->update_time = now();
            $order->save();
            
            // 更新订单项状态
            OrderItem::where('order_id', $order->order_id)
                ->where('status', 3)
                ->update([
                    'status' => 4,
                    'update_time' => now()
                ]);
            
            // 提交事务
            DB::commit();
            
            return response()->json([
                'code' => 0,
                'message' => '订单确认收货成功',
                'data' => $order
            ]);
        } catch (\Exception $e) {
            // 回滚事务
            DB::rollBack();
            Log::error('订单确认收货失败: ' . $e->getMessage());
            
            return response()->json([
                'code' => 1,
                'message' => '订单确认收货失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 批量发货
     */
    public function batchShip(Request $request)
    {
        // 验证请求数据
        $validator = Validator::make($request->all(), [
            'orders' => 'required|array',
            'orders.*.id' => 'required|integer',
            'orders.*.ship_area_name' => 'required|string|max:100',
            'orders.*.ship_area_id' => 'required|string|max:50'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'code' => 1,
                'message' => $validator->errors()->first()
            ], 422);
        }
        
        $successCount = 0;
        $failCount = 0;
        $failOrders = [];
        
        foreach ($request->orders as $orderData) {
            $order = Order::find($orderData['id']);
            
            if (!$order || $order->status != 2 || $order->pay_status != 2 || $order->ship_status != 1) {
                $failCount++;
                $failOrders[] = [
                    'id' => $orderData['id'],
                    'reason' => '订单状态不正确，无法发货'
                ];
                continue;
            }
            
            try {
                // 开启事务
                DB::beginTransaction();
                
                // 更新订单为已发货状态
                $order->status = 3; // 已发货
                $order->ship_status = 2; // 已发货
                $order->ship_area_name = $orderData['ship_area_name'];
                $order->ship_area_id = $orderData['ship_area_id'];
                $order->update_time = now();
                $order->save();
                
                // 更新订单项状态
                OrderItem::where('order_id', $order->order_id)
                    ->where('status', 2)
                    ->update([
                        'status' => 3,
                        'ship_area_name' => $orderData['ship_area_name'],
                        'ship_area_id' => $orderData['ship_area_id'],
                        'update_time' => now()
                    ]);
                
                // 记录物流信息
                $logistics = Logistics::where('number', $orderData['ship_area_id'])->first();
                if (!$logistics) {
                    $logistics = new Logistics();
                    $logistics->number = $orderData['ship_area_id'];
                    $logistics->expName = $orderData['ship_area_name'];
                    $logistics->deliverystatus = 0; // 快递收件(揽件)
                    $logistics->save();
                }
                
                // 提交事务
                DB::commit();
                
                $successCount++;
            } catch (\Exception $e) {
                // 回滚事务
                DB::rollBack();
                $failCount++;
                $failOrders[] = [
                    'id' => $orderData['id'],
                    'reason' => $e->getMessage()
                ];
                
                Log::error('订单发货失败: ' . $e->getMessage());
            }
        }
        
        return response()->json([
            'code' => 0,
            'message' => "批量发货完成，成功: {$successCount}，失败: {$failCount}",
            'data' => [
                'success_count' => $successCount,
                'fail_count' => $failCount,
                'fail_orders' => $failOrders
            ]
        ]);
    }
    
    /**
     * 导出订单
     */
    public function export(Request $request)
    {
        // 获取要导出的订单IDs
        $orderIds = $request->input('order_ids', []);
        
        $query = Order::query();
        
        // 如果指定了订单ID，则只导出这些订单
        if (!empty($orderIds)) {
            $query->whereIn('id', $orderIds);
        } else {
            // 否则按照筛选条件导出
            if ($request->has('keyword') && $request->keyword) {
                $query->where(function($q) use ($request) {
                    $q->where('order_no', 'like', '%' . $request->keyword . '%')
                      ->orWhere('receiver_name', 'like', '%' . $request->keyword . '%')
                      ->orWhere('receiver_phone', 'like', '%' . $request->keyword . '%');
                });
            }
            
            if ($request->has('status') && $request->status !== null) {
                $query->where('status', $request->status);
            }
            
            if ($request->has('start_date') && $request->start_date) {
                $query->where('created_at', '>=', $request->start_date . ' 00:00:00');
            }
            
            if ($request->has('end_date') && $request->end_date) {
                $query->where('created_at', '<=', $request->end_date . ' 23:59:59');
            }
    }

        // 加载订单项
        $orders = $query->with('items')->get();
        
        // 组装导出数据
        $exportData = [];
        foreach ($orders as $order) {
            foreach ($order->items as $item) {
                $exportData[] = [
                    'order_id' => $order->order_id,
                    'create_time' => $order->created_at,
                    'payment_time' => $order->payment_time,
                    'status' => $order->getStatusTextAttribute(),
                    'goods_name' => $item->name,
                    'spec_text' => $item->spec_text,
                    'price' => $item->price,
                    'nums' => $item->nums,
                    'amount' => $item->amount,
                    'ship_name' => $order->ship_name,
                    'ship_mobile' => $order->ship_mobile,
                    'ship_address' => $order->city . ' ' . $order->ship_address,
                    'ship_area_name' => $order->ship_area_name,
                    'ship_area_id' => $order->ship_area_id,
                    'memo' => $order->memo
                ];
            }
        }
        
        return response()->json([
            'code' => 0,
            'message' => 'success',
            'data' => $exportData
        ]);
    }
    
    /**
     * 获取订单统计数据
     */
    public function statistics()
    {
        // 获取订单各状态数量
        $statistics = [
            'wait_pay' => Order::where('status', 1)->count(), // 待付款
            'wait_ship' => Order::where('status', 2)->count(), // 待发货
            'shipped' => Order::where('status', 3)->count(), // 已发货
            'completed' => Order::where('status', 4)->count(), // 已完成
            'refunding' => Order::where('status', 5)->count(), // 申请退款
            'refunded' => Order::where('status', 6)->count(), // 退款完成
            'cancelled' => Order::where('status', 7)->count(), // 已取消
            
            // 今日订单数
            'today_orders' => Order::where('created_at', '>=', date('Y-m-d') . ' 00:00:00')
                ->where('created_at', '<=', date('Y-m-d') . ' 23:59:59')
                ->count(),
                
            // 今日销售额
            'today_sales' => Order::where('created_at', '>=', date('Y-m-d') . ' 00:00:00')
                ->where('created_at', '<=', date('Y-m-d') . ' 23:59:59')
                ->where('pay_status', 2)
                ->sum('order_amount'),
                
            // 本月订单数
            'month_orders' => Order::where('created_at', '>=', date('Y-m-01') . ' 00:00:00')
                ->where('created_at', '<=', date('Y-m-d') . ' 23:59:59')
                ->count(),
                
            // 本月销售额
            'month_sales' => Order::where('created_at', '>=', date('Y-m-01') . ' 00:00:00')
                ->where('created_at', '<=', date('Y-m-d') . ' 23:59:59')
                ->where('pay_status', 2)
                ->sum('order_amount')
        ];
        
        return response()->json([
            'code' => 0,
            'message' => 'success',
            'data' => $statistics
        ]);
    }
}
