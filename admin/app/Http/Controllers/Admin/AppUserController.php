<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

class AppUserController extends Controller
{
    /**
     * 获取APP用户列表
     */
    public function index(Request $request)
    {
        try {
            // 获取请求参数
            $page = $request->input('page', 1);
            $perPage = $request->input('per_page', 10);
            $search = $request->input('search', '');
            $status = $request->input('status', '');
            
            // 记录请求日志
            Log::info('获取APP用户列表', [
                'page' => $page,
                'perPage' => $perPage,
                'search' => $search,
                'status' => $status
            ]);
            
            // 构建查询
            $query = DB::table('app_users');
            
            // 添加搜索条件
            if (!empty($search)) {
                $query->where(function($q) use ($search) {
                    $q->where('name', 'like', "%{$search}%")
                      ->orWhere('phone', 'like', "%{$search}%")
                      ->orWhere('email', 'like', "%{$search}%");
                });
            }
            
            // 添加状态过滤
            if ($status !== '') {
                $query->where('status', $status);
            }
            
            // 获取总记录数
            $total = $query->count();
            
            // 分页查询
            $users = $query->orderBy('id', 'desc')
                           ->offset(($page - 1) * $perPage)
                           ->limit($perPage)
                           ->get();
            
            // 返回结果
            return response()->json([
                'code' => 0,
                'message' => '获取成功',
                'data' => [
                    'total' => $total,
                    'per_page' => $perPage,
                    'current_page' => $page,
                    'last_page' => ceil($total / $perPage),
                    'data' => $users
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('获取APP用户列表失败', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'code' => 500,
                'message' => '获取APP用户列表失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取单个APP用户信息
     */
    public function show($id)
    {
        try {
            // 记录请求日志
            Log::info('获取APP用户信息', ['id' => $id]);
            
            // 查询用户
            $user = DB::table('app_users')->where('id', $id)->first();
            
            if (!$user) {
                return response()->json([
                    'code' => 404,
                    'message' => '用户不存在'
                ], 404);
            }
            
            // 返回结果
            return response()->json([
                'code' => 0,
                'message' => '获取成功',
                'data' => $user
            ]);
        } catch (\Exception $e) {
            Log::error('获取APP用户信息失败', [
                'id' => $id,
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'code' => 500,
                'message' => '获取APP用户信息失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 更新APP用户信息
     */
    public function update(Request $request, $id)
    {
        try {
            // 获取请求参数
            $data = $request->all();
            
            // 记录请求日志
            Log::info('更新APP用户信息', [
                'id' => $id,
                'data' => $data
            ]);
            
            // 查询用户是否存在
            $user = DB::table('app_users')->where('id', $id)->first();
            
            if (!$user) {
                return response()->json([
                    'code' => 404,
                    'message' => '用户不存在'
                ], 404);
            }
            
            // 更新用户信息
            DB::table('app_users')->where('id', $id)->update($data);
            
            // 返回结果
            return response()->json([
                'code' => 0,
                'message' => '更新成功',
                'data' => DB::table('app_users')->where('id', $id)->first()
            ]);
        } catch (\Exception $e) {
            Log::error('更新APP用户信息失败', [
                'id' => $id,
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'code' => 500,
                'message' => '更新APP用户信息失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 删除APP用户
     */
    public function destroy($id)
    {
        try {
            // 记录请求日志
            Log::info('删除APP用户', ['id' => $id]);
            
            // 查询用户是否存在
            $user = DB::table('app_users')->where('id', $id)->first();
            
            if (!$user) {
                return response()->json([
                    'code' => 404,
                    'message' => '用户不存在'
                ], 404);
            }
            
            // 删除用户
            DB::table('app_users')->where('id', $id)->delete();
            
            // 返回结果
            return response()->json([
                'code' => 0,
                'message' => '删除成功'
            ]);
        } catch (\Exception $e) {
            Log::error('删除APP用户失败', [
                'id' => $id,
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'code' => 500,
                'message' => '删除APP用户失败: ' . $e->getMessage()
            ], 500);
        }
    }
}
