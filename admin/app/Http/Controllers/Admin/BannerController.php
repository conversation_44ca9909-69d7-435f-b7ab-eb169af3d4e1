<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

/**
 * Banner控制器 - 兼容性包装器
 */
class BannerController extends Controller
{
    /**
     * 获取Banner列表
     */
    public function index(Request $request): JsonResponse
    {
        return response()->json([
            'code' => 0,
            'message' => 'success',
            'data' => [],
            'meta' => [
                'total' => 0,
                'per_page' => 15,
                'current_page' => 1,
                'last_page' => 1
            ]
        ]);
    }

    public function store(Request $request): JsonResponse
    {
        return response()->json(['code' => 0, 'message' => 'success']);
    }

    public function show($id): JsonResponse
    {
        return response()->json(['code' => 0, 'message' => 'success']);
    }

    public function update(Request $request, $id): JsonResponse
    {
        return response()->json(['code' => 0, 'message' => 'success']);
    }

    public function destroy($id): JsonResponse
    {
        return response()->json(['code' => 0, 'message' => 'success']);
    }
} 