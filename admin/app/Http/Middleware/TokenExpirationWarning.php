<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;

class TokenExpirationWarning
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        $response = $next($request);

        // 只对API请求进行token过期检查
        if ($request->is('api/*') && Auth::guard('sanctum')->check()) {
            $user = Auth::guard('sanctum')->user();
            $token = $user->currentAccessToken();

            if ($token && $token->expires_at) {
                $expiresAt = Carbon::parse($token->expires_at);
                $now = Carbon::now();
                $daysUntilExpiry = $now->diffInDays($expiresAt, false);

                // 如果token在3天内过期，添加警告头
                if ($daysUntilExpiry <= 3 && $daysUntilExpiry > 0) {
                    $response->headers->set('X-Token-Expires-In-Days', $daysUntilExpiry);
                    $response->headers->set('X-Token-Expires-At', $expiresAt->toISOString());
                    $response->headers->set('X-Token-Warning', 'Token will expire soon, please refresh');
                }
            }
        }

        return $response;
    }
}