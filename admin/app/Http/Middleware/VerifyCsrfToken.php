<?php

namespace App\Http\Middleware;

use Illuminate\Foundation\Http\Middleware\VerifyCsrfToken as Middleware;

class VerifyCsrfToken extends Middleware
{
    /**
     * The URIs that should be excluded from CSRF verification.
     *
     * @var array<int, string>
     */
    protected $except = [
        // 微信登录回调相关路由 - 排除CSRF验证
        'auth/wechat/callback',
        'api/auth/wechat/callback',
        'api/wechat/login_callback',
        'api/wechat_login_callback',
        'wechat/login_callback',
        'wechat_login_callback',
        // 添加带斜杠的路径
        '/auth/wechat/callback',
        '/api/auth/wechat/callback',
        '/api/wechat/login_callback',
        '/api/wechat_login_callback',
        '/wechat/login_callback',
        '/wechat_login_callback',
        // 添加API前缀路径
        'api/auth/wechat/callback',
        // 添加所有可能的微信回调路径
        '*wechat*callback*',
        // 手机端V1 API路径 - 排除CSRF验证
        'api/mobile/api/v1/*',
        'api/mobile/v1/*',
        'mobile/api/v1/*',
        // 管理后台API路径 - 排除CSRF验证
        'api/admin/v1/*',
        '/api/admin/v1/*',
        // 盛付通API路径 - 排除CSRF验证
        'api/admin/v1/shengfutong/*',
        '/api/admin/v1/shengfutong/*'
    ];
}
