<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class ApiRedirectMiddleware
{
    /**
     * API路径映射表
     * 旧路径 => 新路径
     */
    protected $pathMappings = [
        // 微信登录相关
        '/admin/api/user/wechat_login_url.php' => '/api/wechat/login-url',
        '/user/wechat_login_url.php' => '/api/wechat/login-url',
        '/wechat_login_url.php' => '/api/wechat/login-url',
        
        '/admin/api/user/wechat_login_callback.php' => '/api/wechat/callback',
        '/user/wechat_login_callback.php' => '/api/wechat/callback',
        '/wechat_login_callback.php' => '/api/wechat/callback',
        
        // 用户认证相关
        '/admin/api/user/login.php' => '/api/auth/login',
        '/user/login.php' => '/api/auth/login',
        '/login.php' => '/api/auth/login',
        
        '/admin/api/user/login_by_sms.php' => '/api/auth/login/code',
        '/user/login_by_sms.php' => '/api/auth/login/code',
        '/login_by_sms.php' => '/api/auth/login/code',
        
        '/admin/api/user/bind_phone.php' => '/api/user/bind-phone',
        '/user/bind_phone.php' => '/api/user/bind-phone',
        '/bind_phone.php' => '/api/user/bind-phone',
        
        '/admin/api/user/check_user_roles.php' => '/api/user/check-roles',
        '/user/check_user_roles.php' => '/api/user/check-roles',
        '/check_user_roles.php' => '/api/user/check-roles',
        
        // 用户信息相关
        '/admin/api/user/info.php' => '/api/user/info',
        '/user/info.php' => '/api/user/info',
        '/info.php' => '/api/user/info',
        
        '/admin/api/user/update_profile.php' => '/api/user/update-info',
        '/user/update_profile.php' => '/api/user/update-info',
        '/update_profile.php' => '/api/user/update-info',
        
        '/admin/api/user/change_password.php' => '/api/user/update-password',
        '/user/change_password.php' => '/api/user/update-password',
        '/change_password.php' => '/api/user/update-password',
    ];

    /**
     * 处理请求
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        // 获取当前请求路径
        $path = $request->getPathInfo();
        
        // 检查是否需要重定向
        if (isset($this->pathMappings[$path])) {
            $newPath = $this->pathMappings[$path];
            
            // 记录重定向日志
            Log::info('API重定向', [
                'old_path' => $path,
                'new_path' => $newPath,
                'method' => $request->method(),
                'ip' => $request->ip()
            ]);
            
            // 创建新的请求
            $newRequest = $request->duplicate();
            
            // 修改请求路径
            $newRequest->server->set('REQUEST_URI', $newPath);
            
            // 修改Laravel内部路径
            $newRequest->server->set('PHP_SELF', $newPath);
            $newRequest->server->set('SCRIPT_NAME', $newPath);
            $newRequest->server->set('PATH_INFO', $newPath);
            
            // 返回新请求
            return $next($newRequest);
        }
        
        return $next($request);
    }
} 