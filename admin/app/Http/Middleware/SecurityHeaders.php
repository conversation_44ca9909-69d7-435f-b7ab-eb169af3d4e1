<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class SecurityHeaders
{
    public function handle(Request $request, Closure $next)
    {
        $response = $next($request);

        // 添加基本安全头
        $response->headers->set('X-Content-Type-Options', 'nosniff');
        $response->headers->set('X-XSS-Protection', '1; mode=block');
        $response->headers->set('Referrer-Policy', 'strict-origin-when-cross-origin');
        
        // 移除 X-Frame-Options 限制，因为它可能导致界面被遮挡
        $response->headers->remove('X-Frame-Options');
        
        // 移除 CSP 限制，因为它可能影响资源加载
        $response->headers->remove('Content-Security-Policy');

        return $response;
    }
} 