<?php

namespace App\Services;

use App\Models\AppUser;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Schema;

class TeamMemberService
{
    /**
     * 记录日志的前缀
     * 
     * @var string
     */
    protected $logPrefix = '[TeamMemberService]';

    /**
     * 更新用户的团队成员关系
     * 当用户VIP状态变化时，需要更新团队成员关系
     * 
     * @param AppUser $user 用户实例
     * @return array 处理结果
     */
    public function updateTeamMembers(AppUser $user)
    {
        Log::info("{$this->logPrefix} 开始更新用户团队成员关系", [
            'user_id' => $user->id,
            'user_name' => $user->name,
            'is_vip' => $user->is_vip,
            'is_vip_paid' => $user->is_vip_paid
        ]);

        try {
            // 使用事务来确保数据一致性
            return DB::transaction(function () use ($user) {
                // 1. 如果用户不再是VIP或未付款，清除相关的团队关系
                if ($user->is_vip != 1 || $user->is_vip_paid != 1) {
                    $this->removeUserFromTeam($user->id);
                    return [
                        'status' => 'success',
                        'message' => '用户不是VIP或未完成付款，已清除团队关系',
                        'user_id' => $user->id
                    ];
                }

                // 2. 如果用户是VIP且已付款，更新团队关系
                // 获取推荐链，并创建或更新团队关系
                $this->updateUserTeamRelationships($user);

                return [
                    'status' => 'success',
                    'message' => '团队成员关系更新成功',
                    'user_id' => $user->id
                ];
            });
        } catch (\Exception $e) {
            Log::error("{$this->logPrefix} 更新团队成员关系失败", [
                'user_id' => $user->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'status' => 'error',
                'message' => '更新团队成员关系失败: ' . $e->getMessage(),
                'user_id' => $user->id
            ];
        }
    }

    /**
     * 更新整个团队的所有成员关系
     * 
     * @return array 处理结果
     */
    public function updateAllTeamMembers()
    {
        Log::info("{$this->logPrefix} 开始更新所有VIP用户的团队成员关系");

        try {
            // 获取所有VIP用户
            $vipUsers = AppUser::where('is_vip', 1)
                ->where('is_vip_paid', 1)
                ->get();

            Log::info("{$this->logPrefix} 找到 " . $vipUsers->count() . " 个VIP用户");

            // 清空现有团队关系表
            DB::table('app_user_team_members')->truncate();
            Log::info("{$this->logPrefix} 已清空团队成员表");

            // 逐个处理用户
            $processedCount = 0;
            $errorCount = 0;

            foreach ($vipUsers as $user) {
                try {
                    $this->updateUserTeamRelationships($user);
                    $processedCount++;
                } catch (\Exception $e) {
                    Log::error("{$this->logPrefix} 处理用户 {$user->id} 失败", [
                        'error' => $e->getMessage()
                    ]);
                    $errorCount++;
                }
            }

            return [
                'status' => 'success',
                'message' => "团队成员关系全量更新完成。处理成功: $processedCount, 失败: $errorCount",
                'processed_count' => $processedCount,
                'error_count' => $errorCount
            ];
        } catch (\Exception $e) {
            Log::error("{$this->logPrefix} 更新所有团队成员关系失败", [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'status' => 'error',
                'message' => '更新所有团队成员关系失败: ' . $e->getMessage()
            ];
        }
    }

    /**
     * 从团队中删除用户
     * 当用户不再是VIP时，需要清除其在团队中的关系
     * 
     * @param int $userId 用户ID
     * @return void
     */
    protected function removeUserFromTeam($userId)
    {
        // 删除该用户作为团队成员的记录
        DB::table('app_user_team_members')
            ->where('member_id', $userId)
            ->delete();

        Log::info("{$this->logPrefix} 已从团队成员表中删除用户", [
            'user_id' => $userId
        ]);
    }

    /**
     * 更新用户的团队关系
     * 查找用户的推荐链，并建立适当的团队关系
     * 
     * @param AppUser $user 用户实例
     * @return void
     */
    protected function updateUserTeamRelationships(AppUser $user)
    {
        // 获取用户的推荐链(所有上级)
        $referrerChain = $this->getReferrerChain($user->id);
        
        Log::info("{$this->logPrefix} 用户推荐链获取完成", [
            'user_id' => $user->id,
            'referrer_chain' => $referrerChain
        ]);

        // 对每个上级，建立团队关系
        $level = 1;
        foreach ($referrerChain as $referrer) {
            // 只为VIP用户建立团队关系
            if ($referrer['is_vip'] == 1 && $referrer['is_vip_paid'] == 1) {
                $this->createOrUpdateTeamMembership($referrer['id'], $user->id, $level);
                $level++;
            }
        }

        // 处理下级关系，将当前用户作为团队领导者
        if ($user->is_vip == 1 && $user->is_vip_paid == 1) {
            $this->updateDownlineMembers($user->id);
        }
    }

    /**
     * 创建或更新团队成员关系
     * 
     * @param int $leaderId 团队领导者ID
     * @param int $memberId 成员ID
     * @param int $level 层级
     * @return void
     */
    protected function createOrUpdateTeamMembership($leaderId, $memberId, $level)
    {
        // 不能将自己添加为自己的团队成员
        if ($leaderId == $memberId) {
            return;
        }

        // 检查是否已存在记录
        $exists = DB::table('app_user_team_members')
            ->where('leader_id', $leaderId)
            ->where('member_id', $memberId)
            ->exists();

        if ($exists) {
            // 更新层级
            DB::table('app_user_team_members')
                ->where('leader_id', $leaderId)
                ->where('member_id', $memberId)
                ->update(['level' => $level]);
            
            Log::info("{$this->logPrefix} 更新团队成员关系", [
                'leader_id' => $leaderId,
                'member_id' => $memberId,
                'level' => $level
            ]);
        } else {
            // 新增记录
            DB::table('app_user_team_members')->insert([
                'leader_id' => $leaderId,
                'member_id' => $memberId,
                'level' => $level,
                'created_at' => now(),
                'updated_at' => now()
            ]);
            
            Log::info("{$this->logPrefix} 新增团队成员关系", [
                'leader_id' => $leaderId,
                'member_id' => $memberId,
                'level' => $level
            ]);
        }
    }

    /**
     * 更新用户的所有下级
     * 
     * @param int $userId 用户ID
     * @return void
     */
    protected function updateDownlineMembers($userId)
    {
        // 获取所有直接和间接下级
        $downlineMembers = $this->getAllDownlineMembers($userId);
        
        Log::info("{$this->logPrefix} 获取到用户下级成员", [
            'user_id' => $userId,
            'downline_count' => count($downlineMembers)
        ]);

        // 为每个下级建立团队关系
        foreach ($downlineMembers as $member) {
            if ($member['is_vip'] == 1 && $member['is_vip_paid'] == 1) {
                $this->createOrUpdateTeamMembership($userId, $member['id'], $member['level']);
            }
        }
    }

    /**
     * 获取用户的所有下级成员
     * 
     * @param int $userId 用户ID
     * @return array 下级成员数组
     */
    protected function getAllDownlineMembers($userId)
    {
        $result = [];
        $this->findDownlineMembers($userId, 1, $result);
        return $result;
    }

    /**
     * 递归查找下级成员
     * 
     * @param int $userId 用户ID
     * @param int $level 当前层级
     * @param array &$result 结果数组
     * @return void
     */
    protected function findDownlineMembers($userId, $level, &$result)
    {
        // 获取直接下级
        $directDownlines = DB::table('app_users')
            ->where('referrer_id', $userId)
            ->select('id', 'name', 'is_vip', 'is_vip_paid')
            ->get();

        foreach ($directDownlines as $downline) {
            // 添加到结果数组
            $result[] = [
                'id' => $downline->id,
                'name' => $downline->name,
                'is_vip' => $downline->is_vip,
                'is_vip_paid' => $downline->is_vip_paid,
                'level' => $level
            ];

            // 递归查找下一级
            $this->findDownlineMembers($downline->id, $level + 1, $result);
        }
    }

    /**
     * 获取用户的推荐链(上级链)
     * 
     * @param int $userId 用户ID
     * @param array $chain 已找到的推荐链
     * @return array 推荐链数组
     */
    protected function getReferrerChain($userId, $chain = [])
    {
        // 获取用户的直接推荐人
        $user = DB::table('app_users')
            ->where('id', $userId)
            ->select('referrer_id')
            ->first();

        // 如果没有推荐人或已经到达链的顶端，返回已收集的链
        if (!$user || !$user->referrer_id) {
            return $chain;
        }

        // 获取推荐人信息
        $referrer = DB::table('app_users')
            ->where('id', $user->referrer_id)
            ->select('id', 'name', 'is_vip', 'is_vip_paid')
            ->first();

        if ($referrer) {
            // 添加到推荐链中
            $chain[] = [
                'id' => $referrer->id,
                'name' => $referrer->name,
                'is_vip' => $referrer->is_vip,
                'is_vip_paid' => $referrer->is_vip_paid
            ];

            // 继续向上查找
            return $this->getReferrerChain($referrer->id, $chain);
        }

        return $chain;
    }

    /**
     * 同步单个用户的团队成员
     * 
     * @param int $userId 用户ID
     * @return array 处理结果
     */
    public function syncUserTeamMembers($userId)
    {
        try {
            $user = AppUser::find($userId);
            
            if (!$user) {
                return [
                    'status' => 'error',
                    'message' => '用户不存在',
                    'user_id' => $userId
                ];
            }
            
            return $this->updateTeamMembers($user);
        } catch (\Exception $e) {
            Log::error("{$this->logPrefix} 同步用户团队成员失败", [
                'user_id' => $userId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return [
                'status' => 'error',
                'message' => '同步用户团队成员失败: ' . $e->getMessage(),
                'user_id' => $userId
            ];
        }
    }

    /**
     * 获取用户的团队成员统计
     * 
     * @param int $userId 用户ID
     * @return array 统计结果
     */
    public function getTeamMemberStatistics($userId)
    {
        try {
            // 获取用户信息
            $user = AppUser::find($userId);
            
            if (!$user) {
                return [
                    'status' => 'error',
                    'message' => '用户不存在',
                    'user_id' => $userId
                ];
            }
            
            // 团队成员数量
            $teamMemberCount = DB::table('app_user_team_members')
                ->where('leader_id', $userId)
                ->count();
                
            // VIP团队成员数量
            $teamVipCount = DB::table('app_user_team_members as team')
                ->join('app_users as u', 'team.member_id', '=', 'u.id')
                ->where('team.leader_id', $userId)
                ->where('u.is_vip', 1)
                ->where('u.is_vip_paid', 1)
                ->count();
                
            // 当月新增VIP团队成员
            $currentMonth = date('Y-m');
            $monthlyNewVipCount = DB::table('app_user_team_members as team')
                ->join('app_users as u', 'team.member_id', '=', 'u.id')
                ->where('team.leader_id', $userId)
                ->where('u.is_vip', 1)
                ->where('u.is_vip_paid', 1)
                ->whereRaw("DATE_FORMAT(u.vip_paid_at, '%Y-%m') = ?", [$currentMonth])
                ->count();
                
            // 计算团队VIP总数（包括自己）
            $totalTeamVipCount = $user->is_vip && $user->is_vip_paid ? $teamVipCount + 1 : $teamVipCount;
            
            // 计算达到中级分红还需要的人数
            $neededForMiddle = $totalTeamVipCount >= 10 ? 0 : 10 - $totalTeamVipCount;
            
            return [
                'status' => 'success',
                'user_id' => $userId,
                'user_name' => $user->name,
                'is_vip' => $user->is_vip,
                'is_vip_paid' => $user->is_vip_paid,
                'team_member_count' => $teamMemberCount,
                'team_vip_count' => $teamVipCount,
                'total_team_vip_count' => $totalTeamVipCount,
                'monthly_new_vip_count' => $monthlyNewVipCount,
                'needed_for_middle_dividend' => $neededForMiddle,
                'qualified_for_junior' => $totalTeamVipCount >= 3,
                'qualified_for_middle' => $totalTeamVipCount >= 10,
                'qualified_for_senior' => $totalTeamVipCount >= 30
            ];
        } catch (\Exception $e) {
            Log::error("{$this->logPrefix} 获取用户团队统计失败", [
                'user_id' => $userId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return [
                'status' => 'error',
                'message' => '获取用户团队统计失败: ' . $e->getMessage(),
                'user_id' => $userId
            ];
        }
    }
} 