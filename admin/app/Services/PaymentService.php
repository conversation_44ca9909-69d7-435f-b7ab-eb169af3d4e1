<?php

namespace App\Services;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class PaymentService
{
    /**
     * 创建微信JSAPI支付参数
     *
     * @param string $orderNo
     * @param float $amount
     * @param string $body
     * @param string $openid
     * @return array
     */
    public function createWechatJsapiPayment($orderNo, $amount, $body, $openid)
    {
        Log::info('创建微信支付参数', [
            'order_no' => $orderNo,
            'amount' => $amount,
            'body' => $body,
            'openid' => $openid
        ]);

        // 实际情况中应该调用微信支付API
        // 本例中返回模拟数据，便于前端开发测试
        $nonce_str = md5(time() . rand(1000, 9999));
        $timeStamp = (string)time();
        
        // 这里需要实际调用微信支付API
        // 模拟支付参数，实际项目中需要使用微信SDK生成真实的支付参数
        return [
            'appId' => config('services.wechat.app_id'),
            'timeStamp' => $timeStamp,
            'nonceStr' => $nonce_str,
            'package' => 'prepay_id=wx' . date('YmdHis') . rand(1000, 9999),
            'signType' => 'MD5',
            'paySign' => md5('appId=' . config('services.wechat.app_id') . '&nonceStr=' . $nonce_str . '&package=prepay_id=wx' . date('YmdHis') . rand(1000, 9999) . '&signType=MD5&timeStamp=' . $timeStamp)
        ];
    }

    /**
     * 创建支付宝支付参数
     *
     * @param string $orderNo
     * @param float $amount
     * @param string $body
     * @return string
     */
    public function createAlipayPayment($orderNo, $amount, $body)
    {
        Log::info('创建支付宝支付参数', [
            'order_no' => $orderNo,
            'amount' => $amount,
            'body' => $body
        ]);

        // 实际情况中应该调用支付宝API
        // 本例中返回模拟数据，便于前端开发测试
        return '支付宝支付参数内容';
    }

    /**
     * 处理支付回调通知
     *
     * @param Request $request
     * @return array|false
     */
    public function handlePaymentNotify(Request $request)
    {
        Log::info('处理支付回调通知', $request->all());
        
        // 实际项目中需要根据微信或支付宝的回调参数进行处理和验签
        // 这里仅作示例，返回关键参数
        try {
            // 微信支付回调示例
            if ($request->has('xml') || $request->isXml()) {
                // 解析XML数据
                $xml = $request->getContent();
                $data = $this->parseXML($xml);
                
                // 验证签名等操作
                // ...
                
                return [
                    'out_trade_no' => $data['out_trade_no'] ?? '',
                    'transaction_id' => $data['transaction_id'] ?? '',
                    'total_fee' => $data['total_fee'] ?? 0,
                    'result_code' => $data['result_code'] ?? ''
                ];
            }
            
            // 支付宝回调示例
            if ($request->has('trade_no')) {
                // 验证签名等操作
                // ...
                
                return [
                    'out_trade_no' => $request->input('out_trade_no', ''),
                    'transaction_id' => $request->input('trade_no', ''),
                    'total_fee' => $request->input('total_amount', 0) * 100, // 转换为分
                    'result_code' => $request->input('trade_status') === 'TRADE_SUCCESS' ? 'SUCCESS' : 'FAIL'
                ];
            }
            
            return false;
        } catch (\Exception $e) {
            Log::error('处理支付回调失败: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 解析微信回调XML数据
     *
     * @param string $xml
     * @return array
     */
    private function parseXML($xml)
    {
        $xml = simplexml_load_string($xml, 'SimpleXMLElement', LIBXML_NOCDATA);
        return json_decode(json_encode($xml), true);
    }
} 