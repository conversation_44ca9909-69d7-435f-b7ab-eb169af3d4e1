<?php

namespace App\Services;

use App\Models\PointRecord;
use App\Models\PointRule;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class PointService
{
    /**
     * 积分变动处理
     * 
     * @param int $userId 用户ID
     * @param string $event 事件类型
     * @param string $description 描述
     * @param array $params 额外参数
     * @return array|null 成功返回数组，失败返回null
     */
    public function handlePointEvent($userId, $event, $description = '', $params = [])
    {
        // 查找匹配的积分规则
        $rule = $this->findMatchingRule($userId, $event);
        
        if (!$rule) {
            Log::info("No matching point rule found for event: {$event}");
            return null;
        }
        
        // 获取用户当前积分
        $user = User::find($userId);
        if (!$user) {
            Log::error("User not found: {$userId}");
            return null;
        }
        
        $currentPoints = $user->points ?? 0;
        
        // 计算积分变动
        $pointsChange = $rule->type === 'reward' ? $rule->points : -$rule->points;
        
        // 确保消费积分后不会为负
        if ($currentPoints + $pointsChange < 0) {
            Log::info("Insufficient points for user: {$userId}");
            return null;
        }
        
        $afterPoints = $currentPoints + $pointsChange;
        
        // 开启事务
        DB::beginTransaction();
        
        try {
            // 更新用户积分
            $user->points = $afterPoints;
            $user->save();
            
            // 创建积分记录
            $record = PointRecord::create([
                'user_id' => $userId,
                'rule_id' => $rule->id,
                'type' => $rule->type,
                'points' => $rule->points,
                'description' => $description ?: $rule->description,
                'before_points' => $currentPoints,
                'after_points' => $afterPoints,
            ]);
            
            DB::commit();
            
            return [
                'user' => $user,
                'rule' => $rule,
                'record' => $record,
                'before_points' => $currentPoints,
                'change' => $pointsChange,
                'after_points' => $afterPoints,
            ];
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("Error handling point event: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * 查找匹配的积分规则
     * 
     * @param int $userId 用户ID
     * @param string $event 事件类型
     * @return PointRule|null
     */
    private function findMatchingRule($userId, $event)
    {
        // 获取激活的规则
        $rules = PointRule::where('event', $event)
            ->where('status', 'active')
            ->get();
            
        if ($rules->isEmpty()) {
            return null;
        }
        
        foreach ($rules as $rule) {
            // 检查规则限制
            if ($this->checkRuleLimit($userId, $rule)) {
                return $rule;
            }
        }
        
        return null;
    }
    
    /**
     * 检查规则限制
     * 
     * @param int $userId 用户ID
     * @param PointRule $rule 规则
     * @return bool 是否满足限制条件
     */
    private function checkRuleLimit($userId, $rule)
    {
        // 没有限制
        if ($rule->limit_cycle === 'none' || $rule->limit_times === 0) {
            return true;
        }
        
        // 根据周期确定查询时间范围
        $startTime = null;
        $now = Carbon::now();
        
        switch ($rule->limit_cycle) {
            case 'once':
                // 不限时间范围，只检查总次数
                break;
            case 'day':
                $startTime = $now->copy()->startOfDay();
                break;
            case 'week':
                $startTime = $now->copy()->startOfWeek();
                break;
            case 'month':
                $startTime = $now->copy()->startOfMonth();
                break;
            case 'year':
                $startTime = $now->copy()->startOfYear();
                break;
            default:
                return true;
        }
        
        // 查询用户在指定时间内触发该规则的次数
        $query = PointRecord::where('user_id', $userId)
            ->where('rule_id', $rule->id);
            
        if ($startTime) {
            $query->where('created_at', '>=', $startTime);
        }
        
        $count = $query->count();
        
        // 检查是否超过限制次数
        return $count < $rule->limit_times;
    }
    
    /**
     * 获取用户的积分余额
     * 
     * @param int $userId 用户ID
     * @return int 积分余额
     */
    public function getUserPoints($userId)
    {
        $user = User::find($userId);
        return $user ? ($user->points ?? 0) : 0;
    }
    
    /**
     * 直接调整用户积分（用于管理员操作或系统调整）
     * 
     * @param int $userId 用户ID
     * @param int $points 积分值（正数为增加，负数为减少）
     * @param string $description 描述
     * @return array|null 成功返回数组，失败返回null
     */
    public function adjustUserPoints($userId, $points, $description)
    {
        // 获取用户当前积分
        $user = User::find($userId);
        if (!$user) {
            Log::error("User not found: {$userId}");
            return null;
        }
        
        $currentPoints = $user->points ?? 0;
        $afterPoints = $currentPoints + $points;
        
        // 确保积分不会为负
        if ($afterPoints < 0) {
            Log::info("Adjust points would result in negative balance for user: {$userId}");
            return null;
        }
        
        // 开启事务
        DB::beginTransaction();
        
        try {
            // 更新用户积分
            $user->points = $afterPoints;
            $user->save();
            
            // 创建积分记录
            $type = $points > 0 ? 'reward' : 'consume';
            $record = PointRecord::create([
                'user_id' => $userId,
                'rule_id' => null, // 手动调整没有关联规则
                'type' => $type,
                'points' => abs($points),
                'description' => $description,
                'before_points' => $currentPoints,
                'after_points' => $afterPoints,
            ]);
            
            DB::commit();
            
            return [
                'user' => $user,
                'record' => $record,
                'before_points' => $currentPoints,
                'change' => $points,
                'after_points' => $afterPoints,
            ];
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("Error adjusting user points: " . $e->getMessage());
            return null;
        }
    }
} 