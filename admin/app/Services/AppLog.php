<?php

namespace App\Services;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Throwable;

/**
 * 应用日志服务
 * 用于记录系统日志和请求日志
 */
class AppLog
{
    // 允许的日志级别
    protected static $allowedLevels = ['debug', 'info', 'warning', 'error', 'critical'];

    /**
     * 记录日志
     *
     * @param string $level 日志级别
     * @param string $message 日志消息
     * @param array $context 上下文数据
     * @return void
     */
    public static function log(string $level, string $message, array $context = []): void
    {
        if (!in_array($level, self::$allowedLevels)) {
            $level = 'info';
        }

        Log::channel('daily')->$level($message, $context);
    }

    /**
     * 记录调试日志
     *
     * @param string $message 日志消息
     * @param array $context 上下文数据
     * @return void
     */
    public static function debug(string $message, array $context = []): void
    {
        self::log('debug', $message, $context);
    }

    /**
     * 记录信息日志
     *
     * @param string $message 日志消息
     * @param array $context 上下文数据
     * @return void
     */
    public static function info(string $message, array $context = []): void
    {
        self::log('info', $message, $context);
    }

    /**
     * 记录警告日志
     *
     * @param string $message 日志消息
     * @param array $context 上下文数据
     * @return void
     */
    public static function warning(string $message, array $context = []): void
    {
        self::log('warning', $message, $context);
    }

    /**
     * 记录错误日志
     *
     * @param string $message 日志消息
     * @param array $context 上下文数据
     * @return void
     */
    public static function error(string $message, array $context = []): void
    {
        self::log('error', $message, $context);
    }

    /**
     * 记录严重错误日志
     *
     * @param string $message 日志消息
     * @param array $context 上下文数据
     * @return void
     */
    public static function critical(string $message, array $context = []): void
    {
        self::log('critical', $message, $context);
    }

    /**
     * 记录请求日志
     *
     * @param Request|array $request 请求对象或请求数据数组
     * @param string|null $userId 用户ID
     * @param string $level 日志级别
     * @return void
     */
    public static function logRequest($request, ?string $userId = null, string $level = 'info'): void
    {
        $requestData = [];
        
        if ($request instanceof Request) {
            $userId = $userId ?? ($request->user() ? $request->user()->id : null);
            $requestData = [
                'url' => $request->fullUrl(),
                'method' => $request->method(),
                'params' => $request->all(),
                'headers' => $request->header(),
                'ip' => $request->ip(),
                'user_id' => $userId,
            ];
        } elseif (is_array($request)) {
            $requestData = $request;
            $userId = $userId ?? ($requestData['user_id'] ?? null);
        }

        self::log($level, '请求日志', $requestData);

        // 如果有用户ID，缓存用户日志
        if ($userId) {
            self::cacheUserLog($userId, '请求日志', $requestData);
        }
    }

    /**
     * 记录短信日志
     * 
     * @param string $phone 手机号 (如: 13800138000)
     * @param string $type 短信类型 (如: 'verify' 验证码, 'notify' 通知, 'marketing' 营销)
     * @param string $code 验证码或短信内容 (如: 123456)
     * @param bool $status 发送状态 (true: 成功, false: 失败)
     * @param string $message 结果消息 (如: 发送成功, 手机号不正确)
     * @param string $provider 短信提供商 (如: 'aliyun' 阿里云, 'tencent' 腾讯云)
     * @param array $requestData 请求数据 (API请求的原始数据数组)
     * @param array $responseData 响应数据 (API响应的原始数据数组)
     * @param string $level 日志级别 (默认: info)
     * @return bool 是否成功记录到数据库
     */
    public static function logSms(
        string $phone, 
        string $type, 
        string $code, 
        bool $status, 
        string $message = '', 
        string $provider = '阿里云', 
        array $requestData = [], 
        array $responseData = [], 
        string $level = 'info'
    ): bool {
        // 掩码手机号，保护用户隐私
        $maskedPhone = substr_replace($phone, '****', 3, 4);
        
        $logData = [
            'phone' => $maskedPhone, // 记录掩码后的手机号到日志
            'type' => $type,
            'code' => $code,
            'status' => $status,
            'message' => $message,
            'provider' => $provider,
            'request_ip' => request()->ip(),
            'time' => now()->toDateTimeString()
        ];

        // 不记录过大的数据到日志文件
        if (!empty($requestData)) {
            $logData['request_summary'] = '请求数据已记录到数据库';
        }
        
        if (!empty($responseData)) {
            $logData['response_summary'] = '响应数据已记录到数据库';
        }

        $logMessage = sprintf(
            '短信日志 [%s] - 手机号: %s, 类型: %s, 验证码: %s, 状态: %s', 
            $status ? '成功' : '失败',
            $maskedPhone,
            $type,
            $code,
            $message
        );

        // 记录到系统日志
        self::log($level, $logMessage, $logData);

        // 保存到数据库
        $dbSaved = false;
        try {
            if (class_exists('\\App\\Models\\SmsLog')) {
                \App\Models\SmsLog::create([
                    'phone' => $phone, // 数据库中存储完整手机号
                    'code' => $code,
                    'type' => $type,
                    'status' => $status ? 1 : 0, // 数据库中使用0/1表示状态
                    'message' => $message,
                    'provider' => $provider,
                    'request_ip' => request()->ip(),
                    'request_data' => !empty($requestData) ? json_encode($requestData, JSON_UNESCAPED_UNICODE) : null,
                    'response_data' => !empty($responseData) ? json_encode($responseData, JSON_UNESCAPED_UNICODE) : null,
                ]);
                $dbSaved = true;
            }
        } catch (\Throwable $e) {
            // 出错时记录异常，但不中断流程
            self::logException($e, 'error', [
                'context' => 'SMS日志保存失败',
                'phone' => $maskedPhone,
                'type' => $type,
                'code' => $code
            ]);
        }

        return $dbSaved;
    }

    /**
     * 记录异常日志
     *
     * @param Throwable $exception 异常对象
     * @param string $level 日志级别
     * @param array $context 上下文数据
     * @return void
     */
    public static function logException(Throwable $exception, string $level = 'error', array $context = []): void
    {
        $context = array_merge($context, [
            'exception' => get_class($exception),
            'message' => $exception->getMessage(),
            'code' => $exception->getCode(),
            'file' => $exception->getFile(),
            'line' => $exception->getLine(),
            'trace' => $exception->getTraceAsString(),
        ]);

        self::log($level, '系统异常', $context);
    }

    /**
     * 缓存用户日志
     *
     * @param string $userId 用户ID
     * @param string $message 日志消息
     * @param array $context 上下文数据
     * @return void
     */
    protected static function cacheUserLog(string $userId, string $message, array $context = []): void
    {
        $cacheKey = "user_logs_{$userId}";
        $logs = Cache::get($cacheKey, []);

        // 添加新日志
        $logs[] = [
            'time' => now()->toDateTimeString(),
            'message' => $message,
            'context' => $context,
        ];

        // 只保留最近的10条日志
        if (count($logs) > 10) {
            $logs = array_slice($logs, -10);
        }

        // 缓存24小时
        Cache::put($cacheKey, $logs, 60 * 24);
    }

    /**
     * 获取用户缓存日志
     *
     * @param string $userId 用户ID
     * @return array 用户日志列表
     */
    public static function getUserLogs(string $userId): array
    {
        $cacheKey = "user_logs_{$userId}";
        return Cache::get($cacheKey, []);
    }
}