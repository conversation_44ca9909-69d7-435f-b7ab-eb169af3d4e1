<?php

namespace App\Services;

use App\Models\AdminNotification;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class NotificationService
{
    /**
     * 创建通知
     *
     * @param array $data
     * @return AdminNotification|null
     */
    public function createNotification(array $data)
    {
        try {
            $notification = new AdminNotification();
            $notification->type = $data['type'] ?? 'info';
            $notification->title = $data['title'];
            $notification->content = $data['content'];
            $notification->admin_id = $data['admin_id'] ?? 1; // 默认发送给管理员
            $notification->sender_id = $data['sender_id'] ?? null;
            $notification->priority = $data['priority'] ?? 'normal';
            $notification->is_read = false;
            $notification->extra_data = isset($data['extra_data']) ? json_encode($data['extra_data']) : null;
            $notification->save();

            // 如果需要语音播报，添加标记
            if (isset($data['voice_enabled']) && $data['voice_enabled']) {
                $this->markForVoiceNotification($notification);
            }

            return $notification;
        } catch (\Exception $e) {
            Log::error('创建通知失败: ' . $e->getMessage(), $data);
            return null;
        }
    }

    /**
     * 新用户登录绑定手机号通知
     *
     * @param User $user
     * @return AdminNotification|null
     */
    public function notifyNewUserLogin(User $user)
    {
        $data = [
            'type' => 'success',
            'title' => '新用户登录通知',
            'content' => "新用户已成功登录并绑定手机号：{$user->phone}，用户昵称：{$user->name}",
            'priority' => 'high',
            'voice_enabled' => true,
            'extra_data' => [
                'user_id' => $user->id,
                'phone' => $user->phone,
                'name' => $user->name,
                'login_time' => $user->last_login_time,
                'action_type' => 'new_user_login'
            ]
        ];

        return $this->createNotification($data);
    }

    /**
     * 新预约安装订单通知
     *
     * @param object $booking
     * @return AdminNotification|null
     */
    public function notifyNewInstallationBooking($booking)
    {
        $data = [
            'type' => 'warning',
            'title' => '新预约安装订单',
            'content' => "收到新的安装预约订单，订单号：{$booking->booking_no}，联系人：{$booking->contact_name}，电话：{$booking->contact_phone}，预约时间：{$booking->install_time}",
            'priority' => 'high',
            'voice_enabled' => true,
            'extra_data' => [
                'booking_id' => $booking->id,
                'booking_no' => $booking->booking_no,
                'contact_name' => $booking->contact_name,
                'contact_phone' => $booking->contact_phone,
                'install_time' => $booking->install_time,
                'package_type' => $booking->package_type,
                'total_amount' => $booking->total_amount,
                'action_type' => 'new_installation_booking'
            ]
        ];

        return $this->createNotification($data);
    }

    /**
     * VIP升级支付通知
     *
     * @param object $order
     * @param User $user
     * @return AdminNotification|null
     */
    public function notifyVipUpgradePayment($order, User $user)
    {
        $data = [
            'type' => 'success',
            'title' => 'VIP升级支付通知',
            'content' => "用户 {$user->name}（{$user->phone}）已成功支付VIP升级费用 ¥{$order->actual_amount}，订单号：{$order->order_no}",
            'priority' => 'urgent',
            'voice_enabled' => true,
            'extra_data' => [
                'order_id' => $order->id,
                'order_no' => $order->order_no,
                'user_id' => $user->id,
                'user_name' => $user->name,
                'user_phone' => $user->phone,
                'amount' => $order->actual_amount,
                'payment_time' => $order->paid_at,
                'action_type' => 'vip_upgrade_payment'
            ]
        ];

        return $this->createNotification($data);
    }

    /**
     * 标记通知需要语音播报
     *
     * @param AdminNotification $notification
     * @return void
     */
    private function markForVoiceNotification(AdminNotification $notification)
    {
        // 在extra_data中添加语音播报标记
        $extraData = $notification->extra_data ? json_decode($notification->extra_data, true) : [];
        $extraData['voice_enabled'] = true;
        $extraData['voice_played'] = false;
        $notification->extra_data = json_encode($extraData);
        $notification->save();
    }

    /**
     * 获取需要语音播报的通知
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getVoiceNotifications()
    {
        return AdminNotification::where('is_read', false)
            ->whereJsonContains('extra_data->voice_enabled', true)
            ->whereJsonContains('extra_data->voice_played', false)
            ->orderBy('created_at', 'desc')
            ->get();
    }

    /**
     * 标记语音通知已播放
     *
     * @param int $notificationId
     * @return bool
     */
    public function markVoiceNotificationPlayed($notificationId)
    {
        try {
            $notification = AdminNotification::find($notificationId);
            if ($notification) {
                $extraData = $notification->extra_data ? json_decode($notification->extra_data, true) : [];
                $extraData['voice_played'] = true;
                $notification->extra_data = json_encode($extraData);
                $notification->save();
                return true;
            }
            return false;
        } catch (\Exception $e) {
            Log::error('标记语音通知已播放失败: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 批量发送通知给所有管理员
     *
     * @param array $data
     * @return int 发送成功的数量
     */
    public function notifyAllAdmins(array $data)
    {
        try {
            // 获取所有管理员用户
            $adminUsers = User::where('is_admin', 1)->get();
            $successCount = 0;

            foreach ($adminUsers as $admin) {
                $notificationData = array_merge($data, ['admin_id' => $admin->id]);
                if ($this->createNotification($notificationData)) {
                    $successCount++;
                }
            }

            return $successCount;
        } catch (\Exception $e) {
            Log::error('批量发送通知失败: ' . $e->getMessage());
            return 0;
        }
    }
} 