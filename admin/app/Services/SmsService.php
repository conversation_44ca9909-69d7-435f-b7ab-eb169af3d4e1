<?php

namespace App\Services;

use Exception;
use Illuminate\Support\Facades\Log;

class SmsService
{
    private $accessKeyId;
    private $accessKeySecret;
    private $signName;
    private $regionId;

    public function __construct()
    {
        $this->accessKeyId = env('ALIYUN_SMS_ACCESS_KEY_ID');
        $this->accessKeySecret = env('ALIYUN_SMS_ACCESS_KEY_SECRET');
        $this->signName = env('ALIYUN_SMS_SIGN_NAME', '点点够');
        $this->regionId = env('ALIYUN_SMS_REGION_ID', 'cn-hangzhou');
    }

    /**
     * 发送生日祝福短信
     */
    public function sendBirthdaySms($phone, $name, $age)
    {
        $templateCode = env('ALIYUN_SMS_BIRTHDAY_TEMPLATE', 'SMS_475005556');
        $templateParam = [
            'user_nick' => $name,
            'age' => (string)$age
        ];

        return $this->sendSms($phone, $templateCode, $templateParam);
    }

    /**
     * 发送短信验证码
     */
    public function sendVerificationCode($phone, $code)
    {
        $templateCode = env('ALIYUN_SMS_VERIFICATION_TEMPLATE', 'SMS_461835941');
        $templateParam = [
            'code' => $code
        ];

        return $this->sendSms($phone, $templateCode, $templateParam);
    }

    /**
     * 通用短信发送方法
     */
    private function sendSms($phone, $templateCode, $templateParam)
    {
        try {
            // 构建请求参数
            $params = [
                'PhoneNumbers' => $phone,
                'SignName' => $this->signName,
                'TemplateCode' => $templateCode,
                'TemplateParam' => json_encode($templateParam, JSON_UNESCAPED_UNICODE)
            ];

            // 构建阿里云API请求
            $result = $this->makeApiRequest($params);

            Log::info('短信发送结果', [
                'phone' => $phone,
                'template' => $templateCode,
                'result' => $result
            ]);

            return $result;

        } catch (Exception $e) {
            Log::error('短信发送异常', [
                'phone' => $phone,
                'template' => $templateCode,
                'error' => $e->getMessage()
            ]);

            return [
                'Code' => 'Error',
                'Message' => $e->getMessage()
            ];
        }
    }

    /**
     * 构建阿里云API请求
     */
    private function makeApiRequest($params)
    {
        $commonParams = [
            'Format' => 'JSON',
            'Version' => '2017-05-25',
            'AccessKeyId' => $this->accessKeyId,
            'SignatureMethod' => 'HMAC-SHA1',
            'Timestamp' => gmdate('Y-m-d\TH:i:s\Z'),
            'SignatureVersion' => '1.0',
            'SignatureNonce' => uniqid(),
            'Action' => 'SendSms',
            'RegionId' => $this->regionId
        ];

        $allParams = array_merge($commonParams, $params);
        
        // 生成签名
        $signature = $this->generateSignature($allParams);
        $allParams['Signature'] = $signature;

        // 发送HTTP请求
        $url = 'https://dysmsapi.aliyuncs.com/?' . http_build_query($allParams);
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        
        if (curl_errno($ch)) {
            throw new Exception('CURL错误: ' . curl_error($ch));
        }
        
        curl_close($ch);

        if ($httpCode !== 200) {
            throw new Exception('HTTP请求失败，状态码: ' . $httpCode);
        }

        $result = json_decode($response, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new Exception('响应解析失败: ' . json_last_error_msg());
        }

        return $result;
    }

    /**
     * 生成阿里云API签名
     */
    private function generateSignature($params)
    {
        ksort($params);
        
        $stringToSign = '';
        foreach ($params as $key => $value) {
            $stringToSign .= '&' . $this->percentEncode($key) . '=' . $this->percentEncode($value);
        }
        
        $stringToSign = 'GET&%2F&' . $this->percentEncode(substr($stringToSign, 1));
        
        return base64_encode(hash_hmac('sha1', $stringToSign, $this->accessKeySecret . '&', true));
    }

    /**
     * URL编码
     */
    private function percentEncode($str)
    {
        $res = urlencode($str);
        $res = preg_replace('/\+/', '%20', $res);
        $res = preg_replace('/\*/', '%2A', $res);
        $res = preg_replace('/%7E/', '~', $res);
        return $res;
    }
} 