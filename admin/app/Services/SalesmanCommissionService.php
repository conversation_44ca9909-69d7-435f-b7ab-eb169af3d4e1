<?php

namespace App\Services;

use App\Models\Salesman;
use App\Models\TappDevice;
use App\Models\WaterOrder;
use App\Models\SalesmanSale;
use App\Models\SalesmanCommission;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class SalesmanCommissionService
{
    /**
     * 提成比例 - 30%
     */
    const COMMISSION_RATE = 0.30;

    /**
     * 计算业务员基于设备销售的提成
     * 
     * @param int $salesmanId 业务员ID
     * @param string $startDate 开始日期
     * @param string $endDate 结束日期
     * @return array
     */
    public function calculateDeviceCommissions($salesmanId, $startDate = null, $endDate = null)
    {
        try {
            $salesman = Salesman::with('user')->findOrFail($salesmanId);
            
            // 默认时间范围为当月
            if (!$startDate) {
                $startDate = Carbon::now()->startOfMonth()->toDateString();
            }
            if (!$endDate) {
                $endDate = Carbon::now()->endOfMonth()->toDateString();
            }

            // 获取业务员销售的设备（通过app_user_id关联）
            $devices = TappDevice::where('app_user_id', $salesman->user_id)
                ->where('is_self_use', 0) // 只计算销售设备，排除自用设备
                ->whereNotNull('activate_date')
                ->whereBetween('activate_date', [$startDate, $endDate])
                ->get();

            $commissions = [];
            $totalCommission = 0;

            foreach ($devices as $device) {
                // 获取设备的充值订单
                $rechargeOrders = $this->getDeviceRechargeOrders($device->device_number, $startDate, $endDate);
                
                foreach ($rechargeOrders as $order) {
                    $commissionAmount = $this->calculateOrderCommission($order);
                    
                    if ($commissionAmount > 0) {
                        $commissions[] = [
                            'device_id' => $device->id,
                            'device_number' => $device->device_number,
                            'order_id' => $order->id,
                            'order_number' => $order->order_number,
                            'order_amount' => $order->money,
                            'commission_amount' => $commissionAmount,
                            'commission_rate' => self::COMMISSION_RATE * 100,
                            'order_date' => $order->create_date,
                            'customer_name' => $device->client_name,
                            'customer_phone' => $device->client_phone,
                            'billing_mode' => $order->billing_mode,
                            'surrogate_type' => $order->surrogate_type
                        ];
                        
                        $totalCommission += $commissionAmount;
                    }
                }
            }

            return [
                'salesman' => $salesman,
                'period' => [
                    'start_date' => $startDate,
                    'end_date' => $endDate
                ],
                'devices_count' => $devices->count(),
                'commissions' => $commissions,
                'total_commission' => $totalCommission,
                'commission_rate' => self::COMMISSION_RATE * 100
            ];

        } catch (\Exception $e) {
            Log::error('计算业务员设备提成失败', [
                'salesman_id' => $salesmanId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            throw $e;
        }
    }

    /**
     * 获取设备的充值订单
     * 
     * @param string $deviceNumber 设备编号
     * @param string $startDate 开始日期
     * @param string $endDate 结束日期
     * @return \Illuminate\Support\Collection
     */
    private function getDeviceRechargeOrders($deviceNumber, $startDate, $endDate)
    {
        try {
            return DB::connection('water_db')
                ->table('wb_order')
                ->where('device_number', $deviceNumber)
                ->where('order_type', '01') // 充值订单
                ->where('order_status', '103') // 支付成功
                ->whereBetween('create_date', [$startDate, $endDate])
                ->orderBy('create_date', 'desc')
                ->get();
        } catch (\Exception $e) {
            Log::error('获取设备充值订单失败', [
                'device_number' => $deviceNumber,
                'error' => $e->getMessage()
            ]);
            
            return collect();
        }
    }

    /**
     * 计算单个订单的提成金额
     * 
     * @param object $order 订单对象
     * @return float
     */
    private function calculateOrderCommission($order)
    {
        $orderAmount = floatval($order->money ?? 0);
        return round($orderAmount * self::COMMISSION_RATE, 2);
    }

    /**
     * 创建或更新业务员销售记录
     * 
     * @param int $salesmanId 业务员ID
     * @param array $commissionData 提成数据
     * @return void
     */
    public function createSalesRecords($salesmanId, $commissionData)
    {
        try {
            DB::beginTransaction();

            foreach ($commissionData['commissions'] as $commission) {
                // 检查是否已存在该订单的销售记录
                $existingSale = SalesmanSale::where('salesman_id', $salesmanId)
                    ->where('order_id', $commission['order_number'])
                    ->first();

                if (!$existingSale) {
                    SalesmanSale::create([
                        'salesman_id' => $salesmanId,
                        'order_id' => $commission['order_number'],
                        'amount' => $commission['order_amount'],
                        'commission_rate' => $commission['commission_rate'],
                        'commission_amount' => $commission['commission_amount'],
                        'product_name' => '净水器设备充值',
                        'product_id' => $commission['device_number'],
                        'quantity' => 1,
                        'customer_name' => $commission['customer_name'],
                        'customer_phone' => $commission['customer_phone'],
                        'status' => 'completed',
                        'sale_date' => $commission['order_date'],
                        'remarks' => "设备{$commission['device_number']}充值提成"
                    ]);
                }
            }

            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('创建业务员销售记录失败', [
                'salesman_id' => $salesmanId,
                'error' => $e->getMessage()
            ]);
            
            throw $e;
        }
    }

    /**
     * 生成业务员提成记录
     * 
     * @param int $salesmanId 业务员ID
     * @param string $period 提成周期
     * @param string $startDate 开始日期
     * @param string $endDate 结束日期
     * @return SalesmanCommission
     */
    public function generateCommissionRecord($salesmanId, $period, $startDate, $endDate)
    {
        try {
            // 计算提成数据
            $commissionData = $this->calculateDeviceCommissions($salesmanId, $startDate, $endDate);
            
            if ($commissionData['total_commission'] <= 0) {
                throw new \Exception('该期间没有可计算的提成');
            }

            // 检查是否已存在该期间的提成记录
            $existingCommission = SalesmanCommission::where('salesman_id', $salesmanId)
                ->where('period', $period)
                ->first();

            if ($existingCommission) {
                throw new \Exception('该期间已存在提成记录');
            }

            DB::beginTransaction();

            // 创建销售记录
            $this->createSalesRecords($salesmanId, $commissionData);

            // 创建提成记录
            $commission = SalesmanCommission::create([
                'salesman_id' => $salesmanId,
                'amount' => $commissionData['total_commission'],
                'period' => $period,
                'start_date' => $startDate,
                'end_date' => $endDate,
                'status' => 'pending',
                'remarks' => "基于{$commissionData['devices_count']}台设备销售的30%提成"
            ]);

            DB::commit();

            return $commission;

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('生成业务员提成记录失败', [
                'salesman_id' => $salesmanId,
                'period' => $period,
                'error' => $e->getMessage()
            ]);
            
            throw $e;
        }
    }

    /**
     * 获取业务员设备销售统计
     * 
     * @param int $salesmanId 业务员ID
     * @param string $timeRange 时间范围
     * @return array
     */
    public function getDeviceSalesStats($salesmanId, $timeRange = 'month')
    {
        try {
            $salesman = Salesman::with('user')->findOrFail($salesmanId);
            
            // 根据时间范围设置日期
            switch ($timeRange) {
                case 'today':
                    $startDate = Carbon::today()->toDateString();
                    $endDate = Carbon::today()->toDateString();
                    break;
                case 'week':
                    $startDate = Carbon::now()->startOfWeek()->toDateString();
                    $endDate = Carbon::now()->endOfWeek()->toDateString();
                    break;
                case 'month':
                    $startDate = Carbon::now()->startOfMonth()->toDateString();
                    $endDate = Carbon::now()->endOfMonth()->toDateString();
                    break;
                case 'quarter':
                    $startDate = Carbon::now()->startOfQuarter()->toDateString();
                    $endDate = Carbon::now()->endOfQuarter()->toDateString();
                    break;
                case 'year':
                    $startDate = Carbon::now()->startOfYear()->toDateString();
                    $endDate = Carbon::now()->endOfYear()->toDateString();
                    break;
                default:
                    $startDate = Carbon::now()->startOfMonth()->toDateString();
                    $endDate = Carbon::now()->endOfMonth()->toDateString();
            }

            // 获取设备销售数据
            $devices = TappDevice::where('app_user_id', $salesman->user_id)
                ->where('is_self_use', 0)
                ->whereNotNull('activate_date');

            // 总设备数
            $totalDevices = $devices->count();
            
            // 期间内激活的设备数
            $periodDevices = $devices->whereBetween('activate_date', [$startDate, $endDate])->count();

            // 计算提成数据
            $commissionData = $this->calculateDeviceCommissions($salesmanId, $startDate, $endDate);

            return [
                'salesman' => $salesman,
                'time_range' => $timeRange,
                'period' => [
                    'start_date' => $startDate,
                    'end_date' => $endDate
                ],
                'stats' => [
                    'total_devices' => $totalDevices,
                    'period_devices' => $periodDevices,
                    'total_commission' => $commissionData['total_commission'],
                    'commission_rate' => self::COMMISSION_RATE * 100,
                    'avg_commission_per_device' => $periodDevices > 0 ? round($commissionData['total_commission'] / $periodDevices, 2) : 0
                ],
                'commissions' => $commissionData['commissions']
            ];

        } catch (\Exception $e) {
            Log::error('获取业务员设备销售统计失败', [
                'salesman_id' => $salesmanId,
                'time_range' => $timeRange,
                'error' => $e->getMessage()
            ]);
            
            throw $e;
        }
    }

    /**
     * 获取业务员排行榜
     * 
     * @param string $timeRange 时间范围
     * @param int $limit 限制数量
     * @return array
     */
    public function getSalesmanRanking($timeRange = 'month', $limit = 10)
    {
        try {
            // 根据时间范围设置日期
            switch ($timeRange) {
                case 'today':
                    $startDate = Carbon::today()->toDateString();
                    $endDate = Carbon::today()->toDateString();
                    break;
                case 'week':
                    $startDate = Carbon::now()->startOfWeek()->toDateString();
                    $endDate = Carbon::now()->endOfWeek()->toDateString();
                    break;
                case 'month':
                    $startDate = Carbon::now()->startOfMonth()->toDateString();
                    $endDate = Carbon::now()->endOfMonth()->toDateString();
                    break;
                case 'quarter':
                    $startDate = Carbon::now()->startOfQuarter()->toDateString();
                    $endDate = Carbon::now()->endOfQuarter()->toDateString();
                    break;
                case 'year':
                    $startDate = Carbon::now()->startOfYear()->toDateString();
                    $endDate = Carbon::now()->endOfYear()->toDateString();
                    break;
                default:
                    $startDate = Carbon::now()->startOfMonth()->toDateString();
                    $endDate = Carbon::now()->endOfMonth()->toDateString();
            }

            // 获取所有业务员的设备销售统计
            $salesmen = Salesman::with('user')
                ->where('status', 'active')
                ->get();

            $ranking = [];

            foreach ($salesmen as $salesman) {
                $stats = $this->getDeviceSalesStats($salesman->id, $timeRange);
                
                $ranking[] = [
                    'salesman_id' => $salesman->id,
                    'salesman_name' => $salesman->user->name ?? $salesman->name,
                    'employee_id' => $salesman->employee_id,
                    'devices_count' => $stats['stats']['period_devices'],
                    'total_commission' => $stats['stats']['total_commission'],
                    'avg_commission' => $stats['stats']['avg_commission_per_device']
                ];
            }

            // 按提成金额排序
            usort($ranking, function($a, $b) {
                return $b['total_commission'] <=> $a['total_commission'];
            });

            return array_slice($ranking, 0, $limit);

        } catch (\Exception $e) {
            Log::error('获取业务员排行榜失败', [
                'time_range' => $timeRange,
                'error' => $e->getMessage()
            ]);
            
            return [];
        }
    }
} 