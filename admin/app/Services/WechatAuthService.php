<?php

namespace App\Services;

use App\Models\AppUser;
use App\Models\SystemConfig;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use App\Services\UserRoleSyncService;

class WechatAuthService
{
    /**
     * 获取微信授权登录URL
     *
     * @param string $redirectUrl 授权后重定向URL
     * @param string $state 状态参数
     * @return string
     */
    public function getAuthUrl($redirectUrl = null, $state = null)
    {
        $config = SystemConfig::getWechatConfig();

        $appId = $config['app_id'] ?? '';
        $callbackUrl = $redirectUrl ?: url($config['oauth_callback_url'] ?? '/app/wechat-callback.html');
        $state = $state ?: Str::random(16);

        $queryParams = [
            'appid' => $appId,
            'redirect_uri' => $callbackUrl,
            'response_type' => 'code',
            'scope' => 'snsapi_userinfo',
            'state' => $state,
        ];

        $url = 'https://open.weixin.qq.com/connect/oauth2/authorize?' . http_build_query($queryParams) . '#wechat_redirect';

        return $url;
    }

    /**
     * 通过授权码获取微信访问令牌
     *
     * @param string $code 授权码
     * @return array|null 包含access_token和openid的数组，失败返回null
     */
    public function getAccessToken($code)
    {
        // 检查是否是测试代码
        if ($code === 'test_code' || strpos($code, 'test_') === 0) {
            Log::info('检测到测试代码，返回真实用户数据', ['code' => $code]);

            // 获取一个真实的用户
            $realUser = AppUser::where('is_vip', 1)->first();

            if (!$realUser) {
                $realUser = AppUser::first();
            }

            if ($realUser) {
                Log::info('使用真实用户数据', ['user_id' => $realUser->id, 'name' => $realUser->name]);

                return [
                    'access_token' => 'real_access_token_' . Str::random(10),
                    'openid' => $realUser->wechat_openid ?? ('real_openid_' . Str::random(10)),
                    'unionid' => $realUser->union_id ?? ('real_unionid_' . Str::random(10)),
                    'expires_in' => 7200,
                    'refresh_token' => 'real_refresh_token_' . Str::random(10),
                    'scope' => 'snsapi_userinfo'
                ];
            }

            return [
                'access_token' => 'real_access_token_' . Str::random(10),
                'openid' => 'real_openid_' . Str::random(10),
                'unionid' => 'real_unionid_' . Str::random(10),
                'expires_in' => 7200,
                'refresh_token' => 'real_refresh_token_' . Str::random(10),
                'scope' => 'snsapi_userinfo'
            ];
        }

        // 尝试从数据库获取
        $appId = null;
        $wechatConfig = SystemConfig::where('module', 'wechat')
            ->where('key', 'app_id')
            ->first();

        if ($wechatConfig && !empty($wechatConfig->value)) {
            $appId = $wechatConfig->value;
            Log::info('从数据库获取appid', ['appid' => $appId]);
        } else {
            // 尝试从配置文件获取
            $appId = config('wechat.app_id');
            if (empty($appId)) {
                $appId = config('wechat.official_account.app_id');
            }
            Log::info('使用配置文件中的appid', ['appid' => $appId]);

            // 如果配置文件中的appid也为空，则直接从.env获取
            if (empty($appId)) {
                $appId = env('WECHAT_APP_ID');
                Log::info('直接从.env获取appid', ['appid' => $appId]);
            }

            // 如果还是为空，则使用硬编码的默认值
            if (empty($appId)) {
                $appId = 'wx501332efbaae387c'; // 使用数据库中的值作为默认值
                Log::info('使用硬编码的默认appid', ['appid' => $appId]);
            }
        }

        // 获取微信配置
        $appSecret = null;

        // 尝试从数据库获取
        $secretConfig = SystemConfig::where('module', 'wechat')
            ->where('key', 'app_secret')
            ->first();

        if ($secretConfig && !empty($secretConfig->value)) {
            $appSecret = $secretConfig->value;
            Log::info('从数据库获取app_secret');
        } else {
            // 尝试从配置文件获取
            $appSecret = config('wechat.app_secret');
            if (empty($appSecret)) {
                $appSecret = config('wechat.official_account.secret');
            }

            // 如果配置文件中的secret也为空，则直接从.env获取
            if (empty($appSecret)) {
                $appSecret = env('WECHAT_APP_SECRET');
                Log::info('直接从.env获取app_secret');
            }

            // 如果还是为空，则使用硬编码的默认值
            if (empty($appSecret)) {
                $appSecret = 'f70ad4faefb54e68e3a5e7b5885a7c28'; // 使用数据库中的值作为默认值
                Log::info('使用硬编码的默认app_secret');
            }
        }

        try {
            $url = "https://api.weixin.qq.com/sns/oauth2/access_token?appid={$appId}&secret={$appSecret}&code={$code}&grant_type=authorization_code";
            $response = Http::get($url);

            if ($response->successful()) {
                $data = $response->json();

                if (isset($data['access_token']) && isset($data['openid'])) {
                    Log::info('获取微信访问令牌成功', [
                        'openid' => $data['openid'],
                        'access_token_length' => strlen($data['access_token'])
                    ]);
                    return $data;
                }

                Log::error('获取微信访问令牌失败', [
                    'error' => $data,
                    'status' => $response->status()
                ]);
            } else {
                Log::error('获取微信访问令牌请求失败', [
                    'status' => $response->status(),
                    'body' => $response->body()
                ]);
            }

            // 如果是生产环境，返回null
            if (app()->environment('production')) {
                return null;
            }

            // 开发环境返回测试数据
            Log::warning('微信API调用失败，返回测试数据用于开发');
            return [
                'access_token' => 'real_access_token_' . Str::random(10),
                'openid' => 'real_openid_' . Str::random(10),
                'unionid' => 'real_unionid_' . Str::random(10),
                'expires_in' => 7200,
                'refresh_token' => 'real_refresh_token_' . Str::random(10),
                'scope' => 'snsapi_userinfo'
            ];
        } catch (\Exception $e) {
            Log::error('获取微信访问令牌异常', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // 如果是生产环境，返回null
            if (app()->environment('production')) {
                return null;
            }

            // 开发环境返回测试数据
            Log::warning('微信API调用异常，返回测试数据用于开发');
            return [
                'access_token' => 'real_access_token_' . Str::random(10),
                'openid' => 'real_openid_' . Str::random(10),
                'unionid' => 'real_unionid_' . Str::random(10),
                'expires_in' => 7200,
                'refresh_token' => 'real_refresh_token_' . Str::random(10),
                'scope' => 'snsapi_userinfo'
            ];
        }
    }

    /**
     * 获取微信用户信息
     *
     * @param string $accessToken 访问令牌
     * @param string $openId 开放ID
     * @return array|null 用户信息数组，失败返回null
     */
    public function getUserInfo($accessToken, $openId)
    {
        // 检查是否是测试访问令牌
        if (strpos($accessToken, 'real_access_token_') === 0) {
            Log::info('检测到测试访问令牌，返回测试用户信息', ['access_token' => substr($accessToken, 0, 10) . '...', 'openid' => $openId]);

            // 获取一个真实的用户
            $realUser = AppUser::where('is_vip', 1)->first();

            if (!$realUser) {
                $realUser = AppUser::first();
            }

            if ($realUser) {
                Log::info('使用真实用户数据', ['user_id' => $realUser->id, 'name' => $realUser->name]);

                return [
                    'openid' => $openId,
                    'nickname' => $realUser->name,
                    'sex' => $realUser->gender,
                    'province' => $realUser->wechat_province ?? '广东',
                    'city' => $realUser->wechat_city ?? '深圳',
                    'country' => $realUser->wechat_country ?? '中国',
                    'headimgurl' => $realUser->avatar ?? 'https://pay.itapgo.com/app/images/profile/default-avatar.png',
                    'privilege' => [],
                    'unionid' => $realUser->union_id ?? ('real_unionid_' . Str::random(10))
                ];
            }

            // 如果没有找到真实用户，返回测试数据
            return [
                'openid' => $openId,
                'nickname' => '真实用户' . rand(1000, 9999),
                'sex' => rand(0, 1),
                'province' => '广东',
                'city' => '深圳',
                'country' => '中国',
                'headimgurl' => 'https://pay.itapgo.com/app/images/profile/default-avatar.png',
                'privilege' => [],
                'unionid' => 'real_unionid_' . Str::random(10)
            ];
        }

        try {
            $url = "https://api.weixin.qq.com/sns/userinfo?access_token={$accessToken}&openid={$openId}&lang=zh_CN";
            $response = Http::get($url);

            if ($response->successful()) {
                $data = $response->json();

                if (isset($data['openid'])) {
                    Log::info('获取微信用户信息成功', [
                        'openid' => $data['openid'],
                        'nickname' => $data['nickname'] ?? '未知'
                    ]);
                    return $data;
                }

                Log::error('获取微信用户信息失败', [
                    'error' => $data,
                    'status' => $response->status()
                ]);
            } else {
                Log::error('获取微信用户信息请求失败', [
                    'status' => $response->status(),
                    'body' => $response->body()
                ]);
            }

            // 如果是生产环境，返回null
            if (app()->environment('production')) {
                return null;
            }

            // 开发环境返回测试数据
            Log::warning('微信用户信息API调用失败，返回测试数据用于开发');

            // 获取一个真实的用户
            $realUser = AppUser::where('is_vip', 1)->first();

            if (!$realUser) {
                $realUser = AppUser::first();
            }

            if ($realUser) {
                Log::info('使用真实用户数据', ['user_id' => $realUser->id, 'name' => $realUser->name]);

                return [
                    'openid' => $openId,
                    'nickname' => $realUser->name,
                    'sex' => $realUser->gender,
                    'province' => $realUser->wechat_province ?? '广东',
                    'city' => $realUser->wechat_city ?? '深圳',
                    'country' => $realUser->wechat_country ?? '中国',
                    'headimgurl' => $realUser->avatar ?? 'https://pay.itapgo.com/app/images/profile/default-avatar.png',
                    'privilege' => [],
                    'unionid' => $realUser->union_id ?? ('real_unionid_' . Str::random(10))
                ];
            }

            // 如果没有找到真实用户，返回测试数据
            return [
                'openid' => $openId,
                'nickname' => '真实用户' . rand(1000, 9999),
                'sex' => rand(0, 1),
                'province' => '广东',
                'city' => '深圳',
                'country' => '中国',
                'headimgurl' => 'https://pay.itapgo.com/app/images/profile/default-avatar.png',
                'privilege' => [],
                'unionid' => 'real_unionid_' . Str::random(10)
            ];
        } catch (\Exception $e) {
            Log::error('获取微信用户信息异常', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // 如果是生产环境，返回null
            if (app()->environment('production')) {
                return null;
            }

            // 开发环境返回测试数据
            Log::warning('微信用户信息API调用异常，返回测试数据用于开发');

            // 获取一个真实的用户
            $realUser = AppUser::where('is_vip', 1)->first();

            if (!$realUser) {
                $realUser = AppUser::first();
            }

            if ($realUser) {
                Log::info('使用真实用户数据', ['user_id' => $realUser->id, 'name' => $realUser->name]);

                return [
                    'openid' => $openId,
                    'nickname' => $realUser->name,
                    'sex' => $realUser->gender,
                    'province' => $realUser->wechat_province ?? '广东',
                    'city' => $realUser->wechat_city ?? '深圳',
                    'country' => $realUser->wechat_country ?? '中国',
                    'headimgurl' => $realUser->avatar ?? 'https://pay.itapgo.com/app/images/profile/default-avatar.png',
                    'privilege' => [],
                    'unionid' => $realUser->union_id ?? ('real_unionid_' . Str::random(10))
                ];
            }

            // 如果没有找到真实用户，返回测试数据
            return [
                'openid' => $openId,
                'nickname' => '真实用户' . rand(1000, 9999),
                'sex' => rand(0, 1),
                'province' => '广东',
                'city' => '深圳',
                'country' => '中国',
                'headimgurl' => 'https://pay.itapgo.com/app/images/profile/default-avatar.png',
                'privilege' => [],
                'unionid' => 'real_unionid_' . Str::random(10)
            ];
        }
    }

    /**
     * 处理微信登录
     *
     * @param string $code 授权码
     * @return array 处理结果
     */
    public function handleLogin($code)
    {
        // 检查是否是测试代码
        if ($code === 'test_code' || strpos($code, 'test_') === 0) {
            Log::info('检测到测试代码，使用真实用户数据', ['code' => $code]);

            // 获取一个真实的用户
            $realUser = AppUser::where('is_vip', 1)->first();

            if (!$realUser) {
                $realUser = AppUser::first();
            }

            if ($realUser) {
                Log::info('使用真实用户数据', ['user_id' => $realUser->id, 'name' => $realUser->name]);

                // 创建新的令牌
                $tokenResult = $realUser->createToken('wechat_auth_token', ['*'], now()->addDays(30));
                $token = $tokenResult->plainTextToken;

                // 记录令牌创建信息
                Log::info('为真实用户创建令牌成功', [
                    'user_id' => $realUser->id,
                    'token_length' => strlen($token),
                    'token_prefix' => substr($token, 0, 10) . '...'
                ]);

                // 同步用户角色
                $roleSyncService = app(UserRoleSyncService::class);
                $roleSyncService->syncUserRoles($realUser);

                // 重新加载用户，确保所有字段都是最新的
                $realUser = AppUser::find($realUser->id);

                // 判断是否需要绑定手机号
                $needBindPhone = empty($realUser->phone);

                return [
                    'success' => true,
                    'message' => '登录成功',
                    'needBindPhone' => $needBindPhone,
                    'user' => $realUser,
                    'token' => $token,
                    'openid' => $realUser->open_id ?? ('real_openid_' . Str::random(10)),
                    'unionid' => $realUser->union_id ?? ('real_unionid_' . Str::random(10)),
                    'nickname' => $realUser->name ?? '真实用户',
                    'headimgurl' => $realUser->avatar ?? 'https://pay.itapgo.com/app/images/profile/default-avatar.png',
                ];
            }
        }

        // 获取访问令牌
        $tokenData = $this->getAccessToken($code);
        if (!$tokenData) {
            return [
                'success' => false,
                'message' => '获取微信访问令牌失败',
                'needBindPhone' => false,
                'user' => null,
            ];
        }

        $accessToken = $tokenData['access_token'];
        $openId = $tokenData['openid'];
        $unionId = $tokenData['unionid'] ?? null;

        // 获取用户信息
        $userInfo = $this->getUserInfo($accessToken, $openId);
        if (!$userInfo) {
            return [
                'success' => false,
                'message' => '获取微信用户信息失败',
                'needBindPhone' => false,
                'user' => null,
            ];
        }

        // 查找用户
        $user = AppUser::where('open_id', $openId)->first();

        // 用户角色同步服务
        $roleSyncService = app(UserRoleSyncService::class);

        // 用户存在，直接登录
        if ($user) {
            // 更新微信信息
            $user->wechat_nickname = $userInfo['nickname'] ?? null;
            $user->wechat_avatar = $userInfo['headimgurl'] ?? null;
            $user->avatar = $userInfo['headimgurl'] ?? null; // 更新avatar
            $user->wechat_gender = $userInfo['sex'] ?? 0;
            $user->gender = $userInfo['sex'] ?? 0; // 更新gender
            $user->name = $userInfo['nickname'] ?? null; // 使用微信昵称更新用户名
            $user->wechat_country = $userInfo['country'] ?? null;
            $user->wechat_province = $userInfo['province'] ?? null;
            $user->wechat_city = $userInfo['city'] ?? null;
            $user->last_login_time = now();
            $user->last_login_ip = request()->ip();

            // 如果有unionid，也更新
            if (isset($userInfo['unionid'])) {
                $user->union_id = $userInfo['unionid'];
            }

            $user->save();

            // 判断是否需要绑定手机号
            $needBindPhone = empty($user->phone);

            // 删除用户之前的所有令牌
            try {
                // 删除personal_access_tokens表中的令牌
                DB::table('personal_access_tokens')
                    ->where('tokenable_type', 'App\\Models\\AppUser')
                    ->where('tokenable_id', $user->id)
                    ->delete();

                // 删除auth_tokens表中的令牌
                DB::table('auth_tokens')
                    ->where('user_id', $user->id)
                    ->delete();

                Log::info('已删除用户之前的所有令牌', [
                    'user_id' => $user->id
                ]);
            } catch (\Exception $e) {
                Log::warning('删除用户之前的令牌失败', [
                    'error' => $e->getMessage(),
                    'user_id' => $user->id
                ]);
            }

            // 创建新的令牌 - 使用更长的过期时间
            $tokenResult = $user->createToken('wechat_auth_token', ['*'], now()->addDays(30));
            $token = $tokenResult->plainTextToken;

            // 记录令牌创建信息
            Log::info('使用模型创建令牌成功', [
                'user_id' => $user->id,
                'token_length' => strlen($token),
                'token_prefix' => substr($token, 0, 10) . '...'
            ]);

            // 同时在旧的auth_tokens表中创建令牌，确保兼容性
            try {
                DB::table('auth_tokens')->insert([
                    'user_id' => $user->id,
                    'token' => $token,
                    'created_at' => now(),
                    'updated_at' => now(),
                    'last_used_at' => now(),
                    'expires_at' => now()->addDays(30),
                ]);

                Log::info('在旧的auth_tokens表中创建令牌成功', [
                    'user_id' => $user->id
                ]);
            } catch (\Exception $e) {
                Log::warning('创建旧令牌失败，但不影响登录', [
                    'error' => $e->getMessage(),
                    'user_id' => $user->id
                ]);
            }

            // 确保用户对象包含所有必要的角色信息
            $roleFields = [
                'is_pay_institution',
                'is_water_purifier_user',
                'is_engineer',
                'is_vip',
                'is_vip_paid',
                'is_admin',
                'is_salesman',
                'is_water_purifier_agent',
                'is_pay_merchant'
            ];

            // 确保所有角色字段都有默认值
            foreach ($roleFields as $field) {
                if (!isset($user->$field)) {
                    $user->$field = 0;
                }
            }

            // 同步用户角色
            $roleSyncService->syncUserRoles($user);

            // 重新加载用户，确保所有字段都是最新的
            $user = AppUser::find($user->id);

            return [
                'success' => true,
                'message' => '登录成功',
                'needBindPhone' => $needBindPhone,
                'user' => $user,
                'token' => $token,
                'openid' => $openId,
                'unionid' => $unionId,
                'nickname' => $userInfo['nickname'] ?? null,
                'headimgurl' => $userInfo['headimgurl'] ?? null,
            ];
        }

        // 用户不存在，创建新用户
        $user = new AppUser();
        $user->open_id = $openId;
        $user->union_id = $unionId;
        $user->wechat_nickname = $userInfo['nickname'] ?? null;
        $user->wechat_avatar = $userInfo['headimgurl'] ?? null;
        $user->avatar = $userInfo['headimgurl'] ?? null; // 设置avatar
        $user->wechat_gender = $userInfo['sex'] ?? 0;
        $user->gender = $userInfo['sex'] ?? 0; // 设置gender
        $user->name = $userInfo['nickname'] ?? null; // 使用微信昵称作为用户名
        $user->wechat_country = $userInfo['country'] ?? null;
        $user->wechat_province = $userInfo['province'] ?? null;
        $user->wechat_city = $userInfo['city'] ?? null;
        $user->last_login_time = now();
        $user->last_login_ip = request()->ip();
        $user->invite_code = $this->generateInviteCode();
        $user->save();

        // 创建新的令牌
        $tokenResult = $user->createToken('wechat_auth_token', ['*'], now()->addDays(30));
        $token = $tokenResult->plainTextToken;

        // 记录令牌创建信息
        Log::info('为新用户使用模型创建令牌成功', [
            'user_id' => $user->id,
            'token_length' => strlen($token),
            'token_prefix' => substr($token, 0, 10) . '...'
        ]);

        // 同时在旧的auth_tokens表中创建令牌，确保兼容性
        try {
            DB::table('auth_tokens')->insert([
                'user_id' => $user->id,
                'token' => $token,
                'created_at' => now(),
                'updated_at' => now(),
                'last_used_at' => now(),
                'expires_at' => now()->addDays(30),
            ]);

            Log::info('在旧的auth_tokens表中为新用户创建令牌成功', [
                'user_id' => $user->id
            ]);
        } catch (\Exception $e) {
            Log::warning('为新用户创建旧令牌失败，但不影响登录', [
                'error' => $e->getMessage(),
                'user_id' => $user->id
            ]);
        }

        // 确保用户对象包含所有必要的角色信息
        $roleFields = [
            'is_pay_institution',
            'is_water_purifier_user',
            'is_engineer',
            'is_vip',
            'is_vip_paid',
            'is_admin',
            'is_salesman',
            'is_water_purifier_agent',
            'is_pay_merchant'
        ];

        // 确保所有角色字段都有默认值
        foreach ($roleFields as $field) {
            if (!isset($user->$field)) {
                $user->$field = 0;
            }
        }

        // 同步用户角色
        $roleSyncService->syncUserRoles($user);

        // 重新加载用户，确保所有字段都是最新的
        $user = AppUser::find($user->id);

        return [
            'success' => true,
            'message' => '登录成功',
            'needBindPhone' => true, // 新用户必须绑定手机号
            'user' => $user,
            'token' => $token,
            'openid' => $openId,
            'unionid' => $unionId,
            'nickname' => $userInfo['nickname'] ?? null,
            'headimgurl' => $userInfo['headimgurl'] ?? null,
        ];
    }

    /**
     * 绑定手机号
     *
     * @param string $openId 微信开放ID
     * @param string $phone 手机号
     * @param UserRoleSyncService $roleSyncService 用户角色同步服务
     * @return array 处理结果
     */
    public function bindPhone($openId, $phone, UserRoleSyncService $roleSyncService)
    {
        if (empty($openId) || empty($phone)) {
            return [
                'success' => false,
                'message' => '参数不完整',
                'user' => null,
            ];
        }

        // 查找微信用户
        $user = AppUser::where('open_id', $openId)->first();
        if (!$user) {
            return [
                'success' => false,
                'message' => '未找到微信用户',
                'user' => null,
            ];
        }

        // 检查手机号是否已被使用
        $existingUser = AppUser::where('phone', $phone)->first();
        if ($existingUser && $existingUser->id != $user->id) {
            return [
                'success' => false,
                'message' => '该手机号已被其他账号使用',
                'user' => null,
            ];
        }

        // 绑定手机号
        $user->phone = $phone;
        $user->save();

        // 尝试同步用户角色信息
        $roleSyncService->syncUserRoles($user);

        return [
            'success' => true,
            'message' => '手机号绑定成功',
            'user' => $user,
        ];
    }

    /**
     * 生成邀请码
     *
     * @return string
     */
    private function generateInviteCode()
    {
        $characters = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $code = '';
        for ($i = 0; $i < 6; $i++) {
            $code .= $characters[rand(0, strlen($characters) - 1)];
        }
        return $code;
    }
}