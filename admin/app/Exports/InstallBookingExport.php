<?php

namespace App\Exports;

use App\Models\Installation\InstallBooking;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class InstallBookingExport implements FromCollection, WithHeadings, WithMapping
{
    protected $request;

    public function __construct(Request $request)
    {
        $this->request = $request;
    }

    /**
     * @return \Illuminate\Support\Collection
     */
    public function collection()
    {
        $query = InstallBooking::query()
            ->leftJoin('app_users as u', 'install_bookings.user_id', '=', 'u.id')
            ->leftJoin('app_users as r', 'install_bookings.referrer_id', '=', 'r.id')
            ->leftJoin('app_users as e', 'install_bookings.engineer_id', '=', 'e.id')
            ->select(
                'install_bookings.*',
                'u.name as user_name',
                'u.wechat_nickname as user_nickname',
                'u.phone as user_phone',
                'r.name as referrer_name',
                'r.wechat_nickname as referrer_nickname',
                'e.name as engineer_name',
                'e.phone as engineer_phone'
            );

        // 关键词搜索
        if ($this->request->has('keyword') && $this->request->keyword) {
            $keyword = $this->request->keyword;
            $query->where(function ($q) use ($keyword) {
                $q->where('install_bookings.contact_name', 'like', "%{$keyword}%")
                  ->orWhere('install_bookings.contact_phone', 'like', "%{$keyword}%")
                  ->orWhere('install_bookings.address', 'like', "%{$keyword}%")
                  ->orWhere('install_bookings.booking_no', 'like', "%{$keyword}%");
            });
        }

        // 状态筛选
        if ($this->request->has('status') && $this->request->status) {
            $query->where('install_bookings.status', $this->request->status);
        }

        // 日期范围筛选
        if ($this->request->has('start_date') && $this->request->start_date) {
            $query->whereDate('install_bookings.created_at', '>=', $this->request->start_date);
        }
        
        if ($this->request->has('end_date') && $this->request->end_date) {
            $query->whereDate('install_bookings.created_at', '<=', $this->request->end_date);
        }

        return $query->orderBy('install_bookings.created_at', 'desc')->get();
    }

    /**
     * @return array
     */
    public function headings(): array
    {
        return [
            '预约编号',
            '联系人',
            '联系电话',
            '安装地址',
            '安装时间',
            '套餐类型',
            '套餐价格',
            '安装费',
            '总金额',
            '支付状态',
            '支付时间',
            '支付方式',
            '交易号',
            '状态',
            '工程师',
            '工程师电话',
            '完成时间',
            '取消原因',
            '设备ID',
            '用户姓名',
            '用户昵称',
            '用户电话',
            '推荐人',
            '推荐人昵称',
            '备注',
            '创建时间',
            '更新时间'
        ];
    }

    /**
     * @param mixed $row
     * @return array
     */
    public function map($row): array
    {
        // 准备支付状态文本
        $paymentStatusMap = [
            'unpaid' => '未支付',
            'paid' => '已支付',
            'refunded' => '已退款',
            'failed' => '支付失败'
        ];
        
        // 准备预约状态文本
        $statusMap = [
            'pending' => '待处理',
            'confirmed' => '已确认',
            'assigned' => '已分配',
            'in_progress' => '进行中',
            'completed' => '已完成',
            'cancelled' => '已取消'
        ];
        
        // 准备套餐类型文本
        $packageTypeMap = [
            'personal' => '个人套餐',
            'unlimited' => '无限续用套餐',
            'business_year' => '商业年费套餐',
            'business_flow' => '商业流量套餐'
        ];

        return [
            $row->booking_no,
            $row->contact_name,
            $row->contact_phone,
            $row->install_address,
            $row->install_time ? date('Y-m-d H:i:s', strtotime($row->install_time)) : '',
            $packageTypeMap[$row->package_type] ?? $row->package_type,
            $row->package_price,
            $row->installation_fee,
            $row->total_amount,
            $paymentStatusMap[$row->payment_status] ?? $row->payment_status,
            $row->payment_time ? date('Y-m-d H:i:s', strtotime($row->payment_time)) : '',
            $row->payment_method,
            $row->transaction_id,
            $statusMap[$row->status] ?? $row->status,
            $row->engineer_name,
            $row->engineer_phone,
            $row->completion_time ? date('Y-m-d H:i:s', strtotime($row->completion_time)) : '',
            $row->cancellation_reason,
            $row->device_id,
            $row->user_name,
            $row->user_nickname,
            $row->user_phone,
            $row->referrer_name,
            $row->referrer_nickname,
            $row->remarks,
            date('Y-m-d H:i:s', strtotime($row->created_at)),
            date('Y-m-d H:i:s', strtotime($row->updated_at))
        ];
    }
} 