<?php

namespace App\Exports;

use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use Illuminate\Http\Request;

class BookingExport implements FromCollection, WithHeadings, WithMapping, ShouldAutoSize, WithStyles
{
    protected $request;
    
    public function __construct(Request $request)
    {
        $this->request = $request;
    }
    
    /**
     * @return \Illuminate\Support\Collection
     */
    public function collection()
    {
        $query = DB::table('install_bookings')
            ->leftJoin('app_users', 'install_bookings.user_id', '=', 'app_users.id')
            ->leftJoin('installation_engineers', 'install_bookings.engineer_id', '=', 'installation_engineers.id')
            ->select(
                'install_bookings.*',
                'app_users.wechat_nickname as user_display_name',
                'app_users.phone as user_phone',
                'installation_engineers.name as engineer_name'
            );
            
        // 关键词搜索
        if ($this->request->has('keyword') && $this->request->keyword) {
            $keyword = $this->request->keyword;
            $query->where(function ($q) use ($keyword) {
                $q->where('install_bookings.contact_name', 'like', "%{$keyword}%")
                  ->orWhere('install_bookings.contact_phone', 'like', "%{$keyword}%")
                  ->orWhere('install_bookings.install_address', 'like', "%{$keyword}%")
                  ->orWhere('install_bookings.booking_no', 'like', "%{$keyword}%");
            });
        }
        
        // 状态筛选
        if ($this->request->has('status') && $this->request->status) {
            $query->where('install_bookings.status', $this->request->status);
        }
        
        // 日期范围筛选
        if ($this->request->has('start_date') && $this->request->has('end_date')) {
            $query->whereBetween('install_bookings.install_time', [$this->request->start_date, $this->request->end_date]);
        }
        
        return $query->orderBy('install_bookings.created_at', 'desc')->get();
    }
    
    /**
     * @return array
     */
    public function headings(): array
    {
        return [
            'ID',
            '预约单号',
            '用户',
            '联系人',
            '联系电话',
            '安装地址',
            '预约时间',
            '套餐类型',
            '套餐价格',
            '安装费用',
            '总金额',
            '状态',
            '工程师',
            '创建时间',
            '更新时间',
            '完成时间',
            '取消原因',
            '备注'
        ];
    }
    
    /**
     * @param mixed $row
     * @return array
     */
    public function map($row): array
    {
        return [
            $row->id,
            $row->booking_no,
            $row->user_display_name,
            $row->contact_name,
            $row->contact_phone,
            $row->install_address,
            $row->install_time,
            $this->getPackageTypeText($row->package_type),
            $row->package_price,
            $row->installation_fee,
            $row->total_amount,
            $this->getStatusText($row->status),
            $row->engineer_name,
            $row->created_at,
            $row->updated_at,
            $row->completion_time,
            $row->cancellation_reason,
            $row->remarks
        ];
    }
    
    /**
     * @param Worksheet $sheet
     * @return void
     */
    public function styles(Worksheet $sheet)
    {
        return [
            1 => ['font' => ['bold' => true]],
        ];
    }
    
    /**
     * 获取状态文本
     */
    private function getStatusText($status)
    {
        $statusMap = [
            'pending' => '待处理',
            'confirmed' => '已确认',
            'assigned' => '已分配',
            'in_progress' => '进行中',
            'completed' => '已完成',
            'cancelled' => '已取消'
        ];
        
        return $statusMap[$status] ?? $status;
    }
    
    /**
     * 获取套餐类型文本
     */
    private function getPackageTypeText($type)
    {
        $typeMap = [
            'basic' => '基础套餐',
            'premium' => '高级套餐',
            'custom' => '定制套餐',
            'personal' => '个人/企业套餐',
            'unlimited' => '无限畅饮套餐',
            'business_year' => '商务机包年套餐',
            'business_flow' => '商务机流量套餐'
        ];
        
        return $typeMap[$type] ?? $type;
    }
}