<?php

namespace App\Exports;

use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\WithTitle;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use Illuminate\Http\Request;
use Carbon\Carbon;

class InstallationStatisticsExport implements FromCollection, WithHeadings, WithMapping, ShouldAutoSize, WithStyles, WithTitle
{
    protected $request;
    
    public function __construct(Request $request)
    {
        $this->request = $request;
    }
    
    /**
     * @return \Illuminate\Support\Collection
     */
    public function collection()
    {
        // 获取日期范围
        $dateStart = $this->request->date_start ?? Carbon::now()->subDays(30)->format('Y-m-d');
        $dateEnd = $this->request->date_end ?? Carbon::now()->format('Y-m-d');
        
        // 获取统计类型
        $type = $this->request->type ?? 'daily';
        
        // 工程师ID
        $engineerId = $this->request->engineer_id ?? null;
        
        $results = [];
        
        // 基础查询
        $query = DB::table('install_bookings')
            ->whereBetween('booking_date', [$dateStart, $dateEnd]);
        
        // 如果指定了工程师ID，则只统计该工程师的数据
        if ($engineerId) {
            $query->where('engineer_id', $engineerId);
        }
        
        switch ($type) {
            case 'daily':
                // 按日统计
                $start = Carbon::parse($dateStart);
                $end = Carbon::parse($dateEnd);
                
                for ($date = $start; $date->lte($end); $date->addDay()) {
                    $currentDate = $date->format('Y-m-d');
                    $dayQuery = clone $query;
                    $dayQuery->where('booking_date', $currentDate);
                    
                    $results[] = $this->getStatsByDate($dayQuery, $currentDate);
                }
                break;
                
            case 'weekly':
                // 按周统计
                $start = Carbon::parse($dateStart)->startOfWeek();
                $end = Carbon::parse($dateEnd)->endOfWeek();
                
                for ($date = $start; $date->lte($end); $date->addWeek()) {
                    $weekStart = $date->format('Y-m-d');
                    $weekEnd = $date->copy()->addDays(6)->format('Y-m-d');
                    
                    $weekQuery = clone $query;
                    $weekQuery->whereBetween('booking_date', [$weekStart, $weekEnd]);
                    
                    $weekNumber = $date->weekOfYear;
                    $year = $date->year;
                    
                    $results[] = $this->getStatsByWeek($weekQuery, $year, $weekNumber, $weekStart, $weekEnd);
                }
                break;
                
            case 'monthly':
                // 按月统计
                $start = Carbon::parse($dateStart)->startOfMonth();
                $end = Carbon::parse($dateEnd)->endOfMonth();
                
                for ($date = $start; $date->lte($end); $date->addMonth()) {
                    $monthStart = $date->copy()->startOfMonth()->format('Y-m-d');
                    $monthEnd = $date->copy()->endOfMonth()->format('Y-m-d');
                    
                    $monthQuery = clone $query;
                    $monthQuery->whereBetween('booking_date', [$monthStart, $monthEnd]);
                    
                    $month = $date->format('Y-m');
                    
                    $results[] = $this->getStatsByMonth($monthQuery, $month);
                }
                break;
        }
        
        return collect($results);
    }
    
    /**
     * 获取单日统计数据
     */
    private function getStatsByDate($dayQuery, $date)
    {
        $totalBookings = $dayQuery->count();
        
        // 获取不同状态的预约数
        $pendingQuery = clone $dayQuery;
        $pendingBookings = $pendingQuery->where('status', 'pending')->count();
        
        $confirmedQuery = clone $dayQuery;
        $confirmedBookings = $confirmedQuery->where('status', 'confirmed')->count();
        
        $assignedQuery = clone $dayQuery;
        $assignedBookings = $assignedQuery->where('status', 'assigned')->count();
        
        $inProgressQuery = clone $dayQuery;
        $inProgressBookings = $inProgressQuery->where('status', 'in_progress')->count();
        
        $completedQuery = clone $dayQuery;
        $completedBookings = $completedQuery->where('status', 'completed')->count();
        
        $cancelledQuery = clone $dayQuery;
        $cancelledBookings = $cancelledQuery->where('status', 'cancelled')->count();
        
        // 计算完成率
        $completionRate = $totalBookings > 0 ? $completedBookings / $totalBookings : 0;
        
        // 获取平均安装时长（小时）
        $avgDuration = $completedBookings > 0 ? 
            $completedQuery->whereNotNull('completed_at')
                ->avg(DB::raw('TIMESTAMPDIFF(HOUR, booking_date, completed_at)')) : null;
        
        return [
            'date_type' => 'date',
            'date_label' => $date,
            'total_bookings' => $totalBookings,
            'pending_bookings' => $pendingBookings,
            'confirmed_bookings' => $confirmedBookings,
            'assigned_bookings' => $assignedBookings,
            'in_progress_bookings' => $inProgressBookings,
            'completed_bookings' => $completedBookings,
            'cancelled_bookings' => $cancelledBookings,
            'completion_rate' => $completionRate,
            'avg_duration' => $avgDuration
        ];
    }
    
    /**
     * 获取单周统计数据
     */
    private function getStatsByWeek($weekQuery, $year, $weekNumber, $weekStart, $weekEnd)
    {
        $totalBookings = $weekQuery->count();
        
        // 获取不同状态的预约数
        $pendingQuery = clone $weekQuery;
        $pendingBookings = $pendingQuery->where('status', 'pending')->count();
        
        $confirmedQuery = clone $weekQuery;
        $confirmedBookings = $confirmedQuery->where('status', 'confirmed')->count();
        
        $assignedQuery = clone $weekQuery;
        $assignedBookings = $assignedQuery->where('status', 'assigned')->count();
        
        $inProgressQuery = clone $weekQuery;
        $inProgressBookings = $inProgressQuery->where('status', 'in_progress')->count();
        
        $completedQuery = clone $weekQuery;
        $completedBookings = $completedQuery->where('status', 'completed')->count();
        
        $cancelledQuery = clone $weekQuery;
        $cancelledBookings = $cancelledQuery->where('status', 'cancelled')->count();
        
        // 计算完成率
        $completionRate = $totalBookings > 0 ? $completedBookings / $totalBookings : 0;
        
        // 获取平均安装时长（小时）
        $avgDuration = $completedBookings > 0 ? 
            $completedQuery->whereNotNull('completed_at')
                ->avg(DB::raw('TIMESTAMPDIFF(HOUR, booking_date, completed_at)')) : null;
        
        return [
            'date_type' => 'week',
            'date_label' => "{$year}年第{$weekNumber}周 ({$weekStart} ~ {$weekEnd})",
            'total_bookings' => $totalBookings,
            'pending_bookings' => $pendingBookings,
            'confirmed_bookings' => $confirmedBookings,
            'assigned_bookings' => $assignedBookings,
            'in_progress_bookings' => $inProgressBookings,
            'completed_bookings' => $completedBookings,
            'cancelled_bookings' => $cancelledBookings,
            'completion_rate' => $completionRate,
            'avg_duration' => $avgDuration
        ];
    }
    
    /**
     * 获取单月统计数据
     */
    private function getStatsByMonth($monthQuery, $month)
    {
        $totalBookings = $monthQuery->count();
        
        // 获取不同状态的预约数
        $pendingQuery = clone $monthQuery;
        $pendingBookings = $pendingQuery->where('status', 'pending')->count();
        
        $confirmedQuery = clone $monthQuery;
        $confirmedBookings = $confirmedQuery->where('status', 'confirmed')->count();
        
        $assignedQuery = clone $monthQuery;
        $assignedBookings = $assignedQuery->where('status', 'assigned')->count();
        
        $inProgressQuery = clone $monthQuery;
        $inProgressBookings = $inProgressQuery->where('status', 'in_progress')->count();
        
        $completedQuery = clone $monthQuery;
        $completedBookings = $completedQuery->where('status', 'completed')->count();
        
        $cancelledQuery = clone $monthQuery;
        $cancelledBookings = $cancelledQuery->where('status', 'cancelled')->count();
        
        // 计算完成率
        $completionRate = $totalBookings > 0 ? $completedBookings / $totalBookings : 0;
        
        // 获取平均安装时长（小时）
        $avgDuration = $completedBookings > 0 ? 
            $completedQuery->whereNotNull('completed_at')
                ->avg(DB::raw('TIMESTAMPDIFF(HOUR, booking_date, completed_at)')) : null;
        
        return [
            'date_type' => 'month',
            'date_label' => $month,
            'total_bookings' => $totalBookings,
            'pending_bookings' => $pendingBookings,
            'confirmed_bookings' => $confirmedBookings,
            'assigned_bookings' => $assignedBookings,
            'in_progress_bookings' => $inProgressBookings,
            'completed_bookings' => $completedBookings,
            'cancelled_bookings' => $cancelledBookings,
            'completion_rate' => $completionRate,
            'avg_duration' => $avgDuration
        ];
    }
    
    /**
     * @return array
     */
    public function headings(): array
    {
        $type = $this->request->type ?? 'daily';
        
        $dateLabel = '日期';
        if ($type === 'weekly') {
            $dateLabel = '周次';
        } else if ($type === 'monthly') {
            $dateLabel = '月份';
        }
        
        return [
            $dateLabel,
            '预约总数',
            '待处理',
            '已确认',
            '已分配',
            '进行中',
            '已完成',
            '已取消',
            '完成率',
            '平均安装时长(小时)'
        ];
    }
    
    /**
     * @param mixed $row
     * @return array
     */
    public function map($row): array
    {
        return [
            $row['date_label'],
            $row['total_bookings'],
            $row['pending_bookings'],
            $row['confirmed_bookings'],
            $row['assigned_bookings'],
            $row['in_progress_bookings'],
            $row['completed_bookings'],
            $row['cancelled_bookings'],
            number_format($row['completion_rate'] * 100, 2) . '%',
            $row['avg_duration'] ? number_format($row['avg_duration'], 2) : '-'
        ];
    }
    
    /**
     * @param Worksheet $sheet
     * @return void
     */
    public function styles(Worksheet $sheet)
    {
        return [
            1 => ['font' => ['bold' => true]],
        ];
    }
    
    /**
     * @return string
     */
    public function title(): string
    {
        $type = $this->request->type ?? 'daily';
        
        $title = '日报表';
        if ($type === 'weekly') {
            $title = '周报表';
        } else if ($type === 'monthly') {
            $title = '月报表';
        }
        
        return $title;
    }
} 