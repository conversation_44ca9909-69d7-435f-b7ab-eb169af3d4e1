<?php

namespace App\Exports;

use App\Models\Installation\InstallBooking;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Concerns\WithMultipleSheets;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class InstallStatisticsExport implements FromCollection, WithHeadings, WithMapping, WithTitle
{
    protected $request;
    protected $type;

    public function __construct(Request $request)
    {
        $this->request = $request;
        $this->type = $request->type ?? 'summary';
    }

    /**
     * @return \Illuminate\Support\Collection
     */
    public function collection()
    {
        // 时间范围设置
        $startDate = $this->request->start_date ? Carbon::parse($this->request->start_date) : Carbon::now()->startOfMonth();
        $endDate = $this->request->end_date ? Carbon::parse($this->request->end_date) : Carbon::now()->endOfMonth();
        
        switch ($this->type) {
            case 'month':
                return $this->getMonthlySummary($startDate, $endDate);
                
            case 'day':
                return $this->getDailySummary($startDate, $endDate);
                
            case 'engineer':
                return $this->getEngineerSummary($startDate, $endDate);
                
            case 'summary':
            default:
                return $this->getSummaryStats($startDate, $endDate);
        }
    }

    /**
     * @return array
     */
    public function headings(): array
    {
        switch ($this->type) {
            case 'month':
                return [
                    '月份',
                    '预约总数',
                    '待处理',
                    '已确认',
                    '已分配',
                    '进行中',
                    '已完成',
                    '已取消',
                    '完成率(%)',
                    '平均安装时长(小时)'
                ];
                
            case 'day':
                return [
                    '日期',
                    '预约总数',
                    '待处理',
                    '已确认',
                    '已分配',
                    '进行中',
                    '已完成',
                    '已取消'
                ];
                
            case 'engineer':
                return [
                    '工程师ID',
                    '工程师姓名',
                    '电话',
                    '分配总数',
                    '完成数量',
                    '进行中数量',
                    '已分配数量',
                    '取消数量',
                    '完成率(%)',
                    '平均安装时长(小时)'
                ];
                
            case 'summary':
            default:
                return [
                    '统计项目',
                    '数值'
                ];
        }
    }

    /**
     * @param mixed $row
     * @return array
     */
    public function map($row): array
    {
        switch ($this->type) {
            case 'month':
                return [
                    $row->month,
                    $row->total_bookings,
                    $row->pending_bookings,
                    $row->confirmed_bookings,
                    $row->assigned_bookings,
                    $row->in_progress_bookings,
                    $row->completed_bookings,
                    $row->cancelled_bookings,
                    $row->completion_rate,
                    $row->avg_duration
                ];
                
            case 'day':
                return [
                    $row->date,
                    $row->total_bookings,
                    $row->pending,
                    $row->confirmed,
                    $row->assigned,
                    $row->in_progress,
                    $row->completed,
                    $row->cancelled
                ];
                
            case 'engineer':
                return [
                    $row->engineer_id,
                    $row->display_name,
                    $row->engineer_phone,
                    $row->total_assigned,
                    $row->completed_count,
                    $row->in_progress_count,
                    $row->assigned_count,
                    $row->cancelled_count,
                    $row->completion_rate,
                    $row->avg_completion_time
                ];
                
            case 'summary':
            default:
                return [
                    $row->item,
                    $row->value
                ];
        }
    }

    /**
     * @return string
     */
    public function title(): string
    {
        switch ($this->type) {
            case 'month':
                return '月度统计';
                
            case 'day':
                return '日统计';
                
            case 'engineer':
                return '工程师统计';
                
            case 'summary':
            default:
                return '汇总统计';
        }
    }

    /**
     * 获取月度统计数据
     *
     * @param Carbon $startDate
     * @param Carbon $endDate
     * @return Collection
     */
    private function getMonthlySummary(Carbon $startDate, Carbon $endDate)
    {
        $months = collect();
        $startMonth = $startDate->copy()->startOfMonth();
        $endMonth = $endDate->copy()->startOfMonth();
        
        while ($startMonth->lte($endMonth)) {
            $monthStart = $startMonth->copy()->startOfMonth();
            $monthEnd = $startMonth->copy()->endOfMonth();
            $monthKey = $startMonth->format('Y-m');
            
            // 查询每个月各状态的预约数量
            $monthStats = DB::table('install_bookings')
                ->select(
                    DB::raw("'{$monthKey}' as month"),
                    DB::raw('COUNT(*) as total_bookings'),
                    DB::raw('SUM(CASE WHEN status = "pending" THEN 1 ELSE 0 END) as pending_bookings'),
                    DB::raw('SUM(CASE WHEN status = "confirmed" THEN 1 ELSE 0 END) as confirmed_bookings'),
                    DB::raw('SUM(CASE WHEN status = "assigned" THEN 1 ELSE 0 END) as assigned_bookings'),
                    DB::raw('SUM(CASE WHEN status = "in_progress" THEN 1 ELSE 0 END) as in_progress_bookings'),
                    DB::raw('SUM(CASE WHEN status = "completed" THEN 1 ELSE 0 END) as completed_bookings'),
                    DB::raw('SUM(CASE WHEN status = "cancelled" THEN 1 ELSE 0 END) as cancelled_bookings')
                )
                ->whereBetween('created_at', [$monthStart, $monthEnd])
                ->first();
                
            // 计算完成率
            $completionRate = $monthStats->total_bookings > 0 
                ? round(($monthStats->completed_bookings / $monthStats->total_bookings) * 100, 2) 
                : 0;
                
            // 获取平均安装时长（小时）
            $avgDuration = 0;
            if ($monthStats->completed_bookings > 0) {
                $avgDurationResult = DB::table('install_bookings')
                    ->select(DB::raw('AVG(TIMESTAMPDIFF(HOUR, install_time, completion_time)) as avg_time'))
                    ->where('status', 'completed')
                    ->whereNotNull('completion_time')
                    ->whereBetween('created_at', [$monthStart, $monthEnd])
                    ->first();
                    
                $avgDuration = $avgDurationResult && is_numeric($avgDurationResult->avg_time) 
                    ? round($avgDurationResult->avg_time, 1) 
                    : 0;
            }
            
            $monthStats->completion_rate = $completionRate;
            $monthStats->avg_duration = $avgDuration;
            
            $months->push($monthStats);
            
            $startMonth->addMonth();
        }
        
        return $months;
    }

    /**
     * 获取日统计数据
     *
     * @param Carbon $startDate
     * @param Carbon $endDate
     * @return Collection
     */
    private function getDailySummary(Carbon $startDate, Carbon $endDate)
    {
        $days = collect();
        $currentDate = $startDate->copy();
        
        while ($currentDate->lte($endDate)) {
            $dayKey = $currentDate->format('Y-m-d');
            
            // 查询每天各状态的预约数量
            $dayStats = new \stdClass();
            $dayStats->date = $dayKey;
            $dayStats->total_bookings = InstallBooking::whereDate('created_at', $dayKey)->count();
            $dayStats->pending = InstallBooking::whereDate('created_at', $dayKey)->where('status', 'pending')->count();
            $dayStats->confirmed = InstallBooking::whereDate('created_at', $dayKey)->where('status', 'confirmed')->count();
            $dayStats->assigned = InstallBooking::whereDate('created_at', $dayKey)->where('status', 'assigned')->count();
            $dayStats->in_progress = InstallBooking::whereDate('created_at', $dayKey)->where('status', 'in_progress')->count();
            $dayStats->completed = InstallBooking::whereDate('completion_time', $dayKey)->where('status', 'completed')->count();
            $dayStats->cancelled = InstallBooking::whereDate('updated_at', $dayKey)->where('status', 'cancelled')->count();
            
            $days->push($dayStats);
            
            $currentDate->addDay();
        }
        
        return $days;
    }

    /**
     * 获取工程师统计数据
     *
     * @param Carbon $startDate
     * @param Carbon $endDate
     * @return Collection
     */
    private function getEngineerSummary(Carbon $startDate, Carbon $endDate)
    {
        // 查询有安装任务的工程师
        $engineerStats = DB::table('install_bookings as b')
            ->join('app_users as u', 'b.engineer_id', '=', 'u.id')
            ->whereBetween('b.created_at', [$startDate, $endDate])
            ->select(
                'u.id as engineer_id',
                'u.name as engineer_name',
                'u.nickname as engineer_nickname',
                'u.wechat_nickname as engineer_wechat_nickname',
                'u.phone as engineer_phone',
                DB::raw('COUNT(b.id) as total_assigned'),
                DB::raw('SUM(CASE WHEN b.status = "completed" THEN 1 ELSE 0 END) as completed_count'),
                DB::raw('SUM(CASE WHEN b.status = "assigned" THEN 1 ELSE 0 END) as assigned_count'),
                DB::raw('SUM(CASE WHEN b.status = "in_progress" THEN 1 ELSE 0 END) as in_progress_count'),
                DB::raw('SUM(CASE WHEN b.status = "cancelled" THEN 1 ELSE 0 END) as cancelled_count'),
                DB::raw('AVG(CASE WHEN b.status = "completed" AND b.completion_time IS NOT NULL THEN TIMESTAMPDIFF(HOUR, b.install_time, b.completion_time) ELSE NULL END) as avg_completion_time')
            )
            ->groupBy('u.id', 'u.name', 'u.nickname', 'u.wechat_nickname', 'u.phone')
            ->orderBy('completed_count', 'desc')
            ->get();
            
        // 处理工程师显示名称和计算其他指标
        $engineerStats = $engineerStats->map(function ($engineer) {
            $engineer->display_name = $engineer->engineer_wechat_nickname ?: $engineer->engineer_nickname ?: $engineer->engineer_name ?: '未知工程师';
            
            // 计算完成率
            $engineer->completion_rate = $engineer->total_assigned > 0 
                ? round(($engineer->completed_count / $engineer->total_assigned) * 100, 2) 
                : 0;
                
            // 格式化平均完成时间
            $engineer->avg_completion_time = $engineer->avg_completion_time 
                ? round($engineer->avg_completion_time, 1) 
                : 0;
                
            return $engineer;
        });
        
        return $engineerStats;
    }

    /**
     * 获取汇总统计数据
     *
     * @param Carbon $startDate
     * @param Carbon $endDate
     * @return Collection
     */
    private function getSummaryStats(Carbon $startDate, Carbon $endDate)
    {
        $stats = collect();
        
        // 查询区间内的预约总数
        $totalBookings = InstallBooking::whereBetween('created_at', [$startDate, $endDate])->count();
        
        // 不同状态的预约数
        $pendingBookings = InstallBooking::whereBetween('created_at', [$startDate, $endDate])
            ->where('status', 'pending')
            ->count();
            
        $confirmedBookings = InstallBooking::whereBetween('created_at', [$startDate, $endDate])
            ->where('status', 'confirmed')
            ->count();
            
        $assignedBookings = InstallBooking::whereBetween('created_at', [$startDate, $endDate])
            ->where('status', 'assigned')
            ->count();
            
        $inProgressBookings = InstallBooking::whereBetween('created_at', [$startDate, $endDate])
            ->where('status', 'in_progress')
            ->count();
            
        $completedBookings = InstallBooking::whereBetween('created_at', [$startDate, $endDate])
            ->where('status', 'completed')
            ->count();
            
        $cancelledBookings = InstallBooking::whereBetween('created_at', [$startDate, $endDate])
            ->where('status', 'cancelled')
            ->count();
        
        // 计算各种百分比
        $completionRate = $totalBookings > 0 ? round(($completedBookings / $totalBookings) * 100, 2) : 0;
        $cancelRate = $totalBookings > 0 ? round(($cancelledBookings / $totalBookings) * 100, 2) : 0;
        
        // 计算平均安装时长（小时）
        $avgInstallTime = 0;
        if ($completedBookings > 0) {
            $avgTimeResult = InstallBooking::where('status', 'completed')
                ->whereNotNull('completion_time')
                ->whereBetween('created_at', [$startDate, $endDate])
                ->select(DB::raw('AVG(TIMESTAMPDIFF(HOUR, install_time, completion_time)) as avg_time'))
                ->first();
                
            $avgInstallTime = $avgTimeResult && is_numeric($avgTimeResult->avg_time) 
                ? round($avgTimeResult->avg_time, 1) 
                : 0;
        }
        
        // 获取收入统计
        $totalAmount = InstallBooking::whereBetween('created_at', [$startDate, $endDate])
            ->where('payment_status', 'paid')
            ->sum('total_amount');
            
        $installationFees = InstallBooking::whereBetween('created_at', [$startDate, $endDate])
            ->where('payment_status', 'paid')
            ->sum('installation_fee');
            
        $packageFees = InstallBooking::whereBetween('created_at', [$startDate, $endDate])
            ->where('payment_status', 'paid')
            ->sum('package_price');
            
        // 添加统计项
        $stats->push((object)['item' => '统计开始日期', 'value' => $startDate->format('Y-m-d')]);
        $stats->push((object)['item' => '统计结束日期', 'value' => $endDate->format('Y-m-d')]);
        $stats->push((object)['item' => '预约总数', 'value' => $totalBookings]);
        $stats->push((object)['item' => '待处理预约数', 'value' => $pendingBookings]);
        $stats->push((object)['item' => '已确认预约数', 'value' => $confirmedBookings]);
        $stats->push((object)['item' => '已分配预约数', 'value' => $assignedBookings]);
        $stats->push((object)['item' => '进行中预约数', 'value' => $inProgressBookings]);
        $stats->push((object)['item' => '已完成预约数', 'value' => $completedBookings]);
        $stats->push((object)['item' => '已取消预约数', 'value' => $cancelledBookings]);
        $stats->push((object)['item' => '完成率(%)', 'value' => $completionRate]);
        $stats->push((object)['item' => '取消率(%)', 'value' => $cancelRate]);
        $stats->push((object)['item' => '平均安装时长(小时)', 'value' => $avgInstallTime]);
        $stats->push((object)['item' => '总收入(元)', 'value' => $totalAmount]);
        $stats->push((object)['item' => '安装费收入(元)', 'value' => $installationFees]);
        $stats->push((object)['item' => '套餐费收入(元)', 'value' => $packageFees]);
        
        return $stats;
    }
} 