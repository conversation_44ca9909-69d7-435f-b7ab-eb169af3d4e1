---
description: 
globs: 
alwaysApply: false
---
# 点点够Nginx配置规范

## 服务器配置
- 运行在配置了Nginx + PHP8.1 + SSL的服务器上
- 域名: https://pay.itapgo.com
- 网站根目录: /www/wwwroot/pay.itapgo.com
- 项目目录: /www/wwwroot/pay.itapgo.com/Tapp

## Nginx配置文件
- 主配置文件: /www/server/panel/vhost/nginx/pay.itapgo.com.conf

## 关键路径配置

### 管理后台
- 管理后台入口: /admin/ -> /www/wwwroot/pay.itapgo.com/Tapp/admin/
- 原生PHP API路径: /admin/api/ -> /www/wwwroot/pay.itapgo.com/Tapp/admin/api/
- Laravel API路径: /api/ -> /www/wwwroot/pay.itapgo.com/Tapp/admin/public/api/

### 手机端应用
- 手机端入口: /app/ -> /www/wwwroot/pay.itapgo.com/Tapp/app-vue/dist/

## 注意事项
- API路径的优先级顺序非常重要，特定路径需先于通用路径配置
- 所有SPA应用需配置try_files确保前端路由正常工作
- 静态资源应设置适当的缓存时间
- 确保禁止访问敏感文件(.env, .git等)
