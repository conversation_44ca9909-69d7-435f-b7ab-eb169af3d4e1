---
description: 
globs: 
alwaysApply: false
---
# 登录流程架构与实现指南

## 登录方式概述

系统目前支持以下登录方式，**优先使用原生PHP API**：

1. **微信一键登录**：
   - **主要实现**：[admin/api/user/wechat_login_url.php](mdc:admin/api/user/wechat_login_url.php) 和 [admin/api/wechat/wechat_login_callback.php](mdc:admin/api/wechat/wechat_login_callback.php)
   - 备选实现：`/api/wechat/login-url` 和 `/api/wechat/callback`

2. **手机号密码登录**：
   - **主要实现**：[admin/api/user/login.php](mdc:admin/api/user/login.php)
   - 备选实现：`/api/auth/login`

3. **手机号验证码登录**：
   - **主要实现**：[admin/api/user/login_by_sms.php](mdc:admin/api/user/login_by_sms.php)
   - 备选实现：`/api/auth/sms-login`

## 前端API调用策略

前端API调用使用多路径尝试机制，优先使用原生PHP API，并在原生API失败时自动回退尝试RESTful API：

```js
// 例如微信登录URL获取
export function getWechatLoginUrl(data = {}) {
  // ...
  
  return new Promise((resolve, reject) => {
    // 首先尝试原生PHP API
    request({
      url: '/api/user/wechat_login_url.php',
      method: 'get',
      params,
      // ...
    }).then(response => {
      resolve(response);
    }).catch(error => {
      // 如果原生API失败，尝试RESTful API
      request({
        url: '/api/wechat/login-url',
        // ...
      }).then(response => {
        resolve(response);
      }).catch(finalError => {
        reject(finalError);
      });
    });
  });
}
```

前端相关API封装位于 [app-vue/src/api/user.js](mdc:app-vue/src/api/user.js)。

## 登录流程详解

### 微信一键登录流程

1. **获取微信授权URL**:
   - 前端调用API获取微信OAuth授权URL：[admin/api/user/wechat_login_url.php](mdc:admin/api/user/wechat_login_url.php)
   - 构建包含appid、redirect_uri、scope和state的授权URL
   - 返回完整授权URL给前端

2. **微信授权回调处理**:
   - 用户授权后微信重定向到回调URL
   - 前端从URL中提取code和state参数
   - 调用回调处理API：[admin/api/wechat/wechat_login_callback.php](mdc:admin/api/wechat/wechat_login_callback.php)
   - 后端验证参数并通过code获取微信access_token和openid
   - 获取微信用户信息(unionid, nickname, headimgurl等)
   - 查询或创建系统用户记录
   - 生成JWT令牌并返回

3. **绑定手机号(如需)**:
   - 检查用户是否已绑定手机号（通过needBindPhone字段标识）
   - 未绑定则前端引导用户进入手机号绑定流程
   - 调用手机号绑定API [admin/api/user/bind_phone.php](mdc:admin/api/user/bind_phone.php)

### 手机号密码登录流程

1. **表单提交**:
   - 前端收集手机号和密码
   - 调用登录API [admin/api/user/login.php](mdc:admin/api/user/login.php)

2. **后端验证**:
   - 验证手机号格式和密码长度
   - 查询用户记录并验证密码哈希
   - 验证成功后生成JWT令牌
   - 返回用户信息和令牌

### 手机号验证码登录流程

1. **发送验证码**:
   - 前端调用发送验证码API [admin/api/user/send_sms_code.php](mdc:admin/api/user/send_sms_code.php)
   - 后端生成随机验证码并发送短信

2. **验证并登录**:
   - 前端提交手机号和验证码
   - 调用验证码登录API [admin/api/user/login_by_sms.php](mdc:admin/api/user/login_by_sms.php)
   - 后端验证验证码有效性
   - 查询或创建用户记录
   - 生成JWT令牌并返回

## 角色验证流程

登录后，系统通过以下流程验证用户角色和权限：

1. **角色判定**:
   - 前端调用角色验证API [admin/api/user/check_user_roles.php](mdc:admin/api/user/check_user_roles.php)
   - 后端根据用户ID查询所有关联角色(普通用户、商家、业务员等)

2. **权限控制**:
   - 前端根据角色信息动态调整UI显示
   - 后端API通过验证请求的Authorization头来控制权限

## 原生API实现注意事项

为确保原生API正常工作，以下文件必须保持原始实现：

1. [admin/api/user/login.php](mdc:admin/api/user/login.php)：手机号密码登录
2. [admin/api/user/wechat_login_url.php](mdc:admin/api/user/wechat_login_url.php)：微信登录URL获取
3. [admin/api/wechat/wechat_login_callback.php](mdc:admin/api/wechat/wechat_login_callback.php)：微信登录回调处理
4. [admin/api/user/bind_phone.php](mdc:admin/api/user/bind_phone.php)：手机号绑定
5. [admin/api/user/check_user_roles.php](mdc:admin/api/user/check_user_roles.php)：用户角色检查
6. [admin/api/user/send_sms_code.php](mdc:admin/api/user/send_sms_code.php)：短信验证码发送
7. [admin/api/user/login_by_sms.php](mdc:admin/api/user/login_by_sms.php)：验证码登录

这些文件应当直接实现功能逻辑，而**不应该**重定向到RESTful API。

## 令牌管理

系统使用JWT(JSON Web Token)进行身份验证：

1. **令牌生成**:
   - 登录成功后生成包含用户ID和角色的JWT
   - 设置令牌过期时间(默认7天)

2. **令牌验证**:
   - 前端请求时在Header中附加令牌
   - 后端函数验证令牌的有效性和过期状态

3. **令牌刷新**:
   - 前端检测令牌即将过期时调用刷新API
   - 后端验证旧令牌并颁发新令牌

## 安全措施

1. **前端安全**:
   - 密码不明文传输，使用HTTPS
   - 令牌存储在localStorage，设置过期时间

2. **后端安全**:
   - 密码使用bcrypt或Argon2哈希存储
   - 实施API限流防止暴力破解
   - 验证码有效期限制(5分钟)
   - 同一手机号发送频率限制(1分钟1次)

## 登录错误处理

常见登录错误及处理：

1. **账号不存在**: 提示用户注册或使用其他登录方式
2. **密码错误**: 提示密码错误，允许尝试3次
3. **验证码错误**: 提示验证码错误或过期，允许重新获取
4. **账号锁定**: 多次登录失败后，锁定账号15分钟
