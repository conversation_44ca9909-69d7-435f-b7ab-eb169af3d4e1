# APP用户模块迁移到Laravel RESTful风格接口

## 背景

点点够系统目前正在进行API架构升级，将原生PHP API逐步迁移到Laravel RESTful风格接口。这个规则定义了APP用户模块的迁移标准和要求。

## 适用范围

适用于点点够系统中的APP用户模块，包括但不限于用户列表、详情、创建、更新、删除等功能。

## 迁移原则

1. **功能完整性**：确保迁移后的API功能与原有API完全一致
2. **响应格式统一**：迁移后的API必须采用统一的响应格式
3. **向后兼容**：支持旧版API调用方式，确保平滑过渡
4. **命名规范**：遵循RESTful API设计规范的命名约定
5. **安全性**：确保认证和授权机制正确实现

## API路径规范

### 旧路径

```
/admin/api/admin/app-users.php
```

### 新路径

```
/api/app-users
```

## API端点规范

| 操作 | HTTP方法 | 路径 | 控制器方法 | 描述 |
|------|----------|------|------------|------|
| 获取用户列表 | GET | /api/app-users | index | 获取APP用户列表，支持筛选和分页 |
| 获取单个用户 | GET | /api/app-users/{id} | show | 获取指定ID的用户详情 |
| 创建用户 | POST | /api/app-users | store | 创建新用户 |
| 更新用户 | PUT | /api/app-users/{id} | update | 更新指定ID的用户 |
| 删除用户 | DELETE | /api/app-users/{id} | destroy | 删除指定ID的用户 |
| 更新用户状态 | PATCH | /api/app-users/{id}/status | updateStatus | 启用或禁用用户 |
| 重置密码 | PATCH | /api/app-users/{id}/password | resetPassword | 重置用户密码 |
| 同步用户角色 | GET | /api/app-users/{id}/sync-roles | syncRoles | 同步单个用户的角色 |
| 同步所有用户角色 | POST | /api/app-users/sync-roles | syncAllUsersRoles | 同步所有用户的角色 |
| 获取机构层级 | GET | /api/app-users/institutions/hierarchy | getInstitutionHierarchy | 获取机构层级关系 |

## 响应格式规范

所有API必须返回统一的JSON格式：

```json
{
  "code": 0,       // 0表示成功，非0表示错误
  "message": "成功", // 成功或错误消息
  "data": [...],   // 数据部分
  "total": 100,    // 总记录数（列表接口）
  "page": 1,       // 当前页码（列表接口）
  "limit": 10      // 每页记录数（列表接口）
}
```

## 前端调用规范

1. 所有前端API调用必须通过`request.js`封装的方法进行：

```javascript
import request from '@/utils/request';

// 获取用户列表
export function fetchAppUsers(query) {
  return request.get('/api/app-users', query);
}

// 获取用户详情
export function getAppUser(id) {
  return request.get(`/api/app-users/${id}`);
}

// 创建用户
export function createAppUser(data) {
  return request.post('/api/app-users', data);
}

// 更新用户
export function updateAppUser(id, data) {
  return request.put(`/api/app-users/${id}`, data);
}

// 删除用户
export function deleteAppUser(id) {
  return request.delete(`/api/app-users/${id}`);
}
```

2. 响应处理必须使用`formatResponse`方法处理：

```javascript
import { formatResponse } from '@/utils/request';

export function fetchAppUsers(query) {
  return request.get('/api/app-users', query)
    .then(response => {
      return formatResponse(response);
    });
}
```

## 认证要求

所有API端点（除非明确指定为公开）必须实现`auth:sanctum`中间件进行认证：

```php
Route::middleware('auth:sanctum')->prefix('app-users')->group(function () {
    // 路由定义...
});
```

## 日志要求

所有API操作必须记录适当的日志：

1. 请求参数日志
2. 错误日志
3. 关键操作日志（如创建、删除用户）

## 错误处理

API必须正确处理以下错误场景：

1. 参数验证失败
2. 资源不存在
3. 权限不足
4. 业务逻辑错误
5. 服务器内部错误

## 测试要求

迁移后的API必须通过以下测试：

1. 单元测试
2. 功能测试
3. 性能测试
4. 安全测试

## 版本控制

每次API变更都必须通过版本控制系统（Git）记录并保留历史版本。

## 文档要求

每个迁移的API端点都必须在API文档中更新，文档应包含：

1. 端点URL
2. 请求方法
3. 请求参数
4. 响应格式
5. 示例
6. 错误代码

## 迁移验收标准

1. 所有API功能测试通过
2. 前端界面所有功能正常
3. API响应时间不超过原有API的1.2倍
4. 无安全漏洞
5. 文档完整 