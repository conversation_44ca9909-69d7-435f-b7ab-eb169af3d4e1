Laravel+Vue+mysql5.7
每次更新完、修复完重大的功能都要去更新README.md文档，按照现在的更新记录去续写；
mysql的配置信息在admin目录下的.env文件，ddg.app是本项目主数据库，b.tapgo.cn支付数据库，jzq_water_plat净水器数据库
当前打开的就是在服务器正式环境，我是通过远程连接到服务器的
api用Laravel RESTful风格
凡修改了前端部分，都要通过运行构建部署脚本进行部署：
手机端构建部署脚本在app-vue/.build.sh;
管理后台构建部署脚本在admin/.build.sh
服务器通过/etc/init.d/nginx restart重启nginx
本项目域名pay.itapgo.com站点的nginx配置文件在：/www/server/panel/vhost/nginx/pay.itapgo.com.conf，这个文件不可以随意删减，任何修改必须获得我的同意。
管理后台的控制器V1版本API路径：/admin/app/Http/Controllers/Admin/Api/V1
手机端的控制器V1版本API路径：/admin/app/Http/Controllers/Mobile/Api/V1
分红制度非常重要，在docs/VIP会员分红机制说明.md